<template>
  <!-- 页面根容器，根据屏幕高度设置最小高度 -->
  <view class="page" :style="{ 'min-height': SCREENHEIGHT() + 'px' }">
    <!-- 顶部用户名、头像 banner，点击可跳转到个人信息页面 -->
    <view @click="goTo('info')" class="mybanner">
      <!-- 头像容器 -->
      <view class="avatar-wraper">
        <!-- 头像图片，根据 roundAvatar 决定是否显示为圆形，加载出错时调用 imageError 方法 -->
        <image
          :src="avatarSrc"
          :style="{ borderRadius: roundAvatar ? '50%' : '3px' }"
          mode="aspectFill"
          class="avatar"
          @error="imageError"
        >
        </image>
      </view>
      <!-- 用户信息容器 -->
      <view class="info-wraper">
        <!-- 用户名显示 -->
        <view class="username">{{ userName }}</view>
        <!-- 用户标签显示，使用 uni-tag 组件循环渲染 -->
        <view>
          <uni-tag
            v-for="(tag, index) of userTag"
            :key="index"
            :text="tag.title"
            :type="tag.type"
            size="small"
          ></uni-tag>
        </view>
      </view>
      <!-- 右侧箭头图标，提示可点击跳转 -->
      <view class="icon-wraper"
        ><learun-icon type="learun-icon-right-arrow" color="#fff"
      /></view>
    </view>

    <!-- 列表容器，包含多个功能列表项 -->
    <view class="learun-panel">
      <uni-list>
        <!-- 消息中心列表项，点击可跳转到消息中心页面 -->
        <uni-list-item
          @click="goTo('msg')"
          showExtraIcon
          :extraIcon="{ color: '#2979ff', size: '16', type: 'chat' }"
          :title="$t('消息中心')"
          showArrow
          clickable
        ></uni-list-item>
        <!-- 通讯录列表项，点击可跳转到通讯录页面 -->
        <uni-list-item
          @click="goTo('connection')"
          showExtraIcon
          :extraIcon="{ color: '#2979ff', size: '16', type: 'contact' }"
          :title="$t('通讯录')"
          showArrow
          clickable
        ></uni-list-item>
        <!-- 我的二维码列表项，点击可跳转到我的二维码页面 -->
        <uni-list-item
          @click="goTo('qrcode')"
          showExtraIcon
          :extraIcon="{ color: '#2979ff', size: '16', type: 'bars' }"
          :title="$t('我的二维码')"
          showArrow
          clickable
        ></uni-list-item>
        <!-- 语言选择列表项，点击可打开语言选择弹窗 -->
        <uni-list-item
          @click="pickerLangType()"
          showExtraIcon
          :extraIcon="{ color: '#2979ff', size: '16', type: 'gear-filled' }"
          :title="`${$t('语言')}：${$t(langName())}`"
          showArrow
          clickable
        ></uni-list-item>
        <!-- 清空缓存列表项，点击可清除服务器缓存 -->
        <uni-list-item
          @click="removeCache()"
          showExtraIcon
          :extraIcon="{ color: '#2979ff', size: '16', type: 'loop' }"
          :title="`${$t('清空缓存')}`"
          showArrow
          clickable
        ></uni-list-item>
        <!-- 重置密码列表项，当 isShowPassword 为 true 时显示，点击可跳转到重置密码页面 -->
        <uni-list-item
          @click="resetPassword()"
          showExtraIcon
          :extraIcon="{ color: '#2979ff', size: '16', type: 'compose' }"
          :title="$t('重置密码')"
          showArrow
          clickable
          v-if="isShowPassword"
        ></uni-list-item>
        <!-- 调整岗位列表项，当前注释掉，当用户岗位列表不为空时可显示，点击可打开岗位选择弹窗 -->
        <!-- <uni-list-item @click="pickerPostType()" showExtraIcon
                    :extraIcon="{color: '#2979ff',size: '16',type: 'person-filled'}"
                    :title="`${$t('岗位')}：${postName()}`" showArrow clickable
                    v-if="userPosts.length > 0"></uni-list-item> -->
      </uni-list>
      <!-- 版本信息列表，设置内边距和背景颜色 -->
      <uni-list style="padding-top: 15px; background-color: #f7f7f7">
        <!-- 显示版本信息 -->
        <uni-list-item
          :title="`APP(${LEARUN_CONFIG('QASorPRD')}) ${$t(
            '版本：'
          )} ${LEARUN_CONFIG('updateDate')} `"
          style="border-radius: "
        ></uni-list-item>
      </uni-list>
    </view>

    <!-- 仅在微信小程序环境下编译 -->
    <!-- #ifdef MP-WEIXIN -->
    <!-- 小程序绑定/解绑按钮容器，根据 MPBind 和 MPUnbind 条件显示 -->
    <!-- <view class="learun-panel" v-if="MPBind || MPUnbind">
            <button v-if="MPBind && !currentUser.f_WxOpenId" @click="userBind()"
                style="background: #3399FF;color: #fff;">{{$t('绑定')}}{{PLATFORM_TEXT}}</button>
            <button v-else-if="MPUnbind" @click="userUnBind()"
                style="background: #FF0033;color: #fff;">{{$t('解除')}}{{PLATFORM_TEXT}}{{$t('绑定')}}</button>
        </view> -->
    <!--#endif-->

    <!-- 退出登录按钮容器 -->
    <view class="learun-panel">
      <!-- 退出登录按钮，点击调用 logout 方法 -->
      <button @click="logout" type="warn">{{ $t("退出登录") }}</button>
    </view>

    <!-- 页面底部版权信息显示 -->
    <view class="footer">{{ copyRightDisplay }}</view>

    <!-- 语言选择弹出框，当语言类型数量大于 1 时显示 -->
    <uni-picker
      v-if="langTypes().length > 1"
      :range="langTypes().map((item) => item.label)"
      @change="onPickerChange"
      class="custom-uni-picker"
    >
    </uni-picker>

    <!-- 岗位切换弹出框，当前注释掉 -->
    <!-- <learun-picker-popup ref="postsPicker" :options="userPosts" @change="changeCurrentPost">
        </learun-picker-popup> -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 用户标签数组
      userTag: [],
      // 当前登录用户信息对象
      currentUser: {},
      // 头像默认图片路径
      defaultSrc: null,
      // 用户岗位列表
      userPosts: [],
      // 是否显示重置查询密码按钮的标志
      isShowPassword: false,
    };
  },

  // 页面加载时执行的钩子函数
  async onLoad() {
    // 检查页面是否可以启动，若可以则初始化页面
    if (await this.PAGE_LAUNCH()) {
      await this.init();
    }
  },

  methods: {
    // 页面初始化方法
    async init() {
      try {
        // 获取全局登录用户信息
        this.currentUser = this.GET_GLOBAL("loginUser");
        // 获取用户标签信息
        this.userTag = await this.getUserTag();
        // 获取用户岗位列表
        this.userPosts = (await this.getPosts()) || [];
        // 并发请求功能模块数据
        const [modules] = await Promise.all([
          this.request({
            url: "/mapp/modules",
          }),
        ]);
        // 判断是否显示重置密码按钮，若模块列表中包含职员工资单则显示
        this.isShowPassword = modules.some(
          (t) => t.f_Name === this.$t("职员工资单")
        );
      } catch (error) {
        // 抛出初始化过程中的错误
        throw error;
      }
    },

    // 点击「注销登录」按钮的处理方法
    async logout() {
      // 弹出确认框，询问是否注销登录
      if (
        await this.CONFIRM(
          this.$t("注销确认"),
          this.$t("确定要注销登录吗？"),
          true
        )
      ) {
        // 清除全局数据
        this.CLEAR_GLOBAL();
        // 清除本地存储的 token
        this.SET_STORAGE("token", null);
        // 重新启动应用并跳转到登录页面
        this.RELAUNCH_TO("/pages/login");
      }
    },

    // 拉取用户标签信息的方法
    async getUserTag() {
      // 获取当前公司名称
      const companyName = (await this.GET_CURRENT_COMPANY()).f_FullName;
      // 获取当前部门名称
      const departmentName = (await this.GET_CURRENT_DEPARTMENT()).f_FullName;

      // 若公司名称不存在，返回总集团公司标签
      if (!companyName) {
        return [
          {
            title: this.$t("总集团公司"),
            type: "success",
          },
        ];
      }

      // 若部门名称不存在，返回公司名称标签
      if (!departmentName) {
        return [
          {
            title: companyName,
            type: "success",
          },
        ];
      }

      // 若公司和部门名称都存在，返回公司和部门名称标签
      return [
        {
          title: companyName,
          type: "success",
        },
        {
          title: departmentName,
          type: "warning",
        },
      ];
    },

    // 多语言切换，打开语言选择弹出框的方法
    async pickerLangType() {
      // 获取当前语言名称
      this.langName();
      // 获取当前存储的语言类型
      const currentLang = this.GET_STORAGE("learun_lang_type");
      try {
        // 发送请求获取语言类型列表
        const response = await this.HTTP_GET({
          url: `/language/types`,
        });
        // 处理响应数据，将其转换为包含 value 和 label 的对象数组
        const types = (response || []).map((t) => ({
          ...t,
          value: t.f_Code,
          label: t.f_Name,
        }));
        // 将语言类型列表存储到全局数据中
        this.SET_GLOBAL("lang_types", types);
        // 若语言类型数量小于等于 1，无法打开语言选择弹窗，输出错误信息并返回
        if (types.length <= 1) {
          console.error("语言类型数量不足，无法打开语言选择弹窗");
          return;
        }

        // 显示操作菜单，让用户选择语言
        uni.showActionSheet({
          itemList: types.map((item) => this.$t(item.label)),
          success: (res) => {
            // 获取用户选择的索引
            const selectedIndex = res.tapIndex;
            // 获取用户选择的语言值
            const selectedValue = types[selectedIndex].value;
            // 调用 changeLangType 方法切换语言
            this.changeLangType({
              index: selectedIndex,
              value: selectedValue,
            });
          },
          fail: (err) => {
            // 打开语言选择弹窗失败时，输出错误信息
            // console.error('打开语言选择弹窗失败:', err);
          },
        });
      } catch (error) {
        // 获取语言类型失败时，输出错误信息
        console.error(this.$t("获取语言类型失败:"), error);
      }
    },

    // 仅在支付宝或微信小程序环境下编译
    // #ifdef MP-ALIPAY || MP-WEIXIN
    // 小程序绑定方法
    async userBind() {
      // 获取当前平台类型
      const type = this.PLATFORM;
      // 弹出确认框，询问是否绑定小程序账号
      const confirm = await this.CONFIRM(
        "绑定确认",
        `${this.$t("确定要将您的Crystal Service Platform账号与当前登录的")}${
          this.PLATFORM_TEXT
        }${this.$t("账号绑定吗？\n(绑定后可以使用一键登录功能)")}`,
        true
      );
      // 若用户取消绑定，返回
      if (!confirm) {
        return;
      }

      // 显示加载提示
      this.LOADING(this.$t("绑定中…"));
      // 调用 uni.login 获取用户授权码
      const [codeErr, { code }] = await uni.login({
        provider: type,
      });
      // 若获取授权码失败，隐藏加载提示，显示错误提示并返回
      if (codeErr || !code) {
        this.HIDE_LOADING();
        this.TOAST(this.$t("获取用户授权码失败"));
        return;
      }

      // 发送绑定请求
      const success = await this.HTTP_POST({
        url: "/login/binding",
        params: {
          code,
          type,
        },
        errorTips: this.$t("绑定失败"),
      });
      // 若绑定成功，隐藏加载提示，更新用户信息，显示成功提示
      if (success) {
        this.HIDE_LOADING();
        this.currentUser.f_WxOpenId = "xxx";
        this.TOAST(
          `${this.$t("已成功绑定到当前的")}${this.PLATFORM_TEXT}${this.$t(
            "账号"
          )}`
        );
      }
    },

    // 解绑小程序的方法
    async userUnBind() {
      // 弹出确认框，询问是否解除小程序账号绑定
      const confirm = await this.CONFIRM(
        this.$t("解绑确认"),
        this.$t(
          "确定要解除小程序账号绑定吗？ （解绑将自动退出登录，需使用Crystal Service Platform账号和密码再次登录）"
        ),
        true
      );
      // 若用户取消解绑，返回
      if (!confirm) {
        return;
      }

      // 发送解绑请求
      const success = await this.HTTP_POST({
        url: "/login/unbinding",
        params: {
          type: this.PLATFORM,
        },
        errorTips: this.$t("解除绑定失败"),
      });
      // 若解绑成功，清除全局数据，清除本地存储的 token，重新启动应用并跳转到登录页面，显示成功提示
      if (success) {
        this.CLEAR_GLOBAL();
        this.SET_STORAGE("token", null);
        this.RELAUNCH_TO("/pages/login");
        this.TOAST(this.$t("已成功解除绑定"));
      }
    },
    // #endif

    // 跳转到指定页面的方法
    goTo(urlPath) {
      // 根据页面路径判断基础路径
      const basePath =
        urlPath === "connection" || urlPath === "msg" ? "/pages" : "/pagesMy";
      // 导航到指定页面
      this.NAV_TO(`${basePath}/${urlPath}`);
    },

    // 清除服务器缓存的方法
    async removeCache() {
      try {
        // 发送清除缓存请求
        const result = await this.request({
          url: "login/cache",
          method: "POST",
        });
        // 若清除成功，显示提示信息
        if (result) {
          this.showToast(this.$t("清除成功！"));
        }
      } catch (error) {
        // 清除缓存失败时，显示提示信息并记录错误日志
        this.showToast(this.$t("清除缓存失败"), "none");
        console.error(this.$t("清除缓存失败:"), error);
      }
    },

    // 重置查询密码，跳转到重置密码页面的方法
    resetPassword() {
      // 导航到重置密码页面
      this.NAV_TO("/pages/password/reset");
    },

    // 切换语言的方法
    async changeLangType({ index, value }) {
      try {
        // 存储选择的语言类型
        this.SET_STORAGE("learun_lang_type", value);
        // 获取语言数据
        await this.FETCH_LANG_DATA();
        // 设置 tabBar 多语言
        const tabBarTexts = ["首页", "工作台", "新闻", "我的"];
        tabBarTexts.forEach((text, index) => {
          uni.setTabBarItem({
            index,
            text: this.$t(text),
          });
        });
      } catch (error) {
        // 切换语言失败时，显示提示信息并记录错误日志
        this.showToast(this.$t("切换语言失败"), "none");
        console.error(this.$t("切换语言失败:"), error);
      }
    },
    // 获取语言类型列表的方法
    langTypes() {
      return this.GET_LANG_TYPES();
    },
    // 获取当前语言名称的方法
    langName() {
      // 获取当前存储的语言类型
      const type = this.GET_STORAGE("learun_lang_type");
      // 获取语言类型列表
      const list = this.langTypes();
      let lang;
      // 若当前存储的语言类型存在，查找对应的语言对象
      if (type) {
        lang = list.find((t) => t.value == type);
      }
      // 若未找到，查找默认语言对象
      if (!lang) {
        lang = list.find((t) => t.f_IsMain == 1);
      }
      // 返回语言名称，若未找到则返回空字符串
      return lang?.label || "";
    },
    // 岗位切换，当前注释掉
    // async pickerPostType() {
    // 	this.$refs.postsPicker.open(this.currentUser.postId)
    // },
    // 获取用户岗位列表的方法
    async getPosts() {
      // 调用 GET_USER_POSTS 方法获取岗位列表
      const res = await this.GET_USER_POSTS(
        String(this.currentUser.postIds || [])
      );
      return res;
    },
    // 切换当前岗位的方法
    async changeCurrentPost({ value }) {
      // 设置当前岗位
      this.SET_CURRENT_POST(value);
      // 获取当前岗位数据
      await this.FETCH_POST_DATA();
    },
    // 获取当前岗位名称的方法
    postName() {
      // 获取当前用户的岗位 ID
      const postId = this.currentUser.postId;
      // 从用户岗位列表中查找当前岗位对象
      const post = this.userPosts.find((t) => t.value === postId) || {};
      // 返回岗位名称，若未找到则返回空字符串
      return post.label || "";
    },
    // 头像图片加载出错的处理方法
    imageError() {
      // 设置头像默认图片路径
      this.defaultSrc = `/static/img-avatar/head.png`;
    },
    // 封装网络请求的方法
    async request(options) {
      const { url, method = "GET", data = {}, errorTips } = options;
      try {
        // 根据请求方法发送请求
        const response = await (method === "GET"
          ? this.HTTP_GET({
              url,
              ...options,
            })
          : this.HTTP_POST({
              url,
              data,
              ...options,
            }));
        return response;
      } catch (error) {
        if (errorTips) {
          // 请求失败时显示提示信息
          this.showToast(errorTips, "none");
        }
        // 抛出请求错误
        throw error;
      }
    },
    // 封装提示信息显示的方法
    showToast(title, icon = "success") {
      uni.showToast({
        title,
        icon,
      });
    },

    // uni-picker 选择事件处理方法
    onPickerChange(e) {
      // 获取用户选择的索引
      const selectedIndex = e.detail.value;
      // 获取语言类型列表
      const types = this.langTypes();
      // 获取用户选择的语言值
      const selectedValue = types[selectedIndex].value;
      // 调用 changeLangType 方法切换语言
      this.changeLangType({
        index: selectedIndex,
        value: selectedValue,
      });
    },
  },

  computed: {
    // 用户头像 url
    avatarSrc() {
      // 若存在默认图片路径，返回默认图片路径
      if (this.defaultSrc) {
        return this.defaultSrc;
      }
      // 获取全局 token
      const token = this.GET_GLOBAL("token");
      // 若用户有头像图标，返回头像图标链接，否则返回默认头像图片路径
      return this.currentUser.f_HeadIcon
        ? `${this.API}/system/annexesfile/${this.currentUser.f_HeadIcon}?token=${token}`
        : `/static/img-avatar/head.png`;
    },

    // 用户名称
    userName() {
      // 若当前用户存在，返回用户真实姓名，否则返回未知用户名
      return this.currentUser
        ? this.currentUser.f_RealName
        : this.$t("(未知用户名)");
    },

    // 头像圆形/方形显示参数
    roundAvatar() {
      // 根据配置项决定头像是否显示为圆形
      return this.LEARUN_CONFIG("pageConfig.roundAvatar");
    },

    // 页面底部公司/版权显示
    copyRightDisplay() {
      // 获取当前年份
      const year = new Date().getFullYear();
      // 获取公司名称
      const company = this.LEARUN_CONFIG("company");

      // 返回版权信息
      return `Copyright © ${year} ${company}`;
    },

    // 仅在支付宝或微信小程序环境下编译
    // #ifdef MP-ALIPAY || MP-WEIXIN
    // 是否显示小程序绑定按钮
    MPBind() {
      // 根据配置项判断是否显示小程序绑定按钮
      return this.LEARUN_CONFIG(`miniProgramAccount.${this.PLATFORM}`).includes(
        "bind"
      );
    },

    // 是否显示小程序解绑按钮
    MPUnbind() {
      // 根据配置项判断是否显示小程序解绑按钮
      return this.LEARUN_CONFIG(`miniProgramAccount.${this.PLATFORM}`).includes(
        "unbind"
      );
    },
    // #endif
  },
};
</script>

<style lang="scss" scoped>
// 页面根容器样式
.page {
  background-color: #f0f4f8;
  min-height: 100vh;
}

// 顶部用户名、头像 banner 样式
.mybanner {
  background-image: linear-gradient(
    to bottom,
    rgba(12, 134, 216, 0.3),
    rgba(12, 134, 216, 0.8)
  );
  height: 180px;
  // padding: 24px 16px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  perspective: 1000px;
}

// 返回按钮样式
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.15);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: white;
  font-size: 22px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease, transform 0.3s ease;
  transform-style: preserve-3d;
}

// 返回按钮悬停样式
.back-button:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateZ(5px);
}

// 返回按钮激活样式
.back-button:active {
  background-color: rgba(255, 255, 255, 0.35);
  transform: translateZ(2px);
}

// 向左箭头样式
.arrow-left {
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 14px solid white;
}

// 头像容器样式
.avatar-wraper {
  display: flex;
  align-items: center;
  margin-left: 25px;
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

// 头像容器悬停样式
.avatar-wraper:hover {
  transform: translateZ(5px);
}

// 头像容器和图标容器样式
.avatar-wraper,
.icon-wraper {
  flex-grow: 0;
  margin-right: 20px;
}

// 头像图片样式
.avatar {
  height: 80px;
  width: 80px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

// 用户信息容器样式
.info-wraper {
  // padding-left: 20px;
  flex-grow: 1;
}

// 用户名样式
.username {
  color: #fff;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: bold;
}

// 列表容器样式
.learun-panel {
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

// 列表容器悬停样式
.learun-panel:hover {
  transform: translateZ(5px);
}

// 页面底部版权信息样式
.footer {
  text-align: center;
  font-size: 12px;
  margin-bottom: 8px;
  color: #718096;
}
</style>
