<template>
	<view>
		<view @click.stop="handleClick" class="learun-input learun-input-border" :class="{'learun-input-placeholder':!hasValue}" >
			<view class="learun-input__content" ><text>{{label}}</text></view>
			<view class="learun-input-icon-right">
				<uni-icons v-if="!hasValue"  type="bottom" size="15" color="#c0c4cc" ></uni-icons>
				<view  v-else-if="!disabled"  @tap.stop="handleClear" >
					<uni-icons type="clear" size="15" color="#c0c4cc" ></uni-icons>
				</view>
			</view>
		</view>
		
		<uni-popup ref="popup" type="bottom">
			<view class="learun-picker-popup" >
				<view class="learun-popup-button-close" @click.stop="close">
					<learun-icon type="learun-icon-error" :size="14" color="#737987" ></learun-icon>
				</view>
				
				<picker-view v-if="isOpen" :value="myValue" @change="bindChange" class="picker-view" >
					<picker-view-column>
						<view class="item" v-for="(item,index) in years" :key="index">{{item}}</view>
					</picker-view-column>
					<picker-view-column v-if="isMonth" >
						<view class="item" v-for="(item,index) in months" :key="index">{{item}}</view>
					</picker-view-column>
					
					<picker-view-column>
						<view style="text-align: center;">-</view>
					</picker-view-column>
					
					<picker-view-column>
						<view class="item" v-for="(item,index) in years" :key="index">{{item}}</view>
					</picker-view-column>
					<picker-view-column v-if="isMonth">
						<view class="item" v-for="(item,index) in months" :key="index">{{item}}</view>
					</picker-view-column>
				</picker-view>
				
				<view class="learun-popup-button-wraper" >
					<view class="learun-popup-button-ok" @click="confirm">{{$t('确认')}}</view>
				</view>
			</view>
		</uni-popup>
		
	</view>
</template>

<script>
	export default {
		name: 'learun-year-month-picker',
		props:{
			value:{
				type:[String],
				default:''
			},
			clearable:{
				type:Boolean,
				default:true
			},
			format:{
				type:String,
				default:'yyyy-MM'
			},
			disabled:Boolean,
			startPlaceholder:{
				type:String,
				default:'开始日期'
			},
			endPlaceholder:{
				type:String,
				default:'结束日期'
			},
			isMonth:{
				type:Boolean,
				default:true
			},
		},
		computed:{
			years(){
				const year = parseInt(this.DATENOW('YYYY')) - 50
				const res = []
				
				for(let i = 0;i<70;i++){
					res.push((year + i) + '年')
				}
				return res
				
			},
			months(){
				return ['01月','02月','03月','04月','05月','06月','07月','08月','09月','10月','11月','12月']
			},
			label(){
				if(this.hasValue){
					return this.value
				}
				return `${this.startPlaceholder} - ${this.endPlaceholder}`
			},
			hasValue(){
				if(!this.VALIDATENULL(this.value)){
					return true
				}
				return false
			}
		},
		data(){
			return {
				myValue:[],
				tmpValue:[],
				isOpen:false
			}
		},
		methods:{
			handleClick(){
				if(this.disabled){
					return
				}
				
				this.myValue = []
				
				if(!this.VALIDATENULL(this.value)){
					const valueList = this.value.split(' - ')
					this.myValue.push(...this.getFormatValue(valueList[0]))
					this.myValue.push(0)
					this.myValue.push(...this.getFormatValue(valueList[1]))
					
				}
				else{
					this.myValue.push(50)
					if(this.isMonth){
						this.myValue.push(0)
					}
					this.myValue.push(0)
					this.myValue.push(50)
					if(this.isMonth){
						this.myValue.push(0)
					}
				}
				
				this.tmpValue = this.myValue
				this.$refs.popup.open()
				this.isOpen = true
			},
			getFormatValue(v){
				const formatValue = this.DATEFORMAT(v,'YYYY:MM',this.format)
				const formatValueList = formatValue.split(':')
			  
				const yearIndex = this.years.findIndex(t=>t== (formatValueList[0] + '年') )
				const monthIndex = this.months.findIndex(t=>t== (formatValueList[1] + '月'))
				
				const res = []
				res.push(yearIndex)
				
				if(this.isMonth){
					res.push(monthIndex)
				}
				
				return res
			},
			bindChange(e){
			  this.tmpValue =	e.detail.value
			},

			close(){
				this.$refs.popup.close()
				this.isOpen = false
			},
			confirm(){
				let startValue = ''
				let endValue = ''
				if(this.isMonth){
					startValue += this.years[this.tmpValue[0]].replace('年','')
					startValue +=":"
					startValue += this.months[this.tmpValue[1]].replace('月','')
					startValue = this.DATEFORMAT(startValue,this.format,"YYYY:MM")
					
					endValue += this.years[this.tmpValue[3]].replace('年','')
					endValue +=":"
					endValue += this.months[this.tmpValue[4]].replace('月','')
					endValue = this.DATEFORMAT(endValue,this.format,"YYYY:MM")
				}
				else{
					startValue += this.years[this.tmpValue[0]].replace('年','')
					startValue = this.DATEFORMAT(startValue,this.format,"YYYY")
					
					endValue += this.years[this.tmpValue[2]].replace('年','')
					endValue = this.DATEFORMAT(endValue,this.format,"YYYY")
				}
				
				const res = `${startValue} - ${endValue}`
				
				console.log(res)
				
				
				this.$emit('input',res)
				this.$emit('change',res)
				
				
				this.$refs.popup.close()
				this.isOpen = false
			},

			handleClear(){
				if(this.disabled){
					return
				}
				this.$emit('input','')
				this.$emit('change','')
			}
		
		}
	}
</script>
