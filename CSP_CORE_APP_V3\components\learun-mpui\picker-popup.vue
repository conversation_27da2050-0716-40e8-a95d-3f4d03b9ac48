<template>
	<uni-popup style="z-index: 999999999;" ref="popup" type="bottom">
		<view class="learun-picker-popup" >
			<view class="learun-popup-button-close" @click.stop="close">
				<learun-icon type="learun-icon-error" :size="14" color="#737987" ></learun-icon>
			</view>

			<uni-search-bar v-model="keyword" @confirm="searchData" @cancel="cancelSearch" @clear="cancelSearch"/>
			
			<picker-view v-if="isopen"  :value="myValue" @change="bindChange" class="picker-view" >
				<picker-view-column>
					<view class="item" v-for="(item,index) in myOptions" :key="index">{{item[labelKey]}}</view>
				</picker-view-column>
			</picker-view>
			
			
			<view class="learun-popup-button-wraper" >
				<view class="learun-popup-button-ok" @click="confirm">确认</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		name:'learun-picker-popup',
		props:{
			labelKey:{
				type:String,
				default:'label'
			},
			valueKey:{
				type:String,
				default:'value'
			},
			options:{
				type:Array,
				default:()=>[]
			}
		},
		data(){
			return {
				myValue:[0],
				tmpValue:0,
				isopen:false,
				keyword: '',
				wordLower: '',
				isAZ: false,
				// myOptions: this.options,
			}
		},
		computed: {
			myOptions(){
				return this.options.filter(item=>{
					if (this.isAZ) {
						let itemLabel = item[this.labelKey].replace(/[A-Z]/g, (word)=>{
  							return word.toLowerCase()
						})
						return itemLabel.includes(this.wordLower)
					} else {
						return item[this.labelKey].includes(this.keyword)
					}
				})
			}
		},
		methods:{
			open(value){
				if(!this.VALIDATENULL(value)){
					const index = this.options.findIndex(t=>t[this.valueKey] == value)
					this.myValue[0] = index
				}
				else{
					this.myValue[0] = 0
				}
				this.tmpValue = this.myValue[0]
				
				this.$refs.popup.open()
				this.isopen = true
			},
			close(){
				this.isopen = false
				this.cancelSearch()
				this.$refs.popup.close()
			},
			bindChange(e){
			  	this.tmpValue = e.detail.value[0]
			},
			confirm(){
				let valueItem = this.myOptions[this.tmpValue][this.valueKey]
				let index = this.options.findIndex(t=>t[this.valueKey] == valueItem)
				this.tmpValue = index
				this.$emit('change',{data:this.options[this.tmpValue],value:this.options[this.tmpValue][this.valueKey],index:this.tmpValue})
				this.close()
			},
			cancelSearch() {
				this.keyword = ''
				// this.myOptions = this.options
			},
			searchData() {
				this.isAZ = false
				let pattern = /[a-zA-Z]/
				if (pattern.test(this.keyword)) {
					this.isAZ = true
					this.wordLower = this.keyword.replace(/[A-Z]/g, (word)=>{
  						return word.toLowerCase()
					});
				}
			}
		}
	}
</script>