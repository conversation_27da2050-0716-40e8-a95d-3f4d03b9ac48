<template>
  <view class="load-web-container">
    <!-- 文档预览模式 -->
    <DocumentViewer
      v-if="(isPdfMode || isWebMode || isOfficeMode) && url"
      :src="url"
      :fileName="documentFileName"
      :manualFileType="manualFileType"
      :universalMode="universalMode"
      :disableEdit="true"
      :showToolbar="false"
      :allowDownload="false"
      :allowPrint="false"
      :previewProvider="previewProvider"
      @preview-success="onDocumentPreviewSuccess"
      @preview-error="onDocumentPreviewError"
      @download-error="onDocumentDownloadError"
    />

    <!-- 网页预览模式 -->
    <!-- <web-view
      v-else-if="isWebMode && url"
      :src="url"
      class="web-view"
      @message="handleWebViewMessage"
      @error="handleWebViewError"
    /> -->

    <!-- HTML内容展示模式 -->
    <view v-else-if="isHtmlMode" class="html-context">
      <view class="title">{{ htmlTitle }}</view>
      <rich-text :nodes="html" class="html-content"></rich-text>
      <view class="time">
        <uni-dateformat
          :date="htmlTime"
          :threshold="[0, 0]"
          format="yyyy-MM-dd"
        ></uni-dateformat>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <text class="empty-text">暂无内容可预览</text>
    </view>
  </view>
</template>

<script>
import DocumentViewer from "@/components/PdfViewer.vue";

export default {
  components: {
    DocumentViewer,
  },
  data() {
    return {
      screenWidth: 0,
      screenHeight: 0,
      url: "",
      html: "",
      htmlTitle: "",
      htmlTime: "",
      documentFileName: "文档",
      mode: "", // 'pdf', 'web', 'html', 'office'
      previewProvider: "microsoft", // 'microsoft', 'google'
      manualFileType: "", // 手动指定的文件类型
      universalMode: false, // 通用预览模式
    };
  },
  computed: {
    isPdfMode() {
      return this.mode === "pdf";
    },
    isWebMode() {
      return this.mode === "web";
    },
    isHtmlMode() {
      return this.mode === "html";
    },
    isOfficeMode() {
      return this.mode === "office";
    },
  },
  async onLoad(param = {}) {
    console.log("页面参数:", param);

    if (param.url) {
      this.url = param.url;
      this.determineModeFromUrl(param.url);
    }

    // 处理手动指定的文件类型参数
    if (param.type) {
      this.manualFileType = param.type;
      this.setModeByFileType(param.type);
    }

    // 处理通用预览模式参数
    if (param.universal === "true" || param.universal === true) {
      this.universalMode = true;
      this.mode = "pdf"; // 默认先尝试PDF模式
      console.log("启用通用预览模式");
    }

    // 处理文档ID参数
    if (this.url && this.url.indexOf("=") >= 0) {
      const urlParts = this.url.split("=");
      switch (urlParts[0]) {
        case "pdfid":
          this.mode = "pdf";
          this.documentFileName = `PDF文档_${urlParts[1]}`;
          break;
        case "docid":
          this.mode = "office";
          this.documentFileName = `Word文档_${urlParts[1]}`;
          break;
        case "xlsid":
          this.mode = "office";
          this.documentFileName = `Excel文档_${urlParts[1]}`;
          break;
      }
    }

    // 处理HTML内容参数
    if (param.html) {
      this.mode = "html";
      this.html = param.html;
      this.htmlTitle = param.title || "";
      this.htmlTime = param.time || "";
    } else {
      // 尝试从全局参数获取HTML内容
      try {
        const globalParam = this.GET_PARAM && this.GET_PARAM();
        if (globalParam && globalParam.html) {
          this.mode = "html";
          this.html = globalParam.html;
          this.htmlTitle = globalParam.title || "";
          this.htmlTime = globalParam.time || "";
        }
      } catch (error) {
        console.warn("获取全局参数失败:", error);
      }
    }

    // 如果没有设置任何模式，根据URL自动判断
    if (!this.mode && this.url) {
      this.determineModeFromUrl(this.url);
    }
  },

  methods: {
    // 根据URL判断预览模式
    determineModeFromUrl(url) {
      const lowerUrl = url.toLowerCase();

      // 检测Office文档
      const officeExtensions = [
        ".doc",
        ".docx",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx",
      ];
      for (let ext of officeExtensions) {
        if (lowerUrl.includes(ext)) {
          this.mode = "office";
          this.setDocumentFileName(url, ext);
          // 根据文件类型选择最佳预览服务
          this.selectBestPreviewProvider(ext);
          return;
        }
      }

      // 检测PDF文档
      if (lowerUrl.includes(".pdf") || lowerUrl.includes("pdf")) {
        this.mode = "pdf";
        this.setDocumentFileName(url, ".pdf");
      } else if (lowerUrl.startsWith("http") || lowerUrl.startsWith("https")) {
        // 如果是HTTP/HTTPS链接但无法识别文件类型，启用通用预览模式
        this.universalMode = true;
        this.mode = "pdf"; // 默认先尝试PDF模式
        console.log("URL无法识别文件类型，自动启用通用预览模式");
      } else {
        this.mode = "web"; // 默认使用web-view
      }
    },

    // 根据文件类型选择最佳预览服务
    selectBestPreviewProvider(extension) {
      // 根据文件类型和网络环境选择最佳预览服务
      if ([".doc", ".docx", ".ppt", ".pptx"].includes(extension)) {
        // Word和PowerPoint文档优先使用微软服务
        this.previewProvider = "microsoft";
      } else if ([".xls", ".xlsx"].includes(extension)) {
        // Excel文档也优先使用微软服务，但可以降级到Google
        this.previewProvider = "microsoft";
      }
    },

    // 设置文档文件名
    setDocumentFileName(url, extension) {
      try {
        const urlParts = url.split("/");
        const fileName = urlParts[urlParts.length - 1];

        // 移除URL参数
        const fileNameWithoutParams = fileName.split("?")[0];

        if (fileNameWithoutParams && fileNameWithoutParams !== "") {
          this.documentFileName = decodeURIComponent(fileNameWithoutParams);
        } else {
          const typeMap = {
            ".pdf": "PDF文档",
            ".doc": "Word文档",
            ".docx": "Word文档",
            ".xls": "Excel文档",
            ".xlsx": "Excel文档",
            ".ppt": "PowerPoint文档",
            ".pptx": "PowerPoint文档",
          };
          this.documentFileName = typeMap[extension] || "文档";
        }
      } catch (error) {
        console.warn("解析文件名失败:", error);
        this.documentFileName = "文档";
      }
    },

    // 文档预览成功回调
    onDocumentPreviewSuccess() {
      console.log("文档预览成功");
      uni.showToast({
        title: "预览成功",
        icon: "success",
      });
    },

    // 文档预览失败回调
    onDocumentPreviewError(error) {
      console.error("文档预览失败:", error);
      uni.showToast({
        title: "文档预览失败",
        icon: "none",
      });

      // 如果微软服务失败，尝试切换到Google服务
      if (this.previewProvider === "microsoft" && this.mode === "office") {
        console.log("尝试切换到Google预览服务");
        this.previewProvider = "google";
        // 触发重新加载
        this.$nextTick(() => {
          this.initDocumentViewer();
        });
      }
    },

    // 文档下载失败回调
    onDocumentDownloadError(error) {
      console.error("文档下载失败:", error);
      uni.showToast({
        title: "文档下载失败",
        icon: "none",
      });
    },

    // WebView消息处理
    handleWebViewMessage(event) {
      console.log("WebView消息:", event);
    },

    // WebView错误处理
    handleWebViewError(event) {
      console.error("WebView错误:", event);
      uni.showToast({
        title: "页面加载失败",
        icon: "none",
      });
    },

    // 手动初始化文档预览（用于重试）
    initDocumentViewer() {
      // 触发DocumentViewer组件重新初始化
      this.$forceUpdate();
    },

    // 根据文件类型设置预览模式
    setModeByFileType(fileType) {
      if (fileType === "pdf") {
        this.mode = "pdf";
      } else if (
        ["doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(fileType)
      ) {
        this.mode = "office";
      }

      // 设置对应的文件名
      const typeMap = {
        pdf: "PDF文档",
        doc: "Word文档",
        docx: "Word文档",
        xls: "Excel文档",
        xlsx: "Excel文档",
        ppt: "PowerPoint文档",
        pptx: "PowerPoint文档",
      };

      if (!this.documentFileName || this.documentFileName === "文档") {
        this.documentFileName = typeMap[fileType] || "文档";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.load-web-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.web-view {
  flex: 1;
  width: 100%;
}

.html-context {
  flex: 1;
  margin: 20rpx;
  overflow-y: auto;

  .title {
    text-align: center;
    font-weight: bold;
    font-size: 36rpx;
    line-height: 52rpx;
    margin-bottom: 30rpx;
    color: #333;
  }

  .html-content {
    line-height: 1.6;
    font-size: 30rpx;
    color: #666;
    word-wrap: break-word;
  }

  .time {
    text-align: right;
    color: #999999;
    margin-top: 40rpx;
    padding-bottom: 20rpx;
    font-size: 26rpx;
  }
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .empty-text {
    font-size: 32rpx;
    color: #999;
  }
}
</style>
