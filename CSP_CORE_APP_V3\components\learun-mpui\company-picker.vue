<template>
	<learun-tree-picker
		:options="options"
		idKey="value"
		pIdKey="f_ParentId"
		:disabled="disabled"
		:placeholder="$t(placeholder)"
		@change="handleChange"
		v-model="myValue"
		>
	</learun-tree-picker>
</template>

<script>
	export default{
		name:'learun-company-picker',
		props:{
			getDataSource:Function,
			disabled:Boolean,
			placeholder:String,
			value:{
				type:String,
				default:''
			},
		},
		computed:{
			options(){
				if(this.getDataSource){
					return this.getDataSource({type:'companySelect'})
				}
				return []
			},
			myValue:{
				set(val){
					this.$emit('input',val)
				},
				get(){
					return this.value
				}
			}
		},
		methods:{
			handleChange(val){
				this.$emit('change',val)
			}
		}
	}
</script>

<style>
</style>
