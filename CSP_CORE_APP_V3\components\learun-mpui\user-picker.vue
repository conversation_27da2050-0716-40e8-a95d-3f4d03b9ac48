<template>
	<view class="learun-input learun-input-border" @click.stop="handleClick"
		:class="{'learun-input-placeholder':VALIDATENULL(value)}">
		<view class="learun-input__content"><text>{{$t(text)}}</text></view>

		<view v-if="VALIDATENULL(value)" class="learun-input-icon-right learun-input-icon-right-bottom">
			<uni-icons type="bottom" size="14" color="#c0c4cc"></uni-icons>
		</view>
		<view v-else-if="!disabled" class="learun-input-icon-right">
			<view @tap.stop="handleClear">
				<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
	import customFormMixins from '@/common/customform.js'
	export default {
		mixins: [customFormMixins],
		name: 'learun-user-picker',
		props: {
			value: {
				default: null
			},
			placeholder: {
				type: String,
				default: '请选择'
			},
			isMulti: {
				type: Boolean,
				default: false
			},
			titleText: {
				type: String,
				default: '选择参与人员'
			},
			disabled: Boolean,
			isNotSaveData: {
				type: Boolean,
				default: false
			}
		},
		computed: {
			text() {
				// 单选
				if (this.VALIDATENULL(this.value)) {
					return this.$t(this.placeholder || '')
				}

				return this.showText
			}
		},

		created() {
			// console.log('user-picker',this.value)
			this.setText();
		},
		data() {
			return {
				showText: '',
				isSelf: false
			}
		},
		watch: {
			value(val) {
				if (this.isSelf) {
					this.isSelf = false;
					return;
				}
				console.log(val, 'new')
				this.setText();
			}
		},
		methods: {
			async setText() {

				if (!this.VALIDATENULL(this.value)) {
					const userMap = this.GET_DATA('learun_users_map') || {}
					if (this.isMulti) {
						const textList = []
						const valueList = this.value.split(',')
						await this.learun_form_fetchOrganizeInfo([valueList], [])
						for (let value of valueList) {
							if (userMap[value]) {
								textList.push(userMap[value].label)
							}
						}
						this.showText = String(textList);
					} else {
						await this.learun_form_fetchOrganizeInfo([this.value], [])
						this.showText = userMap[this.value] ? userMap[this.value].label : ''
					}
				}
			},
			handleClick() {
				if (this.disabled) {
					return
				}

				this.ONCE('select-learun-user', data => {
					this.isSelf = true;
					if (this.isMulti) {
						console.log(data, 'select-learun-user')

						const userMap = this.GET_DATA('learun_users_map') || {}
						const textList = []
						const dataList = []
						for (let value of data.values) {
							const user = userMap[value] || data.data.find(t => t.f_UserId == value)

							if (user) {
								dataList.push(user)
								textList.push(user.label || user.f_RealName)
							}
						}

						this.$emit('input', String(data.values))
						this.$emit('change', dataList)

						this.showText = String(textList);

					} else {
						this.$emit('input', data.value)
						this.$emit('change', data)
						this.showText = data.label
					}

				})

				this.NAV_TO_LAYER('/pages/common/learun-user-picker', {
					isMulti: this.isMulti,
					value: this.isNotSaveData ? '' : this.value,
					titleText: this.titleText
				})
			},
			handleClear() {
				if (this.disabled) {
					return
				}


				this.$emit('input', undefined)
				this.$emit('change', undefined)
			}
		}
	}
</script>