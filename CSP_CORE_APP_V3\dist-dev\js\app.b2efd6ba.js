(() => {
	var s = {
			5002: (s, e, t) => {
				"use strict";
				var n = {};
				t.r(n);
				var r = t(5471),
					a = t(5093),
					o = t.n(a);
				const j = {
						async onLaunch(s) {
							const e = window.location.href;
							if (e) {
								const s = e.split("#code=");
								if (null != s && s.length > 1) {
									const e = s[1];
									this.SET_GLOBAL("azureTokenCode", e)
								}
							}
							await window.addEventListener("beforeinstallprompt", (s => {
									let e = s;
									this.SET_GLOBAL("PWAdeferredPrompt", e), this.SET_GLOBAL(
										"showPWAInstall", !0);
									let t = getCurrentPages(),
										n = t[t.length - 1];
									null != n.onPWAInstall && n.onPWAInstall()
								})), "granted" !== Notification.permission && Notification
								.requestPermission();
							let t = this.GET_GLOBAL("token");
							null == t && (t = localStorage.getItem("token"));
							let n = `${this.API}/message/msg/list/big/`;
							"serviceWorker" in navigator && navigator.serviceWorker?.register(
								"/csp_core_app/static/worker/sw.js?v=" + (new Date).toISOString()).then(
								(s => {
									const e = s.installing || s.waiting || s.active;
									e.postMessage({
										apiUrl: n,
										token: t
									})
								})).catch((() => console.error("registration failed")));
							const r = "/" + s.path;
							["/pages/login", "/pages/home", "/pages/signup", "/pages/wxlogin",
								"/pages/workflow/releasetask/list", "/pages/workflow/releasetask/single",
								"/pages/navToPages", "/pages/appLogin", "/pages/wxloginTest"
							].includes(r) || this.$nextTick((() => {
								this.RELAUNCH_TO("/pages/home")
							})), await this.FETCH_LANG_TYPE(), await this.FETCH_LANG_DATA();
							const a = uni.getUpdateManager();
							a.onUpdateReady((() => {
								this.HIDE_LOADING(), this.CONFIRM("更新提示", "小程序新版本已准备好，是否更新应用？",
									!0).then((s => {
									s && a.applyUpdate()
								}))
							}))
						}
					},
					i = j;
				var l, c, u = t(1656),
					d = (0, u.A)(i, l, c, !1, null, null, null);
				const h = d.exports;
				o().locale("zh-cn"), uni.$learun = {
					data: {
						learun_datasource: {}
					}
				}, r.Ay.mixin(n["default"]), r.Ay.config.productionTip = !1, new r.Ay({
					render: s => s(h)
				}).$mount("#app"), new r.Ay({
					...h,
					mpType: "app"
				}).$mount()
			},
			5358: (s, e, t) => {
				var n = {
					"./af": 5177,
					"./af.js": 5177,
					"./ar": 1509,
					"./ar-dz": 1488,
					"./ar-dz.js": 1488,
					"./ar-kw": 8676,
					"./ar-kw.js": 8676,
					"./ar-ly": 2353,
					"./ar-ly.js": 2353,
					"./ar-ma": 4496,
					"./ar-ma.js": 4496,
					"./ar-sa": 2682,
					"./ar-sa.js": 2682,
					"./ar-tn": 9756,
					"./ar-tn.js": 9756,
					"./ar.js": 1509,
					"./az": 5533,
					"./az.js": 5533,
					"./be": 8959,
					"./be.js": 8959,
					"./bg": 7777,
					"./bg.js": 7777,
					"./bm": 4903,
					"./bm.js": 4903,
					"./bn": 1290,
					"./bn.js": 1290,
					"./bo": 1545,
					"./bo.js": 1545,
					"./br": 1470,
					"./br.js": 1470,
					"./bs": 4429,
					"./bs.js": 4429,
					"./ca": 7306,
					"./ca.js": 7306,
					"./cs": 6464,
					"./cs.js": 6464,
					"./cv": 3635,
					"./cv.js": 3635,
					"./cy": 4226,
					"./cy.js": 4226,
					"./da": 3601,
					"./da.js": 3601,
					"./de": 7853,
					"./de-at": 6111,
					"./de-at.js": 6111,
					"./de-ch": 4697,
					"./de-ch.js": 4697,
					"./de.js": 7853,
					"./dv": 708,
					"./dv.js": 708,
					"./el": 4691,
					"./el.js": 4691,
					"./en-SG": 2748,
					"./en-SG.js": 2748,
					"./en-au": 3872,
					"./en-au.js": 3872,
					"./en-ca": 8298,
					"./en-ca.js": 8298,
					"./en-gb": 6195,
					"./en-gb.js": 6195,
					"./en-ie": 6584,
					"./en-ie.js": 6584,
					"./en-il": 5543,
					"./en-il.js": 5543,
					"./en-nz": 9402,
					"./en-nz.js": 9402,
					"./eo": 2934,
					"./eo.js": 2934,
					"./es": 7650,
					"./es-do": 838,
					"./es-do.js": 838,
					"./es-us": 6575,
					"./es-us.js": 6575,
					"./es.js": 7650,
					"./et": 3035,
					"./et.js": 3035,
					"./eu": 3508,
					"./eu.js": 3508,
					"./fa": 119,
					"./fa.js": 119,
					"./fi": 527,
					"./fi.js": 527,
					"./fo": 2477,
					"./fo.js": 2477,
					"./fr": 5498,
					"./fr-ca": 6435,
					"./fr-ca.js": 6435,
					"./fr-ch": 7892,
					"./fr-ch.js": 7892,
					"./fr.js": 5498,
					"./fy": 7071,
					"./fy.js": 7071,
					"./ga": 1734,
					"./ga.js": 1734,
					"./gd": 217,
					"./gd.js": 217,
					"./gl": 7329,
					"./gl.js": 7329,
					"./gom-latn": 3383,
					"./gom-latn.js": 3383,
					"./gu": 5050,
					"./gu.js": 5050,
					"./he": 1713,
					"./he.js": 1713,
					"./hi": 3861,
					"./hi.js": 3861,
					"./hr": 6308,
					"./hr.js": 6308,
					"./hu": 609,
					"./hu.js": 609,
					"./hy-am": 7160,
					"./hy-am.js": 7160,
					"./id": 4063,
					"./id.js": 4063,
					"./is": 9374,
					"./is.js": 9374,
					"./it": 8383,
					"./it-ch": 1827,
					"./it-ch.js": 1827,
					"./it.js": 8383,
					"./ja": 3827,
					"./ja.js": 3827,
					"./jv": 9722,
					"./jv.js": 9722,
					"./ka": 1794,
					"./ka.js": 1794,
					"./kk": 7088,
					"./kk.js": 7088,
					"./km": 6870,
					"./km.js": 6870,
					"./kn": 4451,
					"./kn.js": 4451,
					"./ko": 3164,
					"./ko.js": 3164,
					"./ku": 8174,
					"./ku.js": 8174,
					"./ky": 8474,
					"./ky.js": 8474,
					"./lb": 9680,
					"./lb.js": 9680,
					"./lo": 5867,
					"./lo.js": 5867,
					"./lt": 5766,
					"./lt.js": 5766,
					"./lv": 9532,
					"./lv.js": 9532,
					"./me": 8076,
					"./me.js": 8076,
					"./mi": 1848,
					"./mi.js": 1848,
					"./mk": 306,
					"./mk.js": 306,
					"./ml": 3739,
					"./ml.js": 3739,
					"./mn": 9053,
					"./mn.js": 9053,
					"./mr": 6169,
					"./mr.js": 6169,
					"./ms": 3386,
					"./ms-my": 2297,
					"./ms-my.js": 2297,
					"./ms.js": 3386,
					"./mt": 7075,
					"./mt.js": 7075,
					"./my": 2264,
					"./my.js": 2264,
					"./nb": 2274,
					"./nb.js": 2274,
					"./ne": 8235,
					"./ne.js": 8235,
					"./nl": 2572,
					"./nl-be": 3784,
					"./nl-be.js": 3784,
					"./nl.js": 2572,
					"./nn": 4566,
					"./nn.js": 4566,
					"./pa-in": 9849,
					"./pa-in.js": 9849,
					"./pl": 4418,
					"./pl.js": 4418,
					"./pt": 9834,
					"./pt-br": 8303,
					"./pt-br.js": 8303,
					"./pt.js": 9834,
					"./ro": 4457,
					"./ro.js": 4457,
					"./ru": 2271,
					"./ru.js": 2271,
					"./sd": 1221,
					"./sd.js": 1221,
					"./se": 3478,
					"./se.js": 3478,
					"./si": 7538,
					"./si.js": 7538,
					"./sk": 5784,
					"./sk.js": 5784,
					"./sl": 6637,
					"./sl.js": 6637,
					"./sq": 6794,
					"./sq.js": 6794,
					"./sr": 5719,
					"./sr-cyrl": 3322,
					"./sr-cyrl.js": 3322,
					"./sr.js": 5719,
					"./ss": 6e3,
					"./ss.js": 6e3,
					"./sv": 1011,
					"./sv.js": 1011,
					"./sw": 748,
					"./sw.js": 748,
					"./ta": 1025,
					"./ta.js": 1025,
					"./te": 1885,
					"./te.js": 1885,
					"./tet": 8861,
					"./tet.js": 8861,
					"./tg": 6571,
					"./tg.js": 6571,
					"./th": 5802,
					"./th.js": 5802,
					"./tl-ph": 9231,
					"./tl-ph.js": 9231,
					"./tlh": 1052,
					"./tlh.js": 1052,
					"./tr": 5096,
					"./tr.js": 5096,
					"./tzl": 9846,
					"./tzl.js": 9846,
					"./tzm": 1765,
					"./tzm-latn": 7711,
					"./tzm-latn.js": 7711,
					"./tzm.js": 1765,
					"./ug-cn": 8414,
					"./ug-cn.js": 8414,
					"./uk": 6618,
					"./uk.js": 6618,
					"./ur": 158,
					"./ur.js": 158,
					"./uz": 7609,
					"./uz-latn": 2475,
					"./uz-latn.js": 2475,
					"./uz.js": 7609,
					"./vi": 1135,
					"./vi.js": 1135,
					"./x-pseudo": 4051,
					"./x-pseudo.js": 4051,
					"./yo": 2218,
					"./yo.js": 2218,
					"./zh-cn": 2648,
					"./zh-cn.js": 2648,
					"./zh-hk": 1632,
					"./zh-hk.js": 1632,
					"./zh-tw": 304,
					"./zh-tw.js": 304
				};

				function r(s) {
					var e = a(s);
					return t(e)
				}

				function a(s) {
					if (!t.o(n, s)) {
						var e = new Error("Cannot find module '" + s + "'");
						throw e.code = "MODULE_NOT_FOUND", e
					}
					return n[s]
				}
				r.keys = function() {
					return Object.keys(n)
				}, r.resolve = a, s.exports = r, r.id = 5358
			}
		},
		e = {};

	function t(n) {
		var r = e[n];
		if (void 0 !== r) return r.exports;
		var a = e[n] = {
			id: n,
			loaded: !1,
			exports: {}
		};
		return s[n].call(a.exports, a, a.exports, t), a.loaded = !0, a.exports
	}
	t.m = s, (() => {
		var s = [];
		t.O = (e, n, r, a) => {
			if (!n) {
				var o = 1 / 0;
				for (c = 0; c < s.length; c++) {
					for (var [n, r, a] = s[c], j = !0, i = 0; i < n.length; i++)(!1 & a || o >= a) &&
						Object.keys(t.O).every((s => t.O[s](n[i]))) ? n.splice(i--, 1) : (j = !1, a <
							o && (o = a));
					if (j) {
						s.splice(c--, 1);
						var l = r();
						void 0 !== l && (e = l)
					}
				}
				return e
			}
			a = a || 0;
			for (var c = s.length; c > 0 && s[c - 1][2] > a; c--) s[c] = s[c - 1];
			s[c] = [n, r, a]
		}
	})(), (() => {
		t.n = s => {
			var e = s && s.__esModule ? () => s["default"] : () => s;
			return t.d(e, {
				a: e
			}), e
		}
	})(), (() => {
		t.d = (s, e) => {
			for (var n in e) t.o(e, n) && !t.o(s, n) && Object.defineProperty(s, n, {
				enumerable: !0,
				get: e[n]
			})
		}
	})(), (() => {
		t.g = function() {
			if ("object" === typeof globalThis) return globalThis;
			try {
				return this || new Function("return this")()
			} catch (s) {
				if ("object" === typeof window) return window
			}
		}()
	})(), (() => {
		t.o = (s, e) => Object.prototype.hasOwnProperty.call(s, e)
	})(), (() => {
		t.r = s => {
			"undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(s, Symbol
				.toStringTag, {
					value: "Module"
				}), Object.defineProperty(s, "__esModule", {
				value: !0
			})
		}
	})(), (() => {
		t.nmd = s => (s.paths = [], s.children || (s.children = []), s)
	})(), (() => {
		var s = {
			524: 0
		};
		t.O.j = e => 0 === s[e];
		var e = (e, n) => {
				var r, a, [o, j, i] = n,
					l = 0;
				if (o.some((e => 0 !== s[e]))) {
					for (r in j) t.o(j, r) && (t.m[r] = j[r]);
					if (i) var c = i(t)
				}
				for (e && e(n); l < o.length; l++) a = o[l], t.o(s, a) && s[a] && s[a][0](), s[a] = 0;
				return t.O(c)
			},
			n = self["webpackChunk"] = self["webpackChunk"] || [];
		n.forEach(e.bind(null, 0)), n.push = e.bind(null, n.push.bind(n))
	})();
	var n = t.O(void 0, [504], (() => t(5002)));
	n = t.O(n)
})();
//# sourceMappingURL=app.b2efd6ba.js.map