<template>
  <view class="learun-collapse" >
		<view  v-if="title" class="learun-edit-table__title" >
			<text>{{$t(title)}}</text>
		</view>
		<view class="learun-edit-table__body" >
			
			<learun-collapse-item
				v-for="(row,index) in  tabList"
				:key="index"
				:num="index + 1"
				:rowData="row"
				:getDataSource="getDataSource"
				:formId="formId"
				:tableId="tableId"
				:editMode="editMode"
				@addRow="addTableRow"
				@deleteRow="deleteTableRow"
				@rowChange="rowChange"
				@input="setValue"
				@change="handleChange"
				@btnClick="handleBtnClick"
				ref="gridtable"
				/>
		</view>
  </view>
</template>

<script>
import get from "lodash/get"
export default {
  name: 'learun-collapse',
  props: {
		title:String,
		editMode: {
			type: Boolean, 
			default: true ,
		},
		value: { 
			type: Array,
			default:()=>[]
		},
		
		
		component: {},
		getDataSource:Function,
		formData:{},
		tableId:String,
		formId:String,
		required:Boolean,
		formId:String,
		
  },
	data(){
		return {
			myData:{},
			isOpen:true,
		}
	},
	computed:{
		tabList() {
			return this.component.children
		}
	},
	
	created(){
	},

  methods: {
		handleClickTitle(){
			this.isOpen = !this.isOpen
		},

		handleBtnClick(event){
			this.$emit('btnClick',event)
		},
		handleChange(event){
			this.$emit('change',event)
		},
			
		setValue(event) {	
			this.$emit('input',event)
		},
		getValue(path) {
			return get(this.GET_DATA(`learun_form_data_${this.formId}`), path)
		},
		rowChange({event,col}){
			this.$emit('rowChange',{event,col})
		},
		addTableRow(event){
			this.$emit('addRow',event)
		},
		deleteTableRow({event,col}){

			this.$emit('deleteRow',{event,col})
		},
		async validate(){
			if(this.$refs.gridtable){
				for(let i = 0,len =  this.$refs.gridtable.length;i<len;i++){
					const res = await this.$refs.gridtable[i].validate()
					if(!res){
						return false
					}
				}
			}
				
			return true
		}
  	}
}
</script>
<style lang="scss" scoped >
	.learun-collapse{
		&__title{
			font-size: 13px;
			color: #666666;
			display: flex;
			align-items: center;
			width: 100%;
			height: 32px;
		}
		
		margin-bottom: 16px;
	}
</style>
