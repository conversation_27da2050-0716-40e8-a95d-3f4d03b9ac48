<template>
	<!-- 当页面准备好时渲染页面内容 -->
	<view v-if="ready" class="page" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<!-- 地图组件，显示地图并标记考勤范围 -->
		<map id="map" :style="{'height':(SCREENHEIGHT() - 48 )+'px',width: '100%'}" :latitude="latitude"
			:longitude="longitude" :circles="circles" show-location></map>
		<!-- 打卡按钮，根据距离决定是否可打卡 -->
		<view @click="handleClick(distance<300)" :class="['learun-bottom-btns','clockBtn',{'active':distance<300}] ">
			<!-- 显示当前时间 -->
			<learun-time-now /><text style="margin-left: 8px;">{{ $t('正常打卡') }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 是否为弹窗层模式
				isLayer: true,
				// 页面是否准备好的标志
				ready: false,
				// 地图中心点纬度
				latitude: 0,
				// 地图中心点经度
				longitude: 0,
				// 地图上显示的圆形区域配置
				circles: [],
				// 用户当前位置的纬度
				myLat: 0,
				// 用户当前位置的经度
				myLng: 0,
				// 打卡位置的纬度
				lat: 0,
				// 打卡位置的经度
				lng: 0,
				// 用户与打卡位置的距离
				distance: 0,
				// 打卡相关数据
				clockData: {}
			};
		},
		// 页面返回时的处理函数，移除事件监听
		onBackPress() {
			this.OFF('learun-clockIn-refresh')
		},
		// 页面卸载时的处理函数，移除事件监听
		onUnload() {
			this.OFF('learun-clockIn-refresh')
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("考勤范围")
			})
			// 检查页面是否可以启动
			const isload = await this.PAGE_LAUNCH()
			if (!isload) return;
			// 标记页面未准备好
			this.ready = false
			// 获取页面传递的参数
			const {
				lng,
				lat,
				myLat,
				myLng,
				distance,
				clockData
			} = this.GET_PARAM()
			// 更新距离和打卡数据
			this.distance = distance
			this.clockData = clockData
			// 将经纬度转换为QQ地图坐标
			const qqLngAndLat = this.toQQ(lng, lat);
			const qqMyLngAndLat = this.toQQ(myLng, myLat);
			// 添加圆形区域到地图上，表示考勤范围
			this.circles.push({
				longitude: qqLngAndLat.lng,
				latitude: qqLngAndLat.lat,
				color: '#fff',
				// 填充颜色，半透明
				fillColor: '#00CACF26',
				// 圆的半径，单位为米
				radius: 300,
				// 边框宽度
				strokeWidth: 1
			})
			// 设置地图中心点
			this.latitude = qqLngAndLat.lat
			this.longitude = qqLngAndLat.lng
			// 保存打卡位置和用户当前位置
			this.lat = lat
			this.lng = lng
			this.myLat = myLat;
			this.myLng = myLng;
			// 开始更新用户位置
			this.setLocation();
			// 标记页面准备好
			this.ready = true
		},
		methods: {
			async setLocation() {
				// 获取用户当前位置
				const res = await this.GET_LOCATION()
				if (res) {
					// 更新用户当前位置
					this.myLat = res.lat;
					this.myLng = res.lng;
					// 计算用户与打卡位置的距离
					this.distance = this.getDistance(this.lat, this.lng, this.myLat, this.myLng)
				}
				// 每10秒更新一次用户位置
				setTimeout(() => {
					this.setLocation();
				}, 10000)
			},
			// 处理打卡按钮点击事件
			handleClick(flag) {
				if (flag) {
					// 如果距离小于300米，触发打卡操作
					this.clockIn()
				}
			},
			// 打卡操作
			async clockIn() {
				// 显示加载提示
				this.LOADING('打卡中...')
				let res;
				if (!this.clockData.f_Id) {
					// 如果没有打卡记录ID，发送POST请求创建打卡记录
					res = await this.HTTP_POST({
						url: '/demoApp/apppunch',
						data: this.clockData
					})
				} else {
					// 如果有打卡记录ID，发送PUT请求更新打卡记录
					res = await this.HTTP_PUT({
						url: `/demoApp/apppunch/${this.clockData.f_Id}`,
						data: this.clockData
					})
				}
				// 隐藏加载提示
				this.HIDE_LOADING()
				if (res) {
					// 打卡成功，显示提示信息
					uni.showToast({
						title: "打卡成功"
					})
				}
				// 触发打卡刷新事件
				this.EMIT('learun-clockIn-refresh')
				// 返回上一页
				this.NAV_BACK()
			},
			// 将经纬度转换为QQ地图坐标
			toQQ(lng, lat) {
				if (lng == null || lng == '' || lat == null || lat == '')
					return {
						lng: lng,
						lat: lat
					}
				let x_pi = 3.14159265358979324 * 3000.0 / 180.0
				let x = lng - 0.0065
				let y = lat - 0.006
				let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi)
				let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi)
				let lngs = z * Math.cos(theta)
				let lats = z * Math.sin(theta)
				return {
					lng: lngs,
					lat: lats
				}
			},
			// 计算两点之间的距离
			getDistance(lat1, lng1, lat2, lng2) {
				lat1 = lat1 || 0;
				lng1 = lng1 || 0;
				lat2 = lat2 || 0;
				lng2 = lng2 || 0;
				// 将纬度转换为弧度
				let rad1 = lat1 * Math.PI / 180.0;
				let rad2 = lat2 * Math.PI / 180.0;
				let a = rad1 - rad2;
				let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
				// 地球半径，单位为米
				let r = 6378137;
				// 计算两点之间的距离
				let distance = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) *
					Math
					.pow(Math.sin(b / 2), 2)));
				return distance;
			},
		}
	};
</script>
<style lang="scss" scoped>
	.clockBtn {
		text-align: center;
		line-height: 48px;
		height: 48px;
		background-color: #E5E5E5;
		color: #fff;
		display: block;
		font-size: 20px;

		&.active {
			background-color: #00CACF;
		}
	}
</style>