<template>
	<!-- 页面主容器，设置最小高度和底部内边距 -->
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-bottom':'40px'}">
		<!-- 渲染自定义表单组件 -->
		<learun-customform-wraper 
			v-if="ready && isLoadForm"
			:formId="formId"
			:editMode="true"
			:scheme="formScheme"
			:isUpdate="isFormUpdate"
			:loadParams="loadFormParams"
			@ready="handleFormReady"
			ref="form"
			/>

			<!-- 操作区按钮，当页面准备好时显示 -->
			<view v-if="ready" class="learun-bottom-btns">
				<!-- 保存草稿按钮，点击触发 wf_draft 方法 -->
				<button @click.stop="wf_draft">{{$t('保存草稿')}}</button>
				<!-- 流程提交按钮，点击触发 wf_submit 方法 -->
				<button @click.stop="wf_submit" type="primary">{{$t('流程提交')}}</button>
			</view>
	</view>
</template>

<script>
	// 引入工作流混入模块
	import workflowMixins from '@/common/workflow.js'
	export default {
		// 使用工作流混入模块
		mixins: [workflowMixins],

		data() {
			return {
				// 页面是否准备好的标志
				ready: false,
				// 页面打开的类型，默认为创建
				type: 'create',
				// 是否为草稿状态的标志
				isDraft: false
			}
		},

		async onLoad({
			type,
			...param
		}) {
			uni.setNavigationBarTitle({
				title: this.$t("创建流程")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面，传入页面打开类型和其他参数
				await this.init(type, param)
			}
		},

		methods: {
			// 初始化页面
			async init(type = 'create', param) {
				// 显示加载提示
				this.LOADING('加载表单中…')
				// type 表示打开页面的方式，create=新建，draft=编辑草稿，again=重新发起
				this.type = type
				const params = param
				// 从参数中解构出流程代码、流程 ID、是否加载表单数据和表单方案代码
				const {
					f_Code: code,
					f_Id: processId,
					isLoadFormData,
					f_SchemeCode
				} = params

				// 设置页面标题
				const title = {
					// 创建新流程时的标题格式
					create: `${this.$t("创建")}[${params.f_Name || ''}]${this.$t("流程")}`,
					// 编辑草稿时的标题格式
					draft: `${this.$t("创建")}[${params.f_Title}]${this.$t("流程")}`,
				} [this.type]

				// 设置工作流标题
				this.wfTitle = title
				// 设置页面标题
				this.SET_TITLE(title)

				// 流程初始化
				if (this.type == 'create') {
					// 当为创建新流程时，调用工作流创建初始化方法
					await this.wfInitCreate(code, params.formData, params.processId, isLoadFormData)
					if (!params.f_Name) {
						// 如果参数中没有流程名称，更新工作流标题和页面标题
						this.wfTitle = `${this.$t("创建")}[${this.wfSchemeName}]${this.$t("流程")}`
						this.SET_TITLE(this.wfTitle)
					}
				} else if (this.type == 'draft') {
					// 当为编辑草稿时，调用工作流草稿初始化方法
					await this.wfInitDraft(processId, f_SchemeCode)
				}

				// 标记页面准备好
				this.ready = true
				if (!this.isLoadForm) {
					// 如果不需要加载表单，隐藏加载提示
					this.HIDE_LOADING()
				}
			},
			// 表单准备好时的处理函数
			handleFormReady() {
				// 隐藏加载提示
				this.HIDE_LOADING()
			},
			// 处理表单保存操作
			async handleSave() {
				if (!this.$refs.form) {
					// 如果没有表单引用，直接返回 true
					return true
				}
				// 调用表单的保存方法，传入表单 ID、表单关联 ID、工作流流程 ID 和是否强制保存的标志
				const res = await this.$refs.form.saveForm(this.formId, this.formRelationId, this.wfProcessId, true)
				if (res) {
					// 如果保存成功，标记表单已更新
					this.isFormUpdate = true
				}
				return res
			}
		}
	}
</script>