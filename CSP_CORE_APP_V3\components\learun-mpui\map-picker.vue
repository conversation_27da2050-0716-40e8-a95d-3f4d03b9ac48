<template>
	<view>
		<view @click.stop="handleClick" class="learun-input learun-input-border" :class="{'learun-input-placeholder':VALIDATENULL(value)}" >
			<view class="learun-input__content" ><text>{{midValue.address}}</text></view>
			
			
			<view v-if="VALIDATENULL(value)" class="learun-input-icon-right learun-input-icon-right-bottom">
				<uni-icons  type="bottom" size="14" color="#c0c4cc"></uni-icons>
			</view>
			<view  v-else-if="!disabled" class="learun-input-icon-right">
				<view @tap.stop="handleClear">
					<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>

export default {
  name: 'learun-map-picker',
  props: {
    value:[String],
		placeholder:{
			type:String,
			default:'请选择'
		},
		disabled:{
			type:Boolean,
			default:false
		},
		clearable:{
			type:Boolean,
			default:true
		}
  },
  methods: {
		handleClick(){
			if (this.disabled) {
			  return
			}
			uni.chooseLocation({
				latitude:this.midValue.lat == 0?undefined:this.midValue.lat,
				longitude:this.midValue.lng == 0?undefined:this.midValue.lng,
				success: res => {					
					let lng = res.longitude
					let lat = res.latitude
					
					// #ifdef MP-WEIXIN || H5
					const baiduData = this.toBaiDu(lng,lat)
					lng = baiduData.lng
					lat = baiduData.lat
					// #endif
					
					const value = `${res.address}${res.name},${lng},${lat}`
					this.$emit('input',value)
					this.$emit('change',{
						address:`${res.address}${res.name}`,
						lng:lng,
						lat:lat
					})
				}
			})			
		},
		handleClear(){
			this.$emit('input', undefined)
			this.$emit('change', undefined)
		},
		// 转化成百度经纬度
		toBaiDu(lng,lat){
			if (lng == null || lng == '' || lat == null || lat == '')
			return {lng: lng,lat: lat}
			let x_pi = 3.14159265358979324 * 3000.0 / 180.0
			let x = lng
			let y = lat
			let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi)
			let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi)
			let lngs = z * Math.cos(theta) + 0.0065
			let lats = z * Math.sin(theta) + 0.006
			return {lng: lngs,lat: lats}
		},
		// 转化成QQ经纬度
		toQQ(lng,lat){
			if (lng == null || lng == '' || lat == null || lat == '')
			return {lng: lng,lat: lat}
			let x_pi = 3.14159265358979324 * 3000.0 / 180.0
			let x = lng - 0.0065
			let y = lat - 0.006
			let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi)
			let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi)
			let lngs = z * Math.cos(theta)
			let lats = z * Math.sin(theta)
			return {lng: lngs,lat: lats}
		}
  },

  computed: {		
		midValue(){
			if (this.VALIDATENULL(this.value)) {
				return {
					address: this.placeholder,
					lng: 0,
					lat: 0
				}
			}
			let lng = parseFloat(this.value.split(",")[1])
			let lat = parseFloat(this.value.split(",")[2])
			// #ifdef MP-WEIXIN || H5
			const qqData = this.toQQ(lng,lat)
			lng = qqData.lng
			lat = qqData.lat
			// #endif
			
			return {
				address: this.value.split(",")[0],
				lng: lng,
				lat: lat
			}
		},
    displayPlaceholder() {
      if (this.disabled) {
        return ''
      }

      if (this.placeholder) {
        return this.$t(this.placeholder)
      }
    }
  }
}
</script>
