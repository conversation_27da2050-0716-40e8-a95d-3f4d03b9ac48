export default {
	// 登录页显示的公司名称
	"company": "Crystal International Group",
	// App 版本号
	"appVersion": "3.2.6",
	// 多语言设置
	"language": {
		mainType: 'chinese',
		type: 'chinese',
	},
	// 是否允许用户注册
	"enableSignUp": true,
	// 请求数据的接口地址；可以配置多个，开发环境下登录页会出现选择菜单供您选择
	//"https://webapp.crystal-csc.cn/csp_core_api",
	// App 更新日期
	//V版本号.年月.日序号
	"updateDate": "V3.2.6.2506.2401",
	"QASorPRD": "PRD",
	"apiHost": [
		// "https://webapp.crystal-csc.cn/csp_core_api",
		// "https://webapp.crystal-csc.cn/csp_core_api_qas",
		"https://webapp.crystal-csc.cn/csp_core_api_v3",
		// "https://tstsystem04.crystal-csc.cn/csp_core_api",
		// "https://tstsystem04.crystal-csc.cn/csp_core_api_321",
		// "https://csp.crystal-cet.com.vn:7443/csp_core_api_v3"
		// "http://*************/csp_core_api",
		//"https://dotnet.learunsoft.cn/devapi",
		//"https://dotnet.learunsoft.cn/devapi", 
		// "http://localhost:5050",
	],
	// 开发环境下自动填充登录账号密码，与接口地址一一对应，只在开发环境下显示
	"devAccount": [{
			username: "system",
			password: ""
		}
		// {
		// username: "lwkcdong",
		// password: ""
		// }
		/*,{
					username: "system",
					password: "learun20221125@!"
				}*/
	],
	// 开发环境使用的接口地址（数组索引）
	"devApiHostIndex": 0,
	// 生产环境使用的接口地址（数组索引）
	"prodApiHostIndex": 0,

	// 地图相关
	"mapKey": 'XZWBZ-JGFC3-2IS3K-YFWJ6-LAWIF-SBBTZ',

	// 租户相关配置
	"isTenant": false, // 开启租户功能

	// 小程序绑定登录等配置（login=登录，bind=绑定，unbind=解绑）
	"miniProgramAccount": {
		// 微信小程序 
		"weixin": [],
		// 支付宝小程序
		"alipay": [],
		// 钉钉小程序
		"dingtalk": []
	},

	// 页面相关配置
	"pageConfig": {
		// 全局设置是否使用圆形头像
		"roundAvatar": true,

		// 「消息」页
		"msg": {
			// 周期轮询消息的时间间隔，单位是毫秒
			"fetchMsg": 3000
		},

		// 「聊天消息」页
		"chat": {
			// 周期轮询消息的时间间隔，单位是毫秒
			"fetchMsg": 3000
		}
	},
	"azure": {
		"TenantId": "19fb5d27-b629-4056-87d0-8a8a39a82762",
		"ClientId": "be8b9d83-8c3b-4292-ba74-c267439f1541",
		"RedirectUri": "https%3A%2F%2Ftstsysem04.crystal-csc.cn%2Fcsp_core_app_321%2F"
	}
}