<template>
	<view>
		<view class="learun-input learun-input-border" :class="{'learun-input-placeholder':VALIDATENULL(value)}"
			@tap.stop="handleOpen">
			<view class="learun-input__content"><text>{{text}}</text></view>
			<view class="learun-input-icon-right">
				<uni-icons v-if="VALIDATENULL(value)" type="bottom" size="15" color="#c0c4cc"></uni-icons>
				<view v-else-if="!disabled" @tap.stop="handleClear">
					<uni-icons type="clear" size="15" color="#c0c4cc"></uni-icons>
				</view>
			</view>
		</view>

		<uni-popup ref="popup" type="bottom">
			<view class="learun-picker-popup">
				<view class="learun-popup-button-close" @click.stop="handleCancel">
					<learun-icon type="learun-icon-error" :size="14" color="#737987"></learun-icon>
				</view>
				<view class="learun-box learun-picker-popup__box">
					<scroll-view scroll-with-animation scroll-y style="height: 100%;">
						<learun-tree-view :options="options" :labelKey="labelKey" :valueKey="valueKey" :idKey="idKey"
							:pIdKey="pIdKey" :isTreeData="isTreeData" :loadData="loadData" @change="handleChange">
						</learun-tree-view>
					</scroll-view>
				</view>

			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name: 'learun-tree-picker',
		props: {
			value: {
				default: null
			},
			labelKey: {
				type: String,
				default: 'label'
			},
			valueKey: {
				type: String,
				default: 'value'
			},
			idKey: {
				type: String,
				default: 'id'
			},
			pIdKey: {
				type: String,
				default: 'pid'
			},
			options: {
				type: Array,
				default: () => []
			},
			isTreeData: Boolean,
			disabled: Boolean,
			placeholder: {
				type: String,
				default: '请选择'
			},
			loadData: Function,
			getShowText: Function
		},
		data() {
			return {
				showText: ''
			}
		},
		computed: {
			myValue: {
				get() {
					if (this.VALIDATENULL(this.value)) {
						return ''
					}
					return this.value
				},
				set(val) {
					if (this.VALIDATENULL(val)) {
						this.$emit('input', '')
						this.$emit('change', undefined)
					} else {
						this.$emit('input', val.value)
						this.$emit('change', val)
					}
				}
			},
			text() {
				if (this.VALIDATENULL(this.myValue)) {
					return this.$t(this.placeholder) || ''
				} else {
					if (!this.VALIDATENULL(this.showText)) {
						return this.showText
					}


					let list = []
					if (this.isTreeData) {
						this.TREE_TO_ARRAY(this.COPY(this.options), list)
					} else {
						list = this.options
					}

					const obj = list.find(t => t[this.valueKey] == this.myValue)
					if (obj) {
						return obj[this.labelKey]
					} else {
						return ''
					}
				}
			}
		},
		created() {
			if (!this.VALIDATENULL(this.value) && this.getShowText) {
				this.showText = this.getShowText(this.value)
			}
		},
		methods: {
			handleOpen() {
				if (this.disabled) {
					return
				}
				this.$refs.popup.open()
			},
			handleCancel() {
				this.$refs.popup.close()
			},
			handleClear() {
				this.myValue = ''
			},
			handleChange(item) {
				this.myValue = item
				this.showText = item.label
				this.$refs.popup.close()
			}
		}
	}
</script>