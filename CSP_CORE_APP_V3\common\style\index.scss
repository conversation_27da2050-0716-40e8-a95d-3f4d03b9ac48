@import './learun.scss';

/*二次开发全局样式请在这边添加，方便框架后续升级*/
/*坑：解决iphoneX等大屏手机底部小黑条挡住tab的解决方法：安全区域距离底部边界距离env() 跟 constant() 需要同时存在，而且顺序不能换。
	参考： https://blog.csdn.net/LzzMandy/article/details/107514847/
	https://blog.csdn.net/qq_59572992/article/details/139682215
*/
/* #ifdef H5 */
// body {
//   padding-bottom: constant(safe-area-inset-bottom);/* 兼容 iOS < 11.2 */
//   padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
// }
/* #endif */