(self["webpackChunk"]=self["webpackChunk"]||[]).push([[504],{119:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"۱",2:"۲",3:"۳",4:"۴",5:"۵",6:"۶",7:"۷",8:"۸",9:"۹",0:"۰"},n={"۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","۰":"0"},a=e.defineLocale("fa",{months:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),monthsShort:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),weekdays:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysShort:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ج_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/قبل از ظهر|بعد از ظهر/,isPM:function(e){return/بعد از ظهر/.test(e)},meridiem:function(e,t,n){return e<12?"قبل از ظهر":"بعد از ظهر"},calendar:{sameDay:"[امروز ساعت] LT",nextDay:"[فردا ساعت] LT",nextWeek:"dddd [ساعت] LT",lastDay:"[دیروز ساعت] LT",lastWeek:"dddd [پیش] [ساعت] LT",sameElse:"L"},relativeTime:{future:"در %s",past:"%s پیش",s:"چند ثانیه",ss:"ثانیه d%",m:"یک دقیقه",mm:"%d دقیقه",h:"یک ساعت",hh:"%d ساعت",d:"یک روز",dd:"%d روز",M:"یک ماه",MM:"%d ماه",y:"یک سال",yy:"%d سال"},preparse:function(e){return e.replace(/[۰-۹]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},dayOfMonthOrdinalParse:/\d{1,2}م/,ordinal:"%dم",week:{dow:6,doy:12}});return a}))},158:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=["جنوری","فروری","مارچ","اپریل","مئی","جون","جولائی","اگست","ستمبر","اکتوبر","نومبر","دسمبر"],n=["اتوار","پیر","منگل","بدھ","جمعرات","جمعہ","ہفتہ"],a=e.defineLocale("ur",{months:t,monthsShort:t,weekdays:n,weekdaysShort:n,weekdaysMin:n,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(e){return"شام"===e},meridiem:function(e,t,n){return e<12?"صبح":"شام"},calendar:{sameDay:"[آج بوقت] LT",nextDay:"[کل بوقت] LT",nextWeek:"dddd [بوقت] LT",lastDay:"[گذشتہ روز بوقت] LT",lastWeek:"[گذشتہ] dddd [بوقت] LT",sameElse:"L"},relativeTime:{future:"%s بعد",past:"%s قبل",s:"چند سیکنڈ",ss:"%d سیکنڈ",m:"ایک منٹ",mm:"%d منٹ",h:"ایک گھنٹہ",hh:"%d گھنٹے",d:"ایک دن",dd:"%d دن",M:"ایک ماہ",MM:"%d ماہ",y:"ایک سال",yy:"%d سال"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}});return a}))},217:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=["Am Faoilleach","An Gearran","Am Màrt","An Giblean","An Cèitean","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],n=["Faoi","Gear","Màrt","Gibl","Cèit","Ògmh","Iuch","Lùn","Sult","Dàmh","Samh","Dùbh"],a=["Didòmhnaich","Diluain","Dimàirt","Diciadain","Diardaoin","Dihaoine","Disathairne"],r=["Did","Dil","Dim","Dic","Dia","Dih","Dis"],s=["Dò","Lu","Mà","Ci","Ar","Ha","Sa"],i=e.defineLocale("gd",{months:t,monthsShort:n,monthsParseExact:!0,weekdays:a,weekdaysShort:r,weekdaysMin:s,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){var t=1===e?"d":e%10===2?"na":"mh";return e+t},week:{dow:1,doy:4}});return i}))},304:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1130?"上午":a<1230?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}});return t}))},306:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("mk",{months:"јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември".split("_"),monthsShort:"јан_фев_мар_апр_мај_јун_јул_авг_сеп_окт_ное_дек".split("_"),weekdays:"недела_понеделник_вторник_среда_четврток_петок_сабота".split("_"),weekdaysShort:"нед_пон_вто_сре_чет_пет_саб".split("_"),weekdaysMin:"нe_пo_вт_ср_че_пе_сa".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Денес во] LT",nextDay:"[Утре во] LT",nextWeek:"[Во] dddd [во] LT",lastDay:"[Вчера во] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Изминатата] dddd [во] LT";case 1:case 2:case 4:case 5:return"[Изминатиот] dddd [во] LT"}},sameElse:"L"},relativeTime:{future:"после %s",past:"пред %s",s:"неколку секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дена",M:"месец",MM:"%d месеци",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(e){var t=e%10,n=e%100;return 0===e?e+"-ев":0===n?e+"-ен":n>10&&n<20?e+"-ти":1===t?e+"-ви":2===t?e+"-ри":7===t||8===t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}});return t}))},527:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän".split(" "),n=["nolla","yhden","kahden","kolmen","neljän","viiden","kuuden",t[7],t[8],t[9]];function a(e,t,n,a){var s="";switch(n){case"s":return a?"muutaman sekunnin":"muutama sekunti";case"ss":return a?"sekunnin":"sekuntia";case"m":return a?"minuutin":"minuutti";case"mm":s=a?"minuutin":"minuuttia";break;case"h":return a?"tunnin":"tunti";case"hh":s=a?"tunnin":"tuntia";break;case"d":return a?"päivän":"päivä";case"dd":s=a?"päivän":"päivää";break;case"M":return a?"kuukauden":"kuukausi";case"MM":s=a?"kuukauden":"kuukautta";break;case"y":return a?"vuoden":"vuosi";case"yy":s=a?"vuoden":"vuotta";break}return s=r(e,a)+" "+s,s}function r(e,a){return e<10?a?n[e]:t[e]:e}var s=e.defineLocale("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] HH.mm",llll:"ddd, Do MMM YYYY, [klo] HH.mm"},calendar:{sameDay:"[tänään] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s päästä",past:"%s sitten",s:a,ss:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return s}))},609:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton".split(" ");function n(e,t,n,a){var r=e;switch(n){case"s":return a||t?"néhány másodperc":"néhány másodperce";case"ss":return r+(a||t)?" másodperc":" másodperce";case"m":return"egy"+(a||t?" perc":" perce");case"mm":return r+(a||t?" perc":" perce");case"h":return"egy"+(a||t?" óra":" órája");case"hh":return r+(a||t?" óra":" órája");case"d":return"egy"+(a||t?" nap":" napja");case"dd":return r+(a||t?" nap":" napja");case"M":return"egy"+(a||t?" hónap":" hónapja");case"MM":return r+(a||t?" hónap":" hónapja");case"y":return"egy"+(a||t?" év":" éve");case"yy":return r+(a||t?" év":" éve")}return""}function a(e){return(e?"":"[múlt] ")+"["+t[this.day()]+"] LT[-kor]"}var r=e.defineLocale("hu",{months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec".split("_"),weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"},meridiemParse:/de|du/i,isPM:function(e){return"u"===e.charAt(1).toLowerCase()},meridiem:function(e,t,n){return e<12?!0===n?"de":"DE":!0===n?"du":"DU"},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return a.call(this,!0)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return a.call(this,!1)},sameElse:"L"},relativeTime:{future:"%s múlva",past:"%s",s:n,ss:n,m:n,mm:n,h:n,hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return r}))},708:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=["ޖެނުއަރީ","ފެބްރުއަރީ","މާރިޗު","އޭޕްރީލު","މޭ","ޖޫން","ޖުލައި","އޯގަސްޓު","ސެޕްޓެމްބަރު","އޮކްޓޯބަރު","ނޮވެމްބަރު","ޑިސެމްބަރު"],n=["އާދިއްތަ","ހޯމަ","އަންގާރަ","ބުދަ","ބުރާސްފަތި","ހުކުރު","ހޮނިހިރު"],a=e.defineLocale("dv",{months:t,monthsShort:t,weekdays:n,weekdaysShort:n,weekdaysMin:"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/M/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/މކ|މފ/,isPM:function(e){return"މފ"===e},meridiem:function(e,t,n){return e<12?"މކ":"މފ"},calendar:{sameDay:"[މިއަދު] LT",nextDay:"[މާދަމާ] LT",nextWeek:"dddd LT",lastDay:"[އިއްޔެ] LT",lastWeek:"[ފާއިތުވި] dddd LT",sameElse:"L"},relativeTime:{future:"ތެރޭގައި %s",past:"ކުރިން %s",s:"ސިކުންތުކޮޅެއް",ss:"d% ސިކުންތު",m:"މިނިޓެއް",mm:"މިނިޓު %d",h:"ގަޑިއިރެއް",hh:"ގަޑިއިރު %d",d:"ދުވަހެއް",dd:"ދުވަސް %d",M:"މަހެއް",MM:"މަސް %d",y:"އަހަރެއް",yy:"އަހަރު %d"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:7,doy:12}});return a}))},748:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("sw",{months:"Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des".split("_"),weekdays:"Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi".split("_"),weekdaysShort:"Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos".split("_"),weekdaysMin:"J2_J3_J4_J5_Al_Ij_J1".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[leo saa] LT",nextDay:"[kesho saa] LT",nextWeek:"[wiki ijayo] dddd [saat] LT",lastDay:"[jana] LT",lastWeek:"[wiki iliyopita] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s baadaye",past:"tokea %s",s:"hivi punde",ss:"sekunde %d",m:"dakika moja",mm:"dakika %d",h:"saa limoja",hh:"masaa %d",d:"siku moja",dd:"masiku %d",M:"mwezi mmoja",MM:"miezi %d",y:"mwaka mmoja",yy:"miaka %d"},week:{dow:1,doy:7}});return t}))},838:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,s=e.defineLocale("es-do",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return s}))},1011:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag".split("_"),weekdaysShort:"sön_mån_tis_ons_tor_fre_lör".split("_"),weekdaysMin:"sö_må_ti_on_to_fr_lö".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [kl.] HH:mm",LLLL:"dddd D MMMM YYYY [kl.] HH:mm",lll:"D MMM YYYY HH:mm",llll:"ddd D MMM YYYY HH:mm"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[Igår] LT",nextWeek:"[På] dddd LT",lastWeek:"[I] dddd[s] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"för %s sedan",s:"några sekunder",ss:"%d sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en månad",MM:"%d månader",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}(e|a)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"e":1===t||2===t?"a":"e";return e+n},week:{dow:1,doy:4}});return t}))},1025:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"௧",2:"௨",3:"௩",4:"௪",5:"௫",6:"௬",7:"௭",8:"௮",9:"௯",0:"௦"},n={"௧":"1","௨":"2","௩":"3","௪":"4","௫":"5","௬":"6","௭":"7","௮":"8","௯":"9","௦":"0"},a=e.defineLocale("ta",{months:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),monthsShort:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),weekdays:"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை".split("_"),weekdaysShort:"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி".split("_"),weekdaysMin:"ஞா_தி_செ_பு_வி_வெ_ச".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, HH:mm",LLLL:"dddd, D MMMM YYYY, HH:mm"},calendar:{sameDay:"[இன்று] LT",nextDay:"[நாளை] LT",nextWeek:"dddd, LT",lastDay:"[நேற்று] LT",lastWeek:"[கடந்த வாரம்] dddd, LT",sameElse:"L"},relativeTime:{future:"%s இல்",past:"%s முன்",s:"ஒரு சில விநாடிகள்",ss:"%d விநாடிகள்",m:"ஒரு நிமிடம்",mm:"%d நிமிடங்கள்",h:"ஒரு மணி நேரம்",hh:"%d மணி நேரம்",d:"ஒரு நாள்",dd:"%d நாட்கள்",M:"ஒரு மாதம்",MM:"%d மாதங்கள்",y:"ஒரு வருடம்",yy:"%d ஆண்டுகள்"},dayOfMonthOrdinalParse:/\d{1,2}வது/,ordinal:function(e){return e+"வது"},preparse:function(e){return e.replace(/[௧௨௩௪௫௬௭௮௯௦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,meridiem:function(e,t,n){return e<2?" யாமம்":e<6?" வைகறை":e<10?" காலை":e<14?" நண்பகல்":e<18?" எற்பாடு":e<22?" மாலை":" யாமம்"},meridiemHour:function(e,t){return 12===e&&(e=0),"யாமம்"===t?e<2?e:e+12:"வைகறை"===t||"காலை"===t||"நண்பகல்"===t&&e>=10?e:e+12},week:{dow:0,doy:6}});return a}))},1052:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut".split("_");function n(e){var t=e;return t=-1!==e.indexOf("jaj")?t.slice(0,-3)+"leS":-1!==e.indexOf("jar")?t.slice(0,-3)+"waQ":-1!==e.indexOf("DIS")?t.slice(0,-3)+"nem":t+" pIq",t}function a(e){var t=e;return t=-1!==e.indexOf("jaj")?t.slice(0,-3)+"Hu’":-1!==e.indexOf("jar")?t.slice(0,-3)+"wen":-1!==e.indexOf("DIS")?t.slice(0,-3)+"ben":t+" ret",t}function r(e,t,n,a){var r=s(e);switch(n){case"ss":return r+" lup";case"mm":return r+" tup";case"hh":return r+" rep";case"dd":return r+" jaj";case"MM":return r+" jar";case"yy":return r+" DIS"}}function s(e){var n=Math.floor(e%1e3/100),a=Math.floor(e%100/10),r=e%10,s="";return n>0&&(s+=t[n]+"vatlh"),a>0&&(s+=(""!==s?" ":"")+t[a]+"maH"),r>0&&(s+=(""!==s?" ":"")+t[r]),""===s?"pagh":s}var i=e.defineLocale("tlh",{months:"tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’".split("_"),monthsShort:"jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’".split("_"),monthsParseExact:!0,weekdays:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysShort:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysMin:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[DaHjaj] LT",nextDay:"[wa’leS] LT",nextWeek:"LLL",lastDay:"[wa’Hu’] LT",lastWeek:"LLL",sameElse:"L"},relativeTime:{future:n,past:a,s:"puS lup",ss:r,m:"wa’ tup",mm:r,h:"wa’ rep",hh:r,d:"wa’ jaj",dd:r,M:"wa’ jar",MM:r,y:"wa’ DIS",yy:r},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return i}))},1135:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("vi",{months:"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12".split("_"),monthsShort:"Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12".split("_"),monthsParseExact:!0,weekdays:"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysParseExact:!0,meridiemParse:/sa|ch/i,isPM:function(e){return/^ch$/i.test(e)},meridiem:function(e,t,n){return e<12?n?"sa":"SA":n?"ch":"CH"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [năm] YYYY",LLL:"D MMMM [năm] YYYY HH:mm",LLLL:"dddd, D MMMM [năm] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[Hôm nay lúc] LT",nextDay:"[Ngày mai lúc] LT",nextWeek:"dddd [tuần tới lúc] LT",lastDay:"[Hôm qua lúc] LT",lastWeek:"dddd [tuần rồi lúc] LT",sameElse:"L"},relativeTime:{future:"%s tới",past:"%s trước",s:"vài giây",ss:"%d giây",m:"một phút",mm:"%d phút",h:"một giờ",hh:"%d giờ",d:"một ngày",dd:"%d ngày",M:"một tháng",MM:"%d tháng",y:"một năm",yy:"%d năm"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}});return t}))},1221:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=["جنوري","فيبروري","مارچ","اپريل","مئي","جون","جولاءِ","آگسٽ","سيپٽمبر","آڪٽوبر","نومبر","ڊسمبر"],n=["آچر","سومر","اڱارو","اربع","خميس","جمع","ڇنڇر"],a=e.defineLocale("sd",{months:t,monthsShort:t,weekdays:n,weekdaysShort:n,weekdaysMin:n,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(e){return"شام"===e},meridiem:function(e,t,n){return e<12?"صبح":"شام"},calendar:{sameDay:"[اڄ] LT",nextDay:"[سڀاڻي] LT",nextWeek:"dddd [اڳين هفتي تي] LT",lastDay:"[ڪالهه] LT",lastWeek:"[گزريل هفتي] dddd [تي] LT",sameElse:"L"},relativeTime:{future:"%s پوء",past:"%s اڳ",s:"چند سيڪنڊ",ss:"%d سيڪنڊ",m:"هڪ منٽ",mm:"%d منٽ",h:"هڪ ڪلاڪ",hh:"%d ڪلاڪ",d:"هڪ ڏينهن",dd:"%d ڏينهن",M:"هڪ مهينو",MM:"%d مهينا",y:"هڪ سال",yy:"%d سال"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}});return a}))},1290:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"},n={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"},a=e.defineLocale("bn",{months:"জানুয়ারী_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব_মার্চ_এপ্র_মে_জুন_জুল_আগ_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গ_বুধ_বৃহঃ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[১২৩৪৫৬৭৮৯০]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/রাত|সকাল|দুপুর|বিকাল|রাত/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t&&e>=4||"দুপুর"===t&&e<5||"বিকাল"===t?e+12:e},meridiem:function(e,t,n){return e<4?"রাত":e<10?"সকাল":e<17?"দুপুর":e<20?"বিকাল":"রাত"},week:{dow:0,doy:6}});return a}))},1470:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n){var a={mm:"munutenn",MM:"miz",dd:"devezh"};return e+" "+r(a[n],e)}function n(e){switch(a(e)){case 1:case 3:case 4:case 5:case 9:return e+" bloaz";default:return e+" vloaz"}}function a(e){return e>9?a(e%10):e}function r(e,t){return 2===t?s(e):e}function s(e){var t={m:"v",b:"v",d:"z"};return void 0===t[e.charAt(0)]?e:t[e.charAt(0)]+e.substring(1)}var i=e.defineLocale("br",{months:"Genver_C'hwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_C'hwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Merc'her_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h[e]mm A",LTS:"h[e]mm:ss A",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY h[e]mm A",LLLL:"dddd, D [a viz] MMMM YYYY h[e]mm A"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warc'hoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Dec'h da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s 'zo",s:"un nebeud segondennoù",ss:"%d eilenn",m:"ur vunutenn",mm:t,h:"un eur",hh:"%d eur",d:"un devezh",dd:t,M:"ur miz",MM:t,y:"ur bloaz",yy:n},dayOfMonthOrdinalParse:/\d{1,2}(añ|vet)/,ordinal:function(e){var t=1===e?"añ":"vet";return e+t},week:{dow:1,doy:4}});return i}))},1488:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ar-dz",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اثنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"أح_إث_ثلا_أر_خم_جم_سب".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:4}});return t}))},1509:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},n={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},a=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},r={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},s=function(e){return function(t,n,s,i){var o=a(t),d=r[e][a(t)];return 2===o&&(d=d[n?0:1]),d.replace(/%d/i,t)}},i=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],o=e.defineLocale("ar",{months:i,monthsShort:i,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:s("s"),ss:s("s"),m:s("m"),mm:s("m"),h:s("h"),hh:s("h"),d:s("d"),dd:s("d"),M:s("M"),MM:s("M"),y:s("y"),yy:s("y")},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}});return o}))},1545:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"༡",2:"༢",3:"༣",4:"༤",5:"༥",6:"༦",7:"༧",8:"༨",9:"༩",0:"༠"},n={"༡":"1","༢":"2","༣":"3","༤":"4","༥":"5","༦":"6","༧":"7","༨":"8","༩":"9","༠":"0"},a=e.defineLocale("bo",{months:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),monthsShort:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),weekdays:"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་".split("_"),weekdaysShort:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),weekdaysMin:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[དི་རིང] LT",nextDay:"[སང་ཉིན] LT",nextWeek:"[བདུན་ཕྲག་རྗེས་མ], LT",lastDay:"[ཁ་སང] LT",lastWeek:"[བདུན་ཕྲག་མཐའ་མ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ལ་",past:"%s སྔན་ལ",s:"ལམ་སང",ss:"%d སྐར་ཆ།",m:"སྐར་མ་གཅིག",mm:"%d སྐར་མ",h:"ཆུ་ཚོད་གཅིག",hh:"%d ཆུ་ཚོད",d:"ཉིན་གཅིག",dd:"%d ཉིན་",M:"ཟླ་བ་གཅིག",MM:"%d ཟླ་བ",y:"ལོ་གཅིག",yy:"%d ལོ"},preparse:function(e){return e.replace(/[༡༢༣༤༥༦༧༨༩༠]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,meridiemHour:function(e,t){return 12===e&&(e=0),"མཚན་མོ"===t&&e>=4||"ཉིན་གུང"===t&&e<5||"དགོང་དག"===t?e+12:e},meridiem:function(e,t,n){return e<4?"མཚན་མོ":e<10?"ཞོགས་ཀས":e<17?"ཉིན་གུང":e<20?"དགོང་དག":"མཚན་མོ"},week:{dow:0,doy:6}});return a}))},1632:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("zh-hk",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1130?"上午":a<1230?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}});return t}))},1656:(e,t,n)=>{"use strict";function a(e,t,n,a,r,s,i,o){var d,_="function"===typeof e?e.options:e;if(t&&(_.render=t,_.staticRenderFns=n,_._compiled=!0),a&&(_.functional=!0),s&&(_._scopeId="data-v-"+s),i?(d=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),r&&r.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(i)},_._ssrRegister=d):r&&(d=o?function(){r.call(this,(_.functional?this.parent:this).$root.$options.shadowRoot)}:r),d)if(_.functional){_._injectStyles=d;var u=_.render;_.render=function(e,t){return d.call(t),u(e,t)}}else{var l=_.beforeCreate;_.beforeCreate=l?[].concat(l,d):[d]}return{exports:e,options:_}}n.d(t,{A:()=>a})},1713:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",ss:"%d שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(e){return 2===e?"שעתיים":e+" שעות"},d:"יום",dd:function(e){return 2===e?"יומיים":e+" ימים"},M:"חודש",MM:function(e){return 2===e?"חודשיים":e+" חודשים"},y:"שנה",yy:function(e){return 2===e?"שנתיים":e%10===0&&10!==e?e+" שנה":e+" שנים"}},meridiemParse:/אחה"צ|לפנה"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,isPM:function(e){return/^(אחה"צ|אחרי הצהריים|בערב)$/.test(e)},meridiem:function(e,t,n){return e<5?"לפנות בוקר":e<10?"בבוקר":e<12?n?'לפנה"צ':"לפני הצהריים":e<18?n?'אחה"צ':"אחרי הצהריים":"בערב"}});return t}))},1734:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=["Eanáir","Feabhra","Márta","Aibreán","Bealtaine","Méitheamh","Iúil","Lúnasa","Meán Fómhair","Deaireadh Fómhair","Samhain","Nollaig"],n=["Eaná","Feab","Márt","Aibr","Beal","Méit","Iúil","Lúna","Meán","Deai","Samh","Noll"],a=["Dé Domhnaigh","Dé Luain","Dé Máirt","Dé Céadaoin","Déardaoin","Dé hAoine","Dé Satharn"],r=["Dom","Lua","Mái","Céa","Déa","hAo","Sat"],s=["Do","Lu","Má","Ce","Dé","hA","Sa"],i=e.defineLocale("ga",{months:t,monthsShort:n,monthsParseExact:!0,weekdays:a,weekdaysShort:r,weekdaysMin:s,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Inniu ag] LT",nextDay:"[Amárach ag] LT",nextWeek:"dddd [ag] LT",lastDay:"[Inné aig] LT",lastWeek:"dddd [seo caite] [ag] LT",sameElse:"L"},relativeTime:{future:"i %s",past:"%s ó shin",s:"cúpla soicind",ss:"%d soicind",m:"nóiméad",mm:"%d nóiméad",h:"uair an chloig",hh:"%d uair an chloig",d:"lá",dd:"%d lá",M:"mí",MM:"%d mí",y:"bliain",yy:"%d bliain"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){var t=1===e?"d":e%10===2?"na":"mh";return e+t},week:{dow:1,doy:4}});return i}))},1765:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("tzm",{months:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),monthsShort:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),weekdays:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysShort:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysMin:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ⴰⵙⴷⵅ ⴴ] LT",nextDay:"[ⴰⵙⴽⴰ ⴴ] LT",nextWeek:"dddd [ⴴ] LT",lastDay:"[ⴰⵚⴰⵏⵜ ⴴ] LT",lastWeek:"dddd [ⴴ] LT",sameElse:"L"},relativeTime:{future:"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s",past:"ⵢⴰⵏ %s",s:"ⵉⵎⵉⴽ",ss:"%d ⵉⵎⵉⴽ",m:"ⵎⵉⵏⵓⴺ",mm:"%d ⵎⵉⵏⵓⴺ",h:"ⵙⴰⵄⴰ",hh:"%d ⵜⴰⵙⵙⴰⵄⵉⵏ",d:"ⴰⵙⵙ",dd:"%d oⵙⵙⴰⵏ",M:"ⴰⵢoⵓⵔ",MM:"%d ⵉⵢⵢⵉⵔⵏ",y:"ⴰⵙⴳⴰⵙ",yy:"%d ⵉⵙⴳⴰⵙⵏ"},week:{dow:6,doy:12}});return t}))},1794:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ka",{months:{standalone:"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი".split("_"),format:"იანვარს_თებერვალს_მარტს_აპრილის_მაისს_ივნისს_ივლისს_აგვისტს_სექტემბერს_ოქტომბერს_ნოემბერს_დეკემბერს".split("_")},monthsShort:"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ".split("_"),weekdays:{standalone:"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი".split("_"),format:"კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს".split("_"),isFormat:/(წინა|შემდეგ)/},weekdaysShort:"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ".split("_"),weekdaysMin:"კვ_ორ_სა_ოთ_ხუ_პა_შა".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[დღეს] LT[-ზე]",nextDay:"[ხვალ] LT[-ზე]",lastDay:"[გუშინ] LT[-ზე]",nextWeek:"[შემდეგ] dddd LT[-ზე]",lastWeek:"[წინა] dddd LT-ზე",sameElse:"L"},relativeTime:{future:function(e){return/(წამი|წუთი|საათი|წელი)/.test(e)?e.replace(/ი$/,"ში"):e+"ში"},past:function(e){return/(წამი|წუთი|საათი|დღე|თვე)/.test(e)?e.replace(/(ი|ე)$/,"ის წინ"):/წელი/.test(e)?e.replace(/წელი$/,"წლის წინ"):void 0},s:"რამდენიმე წამი",ss:"%d წამი",m:"წუთი",mm:"%d წუთი",h:"საათი",hh:"%d საათი",d:"დღე",dd:"%d დღე",M:"თვე",MM:"%d თვე",y:"წელი",yy:"%d წელი"},dayOfMonthOrdinalParse:/0|1-ლი|მე-\d{1,2}|\d{1,2}-ე/,ordinal:function(e){return 0===e?e:1===e?e+"-ლი":e<20||e<=100&&e%20===0||e%100===0?"მე-"+e:e+"-ე"},week:{dow:1,doy:7}});return t}))},1827:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("it-ch",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(e){return(/^[0-9].+$/.test(e)?"tra":"in")+" "+e},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return t}))},1848:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("mi",{months:"Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea".split("_"),monthsShort:"Kohi_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki".split("_"),monthsRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,2}/i,weekdays:"Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei".split("_"),weekdaysShort:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),weekdaysMin:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [i] HH:mm",LLLL:"dddd, D MMMM YYYY [i] HH:mm"},calendar:{sameDay:"[i teie mahana, i] LT",nextDay:"[apopo i] LT",nextWeek:"dddd [i] LT",lastDay:"[inanahi i] LT",lastWeek:"dddd [whakamutunga i] LT",sameElse:"L"},relativeTime:{future:"i roto i %s",past:"%s i mua",s:"te hēkona ruarua",ss:"%d hēkona",m:"he meneti",mm:"%d meneti",h:"te haora",hh:"%d haora",d:"he ra",dd:"%d ra",M:"he marama",MM:"%d marama",y:"he tau",yy:"%d tau"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return t}))},1885:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("te",{months:"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్".split("_"),monthsShort:"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.".split("_"),monthsParseExact:!0,weekdays:"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం".split("_"),weekdaysShort:"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని".split("_"),weekdaysMin:"ఆ_సో_మం_బు_గు_శు_శ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[నేడు] LT",nextDay:"[రేపు] LT",nextWeek:"dddd, LT",lastDay:"[నిన్న] LT",lastWeek:"[గత] dddd, LT",sameElse:"L"},relativeTime:{future:"%s లో",past:"%s క్రితం",s:"కొన్ని క్షణాలు",ss:"%d సెకన్లు",m:"ఒక నిమిషం",mm:"%d నిమిషాలు",h:"ఒక గంట",hh:"%d గంటలు",d:"ఒక రోజు",dd:"%d రోజులు",M:"ఒక నెల",MM:"%d నెలలు",y:"ఒక సంవత్సరం",yy:"%d సంవత్సరాలు"},dayOfMonthOrdinalParse:/\d{1,2}వ/,ordinal:"%dవ",meridiemParse:/రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,meridiemHour:function(e,t){return 12===e&&(e=0),"రాత్రి"===t?e<4?e:e+12:"ఉదయం"===t?e:"మధ్యాహ్నం"===t?e>=10?e:e+12:"సాయంత్రం"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"రాత్రి":e<10?"ఉదయం":e<17?"మధ్యాహ్నం":e<20?"సాయంత్రం":"రాత్రి"},week:{dow:0,doy:6}});return t}))},2218:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("yo",{months:"Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀".split("_"),monthsShort:"Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀".split("_"),weekdays:"Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta".split("_"),weekdaysShort:"Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá".split("_"),weekdaysMin:"Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Ònì ni] LT",nextDay:"[Ọ̀la ni] LT",nextWeek:"dddd [Ọsẹ̀ tón'bọ] [ni] LT",lastDay:"[Àna ni] LT",lastWeek:"dddd [Ọsẹ̀ tólọ́] [ni] LT",sameElse:"L"},relativeTime:{future:"ní %s",past:"%s kọjá",s:"ìsẹjú aayá die",ss:"aayá %d",m:"ìsẹjú kan",mm:"ìsẹjú %d",h:"wákati kan",hh:"wákati %d",d:"ọjọ́ kan",dd:"ọjọ́ %d",M:"osù kan",MM:"osù %d",y:"ọdún kan",yy:"ọdún %d"},dayOfMonthOrdinalParse:/ọjọ́\s\d{1,2}/,ordinal:"ọjọ́ %d",week:{dow:1,doy:4}});return t}))},2264:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"၁",2:"၂",3:"၃",4:"၄",5:"၅",6:"၆",7:"၇",8:"၈",9:"၉",0:"၀"},n={"၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","၀":"0"},a=e.defineLocale("my",{months:"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ".split("_"),monthsShort:"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ".split("_"),weekdays:"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ".split("_"),weekdaysShort:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),weekdaysMin:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ယနေ.] LT [မှာ]",nextDay:"[မနက်ဖြန်] LT [မှာ]",nextWeek:"dddd LT [မှာ]",lastDay:"[မနေ.က] LT [မှာ]",lastWeek:"[ပြီးခဲ့သော] dddd LT [မှာ]",sameElse:"L"},relativeTime:{future:"လာမည့် %s မှာ",past:"လွန်ခဲ့သော %s က",s:"စက္ကန်.အနည်းငယ်",ss:"%d စက္ကန့်",m:"တစ်မိနစ်",mm:"%d မိနစ်",h:"တစ်နာရီ",hh:"%d နာရီ",d:"တစ်ရက်",dd:"%d ရက်",M:"တစ်လ",MM:"%d လ",y:"တစ်နှစ်",yy:"%d နှစ်"},preparse:function(e){return e.replace(/[၁၂၃၄၅၆၇၈၉၀]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},week:{dow:1,doy:4}});return a}))},2271:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t){var n=e.split("_");return t%10===1&&t%100!==11?n[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?n[1]:n[2]}function n(e,n,a){var r={ss:n?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:n?"минута_минуты_минут":"минуту_минуты_минут",hh:"час_часа_часов",dd:"день_дня_дней",MM:"месяц_месяца_месяцев",yy:"год_года_лет"};return"m"===a?n?"минута":"минуту":e+" "+t(r[a],+e)}var a=[/^янв/i,/^фев/i,/^мар/i,/^апр/i,/^ма[йя]/i,/^июн/i,/^июл/i,/^авг/i,/^сен/i,/^окт/i,/^ноя/i,/^дек/i],r=e.defineLocale("ru",{months:{format:"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split("_"),standalone:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_")},monthsShort:{format:"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split("_"),standalone:"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split("_")},weekdays:{standalone:"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split("_"),format:"воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу".split("_"),isFormat:/\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?\] ?dddd/},weekdaysShort:"вс_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"вс_пн_вт_ср_чт_пт_сб".split("_"),monthsParse:a,longMonthsParse:a,shortMonthsParse:a,monthsRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsShortRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsStrictRegex:/^(январ[яь]|феврал[яь]|марта?|апрел[яь]|ма[яй]|июн[яь]|июл[яь]|августа?|сентябр[яь]|октябр[яь]|ноябр[яь]|декабр[яь])/i,monthsShortStrictRegex:/^(янв\.|февр?\.|мар[т.]|апр\.|ма[яй]|июн[ья.]|июл[ья.]|авг\.|сент?\.|окт\.|нояб?\.|дек\.)/i,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., H:mm",LLLL:"dddd, D MMMM YYYY г., H:mm"},calendar:{sameDay:"[Сегодня, в] LT",nextDay:"[Завтра, в] LT",lastDay:"[Вчера, в] LT",nextWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В следующее] dddd, [в] LT";case 1:case 2:case 4:return"[В следующий] dddd, [в] LT";case 3:case 5:case 6:return"[В следующую] dddd, [в] LT"}},lastWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В прошлое] dddd, [в] LT";case 1:case 2:case 4:return"[В прошлый] dddd, [в] LT";case 3:case 5:case 6:return"[В прошлую] dddd, [в] LT"}},sameElse:"L"},relativeTime:{future:"через %s",past:"%s назад",s:"несколько секунд",ss:n,m:n,mm:n,h:"час",hh:n,d:"день",dd:n,M:"месяц",MM:n,y:"год",yy:n},meridiemParse:/ночи|утра|дня|вечера/i,isPM:function(e){return/^(дня|вечера)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночи":e<12?"утра":e<17?"дня":"вечера"},dayOfMonthOrdinalParse:/\d{1,2}-(й|го|я)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":return e+"-й";case"D":return e+"-го";case"w":case"W":return e+"-я";default:return e}},week:{dow:1,doy:4}});return r}))},2274:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_april_mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"sø._ma._ti._on._to._fr._lø.".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] HH:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"noen sekunder",ss:"%d sekunder",m:"ett minutt",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dager",M:"en måned",MM:"%d måneder",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return t}))},2297:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?e>=11?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});return t}))},2353:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",0:"0"},n=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},a={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},r=function(e){return function(t,r,s,i){var o=n(t),d=a[e][n(t)];return 2===o&&(d=d[r?0:1]),d.replace(/%d/i,t)}},s=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],i=e.defineLocale("ar-ly",{months:s,monthsShort:s,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:r("s"),ss:r("s"),m:r("m"),mm:r("m"),h:r("h"),hh:r("h"),d:r("d"),dd:r("d"),M:r("M"),MM:r("M"),y:r("y"),yy:r("y")},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}});return i}))},2475:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("uz-latn",{months:"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr".split("_"),monthsShort:"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek".split("_"),weekdays:"Yakshanba_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba".split("_"),weekdaysShort:"Yak_Dush_Sesh_Chor_Pay_Jum_Shan".split("_"),weekdaysMin:"Ya_Du_Se_Cho_Pa_Ju_Sha".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Bugun soat] LT [da]",nextDay:"[Ertaga] LT [da]",nextWeek:"dddd [kuni soat] LT [da]",lastDay:"[Kecha soat] LT [da]",lastWeek:"[O'tgan] dddd [kuni soat] LT [da]",sameElse:"L"},relativeTime:{future:"Yaqin %s ichida",past:"Bir necha %s oldin",s:"soniya",ss:"%d soniya",m:"bir daqiqa",mm:"%d daqiqa",h:"bir soat",hh:"%d soat",d:"bir kun",dd:"%d kun",M:"bir oy",MM:"%d oy",y:"bir yil",yy:"%d yil"},week:{dow:1,doy:7}});return t}))},2477:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("fo",{months:"januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mán_týs_mik_hós_frí_ley".split("_"),weekdaysMin:"su_má_tý_mi_hó_fr_le".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D. MMMM, YYYY HH:mm"},calendar:{sameDay:"[Í dag kl.] LT",nextDay:"[Í morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Í gjár kl.] LT",lastWeek:"[síðstu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s síðani",s:"fá sekund",ss:"%d sekundir",m:"ein minuttur",mm:"%d minuttir",h:"ein tími",hh:"%d tímar",d:"ein dagur",dd:"%d dagar",M:"ein mánaður",MM:"%d mánaðir",y:"eitt ár",yy:"%d ár"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return t}))},2572:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),n="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),a=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],r=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,s=e.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}});return s}))},2648:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"下午"===t||"晚上"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1130?"上午":a<1230?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s内",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}});return t}))},2682:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},n={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},a=e.defineLocale("ar-sa",{months:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:0,doy:6}});return a}))},2748:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-SG",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},2934:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aŭg_sep_okt_nov_dec".split("_"),weekdays:"dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato".split("_"),weekdaysShort:"dim_lun_mard_merk_ĵaŭ_ven_sab".split("_"),weekdaysMin:"di_lu_ma_me_ĵa_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D[-a de] MMMM, YYYY",LLL:"D[-a de] MMMM, YYYY HH:mm",LLLL:"dddd, [la] D[-a de] MMMM, YYYY HH:mm"},meridiemParse:/[ap]\.t\.m/i,isPM:function(e){return"p"===e.charAt(0).toLowerCase()},meridiem:function(e,t,n){return e>11?n?"p.t.m.":"P.T.M.":n?"a.t.m.":"A.T.M."},calendar:{sameDay:"[Hodiaŭ je] LT",nextDay:"[Morgaŭ je] LT",nextWeek:"dddd [je] LT",lastDay:"[Hieraŭ je] LT",lastWeek:"[pasinta] dddd [je] LT",sameElse:"L"},relativeTime:{future:"post %s",past:"antaŭ %s",s:"sekundoj",ss:"%d sekundoj",m:"minuto",mm:"%d minutoj",h:"horo",hh:"%d horoj",d:"tago",dd:"%d tagoj",M:"monato",MM:"%d monatoj",y:"jaro",yy:"%d jaroj"},dayOfMonthOrdinalParse:/\d{1,2}a/,ordinal:"%da",week:{dow:1,doy:7}});return t}))},3035:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r={s:["mõne sekundi","mõni sekund","paar sekundit"],ss:[e+"sekundi",e+"sekundit"],m:["ühe minuti","üks minut"],mm:[e+" minuti",e+" minutit"],h:["ühe tunni","tund aega","üks tund"],hh:[e+" tunni",e+" tundi"],d:["ühe päeva","üks päev"],M:["kuu aja","kuu aega","üks kuu"],MM:[e+" kuu",e+" kuud"],y:["ühe aasta","aasta","üks aasta"],yy:[e+" aasta",e+" aastat"]};return t?r[n][2]?r[n][2]:r[n][1]:a?r[n][0]:r[n][1]}var n=e.defineLocale("et",{months:"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[Täna,] LT",nextDay:"[Homme,] LT",nextWeek:"[Järgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pärast",past:"%s tagasi",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:"%d päeva",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return n}))},3164:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h:mm",LLLL:"YYYY년 MMMM D일 dddd A h:mm",l:"YYYY.MM.DD.",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 A h:mm",llll:"YYYY년 MMMM D일 dddd A h:mm"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},dayOfMonthOrdinalParse:/\d{1,2}(일|월|주)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"일";case"M":return e+"월";case"w":case"W":return e+"주";default:return e}},meridiemParse:/오전|오후/,isPM:function(e){return"오후"===e},meridiem:function(e,t,n){return e<12?"오전":"오후"}});return t}))},3322:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={words:{ss:["секунда","секунде","секунди"],m:["један минут","једне минуте"],mm:["минут","минуте","минута"],h:["један сат","једног сата"],hh:["сат","сата","сати"],dd:["дан","дана","дана"],MM:["месец","месеца","месеци"],yy:["година","године","година"]},correctGrammaticalCase:function(e,t){return 1===e?t[0]:e>=2&&e<=4?t[1]:t[2]},translate:function(e,n,a){var r=t.words[a];return 1===a.length?n?r[0]:r[1]:e+" "+t.correctGrammaticalCase(e,r)}},n=e.defineLocale("sr-cyrl",{months:"јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар".split("_"),monthsShort:"јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.".split("_"),monthsParseExact:!0,weekdays:"недеља_понедељак_уторак_среда_четвртак_петак_субота".split("_"),weekdaysShort:"нед._пон._уто._сре._чет._пет._суб.".split("_"),weekdaysMin:"не_по_ут_ср_че_пе_су".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[данас у] LT",nextDay:"[сутра у] LT",nextWeek:function(){switch(this.day()){case 0:return"[у] [недељу] [у] LT";case 3:return"[у] [среду] [у] LT";case 6:return"[у] [суботу] [у] LT";case 1:case 2:case 4:case 5:return"[у] dddd [у] LT"}},lastDay:"[јуче у] LT",lastWeek:function(){var e=["[прошле] [недеље] [у] LT","[прошлог] [понедељка] [у] LT","[прошлог] [уторка] [у] LT","[прошле] [среде] [у] LT","[прошлог] [четвртка] [у] LT","[прошлог] [петка] [у] LT","[прошле] [суботе] [у] LT"];return e[this.day()]},sameElse:"L"},relativeTime:{future:"за %s",past:"пре %s",s:"неколико секунди",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"дан",dd:t.translate,M:"месец",MM:t.translate,y:"годину",yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return n}))},3383:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r={s:["thodde secondanim","thodde second"],ss:[e+" secondanim",e+" second"],m:["eka mintan","ek minute"],mm:[e+" mintanim",e+" mintam"],h:["eka voran","ek vor"],hh:[e+" voranim",e+" voram"],d:["eka disan","ek dis"],dd:[e+" disanim",e+" dis"],M:["eka mhoinean","ek mhoino"],MM:[e+" mhoineanim",e+" mhoine"],y:["eka vorsan","ek voros"],yy:[e+" vorsanim",e+" vorsam"]};return t?r[n][0]:r[n][1]}var n=e.defineLocale("gom-latn",{months:"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr".split("_"),monthsShort:"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Aitar_Somar_Mongllar_Budvar_Brestar_Sukrar_Son'var".split("_"),weekdaysShort:"Ait._Som._Mon._Bud._Bre._Suk._Son.".split("_"),weekdaysMin:"Ai_Sm_Mo_Bu_Br_Su_Sn".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [vazta]",LTS:"A h:mm:ss [vazta]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [vazta]",LLLL:"dddd, MMMM[achea] Do, YYYY, A h:mm [vazta]",llll:"ddd, D MMM YYYY, A h:mm [vazta]"},calendar:{sameDay:"[Aiz] LT",nextDay:"[Faleam] LT",nextWeek:"[Ieta to] dddd[,] LT",lastDay:"[Kal] LT",lastWeek:"[Fatlo] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s adim",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}(er)/,ordinal:function(e,t){switch(t){case"D":return e+"er";default:case"M":case"Q":case"DDD":case"d":case"w":case"W":return e}},week:{dow:1,doy:4},meridiemParse:/rati|sokalli|donparam|sanje/,meridiemHour:function(e,t){return 12===e&&(e=0),"rati"===t?e<4?e:e+12:"sokalli"===t?e:"donparam"===t?e>12?e:e+12:"sanje"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"rati":e<12?"sokalli":e<16?"donparam":e<20?"sanje":"rati"}});return n}))},3386:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ms",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?e>=11?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});return t}))},3478:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("se",{months:"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu".split("_"),monthsShort:"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov".split("_"),weekdays:"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat".split("_"),weekdaysShort:"sotn_vuos_maŋ_gask_duor_bear_láv".split("_"),weekdaysMin:"s_v_m_g_d_b_L".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"MMMM D. [b.] YYYY",LLL:"MMMM D. [b.] YYYY [ti.] HH:mm",LLLL:"dddd, MMMM D. [b.] YYYY [ti.] HH:mm"},calendar:{sameDay:"[otne ti] LT",nextDay:"[ihttin ti] LT",nextWeek:"dddd [ti] LT",lastDay:"[ikte ti] LT",lastWeek:"[ovddit] dddd [ti] LT",sameElse:"L"},relativeTime:{future:"%s geažes",past:"maŋit %s",s:"moadde sekunddat",ss:"%d sekunddat",m:"okta minuhta",mm:"%d minuhtat",h:"okta diimmu",hh:"%d diimmut",d:"okta beaivi",dd:"%d beaivvit",M:"okta mánnu",MM:"%d mánut",y:"okta jahki",yy:"%d jagit"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return t}))},3508:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),monthsParseExact:!0,weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] HH:mm",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] HH:mm",llll:"ddd, YYYY[ko] MMM D[a] HH:mm"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",ss:"%d segundo",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return t}))},3601:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"søn_man_tir_ons_tor_fre_lør".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"på dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[i] dddd[s kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"få sekunder",ss:"%d sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en måned",MM:"%d måneder",y:"et år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return t}))},3635:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("cv",{months:"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав".split("_"),monthsShort:"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш".split("_"),weekdays:"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун".split("_"),weekdaysShort:"выр_тун_ытл_юн_кӗҫ_эрн_шӑм".split("_"),weekdaysMin:"вр_тн_ыт_юн_кҫ_эр_шм".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]",LLL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm",LLLL:"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm"},calendar:{sameDay:"[Паян] LT [сехетре]",nextDay:"[Ыран] LT [сехетре]",lastDay:"[Ӗнер] LT [сехетре]",nextWeek:"[Ҫитес] dddd LT [сехетре]",lastWeek:"[Иртнӗ] dddd LT [сехетре]",sameElse:"L"},relativeTime:{future:function(e){var t=/сехет$/i.exec(e)?"рен":/ҫул$/i.exec(e)?"тан":"ран";return e+t},past:"%s каялла",s:"пӗр-ик ҫеккунт",ss:"%d ҫеккунт",m:"пӗр минут",mm:"%d минут",h:"пӗр сехет",hh:"%d сехет",d:"пӗр кун",dd:"%d кун",M:"пӗр уйӑх",MM:"%d уйӑх",y:"пӗр ҫул",yy:"%d ҫул"},dayOfMonthOrdinalParse:/\d{1,2}-мӗш/,ordinal:"%d-мӗш",week:{dow:1,doy:7}});return t}))},3739:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ml",{months:"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ".split("_"),monthsShort:"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.".split("_"),monthsParseExact:!0,weekdays:"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച".split("_"),weekdaysShort:"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി".split("_"),weekdaysMin:"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ".split("_"),longDateFormat:{LT:"A h:mm -നു",LTS:"A h:mm:ss -നു",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm -നു",LLLL:"dddd, D MMMM YYYY, A h:mm -നു"},calendar:{sameDay:"[ഇന്ന്] LT",nextDay:"[നാളെ] LT",nextWeek:"dddd, LT",lastDay:"[ഇന്നലെ] LT",lastWeek:"[കഴിഞ്ഞ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s കഴിഞ്ഞ്",past:"%s മുൻപ്",s:"അൽപ നിമിഷങ്ങൾ",ss:"%d സെക്കൻഡ്",m:"ഒരു മിനിറ്റ്",mm:"%d മിനിറ്റ്",h:"ഒരു മണിക്കൂർ",hh:"%d മണിക്കൂർ",d:"ഒരു ദിവസം",dd:"%d ദിവസം",M:"ഒരു മാസം",MM:"%d മാസം",y:"ഒരു വർഷം",yy:"%d വർഷം"},meridiemParse:/രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,meridiemHour:function(e,t){return 12===e&&(e=0),"രാത്രി"===t&&e>=4||"ഉച്ച കഴിഞ്ഞ്"===t||"വൈകുന്നേരം"===t?e+12:e},meridiem:function(e,t,n){return e<4?"രാത്രി":e<12?"രാവിലെ":e<17?"ഉച്ച കഴിഞ്ഞ്":e<20?"വൈകുന്നേരം":"രാത്രി"}});return t}))},3784:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),n="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),a=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],r=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,s=e.defineLocale("nl-be",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}});return s}))},3827:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ja",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日(ddd) HH:mm"},meridiemParse:/午前|午後/i,isPM:function(e){return"午後"===e},meridiem:function(e,t,n){return e<12?"午前":"午後"},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:function(e){return e.week()<this.week()?"[来週]dddd LT":"dddd LT"},lastDay:"[昨日] LT",lastWeek:function(e){return this.week()<e.week()?"[先週]dddd LT":"dddd LT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}日/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}});return t}))},3861:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},n={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},a=e.defineLocale("hi",{months:"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर".split("_"),monthsShort:"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.".split("_"),monthsParseExact:!0,weekdays:"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm बजे",LTS:"A h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm बजे",LLLL:"dddd, D MMMM YYYY, A h:mm बजे"},calendar:{sameDay:"[आज] LT",nextDay:"[कल] LT",nextWeek:"dddd, LT",lastDay:"[कल] LT",lastWeek:"[पिछले] dddd, LT",sameElse:"L"},relativeTime:{future:"%s में",past:"%s पहले",s:"कुछ ही क्षण",ss:"%d सेकंड",m:"एक मिनट",mm:"%d मिनट",h:"एक घंटा",hh:"%d घंटे",d:"एक दिन",dd:"%d दिन",M:"एक महीने",MM:"%d महीने",y:"एक वर्ष",yy:"%d वर्ष"},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/रात|सुबह|दोपहर|शाम/,meridiemHour:function(e,t){return 12===e&&(e=0),"रात"===t?e<4?e:e+12:"सुबह"===t?e:"दोपहर"===t?e>=10?e:e+12:"शाम"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"रात":e<10?"सुबह":e<17?"दोपहर":e<20?"शाम":"रात"},week:{dow:0,doy:6}});return a}))},3872:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},4051:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("x-pseudo",{months:"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér".split("_"),monthsShort:"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc".split("_"),monthsParseExact:!0,weekdays:"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý".split("_"),weekdaysShort:"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát".split("_"),weekdaysMin:"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[T~ódá~ý át] LT",nextDay:"[T~ómó~rró~w át] LT",nextWeek:"dddd [át] LT",lastDay:"[Ý~ést~érdá~ý át] LT",lastWeek:"[L~ást] dddd [át] LT",sameElse:"L"},relativeTime:{future:"í~ñ %s",past:"%s á~gó",s:"á ~féw ~sécó~ñds",ss:"%d s~écóñ~ds",m:"á ~míñ~úté",mm:"%d m~íñú~tés",h:"á~ñ hó~úr",hh:"%d h~óúrs",d:"á ~dáý",dd:"%d d~áýs",M:"á ~móñ~th",MM:"%d m~óñt~hs",y:"á ~ýéár",yy:"%d ý~éárs"},dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},4063:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"siang"===t?e>=11?e:e+12:"sore"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"siang":e<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});return t}))},4226:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn ôl",s:"ychydig eiliadau",ss:"%d eiliad",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},dayOfMonthOrdinalParse:/\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,ordinal:function(e){var t=e,n="",a=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"];return t>20?n=40===t||50===t||60===t||80===t||100===t?"fed":"ain":t>0&&(n=a[t]),e+n},week:{dow:1,doy:4}});return t}))},4418:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień".split("_"),n="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia".split("_");function a(e){return e%10<5&&e%10>1&&~~(e/10)%10!==1}function r(e,t,n){var r=e+" ";switch(n){case"ss":return r+(a(e)?"sekundy":"sekund");case"m":return t?"minuta":"minutę";case"mm":return r+(a(e)?"minuty":"minut");case"h":return t?"godzina":"godzinę";case"hh":return r+(a(e)?"godziny":"godzin");case"MM":return r+(a(e)?"miesiące":"miesięcy");case"yy":return r+(a(e)?"lata":"lat")}}var s=e.defineLocale("pl",{months:function(e,a){return e?""===a?"("+n[e.month()]+"|"+t[e.month()]+")":/D MMMM/.test(a)?n[e.month()]:t[e.month()]:t},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru".split("_"),weekdays:"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota".split("_"),weekdaysShort:"ndz_pon_wt_śr_czw_pt_sob".split("_"),weekdaysMin:"Nd_Pn_Wt_Śr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Dziś o] LT",nextDay:"[Jutro o] LT",nextWeek:function(){switch(this.day()){case 0:return"[W niedzielę o] LT";case 2:return"[We wtorek o] LT";case 3:return"[W środę o] LT";case 6:return"[W sobotę o] LT";default:return"[W] dddd [o] LT"}},lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszłą niedzielę o] LT";case 3:return"[W zeszłą środę o] LT";case 6:return"[W zeszłą sobotę o] LT";default:return"[W zeszły] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",ss:r,m:r,mm:r,h:r,hh:r,d:"1 dzień",dd:"%d dni",M:"miesiąc",MM:r,y:"rok",yy:r},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return s}))},4429:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n){var a=e+" ";switch(n){case"ss":return a+=1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi",a;case"m":return t?"jedna minuta":"jedne minute";case"mm":return a+=1===e?"minuta":2===e||3===e||4===e?"minute":"minuta",a;case"h":return t?"jedan sat":"jednog sata";case"hh":return a+=1===e?"sat":2===e||3===e||4===e?"sata":"sati",a;case"dd":return a+=1===e?"dan":"dana",a;case"MM":return a+=1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci",a;case"yy":return a+=1===e?"godina":2===e||3===e||4===e?"godine":"godina",a}}var n=e.defineLocale("bs",{months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:t,m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return n}))},4451:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"೧",2:"೨",3:"೩",4:"೪",5:"೫",6:"೬",7:"೭",8:"೮",9:"೯",0:"೦"},n={"೧":"1","೨":"2","೩":"3","೪":"4","೫":"5","೬":"6","೭":"7","೮":"8","೯":"9","೦":"0"},a=e.defineLocale("kn",{months:"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್".split("_"),monthsShort:"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ".split("_"),monthsParseExact:!0,weekdays:"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ".split("_"),weekdaysShort:"ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ".split("_"),weekdaysMin:"ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[ಇಂದು] LT",nextDay:"[ನಾಳೆ] LT",nextWeek:"dddd, LT",lastDay:"[ನಿನ್ನೆ] LT",lastWeek:"[ಕೊನೆಯ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ನಂತರ",past:"%s ಹಿಂದೆ",s:"ಕೆಲವು ಕ್ಷಣಗಳು",ss:"%d ಸೆಕೆಂಡುಗಳು",m:"ಒಂದು ನಿಮಿಷ",mm:"%d ನಿಮಿಷ",h:"ಒಂದು ಗಂಟೆ",hh:"%d ಗಂಟೆ",d:"ಒಂದು ದಿನ",dd:"%d ದಿನ",M:"ಒಂದು ತಿಂಗಳು",MM:"%d ತಿಂಗಳು",y:"ಒಂದು ವರ್ಷ",yy:"%d ವರ್ಷ"},preparse:function(e){return e.replace(/[೧೨೩೪೫೬೭೮೯೦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/ರಾತ್ರಿ|ಬೆಳಿಗ್ಗೆ|ಮಧ್ಯಾಹ್ನ|ಸಂಜೆ/,meridiemHour:function(e,t){return 12===e&&(e=0),"ರಾತ್ರಿ"===t?e<4?e:e+12:"ಬೆಳಿಗ್ಗೆ"===t?e:"ಮಧ್ಯಾಹ್ನ"===t?e>=10?e:e+12:"ಸಂಜೆ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"ರಾತ್ರಿ":e<10?"ಬೆಳಿಗ್ಗೆ":e<17?"ಮಧ್ಯಾಹ್ನ":e<20?"ಸಂಜೆ":"ರಾತ್ರಿ"},dayOfMonthOrdinalParse:/\d{1,2}(ನೇ)/,ordinal:function(e){return e+"ನೇ"},week:{dow:0,doy:6}});return a}))},4457:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n){var a={ss:"secunde",mm:"minute",hh:"ore",dd:"zile",MM:"luni",yy:"ani"},r=" ";return(e%100>=20||e>=100&&e%100===0)&&(r=" de "),e+r+a[n]}var n=e.defineLocale("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._febr._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"duminică_luni_marți_miercuri_joi_vineri_sâmbătă".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_Sâm".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_Sâ".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mâine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s în urmă",s:"câteva secunde",ss:t,m:"un minut",mm:t,h:"o oră",hh:t,d:"o zi",dd:t,M:"o lună",MM:t,y:"un an",yy:t},week:{dow:1,doy:7}});return n}))},4496:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ar-ma",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:6,doy:12}});return t}))},4566:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"sun_mån_tys_ons_tor_fre_lau".split("_"),weekdaysMin:"su_må_ty_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I går klokka] LT",lastWeek:"[Føregåande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s sidan",s:"nokre sekund",ss:"%d sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",M:"ein månad",MM:"%d månader",y:"eit år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return t}))},4691:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e){return e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}var n=e.defineLocale("el",{monthsNominativeEl:"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split("_"),monthsGenitiveEl:"Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου".split("_"),months:function(e,t){return e?"string"===typeof t&&/D/.test(t.substring(0,t.indexOf("MMMM")))?this._monthsGenitiveEl[e.month()]:this._monthsNominativeEl[e.month()]:this._monthsNominativeEl},monthsShort:"Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ".split("_"),weekdays:"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split("_"),weekdaysShort:"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),weekdaysMin:"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),meridiem:function(e,t,n){return e>11?n?"μμ":"ΜΜ":n?"πμ":"ΠΜ"},isPM:function(e){return"μ"===(e+"").toLowerCase()[0]},meridiemParse:/[ΠΜ]\.?Μ?\.?/i,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendarEl:{sameDay:"[Σήμερα {}] LT",nextDay:"[Αύριο {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Χθες {}] LT",lastWeek:function(){switch(this.day()){case 6:return"[το προηγούμενο] dddd [{}] LT";default:return"[την προηγούμενη] dddd [{}] LT"}},sameElse:"L"},calendar:function(e,n){var a=this._calendarEl[e],r=n&&n.hours();return t(a)&&(a=a.apply(n)),a.replace("{}",r%12===1?"στη":"στις")},relativeTime:{future:"σε %s",past:"%s πριν",s:"λίγα δευτερόλεπτα",ss:"%d δευτερόλεπτα",m:"ένα λεπτό",mm:"%d λεπτά",h:"μία ώρα",hh:"%d ώρες",d:"μία μέρα",dd:"%d μέρες",M:"ένας μήνας",MM:"%d μήνες",y:"ένας χρόνος",yy:"%d χρόνια"},dayOfMonthOrdinalParse:/\d{1,2}η/,ordinal:"%dη",week:{dow:1,doy:4}});return n}))},4697:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[n][0]:r[n][1]}var n=e.defineLocale("de-ch",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return n}))},4903:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("bm",{months:"Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo".split("_"),monthsShort:"Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des".split("_"),weekdays:"Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri".split("_"),weekdaysShort:"Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib".split("_"),weekdaysMin:"Ka_Nt_Ta_Ar_Al_Ju_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"MMMM [tile] D [san] YYYY",LLL:"MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm",LLLL:"dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm"},calendar:{sameDay:"[Bi lɛrɛ] LT",nextDay:"[Sini lɛrɛ] LT",nextWeek:"dddd [don lɛrɛ] LT",lastDay:"[Kunu lɛrɛ] LT",lastWeek:"dddd [tɛmɛnen lɛrɛ] LT",sameElse:"L"},relativeTime:{future:"%s kɔnɔ",past:"a bɛ %s bɔ",s:"sanga dama dama",ss:"sekondi %d",m:"miniti kelen",mm:"miniti %d",h:"lɛrɛ kelen",hh:"lɛrɛ %d",d:"tile kelen",dd:"tile %d",M:"kalo kelen",MM:"kalo %d",y:"san kelen",yy:"san %d"},week:{dow:1,doy:4}});return t}))},5050:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"૧",2:"૨",3:"૩",4:"૪",5:"૫",6:"૬",7:"૭",8:"૮",9:"૯",0:"૦"},n={"૧":"1","૨":"2","૩":"3","૪":"4","૫":"5","૬":"6","૭":"7","૮":"8","૯":"9","૦":"0"},a=e.defineLocale("gu",{months:"જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર".split("_"),monthsShort:"જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.".split("_"),monthsParseExact:!0,weekdays:"રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર".split("_"),weekdaysShort:"રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ".split("_"),weekdaysMin:"ર_સો_મં_બુ_ગુ_શુ_શ".split("_"),longDateFormat:{LT:"A h:mm વાગ્યે",LTS:"A h:mm:ss વાગ્યે",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm વાગ્યે",LLLL:"dddd, D MMMM YYYY, A h:mm વાગ્યે"},calendar:{sameDay:"[આજ] LT",nextDay:"[કાલે] LT",nextWeek:"dddd, LT",lastDay:"[ગઇકાલે] LT",lastWeek:"[પાછલા] dddd, LT",sameElse:"L"},relativeTime:{future:"%s મા",past:"%s પેહલા",s:"અમુક પળો",ss:"%d સેકંડ",m:"એક મિનિટ",mm:"%d મિનિટ",h:"એક કલાક",hh:"%d કલાક",d:"એક દિવસ",dd:"%d દિવસ",M:"એક મહિનો",MM:"%d મહિનો",y:"એક વર્ષ",yy:"%d વર્ષ"},preparse:function(e){return e.replace(/[૧૨૩૪૫૬૭૮૯૦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/રાત|બપોર|સવાર|સાંજ/,meridiemHour:function(e,t){return 12===e&&(e=0),"રાત"===t?e<4?e:e+12:"સવાર"===t?e:"બપોર"===t?e>=10?e:e+12:"સાંજ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"રાત":e<10?"સવાર":e<17?"બપોર":e<20?"સાંજ":"રાત"},week:{dow:0,doy:6}});return a}))},5093:function(e,t,n){e=n.nmd(e),function(t,n){e.exports=n()}(0,(function(){"use strict";var t,a;function r(){return t.apply(null,arguments)}function s(e){t=e}function i(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function o(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function d(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(e.hasOwnProperty(t))return!1;return!0}function _(e){return void 0===e}function u(e){return"number"===typeof e||"[object Number]"===Object.prototype.toString.call(e)}function l(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function m(e,t){var n,a=[];for(n=0;n<e.length;++n)a.push(t(e[n],n));return a}function c(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function h(e,t){for(var n in t)c(t,n)&&(e[n]=t[n]);return c(t,"toString")&&(e.toString=t.toString),c(t,"valueOf")&&(e.valueOf=t.valueOf),e}function f(e,t,n,a){return Bn(e,t,n,a,!0).utc()}function M(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function p(e){return null==e._pf&&(e._pf=M()),e._pf}function y(e){if(null==e._isValid){var t=p(e),n=a.call(t.parsedDateParts,(function(e){return null!=e})),r=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n);if(e._strict&&(r=r&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return r;e._isValid=r}return e._isValid}function L(e){var t=f(NaN);return null!=e?h(p(t),e):p(t).userInvalidated=!0,t}a=Array.prototype.some?Array.prototype.some:function(e){for(var t=Object(this),n=t.length>>>0,a=0;a<n;a++)if(a in t&&e.call(this,t[a],a,t))return!0;return!1};var Y=r.momentProperties=[];function v(e,t){var n,a,r;if(_(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),_(t._i)||(e._i=t._i),_(t._f)||(e._f=t._f),_(t._l)||(e._l=t._l),_(t._strict)||(e._strict=t._strict),_(t._tzm)||(e._tzm=t._tzm),_(t._isUTC)||(e._isUTC=t._isUTC),_(t._offset)||(e._offset=t._offset),_(t._pf)||(e._pf=p(t)),_(t._locale)||(e._locale=t._locale),Y.length>0)for(n=0;n<Y.length;n++)a=Y[n],r=t[a],_(r)||(e[a]=r);return e}var k=!1;function D(e){v(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===k&&(k=!0,r.updateOffset(this),k=!1)}function g(e){return e instanceof D||null!=e&&null!=e._isAMomentObject}function T(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function w(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=T(t)),n}function b(e,t,n){var a,r=Math.min(e.length,t.length),s=Math.abs(e.length-t.length),i=0;for(a=0;a<r;a++)(n&&e[a]!==t[a]||!n&&w(e[a])!==w(t[a]))&&i++;return i+s}function S(e){!1===r.suppressDeprecationWarnings&&"undefined"!==typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function H(e,t){var n=!0;return h((function(){if(null!=r.deprecationHandler&&r.deprecationHandler(null,e),n){for(var a,s=[],i=0;i<arguments.length;i++){if(a="","object"===typeof arguments[i]){for(var o in a+="\n["+i+"] ",arguments[0])a+=o+": "+arguments[0][o]+", ";a=a.slice(0,-2)}else a=arguments[i];s.push(a)}S(e+"\nArguments: "+Array.prototype.slice.call(s).join("")+"\n"+(new Error).stack),n=!1}return t.apply(this,arguments)}),t)}var j,x={};function O(e,t){null!=r.deprecationHandler&&r.deprecationHandler(e,t),x[e]||(S(t),x[e]=!0)}function P(e){return e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function W(e){var t,n;for(n in e)t=e[n],P(t)?this[n]=t:this["_"+n]=t;this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function A(e,t){var n,a=h({},e);for(n in t)c(t,n)&&(o(e[n])&&o(t[n])?(a[n]={},h(a[n],e[n]),h(a[n],t[n])):null!=t[n]?a[n]=t[n]:delete a[n]);for(n in e)c(e,n)&&!c(t,n)&&o(e[n])&&(a[n]=h({},a[n]));return a}function E(e){null!=e&&this.set(e)}r.suppressDeprecationWarnings=!1,r.deprecationHandler=null,j=Object.keys?Object.keys:function(e){var t,n=[];for(t in e)c(e,t)&&n.push(t);return n};var F={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function C(e,t,n){var a=this._calendar[e]||this._calendar["sameElse"];return P(a)?a.call(t,n):a}var z={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function $(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.replace(/MMMM|MM|DD|dddd/g,(function(e){return e.slice(1)})),this._longDateFormat[e])}var I="Invalid date";function N(){return this._invalidDate}var J="%d",R=/\d{1,2}/;function U(e){return this._ordinal.replace("%d",e)}var G={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function V(e,t,n,a){var r=this._relativeTime[n];return P(r)?r(e,t,n,a):r.replace(/%d/i,e)}function B(e,t){var n=this._relativeTime[e>0?"future":"past"];return P(n)?n(t):n.replace(/%s/i,t)}var K={};function q(e,t){var n=e.toLowerCase();K[n]=K[n+"s"]=K[t]=e}function Z(e){return"string"===typeof e?K[e]||K[e.toLowerCase()]:void 0}function Q(e){var t,n,a={};for(n in e)c(e,n)&&(t=Z(n),t&&(a[t]=e[n]));return a}var X={};function ee(e,t){X[e]=t}function te(e){var t=[];for(var n in e)t.push({unit:n,priority:X[n]});return t.sort((function(e,t){return e.priority-t.priority})),t}function ne(e,t,n){var a=""+Math.abs(e),r=t-a.length,s=e>=0;return(s?n?"+":"":"-")+Math.pow(10,Math.max(0,r)).toString().substr(1)+a}var ae=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,re=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,se={},ie={};function oe(e,t,n,a){var r=a;"string"===typeof a&&(r=function(){return this[a]()}),e&&(ie[e]=r),t&&(ie[t[0]]=function(){return ne(r.apply(this,arguments),t[1],t[2])}),n&&(ie[n]=function(){return this.localeData().ordinal(r.apply(this,arguments),e)})}function de(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function _e(e){var t,n,a=e.match(ae);for(t=0,n=a.length;t<n;t++)ie[a[t]]?a[t]=ie[a[t]]:a[t]=de(a[t]);return function(t){var r,s="";for(r=0;r<n;r++)s+=P(a[r])?a[r].call(t,e):a[r];return s}}function ue(e,t){return e.isValid()?(t=le(t,e.localeData()),se[t]=se[t]||_e(t),se[t](e)):e.localeData().invalidDate()}function le(e,t){var n=5;function a(e){return t.longDateFormat(e)||e}re.lastIndex=0;while(n>=0&&re.test(e))e=e.replace(re,a),re.lastIndex=0,n-=1;return e}var me=/\d/,ce=/\d\d/,he=/\d{3}/,fe=/\d{4}/,Me=/[+-]?\d{6}/,pe=/\d\d?/,ye=/\d\d\d\d?/,Le=/\d\d\d\d\d\d?/,Ye=/\d{1,3}/,ve=/\d{1,4}/,ke=/[+-]?\d{1,6}/,De=/\d+/,ge=/[+-]?\d+/,Te=/Z|[+-]\d\d:?\d\d/gi,we=/Z|[+-]\d\d(?::?\d\d)?/gi,be=/[+-]?\d+(\.\d{1,3})?/,Se=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,He={};function je(e,t,n){He[e]=P(t)?t:function(e,a){return e&&n?n:t}}function xe(e,t){return c(He,e)?He[e](t._strict,t._locale):new RegExp(Oe(e))}function Oe(e){return Pe(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,n,a,r){return t||n||a||r})))}function Pe(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var We={};function Ae(e,t){var n,a=t;for("string"===typeof e&&(e=[e]),u(t)&&(a=function(e,n){n[t]=w(e)}),n=0;n<e.length;n++)We[e[n]]=a}function Ee(e,t){Ae(e,(function(e,n,a,r){a._w=a._w||{},t(e,a._w,a,r)}))}function Fe(e,t,n){null!=t&&c(We,e)&&We[e](t,n._a,n,e)}var Ce=0,ze=1,$e=2,Ie=3,Ne=4,Je=5,Re=6,Ue=7,Ge=8;function Ve(e){return Be(e)?366:365}function Be(e){return e%4===0&&e%100!==0||e%400===0}oe("Y",0,0,(function(){var e=this.year();return e<=9999?""+e:"+"+e})),oe(0,["YY",2],0,(function(){return this.year()%100})),oe(0,["YYYY",4],0,"year"),oe(0,["YYYYY",5],0,"year"),oe(0,["YYYYYY",6,!0],0,"year"),q("year","y"),ee("year",1),je("Y",ge),je("YY",pe,ce),je("YYYY",ve,fe),je("YYYYY",ke,Me),je("YYYYYY",ke,Me),Ae(["YYYYY","YYYYYY"],Ce),Ae("YYYY",(function(e,t){t[Ce]=2===e.length?r.parseTwoDigitYear(e):w(e)})),Ae("YY",(function(e,t){t[Ce]=r.parseTwoDigitYear(e)})),Ae("Y",(function(e,t){t[Ce]=parseInt(e,10)})),r.parseTwoDigitYear=function(e){return w(e)+(w(e)>68?1900:2e3)};var Ke,qe=Qe("FullYear",!0);function Ze(){return Be(this.year())}function Qe(e,t){return function(n){return null!=n?(et(this,e,n),r.updateOffset(this,t),this):Xe(this,e)}}function Xe(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function et(e,t,n){e.isValid()&&!isNaN(n)&&("FullYear"===t&&Be(e.year())&&1===e.month()&&29===e.date()?e._d["set"+(e._isUTC?"UTC":"")+t](n,e.month(),rt(n,e.month())):e._d["set"+(e._isUTC?"UTC":"")+t](n))}function tt(e){return e=Z(e),P(this[e])?this[e]():this}function nt(e,t){if("object"===typeof e){e=Q(e);for(var n=te(e),a=0;a<n.length;a++)this[n[a].unit](e[n[a].unit])}else if(e=Z(e),P(this[e]))return this[e](t);return this}function at(e,t){return(e%t+t)%t}function rt(e,t){if(isNaN(e)||isNaN(t))return NaN;var n=at(t,12);return e+=(t-n)/12,1===n?Be(e)?29:28:31-n%7%2}Ke=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},oe("M",["MM",2],"Mo",(function(){return this.month()+1})),oe("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),oe("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),q("month","M"),ee("month",8),je("M",pe),je("MM",pe,ce),je("MMM",(function(e,t){return t.monthsShortRegex(e)})),je("MMMM",(function(e,t){return t.monthsRegex(e)})),Ae(["M","MM"],(function(e,t){t[ze]=w(e)-1})),Ae(["MMM","MMMM"],(function(e,t,n,a){var r=n._locale.monthsParse(e,a,n._strict);null!=r?t[ze]=r:p(n).invalidMonth=e}));var st=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,it="January_February_March_April_May_June_July_August_September_October_November_December".split("_");function ot(e,t){return e?i(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||st).test(t)?"format":"standalone"][e.month()]:i(this._months)?this._months:this._months["standalone"]}var dt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function _t(e,t){return e?i(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[st.test(t)?"format":"standalone"][e.month()]:i(this._monthsShort)?this._monthsShort:this._monthsShort["standalone"]}function ut(e,t,n){var a,r,s,i=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],a=0;a<12;++a)s=f([2e3,a]),this._shortMonthsParse[a]=this.monthsShort(s,"").toLocaleLowerCase(),this._longMonthsParse[a]=this.months(s,"").toLocaleLowerCase();return n?"MMM"===t?(r=Ke.call(this._shortMonthsParse,i),-1!==r?r:null):(r=Ke.call(this._longMonthsParse,i),-1!==r?r:null):"MMM"===t?(r=Ke.call(this._shortMonthsParse,i),-1!==r?r:(r=Ke.call(this._longMonthsParse,i),-1!==r?r:null)):(r=Ke.call(this._longMonthsParse,i),-1!==r?r:(r=Ke.call(this._shortMonthsParse,i),-1!==r?r:null))}function lt(e,t,n){var a,r,s;if(this._monthsParseExact)return ut.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),a=0;a<12;a++){if(r=f([2e3,a]),n&&!this._longMonthsParse[a]&&(this._longMonthsParse[a]=new RegExp("^"+this.months(r,"").replace(".","")+"$","i"),this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(r,"").replace(".","")+"$","i")),n||this._monthsParse[a]||(s="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[a]=new RegExp(s.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[a].test(e))return a;if(n&&"MMM"===t&&this._shortMonthsParse[a].test(e))return a;if(!n&&this._monthsParse[a].test(e))return a}}function mt(e,t){var n;if(!e.isValid())return e;if("string"===typeof t)if(/^\d+$/.test(t))t=w(t);else if(t=e.localeData().monthsParse(t),!u(t))return e;return n=Math.min(e.date(),rt(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n),e}function ct(e){return null!=e?(mt(this,e),r.updateOffset(this,!0),this):Xe(this,"Month")}function ht(){return rt(this.year(),this.month())}var ft=Se;function Mt(e){return this._monthsParseExact?(c(this,"_monthsRegex")||Lt.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(c(this,"_monthsShortRegex")||(this._monthsShortRegex=ft),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)}var pt=Se;function yt(e){return this._monthsParseExact?(c(this,"_monthsRegex")||Lt.call(this),e?this._monthsStrictRegex:this._monthsRegex):(c(this,"_monthsRegex")||(this._monthsRegex=pt),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)}function Lt(){function e(e,t){return t.length-e.length}var t,n,a=[],r=[],s=[];for(t=0;t<12;t++)n=f([2e3,t]),a.push(this.monthsShort(n,"")),r.push(this.months(n,"")),s.push(this.months(n,"")),s.push(this.monthsShort(n,""));for(a.sort(e),r.sort(e),s.sort(e),t=0;t<12;t++)a[t]=Pe(a[t]),r[t]=Pe(r[t]);for(t=0;t<24;t++)s[t]=Pe(s[t]);this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Yt(e,t,n,a,r,s,i){var o;return e<100&&e>=0?(o=new Date(e+400,t,n,a,r,s,i),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,a,r,s,i),o}function vt(e){var t;if(e<100&&e>=0){var n=Array.prototype.slice.call(arguments);n[0]=e+400,t=new Date(Date.UTC.apply(null,n)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)}else t=new Date(Date.UTC.apply(null,arguments));return t}function kt(e,t,n){var a=7+t-n,r=(7+vt(e,0,a).getUTCDay()-t)%7;return-r+a-1}function Dt(e,t,n,a,r){var s,i,o=(7+n-a)%7,d=kt(e,a,r),_=1+7*(t-1)+o+d;return _<=0?(s=e-1,i=Ve(s)+_):_>Ve(e)?(s=e+1,i=_-Ve(e)):(s=e,i=_),{year:s,dayOfYear:i}}function gt(e,t,n){var a,r,s=kt(e.year(),t,n),i=Math.floor((e.dayOfYear()-s-1)/7)+1;return i<1?(r=e.year()-1,a=i+Tt(r,t,n)):i>Tt(e.year(),t,n)?(a=i-Tt(e.year(),t,n),r=e.year()+1):(r=e.year(),a=i),{week:a,year:r}}function Tt(e,t,n){var a=kt(e,t,n),r=kt(e+1,t,n);return(Ve(e)-a+r)/7}function wt(e){return gt(e,this._week.dow,this._week.doy).week}oe("w",["ww",2],"wo","week"),oe("W",["WW",2],"Wo","isoWeek"),q("week","w"),q("isoWeek","W"),ee("week",5),ee("isoWeek",5),je("w",pe),je("ww",pe,ce),je("W",pe),je("WW",pe,ce),Ee(["w","ww","W","WW"],(function(e,t,n,a){t[a.substr(0,1)]=w(e)}));var bt={dow:0,doy:6};function St(){return this._week.dow}function Ht(){return this._week.doy}function jt(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")}function xt(e){var t=gt(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")}function Ot(e,t){return"string"!==typeof e?e:isNaN(e)?(e=t.weekdaysParse(e),"number"===typeof e?e:null):parseInt(e,10)}function Pt(e,t){return"string"===typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}function Wt(e,t){return e.slice(t,7).concat(e.slice(0,t))}oe("d",0,"do","day"),oe("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),oe("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),oe("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),oe("e",0,0,"weekday"),oe("E",0,0,"isoWeekday"),q("day","d"),q("weekday","e"),q("isoWeekday","E"),ee("day",11),ee("weekday",11),ee("isoWeekday",11),je("d",pe),je("e",pe),je("E",pe),je("dd",(function(e,t){return t.weekdaysMinRegex(e)})),je("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),je("dddd",(function(e,t){return t.weekdaysRegex(e)})),Ee(["dd","ddd","dddd"],(function(e,t,n,a){var r=n._locale.weekdaysParse(e,a,n._strict);null!=r?t.d=r:p(n).invalidWeekday=e})),Ee(["d","e","E"],(function(e,t,n,a){t[a]=w(e)}));var At="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_");function Et(e,t){var n=i(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===e?Wt(n,this._week.dow):e?n[e.day()]:n}var Ft="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_");function Ct(e){return!0===e?Wt(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort}var zt="Su_Mo_Tu_We_Th_Fr_Sa".split("_");function $t(e){return!0===e?Wt(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin}function It(e,t,n){var a,r,s,i=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],a=0;a<7;++a)s=f([2e3,1]).day(a),this._minWeekdaysParse[a]=this.weekdaysMin(s,"").toLocaleLowerCase(),this._shortWeekdaysParse[a]=this.weekdaysShort(s,"").toLocaleLowerCase(),this._weekdaysParse[a]=this.weekdays(s,"").toLocaleLowerCase();return n?"dddd"===t?(r=Ke.call(this._weekdaysParse,i),-1!==r?r:null):"ddd"===t?(r=Ke.call(this._shortWeekdaysParse,i),-1!==r?r:null):(r=Ke.call(this._minWeekdaysParse,i),-1!==r?r:null):"dddd"===t?(r=Ke.call(this._weekdaysParse,i),-1!==r?r:(r=Ke.call(this._shortWeekdaysParse,i),-1!==r?r:(r=Ke.call(this._minWeekdaysParse,i),-1!==r?r:null))):"ddd"===t?(r=Ke.call(this._shortWeekdaysParse,i),-1!==r?r:(r=Ke.call(this._weekdaysParse,i),-1!==r?r:(r=Ke.call(this._minWeekdaysParse,i),-1!==r?r:null))):(r=Ke.call(this._minWeekdaysParse,i),-1!==r?r:(r=Ke.call(this._weekdaysParse,i),-1!==r?r:(r=Ke.call(this._shortWeekdaysParse,i),-1!==r?r:null)))}function Nt(e,t,n){var a,r,s;if(this._weekdaysParseExact)return It.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),a=0;a<7;a++){if(r=f([2e3,1]).day(a),n&&!this._fullWeekdaysParse[a]&&(this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(r,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(r,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(r,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[a]||(s="^"+this.weekdays(r,"")+"|^"+this.weekdaysShort(r,"")+"|^"+this.weekdaysMin(r,""),this._weekdaysParse[a]=new RegExp(s.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[a].test(e))return a;if(n&&"ddd"===t&&this._shortWeekdaysParse[a].test(e))return a;if(n&&"dd"===t&&this._minWeekdaysParse[a].test(e))return a;if(!n&&this._weekdaysParse[a].test(e))return a}}function Jt(e){if(!this.isValid())return null!=e?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=Ot(e,this.localeData()),this.add(e-t,"d")):t}function Rt(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")}function Ut(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=Pt(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7}var Gt=Se;function Vt(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||Qt.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(c(this,"_weekdaysRegex")||(this._weekdaysRegex=Gt),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)}var Bt=Se;function Kt(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||Qt.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(c(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Bt),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}var qt=Se;function Zt(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||Qt.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(c(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=qt),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Qt(){function e(e,t){return t.length-e.length}var t,n,a,r,s,i=[],o=[],d=[],_=[];for(t=0;t<7;t++)n=f([2e3,1]).day(t),a=this.weekdaysMin(n,""),r=this.weekdaysShort(n,""),s=this.weekdays(n,""),i.push(a),o.push(r),d.push(s),_.push(a),_.push(r),_.push(s);for(i.sort(e),o.sort(e),d.sort(e),_.sort(e),t=0;t<7;t++)o[t]=Pe(o[t]),d[t]=Pe(d[t]),_[t]=Pe(_[t]);this._weekdaysRegex=new RegExp("^("+_.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+d.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function Xt(){return this.hours()%12||12}function en(){return this.hours()||24}function tn(e,t){oe(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function nn(e,t){return t._meridiemParse}function an(e){return"p"===(e+"").toLowerCase().charAt(0)}oe("H",["HH",2],0,"hour"),oe("h",["hh",2],0,Xt),oe("k",["kk",2],0,en),oe("hmm",0,0,(function(){return""+Xt.apply(this)+ne(this.minutes(),2)})),oe("hmmss",0,0,(function(){return""+Xt.apply(this)+ne(this.minutes(),2)+ne(this.seconds(),2)})),oe("Hmm",0,0,(function(){return""+this.hours()+ne(this.minutes(),2)})),oe("Hmmss",0,0,(function(){return""+this.hours()+ne(this.minutes(),2)+ne(this.seconds(),2)})),tn("a",!0),tn("A",!1),q("hour","h"),ee("hour",13),je("a",nn),je("A",nn),je("H",pe),je("h",pe),je("k",pe),je("HH",pe,ce),je("hh",pe,ce),je("kk",pe,ce),je("hmm",ye),je("hmmss",Le),je("Hmm",ye),je("Hmmss",Le),Ae(["H","HH"],Ie),Ae(["k","kk"],(function(e,t,n){var a=w(e);t[Ie]=24===a?0:a})),Ae(["a","A"],(function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e})),Ae(["h","hh"],(function(e,t,n){t[Ie]=w(e),p(n).bigHour=!0})),Ae("hmm",(function(e,t,n){var a=e.length-2;t[Ie]=w(e.substr(0,a)),t[Ne]=w(e.substr(a)),p(n).bigHour=!0})),Ae("hmmss",(function(e,t,n){var a=e.length-4,r=e.length-2;t[Ie]=w(e.substr(0,a)),t[Ne]=w(e.substr(a,2)),t[Je]=w(e.substr(r)),p(n).bigHour=!0})),Ae("Hmm",(function(e,t,n){var a=e.length-2;t[Ie]=w(e.substr(0,a)),t[Ne]=w(e.substr(a))})),Ae("Hmmss",(function(e,t,n){var a=e.length-4,r=e.length-2;t[Ie]=w(e.substr(0,a)),t[Ne]=w(e.substr(a,2)),t[Je]=w(e.substr(r))}));var rn=/[ap]\.?m?\.?/i;function sn(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"}var on,dn=Qe("Hours",!0),_n={calendar:F,longDateFormat:z,invalidDate:I,ordinal:J,dayOfMonthOrdinalParse:R,relativeTime:G,months:it,monthsShort:dt,week:bt,weekdays:At,weekdaysMin:zt,weekdaysShort:Ft,meridiemParse:rn},un={},ln={};function mn(e){return e?e.toLowerCase().replace("_","-"):e}function cn(e){var t,n,a,r,s=0;while(s<e.length){r=mn(e[s]).split("-"),t=r.length,n=mn(e[s+1]),n=n?n.split("-"):null;while(t>0){if(a=hn(r.slice(0,t).join("-")),a)return a;if(n&&n.length>=t&&b(r,n,!0)>=t-1)break;t--}s++}return on}function hn(t){var a=null;if(!un[t]&&e&&e.exports)try{a=on._abbr;n(5358)("./"+t),fn(a)}catch(r){}return un[t]}function fn(e,t){var n;return e&&(n=_(t)?yn(e):Mn(e,t),n?on=n:"undefined"!==typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),on._abbr}function Mn(e,t){if(null!==t){var n,a=_n;if(t.abbr=e,null!=un[e])O("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),a=un[e]._config;else if(null!=t.parentLocale)if(null!=un[t.parentLocale])a=un[t.parentLocale]._config;else{if(n=hn(t.parentLocale),null==n)return ln[t.parentLocale]||(ln[t.parentLocale]=[]),ln[t.parentLocale].push({name:e,config:t}),null;a=n._config}return un[e]=new E(A(a,t)),ln[e]&&ln[e].forEach((function(e){Mn(e.name,e.config)})),fn(e),un[e]}return delete un[e],null}function pn(e,t){if(null!=t){var n,a,r=_n;a=hn(e),null!=a&&(r=a._config),t=A(r,t),n=new E(t),n.parentLocale=un[e],un[e]=n,fn(e)}else null!=un[e]&&(null!=un[e].parentLocale?un[e]=un[e].parentLocale:null!=un[e]&&delete un[e]);return un[e]}function yn(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return on;if(!i(e)){if(t=hn(e),t)return t;e=[e]}return cn(e)}function Ln(){return j(un)}function Yn(e){var t,n=e._a;return n&&-2===p(e).overflow&&(t=n[ze]<0||n[ze]>11?ze:n[$e]<1||n[$e]>rt(n[Ce],n[ze])?$e:n[Ie]<0||n[Ie]>24||24===n[Ie]&&(0!==n[Ne]||0!==n[Je]||0!==n[Re])?Ie:n[Ne]<0||n[Ne]>59?Ne:n[Je]<0||n[Je]>59?Je:n[Re]<0||n[Re]>999?Re:-1,p(e)._overflowDayOfYear&&(t<Ce||t>$e)&&(t=$e),p(e)._overflowWeeks&&-1===t&&(t=Ue),p(e)._overflowWeekday&&-1===t&&(t=Ge),p(e).overflow=t),e}function vn(e,t,n){return null!=e?e:null!=t?t:n}function kn(e){var t=new Date(r.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function Dn(e){var t,n,a,r,s,i=[];if(!e._d){for(a=kn(e),e._w&&null==e._a[$e]&&null==e._a[ze]&&gn(e),null!=e._dayOfYear&&(s=vn(e._a[Ce],a[Ce]),(e._dayOfYear>Ve(s)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),n=vt(s,0,e._dayOfYear),e._a[ze]=n.getUTCMonth(),e._a[$e]=n.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=i[t]=a[t];for(;t<7;t++)e._a[t]=i[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[Ie]&&0===e._a[Ne]&&0===e._a[Je]&&0===e._a[Re]&&(e._nextDay=!0,e._a[Ie]=0),e._d=(e._useUTC?vt:Yt).apply(null,i),r=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[Ie]=24),e._w&&"undefined"!==typeof e._w.d&&e._w.d!==r&&(p(e).weekdayMismatch=!0)}}function gn(e){var t,n,a,r,s,i,o,d;if(t=e._w,null!=t.GG||null!=t.W||null!=t.E)s=1,i=4,n=vn(t.GG,e._a[Ce],gt(Kn(),1,4).year),a=vn(t.W,1),r=vn(t.E,1),(r<1||r>7)&&(d=!0);else{s=e._locale._week.dow,i=e._locale._week.doy;var _=gt(Kn(),s,i);n=vn(t.gg,e._a[Ce],_.year),a=vn(t.w,_.week),null!=t.d?(r=t.d,(r<0||r>6)&&(d=!0)):null!=t.e?(r=t.e+s,(t.e<0||t.e>6)&&(d=!0)):r=s}a<1||a>Tt(n,s,i)?p(e)._overflowWeeks=!0:null!=d?p(e)._overflowWeekday=!0:(o=Dt(n,a,r,s,i),e._a[Ce]=o.year,e._dayOfYear=o.dayOfYear)}var Tn=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,wn=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,bn=/Z|[+-]\d\d(?::?\d\d)?/,Sn=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],Hn=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],jn=/^\/?Date\((\-?\d+)/i;function xn(e){var t,n,a,r,s,i,o=e._i,d=Tn.exec(o)||wn.exec(o);if(d){for(p(e).iso=!0,t=0,n=Sn.length;t<n;t++)if(Sn[t][1].exec(d[1])){r=Sn[t][0],a=!1!==Sn[t][2];break}if(null==r)return void(e._isValid=!1);if(d[3]){for(t=0,n=Hn.length;t<n;t++)if(Hn[t][1].exec(d[3])){s=(d[2]||" ")+Hn[t][0];break}if(null==s)return void(e._isValid=!1)}if(!a&&null!=s)return void(e._isValid=!1);if(d[4]){if(!bn.exec(d[4]))return void(e._isValid=!1);i="Z"}e._f=r+(s||"")+(i||""),In(e)}else e._isValid=!1}var On=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;function Pn(e,t,n,a,r,s){var i=[Wn(e),dt.indexOf(t),parseInt(n,10),parseInt(a,10),parseInt(r,10)];return s&&i.push(parseInt(s,10)),i}function Wn(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function An(e){return e.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function En(e,t,n){if(e){var a=Ft.indexOf(e),r=new Date(t[0],t[1],t[2]).getDay();if(a!==r)return p(n).weekdayMismatch=!0,n._isValid=!1,!1}return!0}var Fn={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Cn(e,t,n){if(e)return Fn[e];if(t)return 0;var a=parseInt(n,10),r=a%100,s=(a-r)/100;return 60*s+r}function zn(e){var t=On.exec(An(e._i));if(t){var n=Pn(t[4],t[3],t[2],t[5],t[6],t[7]);if(!En(t[1],n,e))return;e._a=n,e._tzm=Cn(t[8],t[9],t[10]),e._d=vt.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0}else e._isValid=!1}function $n(e){var t=jn.exec(e._i);null===t?(xn(e),!1===e._isValid&&(delete e._isValid,zn(e),!1===e._isValid&&(delete e._isValid,r.createFromInputFallback(e)))):e._d=new Date(+t[1])}function In(e){if(e._f!==r.ISO_8601)if(e._f!==r.RFC_2822){e._a=[],p(e).empty=!0;var t,n,a,s,i,o=""+e._i,d=o.length,_=0;for(a=le(e._f,e._locale).match(ae)||[],t=0;t<a.length;t++)s=a[t],n=(o.match(xe(s,e))||[])[0],n&&(i=o.substr(0,o.indexOf(n)),i.length>0&&p(e).unusedInput.push(i),o=o.slice(o.indexOf(n)+n.length),_+=n.length),ie[s]?(n?p(e).empty=!1:p(e).unusedTokens.push(s),Fe(s,n,e)):e._strict&&!n&&p(e).unusedTokens.push(s);p(e).charsLeftOver=d-_,o.length>0&&p(e).unusedInput.push(o),e._a[Ie]<=12&&!0===p(e).bigHour&&e._a[Ie]>0&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[Ie]=Nn(e._locale,e._a[Ie],e._meridiem),Dn(e),Yn(e)}else zn(e);else xn(e)}function Nn(e,t,n){var a;return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?(a=e.isPM(n),a&&t<12&&(t+=12),a||12!==t||(t=0),t):t}function Jn(e){var t,n,a,r,s;if(0===e._f.length)return p(e).invalidFormat=!0,void(e._d=new Date(NaN));for(r=0;r<e._f.length;r++)s=0,t=v({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[r],In(t),y(t)&&(s+=p(t).charsLeftOver,s+=10*p(t).unusedTokens.length,p(t).score=s,(null==a||s<a)&&(a=s,n=t));h(e,n||t)}function Rn(e){if(!e._d){var t=Q(e._i);e._a=m([t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)})),Dn(e)}}function Un(e){var t=new D(Yn(Gn(e)));return t._nextDay&&(t.add(1,"d"),t._nextDay=void 0),t}function Gn(e){var t=e._i,n=e._f;return e._locale=e._locale||yn(e._l),null===t||void 0===n&&""===t?L({nullInput:!0}):("string"===typeof t&&(e._i=t=e._locale.preparse(t)),g(t)?new D(Yn(t)):(l(t)?e._d=t:i(n)?Jn(e):n?In(e):Vn(e),y(e)||(e._d=null),e))}function Vn(e){var t=e._i;_(t)?e._d=new Date(r.now()):l(t)?e._d=new Date(t.valueOf()):"string"===typeof t?$n(e):i(t)?(e._a=m(t.slice(0),(function(e){return parseInt(e,10)})),Dn(e)):o(t)?Rn(e):u(t)?e._d=new Date(t):r.createFromInputFallback(e)}function Bn(e,t,n,a,r){var s={};return!0!==n&&!1!==n||(a=n,n=void 0),(o(e)&&d(e)||i(e)&&0===e.length)&&(e=void 0),s._isAMomentObject=!0,s._useUTC=s._isUTC=r,s._l=n,s._i=e,s._f=t,s._strict=a,Un(s)}function Kn(e,t,n,a){return Bn(e,t,n,a,!1)}r.createFromInputFallback=H("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),r.ISO_8601=function(){},r.RFC_2822=function(){};var qn=H("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Kn.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:L()})),Zn=H("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Kn.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:L()}));function Qn(e,t){var n,a;if(1===t.length&&i(t[0])&&(t=t[0]),!t.length)return Kn();for(n=t[0],a=1;a<t.length;++a)t[a].isValid()&&!t[a][e](n)||(n=t[a]);return n}function Xn(){var e=[].slice.call(arguments,0);return Qn("isBefore",e)}function ea(){var e=[].slice.call(arguments,0);return Qn("isAfter",e)}var ta=function(){return Date.now?Date.now():+new Date},na=["year","quarter","month","week","day","hour","minute","second","millisecond"];function aa(e){for(var t in e)if(-1===Ke.call(na,t)||null!=e[t]&&isNaN(e[t]))return!1;for(var n=!1,a=0;a<na.length;++a)if(e[na[a]]){if(n)return!1;parseFloat(e[na[a]])!==w(e[na[a]])&&(n=!0)}return!0}function ra(){return this._isValid}function sa(){return ba(NaN)}function ia(e){var t=Q(e),n=t.year||0,a=t.quarter||0,r=t.month||0,s=t.week||t.isoWeek||0,i=t.day||0,o=t.hour||0,d=t.minute||0,_=t.second||0,u=t.millisecond||0;this._isValid=aa(t),this._milliseconds=+u+1e3*_+6e4*d+1e3*o*60*60,this._days=+i+7*s,this._months=+r+3*a+12*n,this._data={},this._locale=yn(),this._bubble()}function oa(e){return e instanceof ia}function da(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function _a(e,t){oe(e,0,0,(function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+ne(~~(e/60),2)+t+ne(~~e%60,2)}))}_a("Z",":"),_a("ZZ",""),je("Z",we),je("ZZ",we),Ae(["Z","ZZ"],(function(e,t,n){n._useUTC=!0,n._tzm=la(we,e)}));var ua=/([\+\-]|\d\d)/gi;function la(e,t){var n=(t||"").match(e);if(null===n)return null;var a=n[n.length-1]||[],r=(a+"").match(ua)||["-",0,0],s=60*r[1]+w(r[2]);return 0===s?0:"+"===r[0]?s:-s}function ma(e,t){var n,a;return t._isUTC?(n=t.clone(),a=(g(e)||l(e)?e.valueOf():Kn(e).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+a),r.updateOffset(n,!1),n):Kn(e).local()}function ca(e){return 15*-Math.round(e._d.getTimezoneOffset()/15)}function ha(e,t,n){var a,s=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"===typeof e){if(e=la(we,e),null===e)return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(a=ca(this)),this._offset=e,this._isUTC=!0,null!=a&&this.add(a,"m"),s!==e&&(!t||this._changeInProgress?Oa(this,ba(e-s,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,r.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?s:ca(this)}function fa(e,t){return null!=e?("string"!==typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()}function Ma(e){return this.utcOffset(0,e)}function pa(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(ca(this),"m")),this}function ya(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"===typeof this._i){var e=la(Te,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this}function La(e){return!!this.isValid()&&(e=e?Kn(e).utcOffset():0,(this.utcOffset()-e)%60===0)}function Ya(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function va(){if(!_(this._isDSTShifted))return this._isDSTShifted;var e={};if(v(e,this),e=Gn(e),e._a){var t=e._isUTC?f(e._a):Kn(e._a);this._isDSTShifted=this.isValid()&&b(e._a,t.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted}function ka(){return!!this.isValid()&&!this._isUTC}function Da(){return!!this.isValid()&&this._isUTC}function ga(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}r.updateOffset=function(){};var Ta=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,wa=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function ba(e,t){var n,a,r,s=e,i=null;return oa(e)?s={ms:e._milliseconds,d:e._days,M:e._months}:u(e)?(s={},t?s[t]=e:s.milliseconds=e):(i=Ta.exec(e))?(n="-"===i[1]?-1:1,s={y:0,d:w(i[$e])*n,h:w(i[Ie])*n,m:w(i[Ne])*n,s:w(i[Je])*n,ms:w(da(1e3*i[Re]))*n}):(i=wa.exec(e))?(n="-"===i[1]?-1:1,s={y:Sa(i[2],n),M:Sa(i[3],n),w:Sa(i[4],n),d:Sa(i[5],n),h:Sa(i[6],n),m:Sa(i[7],n),s:Sa(i[8],n)}):null==s?s={}:"object"===typeof s&&("from"in s||"to"in s)&&(r=ja(Kn(s.from),Kn(s.to)),s={},s.ms=r.milliseconds,s.M=r.months),a=new ia(s),oa(e)&&c(e,"_locale")&&(a._locale=e._locale),a}function Sa(e,t){var n=e&&parseFloat(e.replace(",","."));return(isNaN(n)?0:n)*t}function Ha(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function ja(e,t){var n;return e.isValid()&&t.isValid()?(t=ma(t,e),e.isBefore(t)?n=Ha(e,t):(n=Ha(t,e),n.milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}function xa(e,t){return function(n,a){var r,s;return null===a||isNaN(+a)||(O(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=n,n=a,a=s),n="string"===typeof n?+n:n,r=ba(n,a),Oa(this,r,e),this}}function Oa(e,t,n,a){var s=t._milliseconds,i=da(t._days),o=da(t._months);e.isValid()&&(a=null==a||a,o&&mt(e,Xe(e,"Month")+o*n),i&&et(e,"Date",Xe(e,"Date")+i*n),s&&e._d.setTime(e._d.valueOf()+s*n),a&&r.updateOffset(e,i||o))}ba.fn=ia.prototype,ba.invalid=sa;var Pa=xa(1,"add"),Wa=xa(-1,"subtract");function Aa(e,t){var n=e.diff(t,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"}function Ea(e,t){var n=e||Kn(),a=ma(n,this).startOf("day"),s=r.calendarFormat(this,a)||"sameElse",i=t&&(P(t[s])?t[s].call(this,n):t[s]);return this.format(i||this.localeData().calendar(s,this,Kn(n)))}function Fa(){return new D(this)}function Ca(e,t){var n=g(e)?e:Kn(e);return!(!this.isValid()||!n.isValid())&&(t=Z(t)||"millisecond","millisecond"===t?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(t).valueOf())}function za(e,t){var n=g(e)?e:Kn(e);return!(!this.isValid()||!n.isValid())&&(t=Z(t)||"millisecond","millisecond"===t?this.valueOf()<n.valueOf():this.clone().endOf(t).valueOf()<n.valueOf())}function $a(e,t,n,a){var r=g(e)?e:Kn(e),s=g(t)?t:Kn(t);return!!(this.isValid()&&r.isValid()&&s.isValid())&&(a=a||"()",("("===a[0]?this.isAfter(r,n):!this.isBefore(r,n))&&(")"===a[1]?this.isBefore(s,n):!this.isAfter(s,n)))}function Ia(e,t){var n,a=g(e)?e:Kn(e);return!(!this.isValid()||!a.isValid())&&(t=Z(t)||"millisecond","millisecond"===t?this.valueOf()===a.valueOf():(n=a.valueOf(),this.clone().startOf(t).valueOf()<=n&&n<=this.clone().endOf(t).valueOf()))}function Na(e,t){return this.isSame(e,t)||this.isAfter(e,t)}function Ja(e,t){return this.isSame(e,t)||this.isBefore(e,t)}function Ra(e,t,n){var a,r,s;if(!this.isValid())return NaN;if(a=ma(e,this),!a.isValid())return NaN;switch(r=6e4*(a.utcOffset()-this.utcOffset()),t=Z(t),t){case"year":s=Ua(this,a)/12;break;case"month":s=Ua(this,a);break;case"quarter":s=Ua(this,a)/3;break;case"second":s=(this-a)/1e3;break;case"minute":s=(this-a)/6e4;break;case"hour":s=(this-a)/36e5;break;case"day":s=(this-a-r)/864e5;break;case"week":s=(this-a-r)/6048e5;break;default:s=this-a}return n?s:T(s)}function Ua(e,t){var n,a,r=12*(t.year()-e.year())+(t.month()-e.month()),s=e.clone().add(r,"months");return t-s<0?(n=e.clone().add(r-1,"months"),a=(t-s)/(s-n)):(n=e.clone().add(r+1,"months"),a=(t-s)/(n-s)),-(r+a)||0}function Ga(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function Va(e){if(!this.isValid())return null;var t=!0!==e,n=t?this.clone().utc():this;return n.year()<0||n.year()>9999?ue(n,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):P(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",ue(n,"Z")):ue(n,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function Ba(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e="moment",t="";this.isLocal()||(e=0===this.utcOffset()?"moment.utc":"moment.parseZone",t="Z");var n="["+e+'("]',a=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",r="-MM-DD[T]HH:mm:ss.SSS",s=t+'[")]';return this.format(n+a+r+s)}function Ka(e){e||(e=this.isUtc()?r.defaultFormatUtc:r.defaultFormat);var t=ue(this,e);return this.localeData().postformat(t)}function qa(e,t){return this.isValid()&&(g(e)&&e.isValid()||Kn(e).isValid())?ba({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function Za(e){return this.from(Kn(),e)}function Qa(e,t){return this.isValid()&&(g(e)&&e.isValid()||Kn(e).isValid())?ba({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function Xa(e){return this.to(Kn(),e)}function er(e){var t;return void 0===e?this._locale._abbr:(t=yn(e),null!=t&&(this._locale=t),this)}r.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",r.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var tr=H("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));function nr(){return this._locale}var ar=1e3,rr=60*ar,sr=60*rr,ir=3506328*sr;function or(e,t){return(e%t+t)%t}function dr(e,t,n){return e<100&&e>=0?new Date(e+400,t,n)-ir:new Date(e,t,n).valueOf()}function _r(e,t,n){return e<100&&e>=0?Date.UTC(e+400,t,n)-ir:Date.UTC(e,t,n)}function ur(e){var t;if(e=Z(e),void 0===e||"millisecond"===e||!this.isValid())return this;var n=this._isUTC?_r:dr;switch(e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=or(t+(this._isUTC?0:this.utcOffset()*rr),sr);break;case"minute":t=this._d.valueOf(),t-=or(t,rr);break;case"second":t=this._d.valueOf(),t-=or(t,ar);break}return this._d.setTime(t),r.updateOffset(this,!0),this}function lr(e){var t;if(e=Z(e),void 0===e||"millisecond"===e||!this.isValid())return this;var n=this._isUTC?_r:dr;switch(e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=sr-or(t+(this._isUTC?0:this.utcOffset()*rr),sr)-1;break;case"minute":t=this._d.valueOf(),t+=rr-or(t,rr)-1;break;case"second":t=this._d.valueOf(),t+=ar-or(t,ar)-1;break}return this._d.setTime(t),r.updateOffset(this,!0),this}function mr(){return this._d.valueOf()-6e4*(this._offset||0)}function cr(){return Math.floor(this.valueOf()/1e3)}function hr(){return new Date(this.valueOf())}function fr(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]}function Mr(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}}function pr(){return this.isValid()?this.toISOString():null}function yr(){return y(this)}function Lr(){return h({},p(this))}function Yr(){return p(this).overflow}function vr(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function kr(e,t){oe(0,[e,e.length],0,t)}function Dr(e){return br.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function gr(e){return br.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)}function Tr(){return Tt(this.year(),1,4)}function wr(){var e=this.localeData()._week;return Tt(this.year(),e.dow,e.doy)}function br(e,t,n,a,r){var s;return null==e?gt(this,a,r).year:(s=Tt(e,a,r),t>s&&(t=s),Sr.call(this,e,t,n,a,r))}function Sr(e,t,n,a,r){var s=Dt(e,t,n,a,r),i=vt(s.year,0,s.dayOfYear);return this.year(i.getUTCFullYear()),this.month(i.getUTCMonth()),this.date(i.getUTCDate()),this}function Hr(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)}oe(0,["gg",2],0,(function(){return this.weekYear()%100})),oe(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),kr("gggg","weekYear"),kr("ggggg","weekYear"),kr("GGGG","isoWeekYear"),kr("GGGGG","isoWeekYear"),q("weekYear","gg"),q("isoWeekYear","GG"),ee("weekYear",1),ee("isoWeekYear",1),je("G",ge),je("g",ge),je("GG",pe,ce),je("gg",pe,ce),je("GGGG",ve,fe),je("gggg",ve,fe),je("GGGGG",ke,Me),je("ggggg",ke,Me),Ee(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,n,a){t[a.substr(0,2)]=w(e)})),Ee(["gg","GG"],(function(e,t,n,a){t[a]=r.parseTwoDigitYear(e)})),oe("Q",0,"Qo","quarter"),q("quarter","Q"),ee("quarter",7),je("Q",me),Ae("Q",(function(e,t){t[ze]=3*(w(e)-1)})),oe("D",["DD",2],"Do","date"),q("date","D"),ee("date",9),je("D",pe),je("DD",pe,ce),je("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),Ae(["D","DD"],$e),Ae("Do",(function(e,t){t[$e]=w(e.match(pe)[0])}));var jr=Qe("Date",!0);function xr(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")}oe("DDD",["DDDD",3],"DDDo","dayOfYear"),q("dayOfYear","DDD"),ee("dayOfYear",4),je("DDD",Ye),je("DDDD",he),Ae(["DDD","DDDD"],(function(e,t,n){n._dayOfYear=w(e)})),oe("m",["mm",2],0,"minute"),q("minute","m"),ee("minute",14),je("m",pe),je("mm",pe,ce),Ae(["m","mm"],Ne);var Or=Qe("Minutes",!1);oe("s",["ss",2],0,"second"),q("second","s"),ee("second",15),je("s",pe),je("ss",pe,ce),Ae(["s","ss"],Je);var Pr,Wr=Qe("Seconds",!1);for(oe("S",0,0,(function(){return~~(this.millisecond()/100)})),oe(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),oe(0,["SSS",3],0,"millisecond"),oe(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),oe(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),oe(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),oe(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),oe(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),oe(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),q("millisecond","ms"),ee("millisecond",16),je("S",Ye,me),je("SS",Ye,ce),je("SSS",Ye,he),Pr="SSSS";Pr.length<=9;Pr+="S")je(Pr,De);function Ar(e,t){t[Re]=w(1e3*("0."+e))}for(Pr="S";Pr.length<=9;Pr+="S")Ae(Pr,Ar);var Er=Qe("Milliseconds",!1);function Fr(){return this._isUTC?"UTC":""}function Cr(){return this._isUTC?"Coordinated Universal Time":""}oe("z",0,0,"zoneAbbr"),oe("zz",0,0,"zoneName");var zr=D.prototype;function $r(e){return Kn(1e3*e)}function Ir(){return Kn.apply(null,arguments).parseZone()}function Nr(e){return e}zr.add=Pa,zr.calendar=Ea,zr.clone=Fa,zr.diff=Ra,zr.endOf=lr,zr.format=Ka,zr.from=qa,zr.fromNow=Za,zr.to=Qa,zr.toNow=Xa,zr.get=tt,zr.invalidAt=Yr,zr.isAfter=Ca,zr.isBefore=za,zr.isBetween=$a,zr.isSame=Ia,zr.isSameOrAfter=Na,zr.isSameOrBefore=Ja,zr.isValid=yr,zr.lang=tr,zr.locale=er,zr.localeData=nr,zr.max=Zn,zr.min=qn,zr.parsingFlags=Lr,zr.set=nt,zr.startOf=ur,zr.subtract=Wa,zr.toArray=fr,zr.toObject=Mr,zr.toDate=hr,zr.toISOString=Va,zr.inspect=Ba,zr.toJSON=pr,zr.toString=Ga,zr.unix=cr,zr.valueOf=mr,zr.creationData=vr,zr.year=qe,zr.isLeapYear=Ze,zr.weekYear=Dr,zr.isoWeekYear=gr,zr.quarter=zr.quarters=Hr,zr.month=ct,zr.daysInMonth=ht,zr.week=zr.weeks=jt,zr.isoWeek=zr.isoWeeks=xt,zr.weeksInYear=wr,zr.isoWeeksInYear=Tr,zr.date=jr,zr.day=zr.days=Jt,zr.weekday=Rt,zr.isoWeekday=Ut,zr.dayOfYear=xr,zr.hour=zr.hours=dn,zr.minute=zr.minutes=Or,zr.second=zr.seconds=Wr,zr.millisecond=zr.milliseconds=Er,zr.utcOffset=ha,zr.utc=Ma,zr.local=pa,zr.parseZone=ya,zr.hasAlignedHourOffset=La,zr.isDST=Ya,zr.isLocal=ka,zr.isUtcOffset=Da,zr.isUtc=ga,zr.isUTC=ga,zr.zoneAbbr=Fr,zr.zoneName=Cr,zr.dates=H("dates accessor is deprecated. Use date instead.",jr),zr.months=H("months accessor is deprecated. Use month instead",ct),zr.years=H("years accessor is deprecated. Use year instead",qe),zr.zone=H("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",fa),zr.isDSTShifted=H("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",va);var Jr=E.prototype;function Rr(e,t,n,a){var r=yn(),s=f().set(a,t);return r[n](s,e)}function Ur(e,t,n){if(u(e)&&(t=e,e=void 0),e=e||"",null!=t)return Rr(e,t,n,"month");var a,r=[];for(a=0;a<12;a++)r[a]=Rr(e,a,n,"month");return r}function Gr(e,t,n,a){"boolean"===typeof e?(u(t)&&(n=t,t=void 0),t=t||""):(t=e,n=t,e=!1,u(t)&&(n=t,t=void 0),t=t||"");var r,s=yn(),i=e?s._week.dow:0;if(null!=n)return Rr(t,(n+i)%7,a,"day");var o=[];for(r=0;r<7;r++)o[r]=Rr(t,(r+i)%7,a,"day");return o}function Vr(e,t){return Ur(e,t,"months")}function Br(e,t){return Ur(e,t,"monthsShort")}function Kr(e,t,n){return Gr(e,t,n,"weekdays")}function qr(e,t,n){return Gr(e,t,n,"weekdaysShort")}function Zr(e,t,n){return Gr(e,t,n,"weekdaysMin")}Jr.calendar=C,Jr.longDateFormat=$,Jr.invalidDate=N,Jr.ordinal=U,Jr.preparse=Nr,Jr.postformat=Nr,Jr.relativeTime=V,Jr.pastFuture=B,Jr.set=W,Jr.months=ot,Jr.monthsShort=_t,Jr.monthsParse=lt,Jr.monthsRegex=yt,Jr.monthsShortRegex=Mt,Jr.week=wt,Jr.firstDayOfYear=Ht,Jr.firstDayOfWeek=St,Jr.weekdays=Et,Jr.weekdaysMin=$t,Jr.weekdaysShort=Ct,Jr.weekdaysParse=Nt,Jr.weekdaysRegex=Vt,Jr.weekdaysShortRegex=Kt,Jr.weekdaysMinRegex=Zt,Jr.isPM=an,Jr.meridiem=sn,fn("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,n=1===w(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}}),r.lang=H("moment.lang is deprecated. Use moment.locale instead.",fn),r.langData=H("moment.langData is deprecated. Use moment.localeData instead.",yn);var Qr=Math.abs;function Xr(){var e=this._data;return this._milliseconds=Qr(this._milliseconds),this._days=Qr(this._days),this._months=Qr(this._months),e.milliseconds=Qr(e.milliseconds),e.seconds=Qr(e.seconds),e.minutes=Qr(e.minutes),e.hours=Qr(e.hours),e.months=Qr(e.months),e.years=Qr(e.years),this}function es(e,t,n,a){var r=ba(t,n);return e._milliseconds+=a*r._milliseconds,e._days+=a*r._days,e._months+=a*r._months,e._bubble()}function ts(e,t){return es(this,e,t,1)}function ns(e,t){return es(this,e,t,-1)}function as(e){return e<0?Math.floor(e):Math.ceil(e)}function rs(){var e,t,n,a,r,s=this._milliseconds,i=this._days,o=this._months,d=this._data;return s>=0&&i>=0&&o>=0||s<=0&&i<=0&&o<=0||(s+=864e5*as(is(o)+i),i=0,o=0),d.milliseconds=s%1e3,e=T(s/1e3),d.seconds=e%60,t=T(e/60),d.minutes=t%60,n=T(t/60),d.hours=n%24,i+=T(n/24),r=T(ss(i)),o+=r,i-=as(is(r)),a=T(o/12),o%=12,d.days=i,d.months=o,d.years=a,this}function ss(e){return 4800*e/146097}function is(e){return 146097*e/4800}function os(e){if(!this.isValid())return NaN;var t,n,a=this._milliseconds;if(e=Z(e),"month"===e||"quarter"===e||"year"===e)switch(t=this._days+a/864e5,n=this._months+ss(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(is(this._months)),e){case"week":return t/7+a/6048e5;case"day":return t+a/864e5;case"hour":return 24*t+a/36e5;case"minute":return 1440*t+a/6e4;case"second":return 86400*t+a/1e3;case"millisecond":return Math.floor(864e5*t)+a;default:throw new Error("Unknown unit "+e)}}function ds(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*w(this._months/12):NaN}function _s(e){return function(){return this.as(e)}}var us=_s("ms"),ls=_s("s"),ms=_s("m"),cs=_s("h"),hs=_s("d"),fs=_s("w"),Ms=_s("M"),ps=_s("Q"),ys=_s("y");function Ls(){return ba(this)}function Ys(e){return e=Z(e),this.isValid()?this[e+"s"]():NaN}function vs(e){return function(){return this.isValid()?this._data[e]:NaN}}var ks=vs("milliseconds"),Ds=vs("seconds"),gs=vs("minutes"),Ts=vs("hours"),ws=vs("days"),bs=vs("months"),Ss=vs("years");function Hs(){return T(this.days()/7)}var js=Math.round,xs={ss:44,s:45,m:45,h:22,d:26,M:11};function Os(e,t,n,a,r){return r.relativeTime(t||1,!!n,e,a)}function Ps(e,t,n){var a=ba(e).abs(),r=js(a.as("s")),s=js(a.as("m")),i=js(a.as("h")),o=js(a.as("d")),d=js(a.as("M")),_=js(a.as("y")),u=r<=xs.ss&&["s",r]||r<xs.s&&["ss",r]||s<=1&&["m"]||s<xs.m&&["mm",s]||i<=1&&["h"]||i<xs.h&&["hh",i]||o<=1&&["d"]||o<xs.d&&["dd",o]||d<=1&&["M"]||d<xs.M&&["MM",d]||_<=1&&["y"]||["yy",_];return u[2]=t,u[3]=+e>0,u[4]=n,Os.apply(null,u)}function Ws(e){return void 0===e?js:"function"===typeof e&&(js=e,!0)}function As(e,t){return void 0!==xs[e]&&(void 0===t?xs[e]:(xs[e]=t,"s"===e&&(xs.ss=t-1),!0))}function Es(e){if(!this.isValid())return this.localeData().invalidDate();var t=this.localeData(),n=Ps(this,!e,t);return e&&(n=t.pastFuture(+this,n)),t.postformat(n)}var Fs=Math.abs;function Cs(e){return(e>0)-(e<0)||+e}function zs(){if(!this.isValid())return this.localeData().invalidDate();var e,t,n,a=Fs(this._milliseconds)/1e3,r=Fs(this._days),s=Fs(this._months);e=T(a/60),t=T(e/60),a%=60,e%=60,n=T(s/12),s%=12;var i=n,o=s,d=r,_=t,u=e,l=a?a.toFixed(3).replace(/\.?0+$/,""):"",m=this.asSeconds();if(!m)return"P0D";var c=m<0?"-":"",h=Cs(this._months)!==Cs(m)?"-":"",f=Cs(this._days)!==Cs(m)?"-":"",M=Cs(this._milliseconds)!==Cs(m)?"-":"";return c+"P"+(i?h+i+"Y":"")+(o?h+o+"M":"")+(d?f+d+"D":"")+(_||u||l?"T":"")+(_?M+_+"H":"")+(u?M+u+"M":"")+(l?M+l+"S":"")}var $s=ia.prototype;return $s.isValid=ra,$s.abs=Xr,$s.add=ts,$s.subtract=ns,$s.as=os,$s.asMilliseconds=us,$s.asSeconds=ls,$s.asMinutes=ms,$s.asHours=cs,$s.asDays=hs,$s.asWeeks=fs,$s.asMonths=Ms,$s.asQuarters=ps,$s.asYears=ys,$s.valueOf=ds,$s._bubble=rs,$s.clone=Ls,$s.get=Ys,$s.milliseconds=ks,$s.seconds=Ds,$s.minutes=gs,$s.hours=Ts,$s.days=ws,$s.weeks=Hs,$s.months=bs,$s.years=Ss,$s.humanize=Es,$s.toISOString=zs,$s.toString=zs,$s.toJSON=zs,$s.locale=er,$s.localeData=nr,$s.toIsoString=H("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",zs),$s.lang=tr,oe("X",0,0,"unix"),oe("x",0,0,"valueOf"),je("x",ge),je("X",be),Ae("X",(function(e,t,n){n._d=new Date(1e3*parseFloat(e,10))})),Ae("x",(function(e,t,n){n._d=new Date(w(e))})),r.version="2.24.0",s(Kn),r.fn=zr,r.min=Xn,r.max=ea,r.now=ta,r.utc=f,r.unix=$r,r.months=Vr,r.isDate=l,r.locale=fn,r.invalid=L,r.duration=ba,r.isMoment=g,r.weekdays=Kr,r.parseZone=Ir,r.localeData=yn,r.isDuration=oa,r.monthsShort=Br,r.weekdaysMin=Zr,r.defineLocale=Mn,r.updateLocale=pn,r.locales=Ln,r.weekdaysShort=qr,r.normalizeUnits=Z,r.relativeTimeRounding=Ws,r.relativeTimeThreshold=As,r.calendarFormat=Aa,r.prototype=zr,r.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},r}))},5096:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"},n=e.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pts_Sal_Çar_Per_Cum_Cts".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[gelecek] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",ss:"%d saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinal:function(e,n){switch(n){case"d":case"D":case"Do":case"DD":return e;default:if(0===e)return e+"'ıncı";var a=e%10,r=e%100-a,s=e>=100?100:null;return e+(t[a]||t[r]||t[s])}},week:{dow:1,doy:7}});return n}))},5177:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("af",{months:"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des".split("_"),weekdays:"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag".split("_"),weekdaysShort:"Son_Maa_Din_Woe_Don_Vry_Sat".split("_"),weekdaysMin:"So_Ma_Di_Wo_Do_Vr_Sa".split("_"),meridiemParse:/vm|nm/i,isPM:function(e){return/^nm$/i.test(e)},meridiem:function(e,t,n){return e<12?n?"vm":"VM":n?"nm":"NM"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Vandag om] LT",nextDay:"[Môre om] LT",nextWeek:"dddd [om] LT",lastDay:"[Gister om] LT",lastWeek:"[Laas] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oor %s",past:"%s gelede",s:"'n paar sekondes",ss:"%d sekondes",m:"'n minuut",mm:"%d minute",h:"'n uur",hh:"%d ure",d:"'n dag",dd:"%d dae",M:"'n maand",MM:"%d maande",y:"'n jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}});return t}))},5471:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>Qa});
/*!
 * Vue.js v2.7.16
 * (c) 2014-2023 Evan You
 * Released under the MIT License.
 */
var a=Object.freeze({}),r=Array.isArray;function s(e){return void 0===e||null===e}function i(e){return void 0!==e&&null!==e}function o(e){return!0===e}function d(e){return!1===e}function _(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function u(e){return"function"===typeof e}function l(e){return null!==e&&"object"===typeof e}var m=Object.prototype.toString;function c(e){return"[object Object]"===m.call(e)}function h(e){return"[object RegExp]"===m.call(e)}function f(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function M(e){return i(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function p(e){return null==e?"":Array.isArray(e)||c(e)&&e.toString===m?JSON.stringify(e,y,2):String(e)}function y(e,t){return t&&t.__v_isRef?t.value:t}function L(e){var t=parseFloat(e);return isNaN(t)?e:t}function Y(e,t){for(var n=Object.create(null),a=e.split(","),r=0;r<a.length;r++)n[a[r]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}Y("slot,component",!0);var v=Y("key,ref,slot,slot-scope,is");function k(e,t){var n=e.length;if(n){if(t===e[n-1])return void(e.length=n-1);var a=e.indexOf(t);if(a>-1)return e.splice(a,1)}}var D=Object.prototype.hasOwnProperty;function g(e,t){return D.call(e,t)}function T(e){var t=Object.create(null);return function(n){var a=t[n];return a||(t[n]=e(n))}}var w=/-(\w)/g,b=T((function(e){return e.replace(w,(function(e,t){return t?t.toUpperCase():""}))})),S=T((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),H=/\B([A-Z])/g,j=T((function(e){return e.replace(H,"-$1").toLowerCase()}));function x(e,t){function n(n){var a=arguments.length;return a?a>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n}function O(e,t){return e.bind(t)}var P=Function.prototype.bind?O:x;function W(e,t){t=t||0;var n=e.length-t,a=new Array(n);while(n--)a[n]=e[n+t];return a}function A(e,t){for(var n in t)e[n]=t[n];return e}function E(e){for(var t={},n=0;n<e.length;n++)e[n]&&A(t,e[n]);return t}function F(e,t,n){}var C=function(e,t,n){return!1},z=function(e){return e};function $(e,t){if(e===t)return!0;var n=l(e),a=l(t);if(!n||!a)return!n&&!a&&String(e)===String(t);try{var r=Array.isArray(e),s=Array.isArray(t);if(r&&s)return e.length===t.length&&e.every((function(e,n){return $(e,t[n])}));if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(r||s)return!1;var i=Object.keys(e),o=Object.keys(t);return i.length===o.length&&i.every((function(n){return $(e[n],t[n])}))}catch(d){return!1}}function I(e,t){for(var n=0;n<e.length;n++)if($(e[n],t))return n;return-1}function N(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}function J(e,t){return e===t?0===e&&1/e!==1/t:e===e||t===t}var R="data-server-rendered",U=["component","directive","filter"],G=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch","renderTracked","renderTriggered"],V={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:C,isReservedAttr:C,isUnknownElement:C,getTagNamespace:F,parsePlatformTagName:z,mustUseProp:C,async:!0,_lifecycleHooks:G},B=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function K(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function q(e,t,n,a){Object.defineProperty(e,t,{value:n,enumerable:!!a,writable:!0,configurable:!0})}var Z=new RegExp("[^".concat(B.source,".$_\\d]"));function Q(e){if(!Z.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}var X="__proto__"in{},ee="undefined"!==typeof window,te=ee&&window.navigator.userAgent.toLowerCase(),ne=te&&/msie|trident/.test(te),ae=te&&te.indexOf("msie 9.0")>0,re=te&&te.indexOf("edge/")>0;te&&te.indexOf("android");var se=te&&/iphone|ipad|ipod|ios/.test(te);te&&/chrome\/\d+/.test(te),te&&/phantomjs/.test(te);var ie,oe=te&&te.match(/firefox\/(\d+)/),de={}.watch,_e=!1;if(ee)try{var ue={};Object.defineProperty(ue,"passive",{get:function(){_e=!0}}),window.addEventListener("test-passive",null,ue)}catch(Qi){}var le=function(){return void 0===ie&&(ie=!ee&&"undefined"!==typeof n.g&&(n.g["process"]&&"server"===n.g["process"].env.VUE_ENV)),ie},me=ee&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function ce(e){return"function"===typeof e&&/native code/.test(e.toString())}var he,fe="undefined"!==typeof Symbol&&ce(Symbol)&&"undefined"!==typeof Reflect&&ce(Reflect.ownKeys);he="undefined"!==typeof Set&&ce(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var Me=null;function pe(e){void 0===e&&(e=null),e||Me&&Me._scope.off(),Me=e,e&&e._scope.on()}var ye=function(){function e(e,t,n,a,r,s,i,o){this.tag=e,this.data=t,this.children=n,this.text=a,this.elm=r,this.ns=void 0,this.context=s,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=i,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=o,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1}return Object.defineProperty(e.prototype,"child",{get:function(){return this.componentInstance},enumerable:!1,configurable:!0}),e}(),Le=function(e){void 0===e&&(e="");var t=new ye;return t.text=e,t.isComment=!0,t};function Ye(e){return new ye(void 0,void 0,void 0,String(e))}function ve(e){var t=new ye(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}"function"===typeof SuppressedError&&SuppressedError;var ke=0,De=[],ge=function(){for(var e=0;e<De.length;e++){var t=De[e];t.subs=t.subs.filter((function(e){return e})),t._pending=!1}De.length=0},Te=function(){function e(){this._pending=!1,this.id=ke++,this.subs=[]}return e.prototype.addSub=function(e){this.subs.push(e)},e.prototype.removeSub=function(e){this.subs[this.subs.indexOf(e)]=null,this._pending||(this._pending=!0,De.push(this))},e.prototype.depend=function(t){e.target&&e.target.addDep(this)},e.prototype.notify=function(e){var t=this.subs.filter((function(e){return e}));for(var n=0,a=t.length;n<a;n++){var r=t[n];0,r.update()}},e}();Te.target=null;var we=[];function be(e){we.push(e),Te.target=e}function Se(){we.pop(),Te.target=we[we.length-1]}var He=Array.prototype,je=Object.create(He),xe=["push","pop","shift","unshift","splice","sort","reverse"];xe.forEach((function(e){var t=He[e];q(je,e,(function(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];var r,s=t.apply(this,n),i=this.__ob__;switch(e){case"push":case"unshift":r=n;break;case"splice":r=n.slice(2);break}return r&&i.observeArray(r),i.dep.notify(),s}))}));var Oe=Object.getOwnPropertyNames(je),Pe={},We=!0;function Ae(e){We=e}var Ee={notify:F,depend:F,addSub:F,removeSub:F},Fe=function(){function e(e,t,n){if(void 0===t&&(t=!1),void 0===n&&(n=!1),this.value=e,this.shallow=t,this.mock=n,this.dep=n?Ee:new Te,this.vmCount=0,q(e,"__ob__",this),r(e)){if(!n)if(X)e.__proto__=je;else for(var a=0,s=Oe.length;a<s;a++){var i=Oe[a];q(e,i,je[i])}t||this.observeArray(e)}else{var o=Object.keys(e);for(a=0;a<o.length;a++){i=o[a];ze(e,i,Pe,void 0,t,n)}}}return e.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)Ce(e[t],!1,this.mock)},e}();function Ce(e,t,n){return e&&g(e,"__ob__")&&e.__ob__ instanceof Fe?e.__ob__:!We||!n&&le()||!r(e)&&!c(e)||!Object.isExtensible(e)||e.__v_skip||Ge(e)||e instanceof ye?void 0:new Fe(e,t,n)}function ze(e,t,n,a,s,i,o){void 0===o&&(o=!1);var d=new Te,_=Object.getOwnPropertyDescriptor(e,t);if(!_||!1!==_.configurable){var u=_&&_.get,l=_&&_.set;u&&!l||n!==Pe&&2!==arguments.length||(n=e[t]);var m=s?n&&n.__ob__:Ce(n,!1,i);return Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=u?u.call(e):n;return Te.target&&(d.depend(),m&&(m.dep.depend(),r(t)&&Ne(t))),Ge(t)&&!s?t.value:t},set:function(t){var a=u?u.call(e):n;if(J(a,t)){if(l)l.call(e,t);else{if(u)return;if(!s&&Ge(a)&&!Ge(t))return void(a.value=t);n=t}m=s?t&&t.__ob__:Ce(t,!1,i),d.notify()}}}),d}}function $e(e,t,n){if(!Ue(e)){var a=e.__ob__;return r(e)&&f(t)?(e.length=Math.max(e.length,t),e.splice(t,1,n),a&&!a.shallow&&a.mock&&Ce(n,!1,!0),n):t in e&&!(t in Object.prototype)?(e[t]=n,n):e._isVue||a&&a.vmCount?n:a?(ze(a.value,t,n,void 0,a.shallow,a.mock),a.dep.notify(),n):(e[t]=n,n)}}function Ie(e,t){if(r(e)&&f(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount||Ue(e)||g(e,t)&&(delete e[t],n&&n.dep.notify())}}function Ne(e){for(var t=void 0,n=0,a=e.length;n<a;n++)t=e[n],t&&t.__ob__&&t.__ob__.dep.depend(),r(t)&&Ne(t)}function Je(e){return Re(e,!0),q(e,"__v_isShallow",!0),e}function Re(e,t){if(!Ue(e)){Ce(e,t,le());0}}function Ue(e){return!(!e||!e.__v_isReadonly)}function Ge(e){return!(!e||!0!==e.__v_isRef)}function Ve(e,t,n){Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:function(){var e=t[n];if(Ge(e))return e.value;var a=e&&e.__ob__;return a&&a.dep.depend(),e},set:function(e){var a=t[n];Ge(a)&&!Ge(e)?a.value=e:t[n]=e}})}var Be="watcher";"".concat(Be," callback"),"".concat(Be," getter"),"".concat(Be," cleanup");var Ke;var qe=function(){function e(e){void 0===e&&(e=!1),this.detached=e,this.active=!0,this.effects=[],this.cleanups=[],this.parent=Ke,!e&&Ke&&(this.index=(Ke.scopes||(Ke.scopes=[])).push(this)-1)}return e.prototype.run=function(e){if(this.active){var t=Ke;try{return Ke=this,e()}finally{Ke=t}}else 0},e.prototype.on=function(){Ke=this},e.prototype.off=function(){Ke=this.parent},e.prototype.stop=function(e){if(this.active){var t=void 0,n=void 0;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].teardown();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var a=this.parent.scopes.pop();a&&a!==this&&(this.parent.scopes[this.index]=a,a.index=this.index)}this.parent=void 0,this.active=!1}},e}();function Ze(e,t){void 0===t&&(t=Ke),t&&t.active&&t.effects.push(e)}function Qe(){return Ke}function Xe(e){var t=e._provided,n=e.$parent&&e.$parent._provided;return n===t?e._provided=Object.create(n):t}var et=T((function(e){var t="&"===e.charAt(0);e=t?e.slice(1):e;var n="~"===e.charAt(0);e=n?e.slice(1):e;var a="!"===e.charAt(0);return e=a?e.slice(1):e,{name:e,once:n,capture:a,passive:t}}));function tt(e,t){function n(){var e=n.fns;if(!r(e))return Zt(e,null,arguments,t,"v-on handler");for(var a=e.slice(),s=0;s<a.length;s++)Zt(a[s],null,arguments,t,"v-on handler")}return n.fns=e,n}function nt(e,t,n,a,r,i){var d,_,u,l;for(d in e)_=e[d],u=t[d],l=et(d),s(_)||(s(u)?(s(_.fns)&&(_=e[d]=tt(_,i)),o(l.once)&&(_=e[d]=r(l.name,_,l.capture)),n(l.name,_,l.capture,l.passive,l.params)):_!==u&&(u.fns=_,e[d]=u));for(d in t)s(e[d])&&(l=et(d),a(l.name,t[d],l.capture))}function at(e,t,n){var a;e instanceof ye&&(e=e.data.hook||(e.data.hook={}));var r=e[t];function d(){n.apply(this,arguments),k(a.fns,d)}s(r)?a=tt([d]):i(r.fns)&&o(r.merged)?(a=r,a.fns.push(d)):a=tt([r,d]),a.merged=!0,e[t]=a}function rt(e,t,n){var a=t.options.props;if(!s(a)){var r={},o=e.attrs,d=e.props;if(i(o)||i(d))for(var _ in a){var u=j(_);st(r,d,_,u,!0)||st(r,o,_,u,!1)}return r}}function st(e,t,n,a,r){if(i(t)){if(g(t,n))return e[n]=t[n],r||delete t[n],!0;if(g(t,a))return e[n]=t[a],r||delete t[a],!0}return!1}function it(e){for(var t=0;t<e.length;t++)if(r(e[t]))return Array.prototype.concat.apply([],e);return e}function ot(e){return _(e)?[Ye(e)]:r(e)?_t(e):void 0}function dt(e){return i(e)&&i(e.text)&&d(e.isComment)}function _t(e,t){var n,a,d,u,l=[];for(n=0;n<e.length;n++)a=e[n],s(a)||"boolean"===typeof a||(d=l.length-1,u=l[d],r(a)?a.length>0&&(a=_t(a,"".concat(t||"","_").concat(n)),dt(a[0])&&dt(u)&&(l[d]=Ye(u.text+a[0].text),a.shift()),l.push.apply(l,a)):_(a)?dt(u)?l[d]=Ye(u.text+a):""!==a&&l.push(Ye(a)):dt(a)&&dt(u)?l[d]=Ye(u.text+a.text):(o(e._isVList)&&i(a.tag)&&s(a.key)&&i(t)&&(a.key="__vlist".concat(t,"_").concat(n,"__")),l.push(a)));return l}function ut(e,t){var n,a,s,o,d=null;if(r(e)||"string"===typeof e)for(d=new Array(e.length),n=0,a=e.length;n<a;n++)d[n]=t(e[n],n);else if("number"===typeof e)for(d=new Array(e),n=0;n<e;n++)d[n]=t(n+1,n);else if(l(e))if(fe&&e[Symbol.iterator]){d=[];var _=e[Symbol.iterator](),u=_.next();while(!u.done)d.push(t(u.value,d.length)),u=_.next()}else for(s=Object.keys(e),d=new Array(s.length),n=0,a=s.length;n<a;n++)o=s[n],d[n]=t(e[o],o,n);return i(d)||(d=[]),d._isVList=!0,d}function lt(e,t,n,a){var r,s=this.$scopedSlots[e];s?(n=n||{},a&&(n=A(A({},a),n)),r=s(n)||(u(t)?t():t)):r=this.$slots[e]||(u(t)?t():t);var i=n&&n.slot;return i?this.$createElement("template",{slot:i},r):r}function mt(e){return Ta(this.$options,"filters",e,!0)||z}function ct(e,t){return r(e)?-1===e.indexOf(t):e!==t}function ht(e,t,n,a,r){var s=V.keyCodes[t]||n;return r&&a&&!V.keyCodes[t]?ct(r,a):s?ct(s,e):a?j(a)!==t:void 0===e}function ft(e,t,n,a,s){if(n)if(l(n)){r(n)&&(n=E(n));var i=void 0,o=function(r){if("class"===r||"style"===r||v(r))i=e;else{var o=e.attrs&&e.attrs.type;i=a||V.mustUseProp(t,o,r)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var d=b(r),_=j(r);if(!(d in i)&&!(_ in i)&&(i[r]=n[r],s)){var u=e.on||(e.on={});u["update:".concat(r)]=function(e){n[r]=e}}};for(var d in n)o(d)}else;return e}function Mt(e,t){var n=this._staticTrees||(this._staticTrees=[]),a=n[e];return a&&!t||(a=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,this._c,this),yt(a,"__static__".concat(e),!1)),a}function pt(e,t,n){return yt(e,"__once__".concat(t).concat(n?"_".concat(n):""),!0),e}function yt(e,t,n){if(r(e))for(var a=0;a<e.length;a++)e[a]&&"string"!==typeof e[a]&&Lt(e[a],"".concat(t,"_").concat(a),n);else Lt(e,t,n)}function Lt(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function Yt(e,t){if(t)if(c(t)){var n=e.on=e.on?A({},e.on):{};for(var a in t){var r=n[a],s=t[a];n[a]=r?[].concat(r,s):s}}else;return e}function vt(e,t,n,a){t=t||{$stable:!n};for(var s=0;s<e.length;s++){var i=e[s];r(i)?vt(i,t,n):i&&(i.proxy&&(i.fn.proxy=!0),t[i.key]=i.fn)}return a&&(t.$key=a),t}function kt(e,t){for(var n=0;n<t.length;n+=2){var a=t[n];"string"===typeof a&&a&&(e[t[n]]=t[n+1])}return e}function Dt(e,t){return"string"===typeof e?t+e:e}function gt(e){e._o=pt,e._n=L,e._s=p,e._l=ut,e._t=lt,e._q=$,e._i=I,e._m=Mt,e._f=mt,e._k=ht,e._b=ft,e._v=Ye,e._e=Le,e._u=vt,e._g=Yt,e._d=kt,e._p=Dt}function Tt(e,t){if(!e||!e.length)return{};for(var n={},a=0,r=e.length;a<r;a++){var s=e[a],i=s.data;if(i&&i.attrs&&i.attrs.slot&&delete i.attrs.slot,s.context!==t&&s.fnContext!==t||!i||null==i.slot)(n.default||(n.default=[])).push(s);else{var o=i.slot,d=n[o]||(n[o]=[]);"template"===s.tag?d.push.apply(d,s.children||[]):d.push(s)}}for(var _ in n)n[_].every(wt)&&delete n[_];return n}function wt(e){return e.isComment&&!e.asyncFactory||" "===e.text}function bt(e){return e.isComment&&e.asyncFactory}function St(e,t,n,r){var s,i=Object.keys(n).length>0,o=t?!!t.$stable:!i,d=t&&t.$key;if(t){if(t._normalized)return t._normalized;if(o&&r&&r!==a&&d===r.$key&&!i&&!r.$hasNormal)return r;for(var _ in s={},t)t[_]&&"$"!==_[0]&&(s[_]=Ht(e,n,_,t[_]))}else s={};for(var u in n)u in s||(s[u]=jt(n,u));return t&&Object.isExtensible(t)&&(t._normalized=s),q(s,"$stable",o),q(s,"$key",d),q(s,"$hasNormal",i),s}function Ht(e,t,n,a){var s=function(){var t=Me;pe(e);var n=arguments.length?a.apply(null,arguments):a({});n=n&&"object"===typeof n&&!r(n)?[n]:ot(n);var s=n&&n[0];return pe(t),n&&(!s||1===n.length&&s.isComment&&!bt(s))?void 0:n};return a.proxy&&Object.defineProperty(t,n,{get:s,enumerable:!0,configurable:!0}),s}function jt(e,t){return function(){return e[t]}}function xt(e){var t=e.$options,n=t.setup;if(n){var a=e._setupContext=Ot(e);pe(e),be();var r=Zt(n,null,[e._props||Je({}),a],e,"setup");if(Se(),pe(),u(r))t.render=r;else if(l(r))if(e._setupState=r,r.__sfc){var s=e._setupProxy={};for(var i in r)"__sfc"!==i&&Ve(s,r,i)}else for(var i in r)K(i)||Ve(e,r,i);else 0}}function Ot(e){return{get attrs(){if(!e._attrsProxy){var t=e._attrsProxy={};q(t,"_v_attr_proxy",!0),Pt(t,e.$attrs,a,e,"$attrs")}return e._attrsProxy},get listeners(){if(!e._listenersProxy){var t=e._listenersProxy={};Pt(t,e.$listeners,a,e,"$listeners")}return e._listenersProxy},get slots(){return At(e)},emit:P(e.$emit,e),expose:function(t){t&&Object.keys(t).forEach((function(n){return Ve(e,t,n)}))}}}function Pt(e,t,n,a,r){var s=!1;for(var i in t)i in e?t[i]!==n[i]&&(s=!0):(s=!0,Wt(e,i,a,r));for(var i in e)i in t||(s=!0,delete e[i]);return s}function Wt(e,t,n,a){Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){return n[a][t]}})}function At(e){return e._slotsProxy||Et(e._slotsProxy={},e.$scopedSlots),e._slotsProxy}function Et(e,t){for(var n in t)e[n]=t[n];for(var n in e)n in t||delete e[n]}function Ft(e){e._vnode=null,e._staticTrees=null;var t=e.$options,n=e.$vnode=t._parentVnode,r=n&&n.context;e.$slots=Tt(t._renderChildren,r),e.$scopedSlots=n?St(e.$parent,n.data.scopedSlots,e.$slots):a,e._c=function(t,n,a,r){return Gt(e,t,n,a,r,!1)},e.$createElement=function(t,n,a,r){return Gt(e,t,n,a,r,!0)};var s=n&&n.data;ze(e,"$attrs",s&&s.attrs||a,null,!0),ze(e,"$listeners",t._parentListeners||a,null,!0)}var Ct=null;function zt(e){gt(e.prototype),e.prototype.$nextTick=function(e){return un(e,this)},e.prototype._render=function(){var e=this,t=e.$options,n=t.render,a=t._parentVnode;a&&e._isMounted&&(e.$scopedSlots=St(e.$parent,a.data.scopedSlots,e.$slots,e.$scopedSlots),e._slotsProxy&&Et(e._slotsProxy,e.$scopedSlots)),e.$vnode=a;var s,i=Me,o=Ct;try{pe(e),Ct=e,s=n.call(e._renderProxy,e.$createElement)}catch(Qi){qt(Qi,e,"render"),s=e._vnode}finally{Ct=o,pe(i)}return r(s)&&1===s.length&&(s=s[0]),s instanceof ye||(s=Le()),s.parent=a,s}}function $t(e,t){return(e.__esModule||fe&&"Module"===e[Symbol.toStringTag])&&(e=e.default),l(e)?t.extend(e):e}function It(e,t,n,a,r){var s=Le();return s.asyncFactory=e,s.asyncMeta={data:t,context:n,children:a,tag:r},s}function Nt(e,t){if(o(e.error)&&i(e.errorComp))return e.errorComp;if(i(e.resolved))return e.resolved;var n=Ct;if(n&&i(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n),o(e.loading)&&i(e.loadingComp))return e.loadingComp;if(n&&!i(e.owners)){var a=e.owners=[n],r=!0,d=null,_=null;n.$on("hook:destroyed",(function(){return k(a,n)}));var u=function(e){for(var t=0,n=a.length;t<n;t++)a[t].$forceUpdate();e&&(a.length=0,null!==d&&(clearTimeout(d),d=null),null!==_&&(clearTimeout(_),_=null))},m=N((function(n){e.resolved=$t(n,t),r?a.length=0:u(!0)})),c=N((function(t){i(e.errorComp)&&(e.error=!0,u(!0))})),h=e(m,c);return l(h)&&(M(h)?s(e.resolved)&&h.then(m,c):M(h.component)&&(h.component.then(m,c),i(h.error)&&(e.errorComp=$t(h.error,t)),i(h.loading)&&(e.loadingComp=$t(h.loading,t),0===h.delay?e.loading=!0:d=setTimeout((function(){d=null,s(e.resolved)&&s(e.error)&&(e.loading=!0,u(!1))}),h.delay||200)),i(h.timeout)&&(_=setTimeout((function(){_=null,s(e.resolved)&&c(null)}),h.timeout)))),r=!1,e.loading?e.loadingComp:e.resolved}}function Jt(e){if(r(e))for(var t=0;t<e.length;t++){var n=e[t];if(i(n)&&(i(n.componentOptions)||bt(n)))return n}}var Rt=1,Ut=2;function Gt(e,t,n,a,s,i){return(r(n)||_(n))&&(s=a,a=n,n=void 0),o(i)&&(s=Ut),Vt(e,t,n,a,s)}function Vt(e,t,n,a,s){if(i(n)&&i(n.__ob__))return Le();if(i(n)&&i(n.is)&&(t=n.is),!t)return Le();var o,d;if(r(a)&&u(a[0])&&(n=n||{},n.scopedSlots={default:a[0]},a.length=0),s===Ut?a=ot(a):s===Rt&&(a=it(a)),"string"===typeof t){var _=void 0;d=e.$vnode&&e.$vnode.ns||V.getTagNamespace(t),o=V.isReservedTag(t)?new ye(V.parsePlatformTagName(t),n,a,void 0,void 0,e):n&&n.pre||!i(_=Ta(e.$options,"components",t))?new ye(t,n,a,void 0,void 0,e):da(_,n,e,a,t)}else o=da(t,n,e,a);return r(o)?o:i(o)?(i(d)&&Bt(o,d),i(n)&&Kt(n),o):Le()}function Bt(e,t,n){if(e.ns=t,"foreignObject"===e.tag&&(t=void 0,n=!0),i(e.children))for(var a=0,r=e.children.length;a<r;a++){var d=e.children[a];i(d.tag)&&(s(d.ns)||o(n)&&"svg"!==d.tag)&&Bt(d,t,n)}}function Kt(e){l(e.style)&&fn(e.style),l(e.class)&&fn(e.class)}function qt(e,t,n){be();try{if(t){var a=t;while(a=a.$parent){var r=a.$options.errorCaptured;if(r)for(var s=0;s<r.length;s++)try{var i=!1===r[s].call(a,e,t,n);if(i)return}catch(Qi){Qt(Qi,a,"errorCaptured hook")}}}Qt(e,t,n)}finally{Se()}}function Zt(e,t,n,a,r){var s;try{s=n?e.apply(t,n):e.call(t),s&&!s._isVue&&M(s)&&!s._handled&&(s.catch((function(e){return qt(e,a,r+" (Promise/async)")})),s._handled=!0)}catch(Qi){qt(Qi,a,r)}return s}function Qt(e,t,n){if(V.errorHandler)try{return V.errorHandler.call(null,e,t,n)}catch(Qi){Qi!==e&&Xt(Qi,null,"config.errorHandler")}Xt(e,t,n)}function Xt(e,t,n){if(!ee||"undefined"===typeof console)throw e;console.error(e)}var en,tn=!1,nn=[],an=!1;function rn(){an=!1;var e=nn.slice(0);nn.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!==typeof Promise&&ce(Promise)){var sn=Promise.resolve();en=function(){sn.then(rn),se&&setTimeout(F)},tn=!0}else if(ne||"undefined"===typeof MutationObserver||!ce(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())en="undefined"!==typeof setImmediate&&ce(setImmediate)?function(){setImmediate(rn)}:function(){setTimeout(rn,0)};else{var on=1,dn=new MutationObserver(rn),_n=document.createTextNode(String(on));dn.observe(_n,{characterData:!0}),en=function(){on=(on+1)%2,_n.data=String(on)},tn=!0}function un(e,t){var n;if(nn.push((function(){if(e)try{e.call(t)}catch(Qi){qt(Qi,t,"nextTick")}else n&&n(t)})),an||(an=!0,en()),!e&&"undefined"!==typeof Promise)return new Promise((function(e){n=e}))}function ln(e){return function(t,n){if(void 0===n&&(n=Me),n)return mn(n,e,t)}}function mn(e,t,n){var a=e.$options;a[t]=pa(a[t],n)}ln("beforeMount"),ln("mounted"),ln("beforeUpdate"),ln("updated"),ln("beforeDestroy"),ln("destroyed"),ln("activated"),ln("deactivated"),ln("serverPrefetch"),ln("renderTracked"),ln("renderTriggered"),ln("errorCaptured");var cn="2.7.16";var hn=new he;function fn(e){return Mn(e,hn),hn.clear(),e}function Mn(e,t){var n,a,s=r(e);if(!(!s&&!l(e)||e.__v_skip||Object.isFrozen(e)||e instanceof ye)){if(e.__ob__){var i=e.__ob__.dep.id;if(t.has(i))return;t.add(i)}if(s){n=e.length;while(n--)Mn(e[n],t)}else if(Ge(e))Mn(e.value,t);else{a=Object.keys(e),n=a.length;while(n--)Mn(e[a[n]],t)}}}var pn,yn=0,Ln=function(){function e(e,t,n,a,r){Ze(this,Ke&&!Ke._vm?Ke:e?e._scope:void 0),(this.vm=e)&&r&&(e._watcher=this),a?(this.deep=!!a.deep,this.user=!!a.user,this.lazy=!!a.lazy,this.sync=!!a.sync,this.before=a.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++yn,this.active=!0,this.post=!1,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new he,this.newDepIds=new he,this.expression="",u(t)?this.getter=t:(this.getter=Q(t),this.getter||(this.getter=F)),this.value=this.lazy?void 0:this.get()}return e.prototype.get=function(){var e;be(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(Qi){if(!this.user)throw Qi;qt(Qi,t,'getter for watcher "'.concat(this.expression,'"'))}finally{this.deep&&fn(e),Se(),this.cleanupDeps()}return e},e.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},e.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},e.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():Zn(this)},e.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||l(e)||this.deep){var t=this.value;if(this.value=e,this.user){var n='callback for watcher "'.concat(this.expression,'"');Zt(this.cb,this.vm,[e,t],this.vm,n)}else this.cb.call(this.vm,e,t)}}},e.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},e.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},e.prototype.teardown=function(){if(this.vm&&!this.vm._isBeingDestroyed&&k(this.vm._scope.effects,this),this.active){var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1,this.onStop&&this.onStop()}},e}();function Yn(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&gn(e,t)}function vn(e,t){pn.$on(e,t)}function kn(e,t){pn.$off(e,t)}function Dn(e,t){var n=pn;return function a(){var r=t.apply(null,arguments);null!==r&&n.$off(e,a)}}function gn(e,t,n){pn=e,nt(t,n||{},vn,kn,Dn,e),pn=void 0}function Tn(e){var t=/^hook:/;e.prototype.$on=function(e,n){var a=this;if(r(e))for(var s=0,i=e.length;s<i;s++)a.$on(e[s],n);else(a._events[e]||(a._events[e]=[])).push(n),t.test(e)&&(a._hasHookEvent=!0);return a},e.prototype.$once=function(e,t){var n=this;function a(){n.$off(e,a),t.apply(n,arguments)}return a.fn=t,n.$on(e,a),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(r(e)){for(var a=0,s=e.length;a<s;a++)n.$off(e[a],t);return n}var i,o=n._events[e];if(!o)return n;if(!t)return n._events[e]=null,n;var d=o.length;while(d--)if(i=o[d],i===t||i.fn===t){o.splice(d,1);break}return n},e.prototype.$emit=function(e){var t=this,n=t._events[e];if(n){n=n.length>1?W(n):n;for(var a=W(arguments,1),r='event handler for "'.concat(e,'"'),s=0,i=n.length;s<i;s++)Zt(n[s],t,a,t,r)}return t}}var wn=null;function bn(e){var t=wn;return wn=e,function(){wn=t}}function Sn(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){while(n.$options.abstract&&n.$parent)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._provided=n?n._provided:Object.create(null),e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}function Hn(e){e.prototype._update=function(e,t){var n=this,a=n.$el,r=n._vnode,s=bn(n);n._vnode=e,n.$el=r?n.__patch__(r,e):n.__patch__(n.$el,e,t,!1),s(),a&&(a.__vue__=null),n.$el&&(n.$el.__vue__=n);var i=n;while(i&&i.$vnode&&i.$parent&&i.$vnode===i.$parent._vnode)i.$parent.$el=i.$el,i=i.$parent},e.prototype.$forceUpdate=function(){var e=this;e._watcher&&e._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){An(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||k(t.$children,e),e._scope.stop(),e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),An(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}function jn(e,t,n){var a;e.$el=t,e.$options.render||(e.$options.render=Le),An(e,"beforeMount"),a=function(){e._update(e._render(),n)};var r={before:function(){e._isMounted&&!e._isDestroyed&&An(e,"beforeUpdate")}};new Ln(e,a,F,r,!0),n=!1;var s=e._preWatchers;if(s)for(var i=0;i<s.length;i++)s[i].run();return null==e.$vnode&&(e._isMounted=!0,An(e,"mounted")),e}function xn(e,t,n,r,s){var i=r.data.scopedSlots,o=e.$scopedSlots,d=!!(i&&!i.$stable||o!==a&&!o.$stable||i&&e.$scopedSlots.$key!==i.$key||!i&&e.$scopedSlots.$key),_=!!(s||e.$options._renderChildren||d),u=e.$vnode;e.$options._parentVnode=r,e.$vnode=r,e._vnode&&(e._vnode.parent=r),e.$options._renderChildren=s;var l=r.data.attrs||a;e._attrsProxy&&Pt(e._attrsProxy,l,u.data&&u.data.attrs||a,e,"$attrs")&&(_=!0),e.$attrs=l,n=n||a;var m=e.$options._parentListeners;if(e._listenersProxy&&Pt(e._listenersProxy,n,m||a,e,"$listeners"),e.$listeners=e.$options._parentListeners=n,gn(e,n,m),t&&e.$options.props){Ae(!1);for(var c=e._props,h=e.$options._propKeys||[],f=0;f<h.length;f++){var M=h[f],p=e.$options.props;c[M]=wa(M,p,t,e)}Ae(!0),e.$options.propsData=t}_&&(e.$slots=Tt(s,r.context),e.$forceUpdate())}function On(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function Pn(e,t){if(t){if(e._directInactive=!1,On(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)Pn(e.$children[n]);An(e,"activated")}}function Wn(e,t){if((!t||(e._directInactive=!0,!On(e)))&&!e._inactive){e._inactive=!0;for(var n=0;n<e.$children.length;n++)Wn(e.$children[n]);An(e,"deactivated")}}function An(e,t,n,a){void 0===a&&(a=!0),be();var r=Me,s=Qe();a&&pe(e);var i=e.$options[t],o="".concat(t," hook");if(i)for(var d=0,_=i.length;d<_;d++)Zt(i[d],e,n||null,e,o);e._hasHookEvent&&e.$emit("hook:"+t),a&&(pe(r),s&&s.on()),Se()}var En=[],Fn=[],Cn={},zn=!1,$n=!1,In=0;function Nn(){In=En.length=Fn.length=0,Cn={},zn=$n=!1}var Jn=0,Rn=Date.now;if(ee&&!ne){var Un=window.performance;Un&&"function"===typeof Un.now&&Rn()>document.createEvent("Event").timeStamp&&(Rn=function(){return Un.now()})}var Gn=function(e,t){if(e.post){if(!t.post)return 1}else if(t.post)return-1;return e.id-t.id};function Vn(){var e,t;for(Jn=Rn(),$n=!0,En.sort(Gn),In=0;In<En.length;In++)e=En[In],e.before&&e.before(),t=e.id,Cn[t]=null,e.run();var n=Fn.slice(),a=En.slice();Nn(),qn(n),Bn(a),ge(),me&&V.devtools&&me.emit("flush")}function Bn(e){var t=e.length;while(t--){var n=e[t],a=n.vm;a&&a._watcher===n&&a._isMounted&&!a._isDestroyed&&An(a,"updated")}}function Kn(e){e._inactive=!1,Fn.push(e)}function qn(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,Pn(e[t],!0)}function Zn(e){var t=e.id;if(null==Cn[t]&&(e!==Te.target||!e.noRecurse)){if(Cn[t]=!0,$n){var n=En.length-1;while(n>In&&En[n].id>e.id)n--;En.splice(n+1,0,e)}else En.push(e);zn||(zn=!0,un(Vn))}}function Qn(e){var t=e.$options.provide;if(t){var n=u(t)?t.call(e):t;if(!l(n))return;for(var a=Xe(e),r=fe?Reflect.ownKeys(n):Object.keys(n),s=0;s<r.length;s++){var i=r[s];Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(n,i))}}}function Xn(e){var t=ea(e.$options.inject,e);t&&(Ae(!1),Object.keys(t).forEach((function(n){ze(e,n,t[n])})),Ae(!0))}function ea(e,t){if(e){for(var n=Object.create(null),a=fe?Reflect.ownKeys(e):Object.keys(e),r=0;r<a.length;r++){var s=a[r];if("__ob__"!==s){var i=e[s].from;if(i in t._provided)n[s]=t._provided[i];else if("default"in e[s]){var o=e[s].default;n[s]=u(o)?o.call(t):o}else 0}}return n}}function ta(e,t,n,s,i){var d,_=this,u=i.options;g(s,"_uid")?(d=Object.create(s),d._original=s):(d=s,s=s._original);var l=o(u._compiled),m=!l;this.data=e,this.props=t,this.children=n,this.parent=s,this.listeners=e.on||a,this.injections=ea(u.inject,s),this.slots=function(){return _.$slots||St(s,e.scopedSlots,_.$slots=Tt(n,s)),_.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return St(s,e.scopedSlots,this.slots())}}),l&&(this.$options=u,this.$slots=this.slots(),this.$scopedSlots=St(s,e.scopedSlots,this.$slots)),u._scopeId?this._c=function(e,t,n,a){var i=Gt(d,e,t,n,a,m);return i&&!r(i)&&(i.fnScopeId=u._scopeId,i.fnContext=s),i}:this._c=function(e,t,n,a){return Gt(d,e,t,n,a,m)}}function na(e,t,n,s,o){var d=e.options,_={},u=d.props;if(i(u))for(var l in u)_[l]=wa(l,u,t||a);else i(n.attrs)&&ra(_,n.attrs),i(n.props)&&ra(_,n.props);var m=new ta(n,_,o,s,e),c=d.render.call(null,m._c,m);if(c instanceof ye)return aa(c,n,m.parent,d,m);if(r(c)){for(var h=ot(c)||[],f=new Array(h.length),M=0;M<h.length;M++)f[M]=aa(h[M],n,m.parent,d,m);return f}}function aa(e,t,n,a,r){var s=ve(e);return s.fnContext=n,s.fnOptions=a,t.slot&&((s.data||(s.data={})).slot=t.slot),s}function ra(e,t){for(var n in t)e[b(n)]=t[n]}function sa(e){return e.name||e.__name||e._componentTag}gt(ta.prototype);var ia={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;ia.prepatch(n,n)}else{var a=e.componentInstance=_a(e,wn);a.$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var n=t.componentOptions,a=t.componentInstance=e.componentInstance;xn(a,n.propsData,n.listeners,t,n.children)},insert:function(e){var t=e.context,n=e.componentInstance;n._isMounted||(n._isMounted=!0,An(n,"mounted")),e.data.keepAlive&&(t._isMounted?Kn(n):Pn(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?Wn(t,!0):t.$destroy())}},oa=Object.keys(ia);function da(e,t,n,a,r){if(!s(e)){var d=n.$options._base;if(l(e)&&(e=d.extend(e)),"function"===typeof e){var _;if(s(e.cid)&&(_=e,e=Nt(_,d),void 0===e))return It(_,t,n,a,r);t=t||{},qa(e),i(t.model)&&ma(e.options,t);var u=rt(t,e,r);if(o(e.options.functional))return na(e,u,t,n,a);var m=t.on;if(t.on=t.nativeOn,o(e.options.abstract)){var c=t.slot;t={},c&&(t.slot=c)}ua(t);var h=sa(e.options)||r,f=new ye("vue-component-".concat(e.cid).concat(h?"-".concat(h):""),t,void 0,void 0,void 0,n,{Ctor:e,propsData:u,listeners:m,tag:r,children:a},_);return f}}}function _a(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},a=e.data.inlineTemplate;return i(a)&&(n.render=a.render,n.staticRenderFns=a.staticRenderFns),new e.componentOptions.Ctor(n)}function ua(e){for(var t=e.hook||(e.hook={}),n=0;n<oa.length;n++){var a=oa[n],r=t[a],s=ia[a];r===s||r&&r._merged||(t[a]=r?la(s,r):s)}}function la(e,t){var n=function(n,a){e(n,a),t(n,a)};return n._merged=!0,n}function ma(e,t){var n=e.model&&e.model.prop||"value",a=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var s=t.on||(t.on={}),o=s[a],d=t.model.callback;i(o)?(r(o)?-1===o.indexOf(d):o!==d)&&(s[a]=[d].concat(o)):s[a]=d}var ca=F,ha=V.optionMergeStrategies;function fa(e,t,n){if(void 0===n&&(n=!0),!t)return e;for(var a,r,s,i=fe?Reflect.ownKeys(t):Object.keys(t),o=0;o<i.length;o++)a=i[o],"__ob__"!==a&&(r=e[a],s=t[a],n&&g(e,a)?r!==s&&c(r)&&c(s)&&fa(r,s):$e(e,a,s));return e}function Ma(e,t,n){return n?function(){var a=u(t)?t.call(n,n):t,r=u(e)?e.call(n,n):e;return a?fa(a,r):r}:t?e?function(){return fa(u(t)?t.call(this,this):t,u(e)?e.call(this,this):e)}:t:e}function pa(e,t){var n=t?e?e.concat(t):r(t)?t:[t]:e;return n?ya(n):n}function ya(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}function La(e,t,n,a){var r=Object.create(e||null);return t?A(r,t):r}ha.data=function(e,t,n){return n?Ma(e,t,n):t&&"function"!==typeof t?e:Ma(e,t)},G.forEach((function(e){ha[e]=pa})),U.forEach((function(e){ha[e+"s"]=La})),ha.watch=function(e,t,n,a){if(e===de&&(e=void 0),t===de&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var s={};for(var i in A(s,e),t){var o=s[i],d=t[i];o&&!r(o)&&(o=[o]),s[i]=o?o.concat(d):r(d)?d:[d]}return s},ha.props=ha.methods=ha.inject=ha.computed=function(e,t,n,a){if(!e)return t;var r=Object.create(null);return A(r,e),t&&A(r,t),r},ha.provide=function(e,t){return e?function(){var n=Object.create(null);return fa(n,u(e)?e.call(this):e),t&&fa(n,u(t)?t.call(this):t,!1),n}:t};var Ya=function(e,t){return void 0===t?e:t};function va(e,t){var n=e.props;if(n){var a,s,i,o={};if(r(n)){a=n.length;while(a--)s=n[a],"string"===typeof s&&(i=b(s),o[i]={type:null})}else if(c(n))for(var d in n)s=n[d],i=b(d),o[i]=c(s)?s:{type:s};else 0;e.props=o}}function ka(e,t){var n=e.inject;if(n){var a=e.inject={};if(r(n))for(var s=0;s<n.length;s++)a[n[s]]={from:n[s]};else if(c(n))for(var i in n){var o=n[i];a[i]=c(o)?A({from:i},o):{from:o}}else 0}}function Da(e){var t=e.directives;if(t)for(var n in t){var a=t[n];u(a)&&(t[n]={bind:a,update:a})}}function ga(e,t,n){if(u(t)&&(t=t.options),va(t,n),ka(t,n),Da(t),!t._base&&(t.extends&&(e=ga(e,t.extends,n)),t.mixins))for(var a=0,r=t.mixins.length;a<r;a++)e=ga(e,t.mixins[a],n);var s,i={};for(s in e)o(s);for(s in t)g(e,s)||o(s);function o(a){var r=ha[a]||Ya;i[a]=r(e[a],t[a],n,a)}return i}function Ta(e,t,n,a){if("string"===typeof n){var r=e[t];if(g(r,n))return r[n];var s=b(n);if(g(r,s))return r[s];var i=S(s);if(g(r,i))return r[i];var o=r[n]||r[s]||r[i];return o}}function wa(e,t,n,a){var r=t[e],s=!g(n,e),i=n[e],o=xa(Boolean,r.type);if(o>-1)if(s&&!g(r,"default"))i=!1;else if(""===i||i===j(e)){var d=xa(String,r.type);(d<0||o<d)&&(i=!0)}if(void 0===i){i=ba(a,r,e);var _=We;Ae(!0),Ce(i),Ae(_)}return i}function ba(e,t,n){if(g(t,"default")){var a=t.default;return e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n]?e._props[n]:u(a)&&"Function"!==Ha(t.type)?a.call(e):a}}var Sa=/^\s*function (\w+)/;function Ha(e){var t=e&&e.toString().match(Sa);return t?t[1]:""}function ja(e,t){return Ha(e)===Ha(t)}function xa(e,t){if(!r(t))return ja(t,e)?0:-1;for(var n=0,a=t.length;n<a;n++)if(ja(t[n],e))return n;return-1}var Oa={enumerable:!0,configurable:!0,get:F,set:F};function Pa(e,t,n){Oa.get=function(){return this[t][n]},Oa.set=function(e){this[t][n]=e},Object.defineProperty(e,n,Oa)}function Wa(e){var t=e.$options;if(t.props&&Aa(e,t.props),xt(e),t.methods&&Ja(e,t.methods),t.data)Ea(e);else{var n=Ce(e._data={});n&&n.vmCount++}t.computed&&za(e,t.computed),t.watch&&t.watch!==de&&Ra(e,t.watch)}function Aa(e,t){var n=e.$options.propsData||{},a=e._props=Je({}),r=e.$options._propKeys=[],s=!e.$parent;s||Ae(!1);var i=function(s){r.push(s);var i=wa(s,t,n,e);ze(a,s,i,void 0,!0),s in e||Pa(e,"_props",s)};for(var o in t)i(o);Ae(!0)}function Ea(e){var t=e.$options.data;t=e._data=u(t)?Fa(t,e):t||{},c(t)||(t={});var n=Object.keys(t),a=e.$options.props,r=(e.$options.methods,n.length);while(r--){var s=n[r];0,a&&g(a,s)||K(s)||Pa(e,"_data",s)}var i=Ce(t);i&&i.vmCount++}function Fa(e,t){be();try{return e.call(t,t)}catch(Qi){return qt(Qi,t,"data()"),{}}finally{Se()}}var Ca={lazy:!0};function za(e,t){var n=e._computedWatchers=Object.create(null),a=le();for(var r in t){var s=t[r],i=u(s)?s:s.get;0,a||(n[r]=new Ln(e,i||F,F,Ca)),r in e||$a(e,r,s)}}function $a(e,t,n){var a=!le();u(n)?(Oa.get=a?Ia(t):Na(n),Oa.set=F):(Oa.get=n.get?a&&!1!==n.cache?Ia(t):Na(n.get):F,Oa.set=n.set||F),Object.defineProperty(e,t,Oa)}function Ia(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),Te.target&&t.depend(),t.value}}function Na(e){return function(){return e.call(this,this)}}function Ja(e,t){e.$options.props;for(var n in t)e[n]="function"!==typeof t[n]?F:P(t[n],e)}function Ra(e,t){for(var n in t){var a=t[n];if(r(a))for(var s=0;s<a.length;s++)Ua(e,n,a[s]);else Ua(e,n,a)}}function Ua(e,t,n,a){return c(n)&&(a=n,n=n.handler),"string"===typeof n&&(n=e[n]),e.$watch(t,n,a)}function Ga(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=$e,e.prototype.$delete=Ie,e.prototype.$watch=function(e,t,n){var a=this;if(c(t))return Ua(a,e,t,n);n=n||{},n.user=!0;var r=new Ln(a,e,t,n);if(n.immediate){var s='callback for immediate watcher "'.concat(r.expression,'"');be(),Zt(t,a,[r.value],a,s),Se()}return function(){r.teardown()}}}var Va=0;function Ba(e){e.prototype._init=function(e){var t=this;t._uid=Va++,t._isVue=!0,t.__v_skip=!0,t._scope=new qe(!0),t._scope.parent=void 0,t._scope._vm=!0,e&&e._isComponent?Ka(t,e):t.$options=ga(qa(t.constructor),e||{},t),t._renderProxy=t,t._self=t,Sn(t),Yn(t),Ft(t),An(t,"beforeCreate",void 0,!1),Xn(t),Wa(t),Qn(t),An(t,"created"),t.$options.el&&t.$mount(t.$options.el)}}function Ka(e,t){var n=e.$options=Object.create(e.constructor.options),a=t._parentVnode;n.parent=t.parent,n._parentVnode=a;var r=a.componentOptions;n.propsData=r.propsData,n._parentListeners=r.listeners,n._renderChildren=r.children,n._componentTag=r.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}function qa(e){var t=e.options;if(e.super){var n=qa(e.super),a=e.superOptions;if(n!==a){e.superOptions=n;var r=Za(e);r&&A(e.extendOptions,r),t=e.options=ga(n,e.extendOptions),t.name&&(t.components[t.name]=e)}}return t}function Za(e){var t,n=e.options,a=e.sealedOptions;for(var r in n)n[r]!==a[r]&&(t||(t={}),t[r]=n[r]);return t}function Qa(e){this._init(e)}function Xa(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=W(arguments,1);return n.unshift(this),u(e.install)?e.install.apply(e,n):u(e)&&e.apply(null,n),t.push(e),this}}function er(e){e.mixin=function(e){return this.options=ga(this.options,e),this}}function tr(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,a=n.cid,r=e._Ctor||(e._Ctor={});if(r[a])return r[a];var s=sa(e)||sa(n.options);var i=function(e){this._init(e)};return i.prototype=Object.create(n.prototype),i.prototype.constructor=i,i.cid=t++,i.options=ga(n.options,e),i["super"]=n,i.options.props&&nr(i),i.options.computed&&ar(i),i.extend=n.extend,i.mixin=n.mixin,i.use=n.use,U.forEach((function(e){i[e]=n[e]})),s&&(i.options.components[s]=i),i.superOptions=n.options,i.extendOptions=e,i.sealedOptions=A({},i.options),r[a]=i,i}}function nr(e){var t=e.options.props;for(var n in t)Pa(e.prototype,"_props",n)}function ar(e){var t=e.options.computed;for(var n in t)$a(e.prototype,n,t[n])}function rr(e){U.forEach((function(t){e[t]=function(e,n){return n?("component"===t&&c(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&u(n)&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}}))}function sr(e){return e&&(sa(e.Ctor.options)||e.tag)}function ir(e,t){return r(e)?e.indexOf(t)>-1:"string"===typeof e?e.split(",").indexOf(t)>-1:!!h(e)&&e.test(t)}function or(e,t){var n=e.cache,a=e.keys,r=e._vnode,s=e.$vnode;for(var i in n){var o=n[i];if(o){var d=o.name;d&&!t(d)&&dr(n,i,a,r)}}s.componentOptions.children=void 0}function dr(e,t,n,a){var r=e[t];!r||a&&r.tag===a.tag||r.componentInstance.$destroy(),e[t]=null,k(n,t)}Ba(Qa),Ga(Qa),Tn(Qa),Hn(Qa),zt(Qa);var _r=[String,RegExp,Array],ur={name:"keep-alive",abstract:!0,props:{include:_r,exclude:_r,max:[String,Number]},methods:{cacheVNode:function(){var e=this,t=e.cache,n=e.keys,a=e.vnodeToCache,r=e.keyToCache;if(a){var s=a.tag,i=a.componentInstance,o=a.componentOptions;t[r]={name:sr(o),tag:s,componentInstance:i},n.push(r),this.max&&n.length>parseInt(this.max)&&dr(t,n[0],n,this._vnode),this.vnodeToCache=null}}},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)dr(this.cache,e,this.keys)},mounted:function(){var e=this;this.cacheVNode(),this.$watch("include",(function(t){or(e,(function(e){return ir(t,e)}))})),this.$watch("exclude",(function(t){or(e,(function(e){return!ir(t,e)}))}))},updated:function(){this.cacheVNode()},render:function(){var e=this.$slots.default,t=Jt(e),n=t&&t.componentOptions;if(n){var a=sr(n),r=this,s=r.include,i=r.exclude;if(s&&(!a||!ir(s,a))||i&&a&&ir(i,a))return t;var o=this,d=o.cache,_=o.keys,u=null==t.key?n.Ctor.cid+(n.tag?"::".concat(n.tag):""):t.key;d[u]?(t.componentInstance=d[u].componentInstance,k(_,u),_.push(u)):(this.vnodeToCache=t,this.keyToCache=u),t.data.keepAlive=!0}return t||e&&e[0]}},lr={KeepAlive:ur};function mr(e){var t={get:function(){return V}};Object.defineProperty(e,"config",t),e.util={warn:ca,extend:A,mergeOptions:ga,defineReactive:ze},e.set=$e,e.delete=Ie,e.nextTick=un,e.observable=function(e){return Ce(e),e},e.options=Object.create(null),U.forEach((function(t){e.options[t+"s"]=Object.create(null)})),e.options._base=e,A(e.options.components,lr),Xa(e),er(e),tr(e),rr(e)}mr(Qa),Object.defineProperty(Qa.prototype,"$isServer",{get:le}),Object.defineProperty(Qa.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(Qa,"FunctionalRenderContext",{value:ta}),Qa.version=cn;var cr=Y("style,class"),hr=Y("input,textarea,option,select,progress"),fr=function(e,t,n){return"value"===n&&hr(e)&&"button"!==t||"selected"===n&&"option"===e||"checked"===n&&"input"===e||"muted"===n&&"video"===e},Mr=Y("contenteditable,draggable,spellcheck"),pr=Y("events,caret,typing,plaintext-only"),yr=function(e,t){return Dr(t)||"false"===t?"false":"contenteditable"===e&&pr(t)?t:"true"},Lr=Y("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,truespeed,typemustmatch,visible"),Yr="http://www.w3.org/1999/xlink",vr=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},kr=function(e){return vr(e)?e.slice(6,e.length):""},Dr=function(e){return null==e||!1===e};function gr(e){var t=e.data,n=e,a=e;while(i(a.componentInstance))a=a.componentInstance._vnode,a&&a.data&&(t=Tr(a.data,t));while(i(n=n.parent))n&&n.data&&(t=Tr(t,n.data));return wr(t.staticClass,t.class)}function Tr(e,t){return{staticClass:br(e.staticClass,t.staticClass),class:i(e.class)?[e.class,t.class]:t.class}}function wr(e,t){return i(e)||i(t)?br(e,Sr(t)):""}function br(e,t){return e?t?e+" "+t:e:t||""}function Sr(e){return Array.isArray(e)?Hr(e):l(e)?jr(e):"string"===typeof e?e:""}function Hr(e){for(var t,n="",a=0,r=e.length;a<r;a++)i(t=Sr(e[a]))&&""!==t&&(n&&(n+=" "),n+=t);return n}function jr(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}var xr={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Or=Y("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),Pr=Y("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Wr=function(e){return Or(e)||Pr(e)};function Ar(e){return Pr(e)?"svg":"math"===e?"math":void 0}var Er=Object.create(null);function Fr(e){if(!ee)return!0;if(Wr(e))return!1;if(e=e.toLowerCase(),null!=Er[e])return Er[e];var t=document.createElement(e);return e.indexOf("-")>-1?Er[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Er[e]=/HTMLUnknownElement/.test(t.toString())}var Cr=Y("text,number,password,search,email,tel,url");function zr(e){if("string"===typeof e){var t=document.querySelector(e);return t||document.createElement("div")}return e}function $r(e,t){var n=document.createElement(e);return"select"!==e||t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&n.setAttribute("multiple","multiple"),n}function Ir(e,t){return document.createElementNS(xr[e],t)}function Nr(e){return document.createTextNode(e)}function Jr(e){return document.createComment(e)}function Rr(e,t,n){e.insertBefore(t,n)}function Ur(e,t){e.removeChild(t)}function Gr(e,t){e.appendChild(t)}function Vr(e){return e.parentNode}function Br(e){return e.nextSibling}function Kr(e){return e.tagName}function qr(e,t){e.textContent=t}function Zr(e,t){e.setAttribute(t,"")}var Qr=Object.freeze({__proto__:null,createElement:$r,createElementNS:Ir,createTextNode:Nr,createComment:Jr,insertBefore:Rr,removeChild:Ur,appendChild:Gr,parentNode:Vr,nextSibling:Br,tagName:Kr,setTextContent:qr,setStyleScope:Zr}),Xr={create:function(e,t){es(t)},update:function(e,t){e.data.ref!==t.data.ref&&(es(e,!0),es(t))},destroy:function(e){es(e,!0)}};function es(e,t){var n=e.data.ref;if(i(n)){var a=e.context,s=e.componentInstance||e.elm,o=t?null:s,d=t?void 0:s;if(u(n))Zt(n,a,[o],a,"template ref function");else{var _=e.data.refInFor,l="string"===typeof n||"number"===typeof n,m=Ge(n),c=a.$refs;if(l||m)if(_){var h=l?c[n]:n.value;t?r(h)&&k(h,s):r(h)?h.includes(s)||h.push(s):l?(c[n]=[s],ts(a,n,c[n])):n.value=[s]}else if(l){if(t&&c[n]!==s)return;c[n]=d,ts(a,n,o)}else if(m){if(t&&n.value!==s)return;n.value=o}else 0}}}function ts(e,t,n){var a=e._setupState;a&&g(a,t)&&(Ge(a[t])?a[t].value=n:a[t]=n)}var ns=new ye("",{},[]),as=["create","activate","update","remove","destroy"];function rs(e,t){return e.key===t.key&&e.asyncFactory===t.asyncFactory&&(e.tag===t.tag&&e.isComment===t.isComment&&i(e.data)===i(t.data)&&ss(e,t)||o(e.isAsyncPlaceholder)&&s(t.asyncFactory.error))}function ss(e,t){if("input"!==e.tag)return!0;var n,a=i(n=e.data)&&i(n=n.attrs)&&n.type,r=i(n=t.data)&&i(n=n.attrs)&&n.type;return a===r||Cr(a)&&Cr(r)}function is(e,t,n){var a,r,s={};for(a=t;a<=n;++a)r=e[a].key,i(r)&&(s[r]=a);return s}function os(e){var t,n,a={},d=e.modules,u=e.nodeOps;for(t=0;t<as.length;++t)for(a[as[t]]=[],n=0;n<d.length;++n)i(d[n][as[t]])&&a[as[t]].push(d[n][as[t]]);function l(e){return new ye(u.tagName(e).toLowerCase(),{},[],void 0,e)}function m(e,t){function n(){0===--n.listeners&&c(e)}return n.listeners=t,n}function c(e){var t=u.parentNode(e);i(t)&&u.removeChild(t,e)}function h(e,t,n,a,r,s,d){if(i(e.elm)&&i(s)&&(e=s[d]=ve(e)),e.isRootInsert=!r,!f(e,t,n,a)){var _=e.data,l=e.children,m=e.tag;i(m)?(e.elm=e.ns?u.createElementNS(e.ns,m):u.createElement(m,e),D(e),L(e,l,t),i(_)&&k(e,t),y(n,e.elm,a)):o(e.isComment)?(e.elm=u.createComment(e.text),y(n,e.elm,a)):(e.elm=u.createTextNode(e.text),y(n,e.elm,a))}}function f(e,t,n,a){var r=e.data;if(i(r)){var s=i(e.componentInstance)&&r.keepAlive;if(i(r=r.hook)&&i(r=r.init)&&r(e,!1),i(e.componentInstance))return M(e,t),y(n,e.elm,a),o(s)&&p(e,t,n,a),!0}}function M(e,t){i(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,v(e)?(k(e,t),D(e)):(es(e),t.push(e))}function p(e,t,n,r){var s,o=e;while(o.componentInstance)if(o=o.componentInstance._vnode,i(s=o.data)&&i(s=s.transition)){for(s=0;s<a.activate.length;++s)a.activate[s](ns,o);t.push(o);break}y(n,e.elm,r)}function y(e,t,n){i(e)&&(i(n)?u.parentNode(n)===e&&u.insertBefore(e,t,n):u.appendChild(e,t))}function L(e,t,n){if(r(t)){0;for(var a=0;a<t.length;++a)h(t[a],n,e.elm,null,!0,t,a)}else _(e.text)&&u.appendChild(e.elm,u.createTextNode(String(e.text)))}function v(e){while(e.componentInstance)e=e.componentInstance._vnode;return i(e.tag)}function k(e,n){for(var r=0;r<a.create.length;++r)a.create[r](ns,e);t=e.data.hook,i(t)&&(i(t.create)&&t.create(ns,e),i(t.insert)&&n.push(e))}function D(e){var t;if(i(t=e.fnScopeId))u.setStyleScope(e.elm,t);else{var n=e;while(n)i(t=n.context)&&i(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t),n=n.parent}i(t=wn)&&t!==e.context&&t!==e.fnContext&&i(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t)}function g(e,t,n,a,r,s){for(;a<=r;++a)h(n[a],s,e,t,!1,n,a)}function T(e){var t,n,r=e.data;if(i(r))for(i(t=r.hook)&&i(t=t.destroy)&&t(e),t=0;t<a.destroy.length;++t)a.destroy[t](e);if(i(t=e.children))for(n=0;n<e.children.length;++n)T(e.children[n])}function w(e,t,n){for(;t<=n;++t){var a=e[t];i(a)&&(i(a.tag)?(b(a),T(a)):c(a.elm))}}function b(e,t){if(i(t)||i(e.data)){var n,r=a.remove.length+1;for(i(t)?t.listeners+=r:t=m(e.elm,r),i(n=e.componentInstance)&&i(n=n._vnode)&&i(n.data)&&b(n,t),n=0;n<a.remove.length;++n)a.remove[n](e,t);i(n=e.data.hook)&&i(n=n.remove)?n(e,t):t()}else c(e.elm)}function S(e,t,n,a,r){var o,d,_,l,m=0,c=0,f=t.length-1,M=t[0],p=t[f],y=n.length-1,L=n[0],Y=n[y],v=!r;while(m<=f&&c<=y)s(M)?M=t[++m]:s(p)?p=t[--f]:rs(M,L)?(j(M,L,a,n,c),M=t[++m],L=n[++c]):rs(p,Y)?(j(p,Y,a,n,y),p=t[--f],Y=n[--y]):rs(M,Y)?(j(M,Y,a,n,y),v&&u.insertBefore(e,M.elm,u.nextSibling(p.elm)),M=t[++m],Y=n[--y]):rs(p,L)?(j(p,L,a,n,c),v&&u.insertBefore(e,p.elm,M.elm),p=t[--f],L=n[++c]):(s(o)&&(o=is(t,m,f)),d=i(L.key)?o[L.key]:H(L,t,m,f),s(d)?h(L,a,e,M.elm,!1,n,c):(_=t[d],rs(_,L)?(j(_,L,a,n,c),t[d]=void 0,v&&u.insertBefore(e,_.elm,M.elm)):h(L,a,e,M.elm,!1,n,c)),L=n[++c]);m>f?(l=s(n[y+1])?null:n[y+1].elm,g(e,l,n,c,y,a)):c>y&&w(t,m,f)}function H(e,t,n,a){for(var r=n;r<a;r++){var s=t[r];if(i(s)&&rs(e,s))return r}}function j(e,t,n,r,d,_){if(e!==t){i(t.elm)&&i(r)&&(t=r[d]=ve(t));var l=t.elm=e.elm;if(o(e.isAsyncPlaceholder))i(t.asyncFactory.resolved)?P(e.elm,t,n):t.isAsyncPlaceholder=!0;else if(o(t.isStatic)&&o(e.isStatic)&&t.key===e.key&&(o(t.isCloned)||o(t.isOnce)))t.componentInstance=e.componentInstance;else{var m,c=t.data;i(c)&&i(m=c.hook)&&i(m=m.prepatch)&&m(e,t);var h=e.children,f=t.children;if(i(c)&&v(t)){for(m=0;m<a.update.length;++m)a.update[m](e,t);i(m=c.hook)&&i(m=m.update)&&m(e,t)}s(t.text)?i(h)&&i(f)?h!==f&&S(l,h,f,n,_):i(f)?(i(e.text)&&u.setTextContent(l,""),g(l,null,f,0,f.length-1,n)):i(h)?w(h,0,h.length-1):i(e.text)&&u.setTextContent(l,""):e.text!==t.text&&u.setTextContent(l,t.text),i(c)&&i(m=c.hook)&&i(m=m.postpatch)&&m(e,t)}}}function x(e,t,n){if(o(n)&&i(e.parent))e.parent.data.pendingInsert=t;else for(var a=0;a<t.length;++a)t[a].data.hook.insert(t[a])}var O=Y("attrs,class,staticClass,staticStyle,key");function P(e,t,n,a){var r,s=t.tag,d=t.data,_=t.children;if(a=a||d&&d.pre,t.elm=e,o(t.isComment)&&i(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(i(d)&&(i(r=d.hook)&&i(r=r.init)&&r(t,!0),i(r=t.componentInstance)))return M(t,n),!0;if(i(s)){if(i(_))if(e.hasChildNodes())if(i(r=d)&&i(r=r.domProps)&&i(r=r.innerHTML)){if(r!==e.innerHTML)return!1}else{for(var u=!0,l=e.firstChild,m=0;m<_.length;m++){if(!l||!P(l,_[m],n,a)){u=!1;break}l=l.nextSibling}if(!u||l)return!1}else L(t,_,n);if(i(d)){var c=!1;for(var h in d)if(!O(h)){c=!0,k(t,n);break}!c&&d["class"]&&fn(d["class"])}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,n,r){if(!s(t)){var d=!1,_=[];if(s(e))d=!0,h(t,_);else{var m=i(e.nodeType);if(!m&&rs(e,t))j(e,t,_,null,null,r);else{if(m){if(1===e.nodeType&&e.hasAttribute(R)&&(e.removeAttribute(R),n=!0),o(n)&&P(e,t,_))return x(t,_,!0),e;e=l(e)}var c=e.elm,f=u.parentNode(c);if(h(t,_,c._leaveCb?null:f,u.nextSibling(c)),i(t.parent)){var M=t.parent,p=v(t);while(M){for(var y=0;y<a.destroy.length;++y)a.destroy[y](M);if(M.elm=t.elm,p){for(var L=0;L<a.create.length;++L)a.create[L](ns,M);var Y=M.data.hook.insert;if(Y.merged)for(var k=Y.fns.slice(1),D=0;D<k.length;D++)k[D]()}else es(M);M=M.parent}}i(f)?w([e],0,0):i(e.tag)&&T(e)}}return x(t,_,d),t.elm}i(e)&&T(e)}}var ds={create:_s,update:_s,destroy:function(e){_s(e,ns)}};function _s(e,t){(e.data.directives||t.data.directives)&&us(e,t)}function us(e,t){var n,a,r,s=e===ns,i=t===ns,o=ms(e.data.directives,e.context),d=ms(t.data.directives,t.context),_=[],u=[];for(n in d)a=o[n],r=d[n],a?(r.oldValue=a.value,r.oldArg=a.arg,hs(r,"update",t,e),r.def&&r.def.componentUpdated&&u.push(r)):(hs(r,"bind",t,e),r.def&&r.def.inserted&&_.push(r));if(_.length){var l=function(){for(var n=0;n<_.length;n++)hs(_[n],"inserted",t,e)};s?at(t,"insert",l):l()}if(u.length&&at(t,"postpatch",(function(){for(var n=0;n<u.length;n++)hs(u[n],"componentUpdated",t,e)})),!s)for(n in o)d[n]||hs(o[n],"unbind",e,e,i)}var ls=Object.create(null);function ms(e,t){var n,a,r=Object.create(null);if(!e)return r;for(n=0;n<e.length;n++){if(a=e[n],a.modifiers||(a.modifiers=ls),r[cs(a)]=a,t._setupState&&t._setupState.__sfc){var s=a.def||Ta(t,"_setupState","v-"+a.name);a.def="function"===typeof s?{bind:s,update:s}:s}a.def=a.def||Ta(t.$options,"directives",a.name,!0)}return r}function cs(e){return e.rawName||"".concat(e.name,".").concat(Object.keys(e.modifiers||{}).join("."))}function hs(e,t,n,a,r){var s=e.def&&e.def[t];if(s)try{s(n.elm,e,n,a,r)}catch(Qi){qt(Qi,n.context,"directive ".concat(e.name," ").concat(t," hook"))}}var fs=[Xr,ds];function Ms(e,t){var n=t.componentOptions;if((!i(n)||!1!==n.Ctor.options.inheritAttrs)&&(!s(e.data.attrs)||!s(t.data.attrs))){var a,r,d,_=t.elm,u=e.data.attrs||{},l=t.data.attrs||{};for(a in(i(l.__ob__)||o(l._v_attr_proxy))&&(l=t.data.attrs=A({},l)),l)r=l[a],d=u[a],d!==r&&ps(_,a,r,t.data.pre);for(a in(ne||re)&&l.value!==u.value&&ps(_,"value",l.value),u)s(l[a])&&(vr(a)?_.removeAttributeNS(Yr,kr(a)):Mr(a)||_.removeAttribute(a))}}function ps(e,t,n,a){a||e.tagName.indexOf("-")>-1?ys(e,t,n):Lr(t)?Dr(n)?e.removeAttribute(t):(n="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,n)):Mr(t)?e.setAttribute(t,yr(t,n)):vr(t)?Dr(n)?e.removeAttributeNS(Yr,kr(t)):e.setAttributeNS(Yr,t,n):ys(e,t,n)}function ys(e,t,n){if(Dr(n))e.removeAttribute(t);else{if(ne&&!ae&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==n&&!e.__ieph){var a=function(t){t.stopImmediatePropagation(),e.removeEventListener("input",a)};e.addEventListener("input",a),e.__ieph=!0}e.setAttribute(t,n)}}var Ls={create:Ms,update:Ms};function Ys(e,t){var n=t.elm,a=t.data,r=e.data;if(!(s(a.staticClass)&&s(a.class)&&(s(r)||s(r.staticClass)&&s(r.class)))){var o=gr(t),d=n._transitionClasses;i(d)&&(o=br(o,Sr(d))),o!==n._prevClass&&(n.setAttribute("class",o),n._prevClass=o)}}var vs,ks={create:Ys,update:Ys},Ds="__r",gs="__c";function Ts(e){if(i(e[Ds])){var t=ne?"change":"input";e[t]=[].concat(e[Ds],e[t]||[]),delete e[Ds]}i(e[gs])&&(e.change=[].concat(e[gs],e.change||[]),delete e[gs])}function ws(e,t,n){var a=vs;return function r(){var s=t.apply(null,arguments);null!==s&&Hs(e,r,n,a)}}var bs=tn&&!(oe&&Number(oe[1])<=53);function Ss(e,t,n,a){if(bs){var r=Jn,s=t;t=s._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=r||e.timeStamp<=0||e.target.ownerDocument!==document)return s.apply(this,arguments)}}vs.addEventListener(e,t,_e?{capture:n,passive:a}:n)}function Hs(e,t,n,a){(a||vs).removeEventListener(e,t._wrapper||t,n)}function js(e,t){if(!s(e.data.on)||!s(t.data.on)){var n=t.data.on||{},a=e.data.on||{};vs=t.elm||e.elm,Ts(n),nt(n,a,Ss,Hs,ws,t.context),vs=void 0}}var xs,Os={create:js,update:js,destroy:function(e){return js(e,ns)}};function Ps(e,t){if(!s(e.data.domProps)||!s(t.data.domProps)){var n,a,r=t.elm,d=e.data.domProps||{},_=t.data.domProps||{};for(n in(i(_.__ob__)||o(_._v_attr_proxy))&&(_=t.data.domProps=A({},_)),d)n in _||(r[n]="");for(n in _){if(a=_[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),a===d[n])continue;1===r.childNodes.length&&r.removeChild(r.childNodes[0])}if("value"===n&&"PROGRESS"!==r.tagName){r._value=a;var u=s(a)?"":String(a);Ws(r,u)&&(r.value=u)}else if("innerHTML"===n&&Pr(r.tagName)&&s(r.innerHTML)){xs=xs||document.createElement("div"),xs.innerHTML="<svg>".concat(a,"</svg>");var l=xs.firstChild;while(r.firstChild)r.removeChild(r.firstChild);while(l.firstChild)r.appendChild(l.firstChild)}else if(a!==d[n])try{r[n]=a}catch(Qi){}}}}function Ws(e,t){return!e.composing&&("OPTION"===e.tagName||As(e,t)||Es(e,t))}function As(e,t){var n=!0;try{n=document.activeElement!==e}catch(Qi){}return n&&e.value!==t}function Es(e,t){var n=e.value,a=e._vModifiers;if(i(a)){if(a.number)return L(n)!==L(t);if(a.trim)return n.trim()!==t.trim()}return n!==t}var Fs={create:Ps,update:Ps},Cs=T((function(e){var t={},n=/;(?![^(]*\))/g,a=/:(.+)/;return e.split(n).forEach((function(e){if(e){var n=e.split(a);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}));function zs(e){var t=$s(e.style);return e.staticStyle?A(e.staticStyle,t):t}function $s(e){return Array.isArray(e)?E(e):"string"===typeof e?Cs(e):e}function Is(e,t){var n,a={};if(t){var r=e;while(r.componentInstance)r=r.componentInstance._vnode,r&&r.data&&(n=zs(r.data))&&A(a,n)}(n=zs(e.data))&&A(a,n);var s=e;while(s=s.parent)s.data&&(n=zs(s.data))&&A(a,n);return a}var Ns,Js=/^--/,Rs=/\s*!important$/,Us=function(e,t,n){if(Js.test(t))e.style.setProperty(t,n);else if(Rs.test(n))e.style.setProperty(j(t),n.replace(Rs,""),"important");else{var a=Vs(t);if(Array.isArray(n))for(var r=0,s=n.length;r<s;r++)e.style[a]=n[r];else e.style[a]=n}},Gs=["Webkit","Moz","ms"],Vs=T((function(e){if(Ns=Ns||document.createElement("div").style,e=b(e),"filter"!==e&&e in Ns)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<Gs.length;n++){var a=Gs[n]+t;if(a in Ns)return a}}));function Bs(e,t){var n=t.data,a=e.data;if(!(s(n.staticStyle)&&s(n.style)&&s(a.staticStyle)&&s(a.style))){var r,o,d=t.elm,_=a.staticStyle,u=a.normalizedStyle||a.style||{},l=_||u,m=$s(t.data.style)||{};t.data.normalizedStyle=i(m.__ob__)?A({},m):m;var c=Is(t,!0);for(o in l)s(c[o])&&Us(d,o,"");for(o in c)r=c[o],Us(d,o,null==r?"":r)}}var Ks={create:Bs,update:Bs},qs=/\s+/;function Zs(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(qs).forEach((function(t){return e.classList.add(t)})):e.classList.add(t);else{var n=" ".concat(e.getAttribute("class")||""," ");n.indexOf(" "+t+" ")<0&&e.setAttribute("class",(n+t).trim())}}function Qs(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(qs).forEach((function(t){return e.classList.remove(t)})):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{var n=" ".concat(e.getAttribute("class")||""," "),a=" "+t+" ";while(n.indexOf(a)>=0)n=n.replace(a," ");n=n.trim(),n?e.setAttribute("class",n):e.removeAttribute("class")}}function Xs(e){if(e){if("object"===typeof e){var t={};return!1!==e.css&&A(t,ei(e.name||"v")),A(t,e),t}return"string"===typeof e?ei(e):void 0}}var ei=T((function(e){return{enterClass:"".concat(e,"-enter"),enterToClass:"".concat(e,"-enter-to"),enterActiveClass:"".concat(e,"-enter-active"),leaveClass:"".concat(e,"-leave"),leaveToClass:"".concat(e,"-leave-to"),leaveActiveClass:"".concat(e,"-leave-active")}})),ti=ee&&!ae,ni="transition",ai="animation",ri="transition",si="transitionend",ii="animation",oi="animationend";ti&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(ri="WebkitTransition",si="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(ii="WebkitAnimation",oi="webkitAnimationEnd"));var di=ee?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function _i(e){di((function(){di(e)}))}function ui(e,t){var n=e._transitionClasses||(e._transitionClasses=[]);n.indexOf(t)<0&&(n.push(t),Zs(e,t))}function li(e,t){e._transitionClasses&&k(e._transitionClasses,t),Qs(e,t)}function mi(e,t,n){var a=hi(e,t),r=a.type,s=a.timeout,i=a.propCount;if(!r)return n();var o=r===ni?si:oi,d=0,_=function(){e.removeEventListener(o,u),n()},u=function(t){t.target===e&&++d>=i&&_()};setTimeout((function(){d<i&&_()}),s+1),e.addEventListener(o,u)}var ci=/\b(transform|all)(,|$)/;function hi(e,t){var n,a=window.getComputedStyle(e),r=(a[ri+"Delay"]||"").split(", "),s=(a[ri+"Duration"]||"").split(", "),i=fi(r,s),o=(a[ii+"Delay"]||"").split(", "),d=(a[ii+"Duration"]||"").split(", "),_=fi(o,d),u=0,l=0;t===ni?i>0&&(n=ni,u=i,l=s.length):t===ai?_>0&&(n=ai,u=_,l=d.length):(u=Math.max(i,_),n=u>0?i>_?ni:ai:null,l=n?n===ni?s.length:d.length:0);var m=n===ni&&ci.test(a[ri+"Property"]);return{type:n,timeout:u,propCount:l,hasTransform:m}}function fi(e,t){while(e.length<t.length)e=e.concat(e);return Math.max.apply(null,t.map((function(t,n){return Mi(t)+Mi(e[n])})))}function Mi(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function pi(e,t){var n=e.elm;i(n._leaveCb)&&(n._leaveCb.cancelled=!0,n._leaveCb());var a=Xs(e.data.transition);if(!s(a)&&!i(n._enterCb)&&1===n.nodeType){var r=a.css,o=a.type,d=a.enterClass,_=a.enterToClass,m=a.enterActiveClass,c=a.appearClass,h=a.appearToClass,f=a.appearActiveClass,M=a.beforeEnter,p=a.enter,y=a.afterEnter,Y=a.enterCancelled,v=a.beforeAppear,k=a.appear,D=a.afterAppear,g=a.appearCancelled,T=a.duration,w=wn,b=wn.$vnode;while(b&&b.parent)w=b.context,b=b.parent;var S=!w._isMounted||!e.isRootInsert;if(!S||k||""===k){var H=S&&c?c:d,j=S&&f?f:m,x=S&&h?h:_,O=S&&v||M,P=S&&u(k)?k:p,W=S&&D||y,A=S&&g||Y,E=L(l(T)?T.enter:T);0;var F=!1!==r&&!ae,C=Yi(P),z=n._enterCb=N((function(){F&&(li(n,x),li(n,j)),z.cancelled?(F&&li(n,H),A&&A(n)):W&&W(n),n._enterCb=null}));e.data.show||at(e,"insert",(function(){var t=n.parentNode,a=t&&t._pending&&t._pending[e.key];a&&a.tag===e.tag&&a.elm._leaveCb&&a.elm._leaveCb(),P&&P(n,z)})),O&&O(n),F&&(ui(n,H),ui(n,j),_i((function(){li(n,H),z.cancelled||(ui(n,x),C||(Li(E)?setTimeout(z,E):mi(n,o,z)))}))),e.data.show&&(t&&t(),P&&P(n,z)),F||C||z()}}}function yi(e,t){var n=e.elm;i(n._enterCb)&&(n._enterCb.cancelled=!0,n._enterCb());var a=Xs(e.data.transition);if(s(a)||1!==n.nodeType)return t();if(!i(n._leaveCb)){var r=a.css,o=a.type,d=a.leaveClass,_=a.leaveToClass,u=a.leaveActiveClass,m=a.beforeLeave,c=a.leave,h=a.afterLeave,f=a.leaveCancelled,M=a.delayLeave,p=a.duration,y=!1!==r&&!ae,Y=Yi(c),v=L(l(p)?p.leave:p);0;var k=n._leaveCb=N((function(){n.parentNode&&n.parentNode._pending&&(n.parentNode._pending[e.key]=null),y&&(li(n,_),li(n,u)),k.cancelled?(y&&li(n,d),f&&f(n)):(t(),h&&h(n)),n._leaveCb=null}));M?M(D):D()}function D(){k.cancelled||(!e.data.show&&n.parentNode&&((n.parentNode._pending||(n.parentNode._pending={}))[e.key]=e),m&&m(n),y&&(ui(n,d),ui(n,u),_i((function(){li(n,d),k.cancelled||(ui(n,_),Y||(Li(v)?setTimeout(k,v):mi(n,o,k)))}))),c&&c(n,k),y||Y||k())}}function Li(e){return"number"===typeof e&&!isNaN(e)}function Yi(e){if(s(e))return!1;var t=e.fns;return i(t)?Yi(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function vi(e,t){!0!==t.data.show&&pi(t)}var ki=ee?{create:vi,activate:vi,remove:function(e,t){!0!==e.data.show?yi(e,t):t()}}:{},Di=[Ls,ks,Os,Fs,Ks,ki],gi=Di.concat(fs),Ti=os({nodeOps:Qr,modules:gi});ae&&document.addEventListener("selectionchange",(function(){var e=document.activeElement;e&&e.vmodel&&Pi(e,"input")}));var wi={inserted:function(e,t,n,a){"select"===n.tag?(a.elm&&!a.elm._vOptions?at(n,"postpatch",(function(){wi.componentUpdated(e,t,n)})):bi(e,t,n.context),e._vOptions=[].map.call(e.options,ji)):("textarea"===n.tag||Cr(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",xi),e.addEventListener("compositionend",Oi),e.addEventListener("change",Oi),ae&&(e.vmodel=!0)))},componentUpdated:function(e,t,n){if("select"===n.tag){bi(e,t,n.context);var a=e._vOptions,r=e._vOptions=[].map.call(e.options,ji);if(r.some((function(e,t){return!$(e,a[t])}))){var s=e.multiple?t.value.some((function(e){return Hi(e,r)})):t.value!==t.oldValue&&Hi(t.value,r);s&&Pi(e,"change")}}}};function bi(e,t,n){Si(e,t,n),(ne||re)&&setTimeout((function(){Si(e,t,n)}),0)}function Si(e,t,n){var a=t.value,r=e.multiple;if(!r||Array.isArray(a)){for(var s,i,o=0,d=e.options.length;o<d;o++)if(i=e.options[o],r)s=I(a,ji(i))>-1,i.selected!==s&&(i.selected=s);else if($(ji(i),a))return void(e.selectedIndex!==o&&(e.selectedIndex=o));r||(e.selectedIndex=-1)}}function Hi(e,t){return t.every((function(t){return!$(t,e)}))}function ji(e){return"_value"in e?e._value:e.value}function xi(e){e.target.composing=!0}function Oi(e){e.target.composing&&(e.target.composing=!1,Pi(e.target,"input"))}function Pi(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!0),e.dispatchEvent(n)}function Wi(e){return!e.componentInstance||e.data&&e.data.transition?e:Wi(e.componentInstance._vnode)}var Ai={bind:function(e,t,n){var a=t.value;n=Wi(n);var r=n.data&&n.data.transition,s=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;a&&r?(n.data.show=!0,pi(n,(function(){e.style.display=s}))):e.style.display=a?s:"none"},update:function(e,t,n){var a=t.value,r=t.oldValue;if(!a!==!r){n=Wi(n);var s=n.data&&n.data.transition;s?(n.data.show=!0,a?pi(n,(function(){e.style.display=e.__vOriginalDisplay})):yi(n,(function(){e.style.display="none"}))):e.style.display=a?e.__vOriginalDisplay:"none"}},unbind:function(e,t,n,a,r){r||(e.style.display=e.__vOriginalDisplay)}},Ei={model:wi,show:Ai},Fi={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function Ci(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?Ci(Jt(t.children)):e}function zi(e){var t={},n=e.$options;for(var a in n.propsData)t[a]=e[a];var r=n._parentListeners;for(var a in r)t[b(a)]=r[a];return t}function $i(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}function Ii(e){while(e=e.parent)if(e.data.transition)return!0}function Ni(e,t){return t.key===e.key&&t.tag===e.tag}var Ji=function(e){return e.tag||bt(e)},Ri=function(e){return"show"===e.name},Ui={name:"transition",props:Fi,abstract:!0,render:function(e){var t=this,n=this.$slots.default;if(n&&(n=n.filter(Ji),n.length)){0;var a=this.mode;0;var r=n[0];if(Ii(this.$vnode))return r;var s=Ci(r);if(!s)return r;if(this._leaving)return $i(e,r);var i="__transition-".concat(this._uid,"-");s.key=null==s.key?s.isComment?i+"comment":i+s.tag:_(s.key)?0===String(s.key).indexOf(i)?s.key:i+s.key:s.key;var o=(s.data||(s.data={})).transition=zi(this),d=this._vnode,u=Ci(d);if(s.data.directives&&s.data.directives.some(Ri)&&(s.data.show=!0),u&&u.data&&!Ni(s,u)&&!bt(u)&&(!u.componentInstance||!u.componentInstance._vnode.isComment)){var l=u.data.transition=A({},o);if("out-in"===a)return this._leaving=!0,at(l,"afterLeave",(function(){t._leaving=!1,t.$forceUpdate()})),$i(e,r);if("in-out"===a){if(bt(s))return d;var m,c=function(){m()};at(o,"afterEnter",c),at(o,"enterCancelled",c),at(l,"delayLeave",(function(e){m=e}))}}return r}}},Gi=A({tag:String,moveClass:String},Fi);delete Gi.mode;var Vi={props:Gi,beforeMount:function(){var e=this,t=this._update;this._update=function(n,a){var r=bn(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,r(),t.call(e,n,a)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",n=Object.create(null),a=this.prevChildren=this.children,r=this.$slots.default||[],s=this.children=[],i=zi(this),o=0;o<r.length;o++){var d=r[o];if(d.tag)if(null!=d.key&&0!==String(d.key).indexOf("__vlist"))s.push(d),n[d.key]=d,(d.data||(d.data={})).transition=i;else;}if(a){var _=[],u=[];for(o=0;o<a.length;o++){d=a[o];d.data.transition=i,d.data.pos=d.elm.getBoundingClientRect(),n[d.key]?_.push(d):u.push(d)}this.kept=e(t,null,_),this.removed=u}return e(t,null,s)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(Bi),e.forEach(Ki),e.forEach(qi),this._reflow=document.body.offsetHeight,e.forEach((function(e){if(e.data.moved){var n=e.elm,a=n.style;ui(n,t),a.transform=a.WebkitTransform=a.transitionDuration="",n.addEventListener(si,n._moveCb=function e(a){a&&a.target!==n||a&&!/transform$/.test(a.propertyName)||(n.removeEventListener(si,e),n._moveCb=null,li(n,t))})}})))},methods:{hasMove:function(e,t){if(!ti)return!1;if(this._hasMove)return this._hasMove;var n=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach((function(e){Qs(n,e)})),Zs(n,t),n.style.display="none",this.$el.appendChild(n);var a=hi(n);return this.$el.removeChild(n),this._hasMove=a.hasTransform}}};function Bi(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function Ki(e){e.data.newPos=e.elm.getBoundingClientRect()}function qi(e){var t=e.data.pos,n=e.data.newPos,a=t.left-n.left,r=t.top-n.top;if(a||r){e.data.moved=!0;var s=e.elm.style;s.transform=s.WebkitTransform="translate(".concat(a,"px,").concat(r,"px)"),s.transitionDuration="0s"}}var Zi={Transition:Ui,TransitionGroup:Vi};Qa.config.mustUseProp=fr,Qa.config.isReservedTag=Wr,Qa.config.isReservedAttr=cr,Qa.config.getTagNamespace=Ar,Qa.config.isUnknownElement=Fr,A(Qa.options.directives,Ei),A(Qa.options.components,Zi),Qa.prototype.__patch__=ee?Ti:F,Qa.prototype.$mount=function(e,t){return e=e&&ee?zr(e):void 0,jn(this,e,t)},ee&&setTimeout((function(){V.devtools&&me&&me.emit("init",Qa)}),0)},5498:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}});return t}))},5533:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-üncü",4:"-üncü",100:"-üncü",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"},n=e.defineLocale("az",{months:"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr".split("_"),monthsShort:"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek".split("_"),weekdays:"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə".split("_"),weekdaysShort:"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən".split("_"),weekdaysMin:"Bz_BE_ÇA_Çə_CA_Cü_Şə".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[sabah saat] LT",nextWeek:"[gələn həftə] dddd [saat] LT",lastDay:"[dünən] LT",lastWeek:"[keçən həftə] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s əvvəl",s:"birneçə saniyə",ss:"%d saniyə",m:"bir dəqiqə",mm:"%d dəqiqə",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir il",yy:"%d il"},meridiemParse:/gecə|səhər|gündüz|axşam/,isPM:function(e){return/^(gündüz|axşam)$/.test(e)},meridiem:function(e,t,n){return e<4?"gecə":e<12?"səhər":e<17?"gündüz":"axşam"},dayOfMonthOrdinalParse:/\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,ordinal:function(e){if(0===e)return e+"-ıncı";var n=e%10,a=e%100-n,r=e>=100?100:null;return e+(t[n]||t[a]||t[r])},week:{dow:1,doy:7}});return n}))},5543:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-il",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}});return t}))},5719:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={words:{ss:["sekunda","sekunde","sekundi"],m:["jedan minut","jedne minute"],mm:["minut","minute","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mesec","meseca","meseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(e,t){return 1===e?t[0]:e>=2&&e<=4?t[1]:t[2]},translate:function(e,n,a){var r=t.words[a];return 1===a.length?n?r[0]:r[1]:e+" "+t.correctGrammaticalCase(e,r)}},n=e.defineLocale("sr",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljak_utorak_sreda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sre._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){var e=["[prošle] [nedelje] [u] LT","[prošlog] [ponedeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"];return e[this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"dan",dd:t.translate,M:"mesec",MM:t.translate,y:"godinu",yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return n}))},5766:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={ss:"sekundė_sekundžių_sekundes",m:"minutė_minutės_minutę",mm:"minutės_minučių_minutes",h:"valanda_valandos_valandą",hh:"valandos_valandų_valandas",d:"diena_dienos_dieną",dd:"dienos_dienų_dienas",M:"mėnuo_mėnesio_mėnesį",MM:"mėnesiai_mėnesių_mėnesius",y:"metai_metų_metus",yy:"metai_metų_metus"};function n(e,t,n,a){return t?"kelios sekundės":a?"kelių sekundžių":"kelias sekundes"}function a(e,t,n,a){return t?s(n)[0]:a?s(n)[1]:s(n)[2]}function r(e){return e%10===0||e>10&&e<20}function s(e){return t[e].split("_")}function i(e,t,n,i){var o=e+" ";return 1===e?o+a(e,t,n[0],i):t?o+(r(e)?s(n)[1]:s(n)[0]):i?o+s(n)[1]:o+(r(e)?s(n)[1]:s(n)[2])}var o=e.defineLocale("lt",{months:{format:"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio".split("_"),standalone:"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis".split("_"),isFormat:/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?|MMMM?(\[[^\[\]]*\]|\s)+D[oD]?/},monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:{format:"sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį".split("_"),standalone:"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis".split("_"),isFormat:/dddd HH:mm/},weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Šeš".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Š".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], HH:mm [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], HH:mm [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]"},calendar:{sameDay:"[Šiandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[Praėjusį] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieš %s",s:n,ss:i,m:a,mm:i,h:a,hh:i,d:a,dd:i,M:a,MM:i,y:a,yy:i},dayOfMonthOrdinalParse:/\d{1,2}-oji/,ordinal:function(e){return e+"-oji"},week:{dow:1,doy:4}});return o}))},5784:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="január_február_marec_apríl_máj_jún_júl_august_september_október_november_december".split("_"),n="jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec".split("_");function a(e){return e>1&&e<5}function r(e,t,n,r){var s=e+" ";switch(n){case"s":return t||r?"pár sekúnd":"pár sekundami";case"ss":return t||r?s+(a(e)?"sekundy":"sekúnd"):s+"sekundami";case"m":return t?"minúta":r?"minútu":"minútou";case"mm":return t||r?s+(a(e)?"minúty":"minút"):s+"minútami";case"h":return t?"hodina":r?"hodinu":"hodinou";case"hh":return t||r?s+(a(e)?"hodiny":"hodín"):s+"hodinami";case"d":return t||r?"deň":"dňom";case"dd":return t||r?s+(a(e)?"dni":"dní"):s+"dňami";case"M":return t||r?"mesiac":"mesiacom";case"MM":return t||r?s+(a(e)?"mesiace":"mesiacov"):s+"mesiacmi";case"y":return t||r?"rok":"rokom";case"yy":return t||r?s+(a(e)?"roky":"rokov"):s+"rokmi"}}var s=e.defineLocale("sk",{months:t,monthsShort:n,weekdays:"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_št_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_št_pi_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeľu o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo štvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[včera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulú nedeľu o] LT";case 1:case 2:return"[minulý] dddd [o] LT";case 3:return"[minulú stredu o] LT";case 4:case 5:return"[minulý] dddd [o] LT";case 6:return"[minulú sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:r,ss:r,m:r,mm:r,h:r,hh:r,d:r,dd:r,M:r,MM:r,y:r,yy:r},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return s}))},5802:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("th",{months:"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),monthsShort:"ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),monthsParseExact:!0,weekdays:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),weekdaysShort:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),weekdaysMin:"อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY เวลา H:mm",LLLL:"วันddddที่ D MMMM YYYY เวลา H:mm"},meridiemParse:/ก่อนเที่ยง|หลังเที่ยง/,isPM:function(e){return"หลังเที่ยง"===e},meridiem:function(e,t,n){return e<12?"ก่อนเที่ยง":"หลังเที่ยง"},calendar:{sameDay:"[วันนี้ เวลา] LT",nextDay:"[พรุ่งนี้ เวลา] LT",nextWeek:"dddd[หน้า เวลา] LT",lastDay:"[เมื่อวานนี้ เวลา] LT",lastWeek:"[วัน]dddd[ที่แล้ว เวลา] LT",sameElse:"L"},relativeTime:{future:"อีก %s",past:"%sที่แล้ว",s:"ไม่กี่วินาที",ss:"%d วินาที",m:"1 นาที",mm:"%d นาที",h:"1 ชั่วโมง",hh:"%d ชั่วโมง",d:"1 วัน",dd:"%d วัน",M:"1 เดือน",MM:"%d เดือน",y:"1 ปี",yy:"%d ปี"}});return t}))},5867:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("lo",{months:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),monthsShort:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),weekdays:"ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysShort:"ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysMin:"ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"ວັນdddd D MMMM YYYY HH:mm"},meridiemParse:/ຕອນເຊົ້າ|ຕອນແລງ/,isPM:function(e){return"ຕອນແລງ"===e},meridiem:function(e,t,n){return e<12?"ຕອນເຊົ້າ":"ຕອນແລງ"},calendar:{sameDay:"[ມື້ນີ້ເວລາ] LT",nextDay:"[ມື້ອື່ນເວລາ] LT",nextWeek:"[ວັນ]dddd[ໜ້າເວລາ] LT",lastDay:"[ມື້ວານນີ້ເວລາ] LT",lastWeek:"[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT",sameElse:"L"},relativeTime:{future:"ອີກ %s",past:"%sຜ່ານມາ",s:"ບໍ່ເທົ່າໃດວິນາທີ",ss:"%d ວິນາທີ",m:"1 ນາທີ",mm:"%d ນາທີ",h:"1 ຊົ່ວໂມງ",hh:"%d ຊົ່ວໂມງ",d:"1 ມື້",dd:"%d ມື້",M:"1 ເດືອນ",MM:"%d ເດືອນ",y:"1 ປີ",yy:"%d ປີ"},dayOfMonthOrdinalParse:/(ທີ່)\d{1,2}/,ordinal:function(e){return"ທີ່"+e}});return t}))},6e3:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ss",{months:"Bhimbidvwane_Indlovana_Indlov'lenkhulu_Mabasa_Inkhwekhweti_Inhlaba_Kholwane_Ingci_Inyoni_Imphala_Lweti_Ingongoni".split("_"),monthsShort:"Bhi_Ina_Inu_Mab_Ink_Inh_Kho_Igc_Iny_Imp_Lwe_Igo".split("_"),weekdays:"Lisontfo_Umsombuluko_Lesibili_Lesitsatfu_Lesine_Lesihlanu_Umgcibelo".split("_"),weekdaysShort:"Lis_Umb_Lsb_Les_Lsi_Lsh_Umg".split("_"),weekdaysMin:"Li_Us_Lb_Lt_Ls_Lh_Ug".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Namuhla nga] LT",nextDay:"[Kusasa nga] LT",nextWeek:"dddd [nga] LT",lastDay:"[Itolo nga] LT",lastWeek:"dddd [leliphelile] [nga] LT",sameElse:"L"},relativeTime:{future:"nga %s",past:"wenteka nga %s",s:"emizuzwana lomcane",ss:"%d mzuzwana",m:"umzuzu",mm:"%d emizuzu",h:"lihora",hh:"%d emahora",d:"lilanga",dd:"%d emalanga",M:"inyanga",MM:"%d tinyanga",y:"umnyaka",yy:"%d iminyaka"},meridiemParse:/ekuseni|emini|entsambama|ebusuku/,meridiem:function(e,t,n){return e<11?"ekuseni":e<15?"emini":e<19?"entsambama":"ebusuku"},meridiemHour:function(e,t){return 12===e&&(e=0),"ekuseni"===t?e:"emini"===t?e>=11?e:e+12:"entsambama"===t||"ebusuku"===t?0===e?0:e+12:void 0},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:"%d",week:{dow:1,doy:4}});return t}))},6111:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[n][0]:r[n][1]}var n=e.defineLocale("de-at",{months:"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jän._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return n}))},6169:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},n={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};function a(e,t,n,a){var r="";if(t)switch(n){case"s":r="काही सेकंद";break;case"ss":r="%d सेकंद";break;case"m":r="एक मिनिट";break;case"mm":r="%d मिनिटे";break;case"h":r="एक तास";break;case"hh":r="%d तास";break;case"d":r="एक दिवस";break;case"dd":r="%d दिवस";break;case"M":r="एक महिना";break;case"MM":r="%d महिने";break;case"y":r="एक वर्ष";break;case"yy":r="%d वर्षे";break}else switch(n){case"s":r="काही सेकंदां";break;case"ss":r="%d सेकंदां";break;case"m":r="एका मिनिटा";break;case"mm":r="%d मिनिटां";break;case"h":r="एका तासा";break;case"hh":r="%d तासां";break;case"d":r="एका दिवसा";break;case"dd":r="%d दिवसां";break;case"M":r="एका महिन्या";break;case"MM":r="%d महिन्यां";break;case"y":r="एका वर्षा";break;case"yy":r="%d वर्षां";break}return r.replace(/%d/i,e)}var r=e.defineLocale("mr",{months:"जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),monthsShort:"जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm वाजता",LTS:"A h:mm:ss वाजता",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm वाजता",LLLL:"dddd, D MMMM YYYY, A h:mm वाजता"},calendar:{sameDay:"[आज] LT",nextDay:"[उद्या] LT",nextWeek:"dddd, LT",lastDay:"[काल] LT",lastWeek:"[मागील] dddd, LT",sameElse:"L"},relativeTime:{future:"%sमध्ये",past:"%sपूर्वी",s:a,ss:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/रात्री|सकाळी|दुपारी|सायंकाळी/,meridiemHour:function(e,t){return 12===e&&(e=0),"रात्री"===t?e<4?e:e+12:"सकाळी"===t?e:"दुपारी"===t?e>=10?e:e+12:"सायंकाळी"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"रात्री":e<10?"सकाळी":e<17?"दुपारी":e<20?"सायंकाळी":"रात्री"},week:{dow:0,doy:6}});return r}))},6195:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},6308:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n){var a=e+" ";switch(n){case"ss":return a+=1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi",a;case"m":return t?"jedna minuta":"jedne minute";case"mm":return a+=1===e?"minuta":2===e||3===e||4===e?"minute":"minuta",a;case"h":return t?"jedan sat":"jednog sata";case"hh":return a+=1===e?"sat":2===e||3===e||4===e?"sata":"sati",a;case"dd":return a+=1===e?"dan":"dana",a;case"MM":return a+=1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci",a;case"yy":return a+=1===e?"godina":2===e||3===e||4===e?"godine":"godina",a}}var n=e.defineLocale("hr",{months:{format:"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split("_"),standalone:"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_")},monthsShort:"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:t,m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return n}))},6435:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}}});return t}))},6464:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"),n="led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_"),a=[/^led/i,/^úno/i,/^bře/i,/^dub/i,/^kvě/i,/^(čvn|červen$|června)/i,/^(čvc|červenec|července)/i,/^srp/i,/^zář/i,/^říj/i,/^lis/i,/^pro/i],r=/^(leden|únor|březen|duben|květen|červenec|července|červen|června|srpen|září|říjen|listopad|prosinec|led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i;function s(e){return e>1&&e<5&&1!==~~(e/10)}function i(e,t,n,a){var r=e+" ";switch(n){case"s":return t||a?"pár sekund":"pár sekundami";case"ss":return t||a?r+(s(e)?"sekundy":"sekund"):r+"sekundami";case"m":return t?"minuta":a?"minutu":"minutou";case"mm":return t||a?r+(s(e)?"minuty":"minut"):r+"minutami";case"h":return t?"hodina":a?"hodinu":"hodinou";case"hh":return t||a?r+(s(e)?"hodiny":"hodin"):r+"hodinami";case"d":return t||a?"den":"dnem";case"dd":return t||a?r+(s(e)?"dny":"dní"):r+"dny";case"M":return t||a?"měsíc":"měsícem";case"MM":return t||a?r+(s(e)?"měsíce":"měsíců"):r+"měsíci";case"y":return t||a?"rok":"rokem";case"yy":return t||a?r+(s(e)?"roky":"let"):r+"lety"}}var o=e.defineLocale("cs",{months:t,monthsShort:n,monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(leden|ledna|února|únor|březen|března|duben|dubna|květen|května|červenec|července|červen|června|srpen|srpna|září|říjen|října|listopadu|listopad|prosinec|prosince)/i,monthsShortStrictRegex:/^(led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zítra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v neděli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve středu v] LT";case 4:return"[ve čtvrtek v] LT";case 5:return"[v pátek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[včera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou neděli v] LT";case 1:case 2:return"[minulé] dddd [v] LT";case 3:return"[minulou středu v] LT";case 4:case 5:return"[minulý] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"před %s",s:i,ss:i,m:i,mm:i,h:i,hh:i,d:i,dd:i,M:i,MM:i,y:i,yy:i},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return o}))},6571:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={0:"-ум",1:"-ум",2:"-юм",3:"-юм",4:"-ум",5:"-ум",6:"-ум",7:"-ум",8:"-ум",9:"-ум",10:"-ум",12:"-ум",13:"-ум",20:"-ум",30:"-юм",40:"-ум",50:"-ум",60:"-ум",70:"-ум",80:"-ум",90:"-ум",100:"-ум"},n=e.defineLocale("tg",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе".split("_"),weekdaysShort:"яшб_дшб_сшб_чшб_пшб_ҷум_шнб".split("_"),weekdaysMin:"яш_дш_сш_чш_пш_ҷм_шб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Имрӯз соати] LT",nextDay:"[Пагоҳ соати] LT",lastDay:"[Дирӯз соати] LT",nextWeek:"dddd[и] [ҳафтаи оянда соати] LT",lastWeek:"dddd[и] [ҳафтаи гузашта соати] LT",sameElse:"L"},relativeTime:{future:"баъди %s",past:"%s пеш",s:"якчанд сония",m:"як дақиқа",mm:"%d дақиқа",h:"як соат",hh:"%d соат",d:"як рӯз",dd:"%d рӯз",M:"як моҳ",MM:"%d моҳ",y:"як сол",yy:"%d сол"},meridiemParse:/шаб|субҳ|рӯз|бегоҳ/,meridiemHour:function(e,t){return 12===e&&(e=0),"шаб"===t?e<4?e:e+12:"субҳ"===t?e:"рӯз"===t?e>=11?e:e+12:"бегоҳ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"шаб":e<11?"субҳ":e<16?"рӯз":e<19?"бегоҳ":"шаб"},dayOfMonthOrdinalParse:/\d{1,2}-(ум|юм)/,ordinal:function(e){var n=e%10,a=e>=100?100:null;return e+(t[e]||t[n]||t[a])},week:{dow:1,doy:7}});return n}))},6575:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,s=e.defineLocale("es-us",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"MM/DD/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:0,doy:6}});return s}))},6584:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-ie",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},6618:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t){var n=e.split("_");return t%10===1&&t%100!==11?n[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?n[1]:n[2]}function n(e,n,a){var r={ss:n?"секунда_секунди_секунд":"секунду_секунди_секунд",mm:n?"хвилина_хвилини_хвилин":"хвилину_хвилини_хвилин",hh:n?"година_години_годин":"годину_години_годин",dd:"день_дні_днів",MM:"місяць_місяці_місяців",yy:"рік_роки_років"};return"m"===a?n?"хвилина":"хвилину":"h"===a?n?"година":"годину":e+" "+t(r[a],+e)}function a(e,t){var n={nominative:"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"),accusative:"неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу".split("_"),genitive:"неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи".split("_")};if(!0===e)return n["nominative"].slice(1,7).concat(n["nominative"].slice(0,1));if(!e)return n["nominative"];var a=/(\[[ВвУу]\]) ?dddd/.test(t)?"accusative":/\[?(?:минулої|наступної)? ?\] ?dddd/.test(t)?"genitive":"nominative";return n[a][e.day()]}function r(e){return function(){return e+"о"+(11===this.hours()?"б":"")+"] LT"}}var s=e.defineLocale("uk",{months:{format:"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"),standalone:"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_")},monthsShort:"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"),weekdays:a,weekdaysShort:"нд_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY р.",LLL:"D MMMM YYYY р., HH:mm",LLLL:"dddd, D MMMM YYYY р., HH:mm"},calendar:{sameDay:r("[Сьогодні "),nextDay:r("[Завтра "),lastDay:r("[Вчора "),nextWeek:r("[У] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return r("[Минулої] dddd [").call(this);case 1:case 2:case 4:return r("[Минулого] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"за %s",past:"%s тому",s:"декілька секунд",ss:n,m:n,mm:n,h:"годину",hh:n,d:"день",dd:n,M:"місяць",MM:n,y:"рік",yy:n},meridiemParse:/ночі|ранку|дня|вечора/,isPM:function(e){return/^(дня|вечора)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночі":e<12?"ранку":e<17?"дня":"вечора"},dayOfMonthOrdinalParse:/\d{1,2}-(й|го)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e+"-й";case"D":return e+"-го";default:return e}},week:{dow:1,doy:7}});return s}))},6637:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r=e+" ";switch(n){case"s":return t||a?"nekaj sekund":"nekaj sekundami";case"ss":return r+=1===e?t?"sekundo":"sekundi":2===e?t||a?"sekundi":"sekundah":e<5?t||a?"sekunde":"sekundah":"sekund",r;case"m":return t?"ena minuta":"eno minuto";case"mm":return r+=1===e?t?"minuta":"minuto":2===e?t||a?"minuti":"minutama":e<5?t||a?"minute":"minutami":t||a?"minut":"minutami",r;case"h":return t?"ena ura":"eno uro";case"hh":return r+=1===e?t?"ura":"uro":2===e?t||a?"uri":"urama":e<5?t||a?"ure":"urami":t||a?"ur":"urami",r;case"d":return t||a?"en dan":"enim dnem";case"dd":return r+=1===e?t||a?"dan":"dnem":2===e?t||a?"dni":"dnevoma":t||a?"dni":"dnevi",r;case"M":return t||a?"en mesec":"enim mesecem";case"MM":return r+=1===e?t||a?"mesec":"mesecem":2===e?t||a?"meseca":"mesecema":e<5?t||a?"mesece":"meseci":t||a?"mesecev":"meseci",r;case"y":return t||a?"eno leto":"enim letom";case"yy":return r+=1===e?t||a?"leto":"letom":2===e?t||a?"leti":"letoma":e<5?t||a?"leta":"leti":t||a?"let":"leti",r}}var n=e.defineLocale("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[včeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:return"[prejšnjo] [nedeljo] [ob] LT";case 3:return"[prejšnjo] [sredo] [ob] LT";case 6:return"[prejšnjo] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[prejšnji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"čez %s",past:"pred %s",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return n}))},6794:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj".split("_"),weekdays:"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë".split("_"),weekdaysShort:"Die_Hën_Mar_Mër_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_Më_E_P_Sh".split("_"),weekdaysParseExact:!0,meridiemParse:/PD|MD/,isPM:function(e){return"M"===e.charAt(0)},meridiem:function(e,t,n){return e<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Sot në] LT",nextDay:"[Nesër në] LT",nextWeek:"dddd [në] LT",lastDay:"[Dje në] LT",lastWeek:"dddd [e kaluar në] LT",sameElse:"L"},relativeTime:{future:"në %s",past:"%s më parë",s:"disa sekonda",ss:"%d sekonda",m:"një minutë",mm:"%d minuta",h:"një orë",hh:"%d orë",d:"një ditë",dd:"%d ditë",M:"një muaj",MM:"%d muaj",y:"një vit",yy:"%d vite"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return t}))},6870:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"១",2:"២",3:"៣",4:"៤",5:"៥",6:"៦",7:"៧",8:"៨",9:"៩",0:"០"},n={"១":"1","២":"2","៣":"3","៤":"4","៥":"5","៦":"6","៧":"7","៨":"8","៩":"9","០":"0"},a=e.defineLocale("km",{months:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),monthsShort:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),weekdays:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysShort:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysMin:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ព្រឹក|ល្ងាច/,isPM:function(e){return"ល្ងាច"===e},meridiem:function(e,t,n){return e<12?"ព្រឹក":"ល្ងាច"},calendar:{sameDay:"[ថ្ងៃនេះ ម៉ោង] LT",nextDay:"[ស្អែក ម៉ោង] LT",nextWeek:"dddd [ម៉ោង] LT",lastDay:"[ម្សិលមិញ ម៉ោង] LT",lastWeek:"dddd [សប្តាហ៍មុន] [ម៉ោង] LT",sameElse:"L"},relativeTime:{future:"%sទៀត",past:"%sមុន",s:"ប៉ុន្មានវិនាទី",ss:"%d វិនាទី",m:"មួយនាទី",mm:"%d នាទី",h:"មួយម៉ោង",hh:"%d ម៉ោង",d:"មួយថ្ងៃ",dd:"%d ថ្ងៃ",M:"មួយខែ",MM:"%d ខែ",y:"មួយឆ្នាំ",yy:"%d ឆ្នាំ"},dayOfMonthOrdinalParse:/ទី\d{1,2}/,ordinal:"ទី%d",preparse:function(e){return e.replace(/[១២៣៤៥៦៧៨៩០]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},week:{dow:1,doy:4}});return a}))},7071:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.".split("_"),n="jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),a=e.defineLocale("fy",{months:"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsParseExact:!0,weekdays:"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon".split("_"),weekdaysShort:"si._mo._ti._wo._to._fr._so.".split("_"),weekdaysMin:"Si_Mo_Ti_Wo_To_Fr_So".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[hjoed om] LT",nextDay:"[moarn om] LT",nextWeek:"dddd [om] LT",lastDay:"[juster om] LT",lastWeek:"[ôfrûne] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oer %s",past:"%s lyn",s:"in pear sekonden",ss:"%d sekonden",m:"ien minút",mm:"%d minuten",h:"ien oere",hh:"%d oeren",d:"ien dei",dd:"%d dagen",M:"ien moanne",MM:"%d moannen",y:"ien jier",yy:"%d jierren"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}});return a}))},7075:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("mt",{months:"Jannar_Frar_Marzu_April_Mejju_Ġunju_Lulju_Awwissu_Settembru_Ottubru_Novembru_Diċembru".split("_"),monthsShort:"Jan_Fra_Mar_Apr_Mej_Ġun_Lul_Aww_Set_Ott_Nov_Diċ".split("_"),weekdays:"Il-Ħadd_It-Tnejn_It-Tlieta_L-Erbgħa_Il-Ħamis_Il-Ġimgħa_Is-Sibt".split("_"),weekdaysShort:"Ħad_Tne_Tli_Erb_Ħam_Ġim_Sib".split("_"),weekdaysMin:"Ħa_Tn_Tl_Er_Ħa_Ġi_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Illum fil-]LT",nextDay:"[Għada fil-]LT",nextWeek:"dddd [fil-]LT",lastDay:"[Il-bieraħ fil-]LT",lastWeek:"dddd [li għadda] [fil-]LT",sameElse:"L"},relativeTime:{future:"f’ %s",past:"%s ilu",s:"ftit sekondi",ss:"%d sekondi",m:"minuta",mm:"%d minuti",h:"siegħa",hh:"%d siegħat",d:"ġurnata",dd:"%d ġranet",M:"xahar",MM:"%d xhur",y:"sena",yy:"%d sni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return t}))},7088:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={0:"-ші",1:"-ші",2:"-ші",3:"-ші",4:"-ші",5:"-ші",6:"-шы",7:"-ші",8:"-ші",9:"-шы",10:"-шы",20:"-шы",30:"-шы",40:"-шы",50:"-ші",60:"-шы",70:"-ші",80:"-ші",90:"-шы",100:"-ші"},n=e.defineLocale("kk",{months:"қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан".split("_"),monthsShort:"қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел".split("_"),weekdays:"жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі".split("_"),weekdaysShort:"жек_дүй_сей_сәр_бей_жұм_сен".split("_"),weekdaysMin:"жк_дй_сй_ср_бй_жм_сн".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгін сағат] LT",nextDay:"[Ертең сағат] LT",nextWeek:"dddd [сағат] LT",lastDay:"[Кеше сағат] LT",lastWeek:"[Өткен аптаның] dddd [сағат] LT",sameElse:"L"},relativeTime:{future:"%s ішінде",past:"%s бұрын",s:"бірнеше секунд",ss:"%d секунд",m:"бір минут",mm:"%d минут",h:"бір сағат",hh:"%d сағат",d:"бір күн",dd:"%d күн",M:"бір ай",MM:"%d ай",y:"бір жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(ші|шы)/,ordinal:function(e){var n=e%10,a=e>=100?100:null;return e+(t[e]||t[n]||t[a])},week:{dow:1,doy:7}});return n}))},7160:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("hy-am",{months:{format:"հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի".split("_"),standalone:"հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր".split("_")},monthsShort:"հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ".split("_"),weekdays:"կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ".split("_"),weekdaysShort:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),weekdaysMin:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY թ.",LLL:"D MMMM YYYY թ., HH:mm",LLLL:"dddd, D MMMM YYYY թ., HH:mm"},calendar:{sameDay:"[այսօր] LT",nextDay:"[վաղը] LT",lastDay:"[երեկ] LT",nextWeek:function(){return"dddd [օրը ժամը] LT"},lastWeek:function(){return"[անցած] dddd [օրը ժամը] LT"},sameElse:"L"},relativeTime:{future:"%s հետո",past:"%s առաջ",s:"մի քանի վայրկյան",ss:"%d վայրկյան",m:"րոպե",mm:"%d րոպե",h:"ժամ",hh:"%d ժամ",d:"օր",dd:"%d օր",M:"ամիս",MM:"%d ամիս",y:"տարի",yy:"%d տարի"},meridiemParse:/գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,isPM:function(e){return/^(ցերեկվա|երեկոյան)$/.test(e)},meridiem:function(e){return e<4?"գիշերվա":e<12?"առավոտվա":e<17?"ցերեկվա":"երեկոյան"},dayOfMonthOrdinalParse:/\d{1,2}|\d{1,2}-(ին|րդ)/,ordinal:function(e,t){switch(t){case"DDD":case"w":case"W":case"DDDo":return 1===e?e+"-ին":e+"-րդ";default:return e}},week:{dow:1,doy:7}});return t}))},7306:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ca",{months:{standalone:"gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),format:"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.".split("_"),monthsParseExact:!0,weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dt_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a les] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a les] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:function(){return"[avui a "+(1!==this.hours()?"les":"la")+"] LT"},nextDay:function(){return"[demà a "+(1!==this.hours()?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+(1!==this.hours()?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+(1!==this.hours()?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+(1!==this.hours()?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"uns segons",ss:"%d segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(e,t){var n=1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è";return"w"!==t&&"W"!==t||(n="a"),e+n},week:{dow:1,doy:4}});return t}))},7329:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("gl",{months:"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro".split("_"),monthsShort:"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"domingo_luns_martes_mércores_xoves_venres_sábado".split("_"),weekdaysShort:"dom._lun._mar._mér._xov._ven._sáb.".split("_"),weekdaysMin:"do_lu_ma_mé_xo_ve_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoxe "+(1!==this.hours()?"ás":"á")+"] LT"},nextDay:function(){return"[mañá "+(1!==this.hours()?"ás":"á")+"] LT"},nextWeek:function(){return"dddd ["+(1!==this.hours()?"ás":"a")+"] LT"},lastDay:function(){return"[onte "+(1!==this.hours()?"á":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+(1!==this.hours()?"ás":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(e){return 0===e.indexOf("un")?"n"+e:"en "+e},past:"hai %s",s:"uns segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return t}))},7538:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("si",{months:"ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්".split("_"),monthsShort:"ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ".split("_"),weekdays:"ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා".split("_"),weekdaysShort:"ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන".split("_"),weekdaysMin:"ඉ_ස_අ_බ_බ්‍ර_සි_සෙ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"a h:mm",LTS:"a h:mm:ss",L:"YYYY/MM/DD",LL:"YYYY MMMM D",LLL:"YYYY MMMM D, a h:mm",LLLL:"YYYY MMMM D [වැනි] dddd, a h:mm:ss"},calendar:{sameDay:"[අද] LT[ට]",nextDay:"[හෙට] LT[ට]",nextWeek:"dddd LT[ට]",lastDay:"[ඊයේ] LT[ට]",lastWeek:"[පසුගිය] dddd LT[ට]",sameElse:"L"},relativeTime:{future:"%sකින්",past:"%sකට පෙර",s:"තත්පර කිහිපය",ss:"තත්පර %d",m:"මිනිත්තුව",mm:"මිනිත්තු %d",h:"පැය",hh:"පැය %d",d:"දිනය",dd:"දින %d",M:"මාසය",MM:"මාස %d",y:"වසර",yy:"වසර %d"},dayOfMonthOrdinalParse:/\d{1,2} වැනි/,ordinal:function(e){return e+" වැනි"},meridiemParse:/පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,isPM:function(e){return"ප.ව."===e||"පස් වරු"===e},meridiem:function(e,t,n){return e>11?n?"ප.ව.":"පස් වරු":n?"පෙ.ව.":"පෙර වරු"}});return t}))},7609:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("uz",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба".split("_"),weekdaysShort:"Якш_Душ_Сеш_Чор_Пай_Жум_Шан".split("_"),weekdaysMin:"Як_Ду_Се_Чо_Па_Жу_Ша".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Бугун соат] LT [да]",nextDay:"[Эртага] LT [да]",nextWeek:"dddd [куни соат] LT [да]",lastDay:"[Кеча соат] LT [да]",lastWeek:"[Утган] dddd [куни соат] LT [да]",sameElse:"L"},relativeTime:{future:"Якин %s ичида",past:"Бир неча %s олдин",s:"фурсат",ss:"%d фурсат",m:"бир дакика",mm:"%d дакика",h:"бир соат",hh:"%d соат",d:"бир кун",dd:"%d кун",M:"бир ой",MM:"%d ой",y:"бир йил",yy:"%d йил"},week:{dow:1,doy:7}});return t}))},7650:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,s=e.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return s}))},7711:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("tzm-latn",{months:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",ss:"%d imik",m:"minuḍ",mm:"%d minuḍ",h:"saɛa",hh:"%d tassaɛin",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}});return t}))},7777:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("bg",{months:"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември".split("_"),monthsShort:"янр_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек".split("_"),weekdays:"неделя_понеделник_вторник_сряда_четвъртък_петък_събота".split("_"),weekdaysShort:"нед_пон_вто_сря_чет_пет_съб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Днес в] LT",nextDay:"[Утре в] LT",nextWeek:"dddd [в] LT",lastDay:"[Вчера в] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[В изминалата] dddd [в] LT";case 1:case 2:case 4:case 5:return"[В изминалия] dddd [в] LT"}},sameElse:"L"},relativeTime:{future:"след %s",past:"преди %s",s:"няколко секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дни",M:"месец",MM:"%d месеца",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(e){var t=e%10,n=e%100;return 0===e?e+"-ев":0===n?e+"-ен":n>10&&n<20?e+"-ти":1===t?e+"-ви":2===t?e+"-ри":7===t||8===t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}});return t}))},7853:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[n][0]:r[n][1]}var n=e.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return n}))},7892:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("fr-ch",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}});return t}))},8076:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={words:{ss:["sekund","sekunda","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mjesec","mjeseca","mjeseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(e,t){return 1===e?t[0]:e>=2&&e<=4?t[1]:t[2]},translate:function(e,n,a){var r=t.words[a];return 1===a.length?n?r[0]:r[1]:e+" "+t.correctGrammaticalCase(e,r)}},n=e.defineLocale("me",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sjutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){var e=["[prošle] [nedjelje] [u] LT","[prošlog] [ponedjeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srijede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"];return e[this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"nekoliko sekundi",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"dan",dd:t.translate,M:"mjesec",MM:t.translate,y:"godinu",yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});return n}))},8174:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},n={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},a=["کانونی دووەم","شوبات","ئازار","نیسان","ئایار","حوزەیران","تەمموز","ئاب","ئەیلوول","تشرینی یەكەم","تشرینی دووەم","كانونی یەکەم"],r=e.defineLocale("ku",{months:a,monthsShort:a,weekdays:"یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌".split("_"),weekdaysShort:"یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌".split("_"),weekdaysMin:"ی_د_س_چ_پ_ه_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ئێواره‌|به‌یانی/,isPM:function(e){return/ئێواره‌/.test(e)},meridiem:function(e,t,n){return e<12?"به‌یانی":"ئێواره‌"},calendar:{sameDay:"[ئه‌مرۆ كاتژمێر] LT",nextDay:"[به‌یانی كاتژمێر] LT",nextWeek:"dddd [كاتژمێر] LT",lastDay:"[دوێنێ كاتژمێر] LT",lastWeek:"dddd [كاتژمێر] LT",sameElse:"L"},relativeTime:{future:"له‌ %s",past:"%s",s:"چه‌ند چركه‌یه‌ك",ss:"چركه‌ %d",m:"یه‌ك خوله‌ك",mm:"%d خوله‌ك",h:"یه‌ك كاتژمێر",hh:"%d كاتژمێر",d:"یه‌ك ڕۆژ",dd:"%d ڕۆژ",M:"یه‌ك مانگ",MM:"%d مانگ",y:"یه‌ك ساڵ",yy:"%d ساڵ"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}});return r}))},8235:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},n={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},a=e.defineLocale("ne",{months:"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मई_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर".split("_"),monthsShort:"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.".split("_"),monthsParseExact:!0,weekdays:"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार".split("_"),weekdaysShort:"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.".split("_"),weekdaysMin:"आ._सो._मं._बु._बि._शु._श.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"Aको h:mm बजे",LTS:"Aको h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, Aको h:mm बजे",LLLL:"dddd, D MMMM YYYY, Aको h:mm बजे"},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/राति|बिहान|दिउँसो|साँझ/,meridiemHour:function(e,t){return 12===e&&(e=0),"राति"===t?e<4?e:e+12:"बिहान"===t?e:"दिउँसो"===t?e>=10?e:e+12:"साँझ"===t?e+12:void 0},meridiem:function(e,t,n){return e<3?"राति":e<12?"बिहान":e<16?"दिउँसो":e<20?"साँझ":"राति"},calendar:{sameDay:"[आज] LT",nextDay:"[भोलि] LT",nextWeek:"[आउँदो] dddd[,] LT",lastDay:"[हिजो] LT",lastWeek:"[गएको] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sमा",past:"%s अगाडि",s:"केही क्षण",ss:"%d सेकेण्ड",m:"एक मिनेट",mm:"%d मिनेट",h:"एक घण्टा",hh:"%d घण्टा",d:"एक दिन",dd:"%d दिन",M:"एक महिना",MM:"%d महिना",y:"एक बर्ष",yy:"%d बर्ष"},week:{dow:0,doy:6}});return a}))},8298:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"YYYY-MM-DD",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}});return t}))},8303:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("pt-br",{months:"Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº"});return t}))},8383:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(e){return(/^[0-9].+$/.test(e)?"tra":"in")+" "+e},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return t}))},8414:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ug-cn",{months:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),monthsShort:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),weekdays:"يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە".split("_"),weekdaysShort:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),weekdaysMin:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY-يىلىM-ئاينىڭD-كۈنى",LLL:"YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm",LLLL:"dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm"},meridiemParse:/يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,meridiemHour:function(e,t){return 12===e&&(e=0),"يېرىم كېچە"===t||"سەھەر"===t||"چۈشتىن بۇرۇن"===t?e:"چۈشتىن كېيىن"===t||"كەچ"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,n){var a=100*e+t;return a<600?"يېرىم كېچە":a<900?"سەھەر":a<1130?"چۈشتىن بۇرۇن":a<1230?"چۈش":a<1800?"چۈشتىن كېيىن":"كەچ"},calendar:{sameDay:"[بۈگۈن سائەت] LT",nextDay:"[ئەتە سائەت] LT",nextWeek:"[كېلەركى] dddd [سائەت] LT",lastDay:"[تۆنۈگۈن] LT",lastWeek:"[ئالدىنقى] dddd [سائەت] LT",sameElse:"L"},relativeTime:{future:"%s كېيىن",past:"%s بۇرۇن",s:"نەچچە سېكونت",ss:"%d سېكونت",m:"بىر مىنۇت",mm:"%d مىنۇت",h:"بىر سائەت",hh:"%d سائەت",d:"بىر كۈن",dd:"%d كۈن",M:"بىر ئاي",MM:"%d ئاي",y:"بىر يىل",yy:"%d يىل"},dayOfMonthOrdinalParse:/\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"-كۈنى";case"w":case"W":return e+"-ھەپتە";default:return e}},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:7}});return t}))},8474:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={0:"-чү",1:"-чи",2:"-чи",3:"-чү",4:"-чү",5:"-чи",6:"-чы",7:"-чи",8:"-чи",9:"-чу",10:"-чу",20:"-чы",30:"-чу",40:"-чы",50:"-чү",60:"-чы",70:"-чи",80:"-чи",90:"-чу",100:"-чү"},n=e.defineLocale("ky",{months:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_"),monthsShort:"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек".split("_"),weekdays:"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби".split("_"),weekdaysShort:"Жек_Дүй_Шей_Шар_Бей_Жум_Ише".split("_"),weekdaysMin:"Жк_Дй_Шй_Шр_Бй_Жм_Иш".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгүн саат] LT",nextDay:"[Эртең саат] LT",nextWeek:"dddd [саат] LT",lastDay:"[Кечээ саат] LT",lastWeek:"[Өткөн аптанын] dddd [күнү] [саат] LT",sameElse:"L"},relativeTime:{future:"%s ичинде",past:"%s мурун",s:"бирнече секунд",ss:"%d секунд",m:"бир мүнөт",mm:"%d мүнөт",h:"бир саат",hh:"%d саат",d:"бир күн",dd:"%d күн",M:"бир ай",MM:"%d ай",y:"бир жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(чи|чы|чү|чу)/,ordinal:function(e){var n=e%10,a=e>=100?100:null;return e+(t[e]||t[n]||t[a])},week:{dow:1,doy:7}});return n}))},8676:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ar-kw",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:12}});return t}))},8861:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("tet",{months:"Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu".split("_"),weekdaysShort:"Dom_Seg_Ters_Kua_Kint_Sest_Sab".split("_"),weekdaysMin:"Do_Seg_Te_Ku_Ki_Ses_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Ohin iha] LT",nextDay:"[Aban iha] LT",nextWeek:"dddd [iha] LT",lastDay:"[Horiseik iha] LT",lastWeek:"dddd [semana kotuk] [iha] LT",sameElse:"L"},relativeTime:{future:"iha %s",past:"%s liuba",s:"minutu balun",ss:"minutu %d",m:"minutu ida",mm:"minutu %d",h:"oras ida",hh:"oras %d",d:"loron ida",dd:"loron %d",M:"fulan ida",MM:"fulan %d",y:"tinan ida",yy:"tinan %d"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},8959:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t){var n=e.split("_");return t%10===1&&t%100!==11?n[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?n[1]:n[2]}function n(e,n,a){var r={ss:n?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:n?"хвіліна_хвіліны_хвілін":"хвіліну_хвіліны_хвілін",hh:n?"гадзіна_гадзіны_гадзін":"гадзіну_гадзіны_гадзін",dd:"дзень_дні_дзён",MM:"месяц_месяцы_месяцаў",yy:"год_гады_гадоў"};return"m"===a?n?"хвіліна":"хвіліну":"h"===a?n?"гадзіна":"гадзіну":e+" "+t(r[a],+e)}var a=e.defineLocale("be",{months:{format:"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня".split("_"),standalone:"студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань".split("_")},monthsShort:"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж".split("_"),weekdays:{format:"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу".split("_"),standalone:"нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота".split("_"),isFormat:/\[ ?[Ууў] ?(?:мінулую|наступную)? ?\] ?dddd/},weekdaysShort:"нд_пн_ат_ср_чц_пт_сб".split("_"),weekdaysMin:"нд_пн_ат_ср_чц_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сёння ў] LT",nextDay:"[Заўтра ў] LT",lastDay:"[Учора ў] LT",nextWeek:function(){return"[У] dddd [ў] LT"},lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return"[У мінулую] dddd [ў] LT";case 1:case 2:case 4:return"[У мінулы] dddd [ў] LT"}},sameElse:"L"},relativeTime:{future:"праз %s",past:"%s таму",s:"некалькі секунд",m:n,mm:n,h:n,hh:n,d:"дзень",dd:n,M:"месяц",MM:n,y:"год",yy:n},meridiemParse:/ночы|раніцы|дня|вечара/,isPM:function(e){return/^(дня|вечара)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночы":e<12?"раніцы":e<17?"дня":"вечара"},dayOfMonthOrdinalParse:/\d{1,2}-(і|ы|га)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e%10!==2&&e%10!==3||e%100===12||e%100===13?e+"-ы":e+"-і";case"D":return e+"-га";default:return e}},week:{dow:1,doy:7}});return a}))},9053:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){switch(n){case"s":return t?"хэдхэн секунд":"хэдхэн секундын";case"ss":return e+(t?" секунд":" секундын");case"m":case"mm":return e+(t?" минут":" минутын");case"h":case"hh":return e+(t?" цаг":" цагийн");case"d":case"dd":return e+(t?" өдөр":" өдрийн");case"M":case"MM":return e+(t?" сар":" сарын");case"y":case"yy":return e+(t?" жил":" жилийн");default:return e}}var n=e.defineLocale("mn",{months:"Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар".split("_"),monthsShort:"1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар".split("_"),monthsParseExact:!0,weekdays:"Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба".split("_"),weekdaysShort:"Ням_Дав_Мяг_Лха_Пүр_Баа_Бям".split("_"),weekdaysMin:"Ня_Да_Мя_Лх_Пү_Ба_Бя".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY оны MMMMын D",LLL:"YYYY оны MMMMын D HH:mm",LLLL:"dddd, YYYY оны MMMMын D HH:mm"},meridiemParse:/ҮӨ|ҮХ/i,isPM:function(e){return"ҮХ"===e},meridiem:function(e,t,n){return e<12?"ҮӨ":"ҮХ"},calendar:{sameDay:"[Өнөөдөр] LT",nextDay:"[Маргааш] LT",nextWeek:"[Ирэх] dddd LT",lastDay:"[Өчигдөр] LT",lastWeek:"[Өнгөрсөн] dddd LT",sameElse:"L"},relativeTime:{future:"%s дараа",past:"%s өмнө",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2} өдөр/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+" өдөр";default:return e}}});return n}))},9231:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}});return t}))},9374:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e){return e%100===11||e%10!==1}function n(e,n,a,r){var s=e+" ";switch(a){case"s":return n||r?"nokkrar sekúndur":"nokkrum sekúndum";case"ss":return t(e)?s+(n||r?"sekúndur":"sekúndum"):s+"sekúnda";case"m":return n?"mínúta":"mínútu";case"mm":return t(e)?s+(n||r?"mínútur":"mínútum"):n?s+"mínúta":s+"mínútu";case"hh":return t(e)?s+(n||r?"klukkustundir":"klukkustundum"):s+"klukkustund";case"d":return n?"dagur":r?"dag":"degi";case"dd":return t(e)?n?s+"dagar":s+(r?"daga":"dögum"):n?s+"dagur":s+(r?"dag":"degi");case"M":return n?"mánuður":r?"mánuð":"mánuði";case"MM":return t(e)?n?s+"mánuðir":s+(r?"mánuði":"mánuðum"):n?s+"mánuður":s+(r?"mánuð":"mánuði");case"y":return n||r?"ár":"ári";case"yy":return t(e)?s+(n||r?"ár":"árum"):s+(n||r?"ár":"ári")}}var a=e.defineLocale("is",{months:"janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des".split("_"),weekdays:"sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur".split("_"),weekdaysShort:"sun_mán_þri_mið_fim_fös_lau".split("_"),weekdaysMin:"Su_Má_Þr_Mi_Fi_Fö_La".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd, D. MMMM YYYY [kl.] H:mm"},calendar:{sameDay:"[í dag kl.] LT",nextDay:"[á morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[í gær kl.] LT",lastWeek:"[síðasta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s síðan",s:n,ss:n,m:n,mm:n,h:"klukkustund",hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return a}))},9402:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("en-nz",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10,n=1===~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n},week:{dow:1,doy:4}});return t}))},9532:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={ss:"sekundes_sekundēm_sekunde_sekundes".split("_"),m:"minūtes_minūtēm_minūte_minūtes".split("_"),mm:"minūtes_minūtēm_minūte_minūtes".split("_"),h:"stundas_stundām_stunda_stundas".split("_"),hh:"stundas_stundām_stunda_stundas".split("_"),d:"dienas_dienām_diena_dienas".split("_"),dd:"dienas_dienām_diena_dienas".split("_"),M:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),MM:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),y:"gada_gadiem_gads_gadi".split("_"),yy:"gada_gadiem_gads_gadi".split("_")};function n(e,t,n){return n?t%10===1&&t%100!==11?e[2]:e[3]:t%10===1&&t%100!==11?e[0]:e[1]}function a(e,a,r){return e+" "+n(t[r],e,a)}function r(e,a,r){return n(t[r],e,a)}function s(e,t){return t?"dažas sekundes":"dažām sekundēm"}var i=e.defineLocale("lv",{months:"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec".split("_"),weekdays:"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY.",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, HH:mm",LLLL:"YYYY. [gada] D. MMMM, dddd, HH:mm"},calendar:{sameDay:"[Šodien pulksten] LT",nextDay:"[Rīt pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[Pagājušā] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"pēc %s",past:"pirms %s",s,ss:a,m:r,mm:a,h:r,hh:a,d:r,dd:a,M:r,MM:a,y:r,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return i}))},9680:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";function t(e,t,n,a){var r={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],M:["ee Mount","engem Mount"],y:["ee Joer","engem Joer"]};return t?r[n][0]:r[n][1]}function n(e){var t=e.substr(0,e.indexOf(" "));return r(t)?"a "+e:"an "+e}function a(e){var t=e.substr(0,e.indexOf(" "));return r(t)?"viru "+e:"virun "+e}function r(e){if(e=parseInt(e,10),isNaN(e))return!1;if(e<0)return!0;if(e<10)return 4<=e&&e<=7;if(e<100){var t=e%10,n=e/10;return r(0===t?n:t)}if(e<1e4){while(e>=10)e/=10;return r(e)}return e/=1e3,r(e)}var s=e.defineLocale("lb",{months:"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._Mé._Dë._Më._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mé_Dë_Më_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm [Auer]",LTS:"H:mm:ss [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm [Auer]",LLLL:"dddd, D. MMMM YYYY H:mm [Auer]"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gëschter um] LT",lastWeek:function(){switch(this.day()){case 2:case 4:return"[Leschten] dddd [um] LT";default:return"[Leschte] dddd [um] LT"}}},relativeTime:{future:n,past:a,s:"e puer Sekonnen",ss:"%d Sekonnen",m:t,mm:"%d Minutten",h:t,hh:"%d Stonnen",d:t,dd:"%d Deeg",M:t,MM:"%d Méint",y:t,yy:"%d Joer"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});return s}))},9722:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("jv",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des".split("_"),weekdays:"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu".split("_"),weekdaysShort:"Min_Sen_Sel_Reb_Kem_Jem_Sep".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sp".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/enjing|siyang|sonten|ndalu/,meridiemHour:function(e,t){return 12===e&&(e=0),"enjing"===t?e:"siyang"===t?e>=11?e:e+12:"sonten"===t||"ndalu"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"enjing":e<15?"siyang":e<19?"sonten":"ndalu"},calendar:{sameDay:"[Dinten puniko pukul] LT",nextDay:"[Mbenjang pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kala wingi pukul] LT",lastWeek:"dddd [kepengker pukul] LT",sameElse:"L"},relativeTime:{future:"wonten ing %s",past:"%s ingkang kepengker",s:"sawetawis detik",ss:"%d detik",m:"setunggal menit",mm:"%d menit",h:"setunggal jam",hh:"%d jam",d:"sedinten",dd:"%d dinten",M:"sewulan",MM:"%d wulan",y:"setaun",yy:"%d taun"},week:{dow:1,doy:7}});return t}))},9756:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("ar-tn",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}});return t}))},9834:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("pt",{months:"Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});return t}))},9846:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t=e.defineLocale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiemParse:/d\'o|d\'a/i,isPM:function(e){return"d'o"===e.toLowerCase()},meridiem:function(e,t,n){return e>11?n?"d'o":"D'O":n?"d'a":"D'A"},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:n,ss:n,m:n,mm:n,h:n,hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});function n(e,t,n,a){var r={s:["viensas secunds","'iensas secunds"],ss:[e+" secunds",e+" secunds"],m:["'n míut","'iens míut"],mm:[e+" míuts",e+" míuts"],h:["'n þora","'iensa þora"],hh:[e+" þoras",e+" þoras"],d:["'n ziua","'iensa ziua"],dd:[e+" ziuas",e+" ziuas"],M:["'n mes","'iens mes"],MM:[e+" mesen",e+" mesen"],y:["'n ar","'iens ar"],yy:[e+" ars",e+" ars"]};return a||t?r[n][0]:r[n][1]}return t}))},9849:function(e,t,n){(function(e,t){t(n(5093))})(0,(function(e){"use strict";var t={1:"੧",2:"੨",3:"੩",4:"੪",5:"੫",6:"੬",7:"੭",8:"੮",9:"੯",0:"੦"},n={"੧":"1","੨":"2","੩":"3","੪":"4","੫":"5","੬":"6","੭":"7","੮":"8","੯":"9","੦":"0"},a=e.defineLocale("pa-in",{months:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),monthsShort:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),weekdays:"ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ".split("_"),weekdaysShort:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),weekdaysMin:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),longDateFormat:{LT:"A h:mm ਵਜੇ",LTS:"A h:mm:ss ਵਜੇ",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm ਵਜੇ",LLLL:"dddd, D MMMM YYYY, A h:mm ਵਜੇ"},calendar:{sameDay:"[ਅਜ] LT",nextDay:"[ਕਲ] LT",nextWeek:"[ਅਗਲਾ] dddd, LT",lastDay:"[ਕਲ] LT",lastWeek:"[ਪਿਛਲੇ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ਵਿੱਚ",past:"%s ਪਿਛਲੇ",s:"ਕੁਝ ਸਕਿੰਟ",ss:"%d ਸਕਿੰਟ",m:"ਇਕ ਮਿੰਟ",mm:"%d ਮਿੰਟ",h:"ਇੱਕ ਘੰਟਾ",hh:"%d ਘੰਟੇ",d:"ਇੱਕ ਦਿਨ",dd:"%d ਦਿਨ",M:"ਇੱਕ ਮਹੀਨਾ",MM:"%d ਮਹੀਨੇ",y:"ਇੱਕ ਸਾਲ",yy:"%d ਸਾਲ"},preparse:function(e){return e.replace(/[੧੨੩੪੫੬੭੮੯੦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/ਰਾਤ|ਸਵੇਰ|ਦੁਪਹਿਰ|ਸ਼ਾਮ/,meridiemHour:function(e,t){return 12===e&&(e=0),"ਰਾਤ"===t?e<4?e:e+12:"ਸਵੇਰ"===t?e:"ਦੁਪਹਿਰ"===t?e>=10?e:e+12:"ਸ਼ਾਮ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"ਰਾਤ":e<10?"ਸਵੇਰ":e<17?"ਦੁਪਹਿਰ":e<20?"ਸ਼ਾਮ":"ਰਾਤ"},week:{dow:0,doy:6}});return a}))}}]);
//# sourceMappingURL=chunk-vendors.b066fcd5.js.map