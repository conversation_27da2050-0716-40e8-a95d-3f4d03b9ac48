<template>
	<learun-timeline-wraper>
		<learun-timeline-item v-for="(item,index) in wfLogs" 
			:time="item.time" 
			:key="index"
			:isFirst="index == 0"
			:isLast="index == wfLogs.length -1"
			
			:color="item.type == '1'?'#2979ff':''"
			>
			<view style="font-size: 12px;" >
				<text>{{$t(item.name)}}</text>
				<view v-if="fieldReady">
					<learun-upload-file
					v-if="item.fileId"
					:value="filedMap[item.fileId]"
					:disabled="true"
				/>
				</view>
			</view>
			<view style="color: #6a6a6a;" >
				<text style="color: #2979ff;" >{{String(item.userNames)}}：</text>
				<text>{{$t(item.des)}}</text>
			</view>
			<view v-if="item.tag">
				<text>【{{ $t('审核要点') }}】{{ item.tag }}</text>
			</view>
		</learun-timeline-item>
	</learun-timeline-wraper>
</template>

<script>
export default {
  name: 'learun-workflow-timeline',
  props: {
    wfLogs: { default: () => [] }
  },
  data() {
	return {
		filedMap: {},
		fieldReady: false,
	}
  },
  async created() {
	this.fieldReady = false;
	await this.loadFile();
	this.fieldReady = true;
  },
  methods: {
	async loadFile() {
		for (const log of this.wfLogs) {  
			if (log.fileId) {  
				this.filedMap[log.fileId] = await this.DOWNLOAD_FILE(log.fileId)
			}  
		}
	}
  },
}
</script>
