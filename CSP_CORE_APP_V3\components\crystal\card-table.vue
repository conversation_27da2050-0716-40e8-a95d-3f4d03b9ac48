<template>
	<view class="learun-table" v-if="customListFlag">
		<view class="crystal-table-header" style="margin:0px 10px 10px 10px" ref="tableHeader">
			<text style="width: 100%;">{{ $t('个人消费') }}: 146.66</text>
			<text>{{ $t('日期范围') }}: 2024-03-18 至 2024-03-18</text>
		</view>
		<scroll-view scroll-y :style="{'width':'100%','height':tableHeaderHeight - (showPagination?40:0) + 'px'}"
			:show-scrollbar="false" :scroll-with-animation="false" bindscrolltolower="handleScrollToLower">
			<view class="learun-table__wraper" style="padding-top: 0px;padding-bottom:5px">
				<view>
					<view class="table-body">
						<view v-for="(row,index) in dataSource" :key="index" class="table-view"
							@click.stop="rowClick(row,index)">
							<text style="width: 25%">{{ $t('日期') }}: 2024-03-18</text>
							<text style="width: 65%">{{ $t('项目') }}: 早餐</text>
							<text style="width: 50%">{{ $t('总金额') }}: 50</text>
							<text style="width: 50%;text-align: right;font-size:18px;">45.60</text>
							<text style="width: 50%">{{ $t('公司补贴') }}: 11.97</text>
							<text style="width: 50%;text-align: right;font-size:12px">{{ $t('支付方式') }}: 个人支付</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="learun-table__floor" v-if="showPagination">
			<uni-pagination showIcon :pageSize="pageSize" :current="page" :total="total"
				@change="pageChange"></uni-pagination>
		</view>
	</view>

	<view class="crystal-table" v-else>
		<scroll-view scroll-y :style="{'width': '100%','height': ListHeight-(showPagination?40:0)+'px'}"
			:show-scrollbar="false" :scroll-with-animation="false">
			<view class="learun-table__wraper" style="padding-top: 0px;">
				<view class="learun-table__header" :style="{height: '0px'}">
				</view>
				<view class="table-body">
					<view v-for="(row,index) in dataSource" :key="index" class="table-view"
						@click.stop="rowClick(row,index)">
						<view class="table-item" v-for="(col,index2) in columns" :key="index2"
							:style="{width:'100%', color: '#090909'}">
							<view class="table-item-content">
								<text class="item-label">{{ col.label2 }}</text>
								<learun-table-item v-if="col.myScheme" :component="col.myScheme"
									:getDataSource="getDataSource" :value="row[col.rowid]" :rowData="row"
									@input="setValue($event.path,$event.value,row)">
								</learun-table-item>
								<template v-else>
									<text
										class="item-value text-wrap">{{ myDisplayText(row[col.rowid], col.scheme) }}</text>
								</template>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="learun-table__floor">
			<uni-pagination showIcon :pageSize="pageSize" :current="page" :total="total"
				@change="pageChange"></uni-pagination>
		</view>
	</view>
</template>

<script>
	import {
		forEach
	} from "lodash"
	import debounce from "lodash/debounce"
	import set from "lodash/set"
	export default {
		name: 'learun-table',
		props: {
			page: {
				default: 1
			},
			pageSize: {
				default: 20
			},
			total: {
				default: 0
			},
			columns: {
				default: () => [{
						label: '日期',
						rowid: 'date',
						width: 120
					},
					{
						label: '项目',
						rowid: 'item',
						width: 180
					},
					{
						label: '总金额',
						rowid: 'total',
						width: 120
					},
					{
						label: '实际支付',
						rowid: 'payment',
						width: 120
					},
					{
						label: '公司补贴',
						rowid: 'subsidy',
						width: 120
					},
					{
						label: '支付方式',
						rowid: 'method',
						width: 120
					}
				]
			},
			dataSource: {
				default: () => []
			},
			displayText: Function,
			getDataSource: Function,
			headerValue: {
				default: ""
			},
			queryParams: {
				default: () => []
			},
			ListHeight: {
				default: 1
			},
			showPagination: {
				default: true
			}
		},
		data() {
			return {
				scrollTop: 0,
				customListFlag: false
			}
		},

		computed: {
			myWidth() {
				let width = 0
				this.columns.forEach(col => {
					width += col.width || 120
				})

				return width
			}
		},
		created() {
			this.labelTextWidth();
		},
		mounted() {
			this.$nextTick(() => {
				this.tableHeaderHeight = this.$refs.tableHeader?.offsetHeight || 0;
			});
		},

		methods: {
			myDisplayText(value, scheme) {
				if (this.displayText && scheme) {
					return this.displayText(value, scheme)
				}
				if (typeof value === 'object' && value.label) {
					return this.$t(value.label);
				}
				return value || '';
			},

			rowClick(row, index) {
				this.$emit('rowClick', {
					row,
					index
				});
			},

			pageChange(e) {
				this.scrollTop = 1000;
				this.$nextTick(() => {
					this.scrollTop = 0;
				});
				this.$emit('pageChange', e);
			},

			setValue(prop, value, data) {
				set(data, prop, value);
			},

			labelTextWidth() {
				this.columns.forEach(col => {
					col.label2 = this.$t(col.label) + this.$t("：");

					if (col.label === '日期') {
						col.width = 150;
					}
				});
			}
		},
		watch: {
			'$i18n.locale'(newLang) {
				this.labelTextWidth();
			}
		}
	}
</script>

<style lang="less" scoped>
	.table-body {
		background-color: #f5f7fa;
		padding: 5px 0;
	}

	.table-view {
		display: flex;
		flex-wrap: wrap;
		margin: 12px;
		border-bottom: solid 2rpx #ededed;
		padding: 10px;
		background-color: #ffffff;
		border-radius: 10px;
		box-shadow: 5px 5px 5px 0px rgba(0, 0, 0, 0.05);
	}

	.table-item {
		padding: 8rpx 5rpx;
		box-sizing: border-box;

		.table-item-content {
			display: flex; // 使用flex布局使标题和内容在同一行
			flex-wrap: nowrap;
			align-items: flex-start;
		}

		.item-label {
			font-weight: 500;
			color: #606266;
			margin-right: 5rpx; // 标题与内容之间的间距
			white-space: nowrap; // 标题不换行
		}

		.item-value {
			flex: 1; // 内容占据剩余空间
			min-height: 30rpx;
			line-height: 1.4;
		}
	}

	.crystal-table-header {
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		margin: 10rpx;
	}

	.learun-table__floor {
		padding: 10rpx;
	}

	/* 文本换行样式 */
	.text-wrap {
		white-space: normal;
		word-wrap: break-word;
		word-break: break-all;
		display: block;
	}
</style>