<template>
  <view class="learun-timeline__item">
		<view class="learun-timeline__column-line">
			<view class="learun-timeline__line-before" :style="{'background-color':isFirst?'transparent':''}" ></view>
			<view class="learun-timeline__circle" :style="{'background-color':color}" ></view>
			<view class="learun-timeline__line-after"  :style="{'background-color':isLast?'transparent':''}"  ></view>
		</view>
    <view :style="contentStyle" class="learun-timeline__content">
      <slot name="time">
        <view v-if="time" >
					<text class="learun-timeline__content-time">{{TABLEITEM_DATEFORMAT(time,'YYYY-MM-DD HH:mm:ss').toString()}}</text>
				</view> 
      </slot>
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'learun-timeline-item',
  props: {
		isFirst:{
			default:false
		},
		isLast:{
			default:false
		},
    time: {},
    color: { default: '' },
    contentStyle: {}
  }
}
</script>
