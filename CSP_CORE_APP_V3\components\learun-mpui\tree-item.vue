<template>
	<view class="l-tree-item" >
		<view  @tap.stop="handleClick" class="l-tree-item__content" >
			<uni-icons v-if="hasChildren() || data.isLoad == false"  class="l-tree-item__lefticon"  :type="isOpen? 'bottom':'right'" size="14" color="#c0c4cc" ></uni-icons>
			<view v-else  class="l-tree-item__lefticon_empty"  size="14" color="#c0c4cc" ></view>
			<view class="l-tree-item__text" >{{data.label}}</view>
			<view v-if="!data.disabled" class="l-tree-item__btn" @tap.stop="handleSelect" >{{$t('选择')}}</view>
			<view class="learun-popup-line" ></view>
		</view>
		
		
		
		<view style="padding-left: 16px;"  v-if="isOpen && hasChildren()">
			<learun-tree-item :loadData="loadData" @select="handleChildrenSelect" :data="item"  v-for="(item,index) in getChildren()" :key="index" ></learun-tree-item>
		</view>
		
	</view>
</template>

<script>
	export default {
		name:'learun-tree-item',
		props:{
			data:{
				type:Object,
				default:()=>{}
			},
			loadData:Function
		},
		
		data(){
			return {
				isOpen:false,
				isLoading:false,
				isLoad:false
			}
		},
		methods:{
			async handleClick(){		
				if(this.data.isLoad == false && this.loadData){// 数据懒加载
					if(!this.isLoading){
						this.LOADING(this.$t('加载数据'))
						this.isLoading = true
						const list = await this.loadData(this.data.value,this.data)
						
						this.data.isLoad = true
						const children = this.data.children || []
						children.push(...list)
						this.$set(this.data,'children',children)
						
						this.$nextTick(()=>{
							this.isOpen = true
							this.isLoading = false
							this.HIDE_LOADING()
						})
					}
				}
				else if(this.hasChildren()){
					this.isOpen = !this.isOpen
				}
			},
			hasChildren(){
				console.log(this.data.children)
				return !this.VALIDATENULL(this.data.children)
			},
			getChildren(){
				return this.data.children
			},
			
			handleSelect(){
				this.$emit('select',this.data)
			},
			handleChildrenSelect(e){
				this.$emit('select',e)
			}
		}
	}
</script>
<style lang="scss" scoped >
	.l-tree-item{
		&__content{
			height: 40px;
			display: flex;
			align-items: center;
			position: relative;
		}
		
		&__lefticon{
			margin-right: 4px;
		}
		&__lefticon_empty{
			height: 14px;
			width: 22px;
		}
		&__text{
			width: 100%;
		}
		&__btn{
			color: #2979ff;
			font-size: 14px;
			width: 40px;
		}
	}
</style>
