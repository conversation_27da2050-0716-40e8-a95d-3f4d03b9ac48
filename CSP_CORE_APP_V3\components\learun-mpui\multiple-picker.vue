<template>
	<view >
		<view  class="learun-box learun-input learun-input-border" :class="{'learun-input-placeholder':VALIDATENULL(value)}"  @tap.stop="handleOpen">
			<view class="learun-input__content" ><text>{{$t(text)}}</text></view>
			
			
			<view v-if="VALIDATENULL(value)" class="learun-input-icon-right learun-input-icon-right-bottom">
				<uni-icons  type="bottom" size="14" color="#c0c4cc"></uni-icons>
			</view>
			<view  v-else-if="!disabled && clearable" class="learun-input-icon-right">
				<view @tap.stop="handleClear">
					<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
				</view>
			</view>
			
			
		</view>
		
		<uni-popup ref="popup" type="bottom" >
			<view class="learun-picker-popup" >
				<view class="learun-popup-button-close" @click.stop="handleCancel">
					<learun-icon type="learun-icon-error" :size="14" color="#737987" ></learun-icon>
				</view>
				
				<view class="learun-box learun-picker-popup__box" >
					<scroll-view
						scroll-with-animation
						scroll-y
						style="height: 100%;"
					>
					<view class="learun-select-list">
							<checkbox-group @change="checkboxChange">
									<label class="learun-select-list-item" v-for="(item,index) in myOptions" :key="index">
											<view>
													<checkbox style="transform:scale(0.8)" :value="item.value" :checked="midValue.indexOf(item.value) != -1" />
											</view>
											<view>{{item.text}}</view>
											<view class="learun-popup-line" ></view>
									</label>
							</checkbox-group>
					</view>
						
					</scroll-view>
				</view>
				
				<view class="learun-popup-button-wraper" >
					<view class="learun-popup-button-ok" @click="handleSave">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name:'learun-multiple-picker',
		props:{
			value:{
				default:null
			},
			placeholder:{
				type:String,
				default:'请选择'
			},
			labelKey:{
				type:String,
				default:'label'
			},
			valueKey:{
				type:String,
				default:'value'
			},
			options:{
				type:Array,
				default:()=>[]
			},
			clearable:{
				type:Boolean,
				default:true
			},
			disabled:Boolean
		},
		data(){
			return {
				midValue:[]
			}
		},
		computed:{
			myValue:{
				get(){
					if(this.VALIDATENULL(this.value)){
						return []
					}
					return this.value.split(',')
				},
				set(val){
					if(val.length == 0){
						this.$emit('input', '')
						this.$emit('change', undefined)
					}
					else{
						this.$emit('input', String(val))
						this.$emit('change', this.options.filter(t=>val.indexOf(t[this.valueKey]) != -1 ))
					}
				}
			},
			myOptions(){
				return this.options.map(t=>({value:t[this.valueKey],text:t[this.labelKey]}))
			},
			text(){
				if(this.myValue.length == 0){
					return this.$t(this.placeholder) || ''
				}
				else{
					const list = this.myOptions.filter(t=>this.myValue.indexOf(t.value) != -1 ).map(t=>t.text)
					return String(list)
				}
			}
		},
		methods:{
			handleOpen(){
				if(this.disabled){
					return
				}
				
				this.midValue = this.myValue 
				this.$refs.popup.open()
			},
			handleCancel(){
				this.$refs.popup.close()
			},
			handleSave(){
				this.myValue = this.midValue
				this.$refs.popup.close()
			},
			handleClear(){
				this.myValue = []
			},
			checkboxChange(e){
				const value = e.detail.value
				this.midValue = value
			}
		}
	}
</script>
<style lang="scss" scoped >
	.learun-select-list{
		.learun-select-list-item{
			position: relative;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			min-height: 32px;
			padding: 8px 0 0 8px;
		}
	}
</style>


