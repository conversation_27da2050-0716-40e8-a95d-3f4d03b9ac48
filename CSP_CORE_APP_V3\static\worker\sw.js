// worker.js
self.onmessage = function(event) {
	// console.log(3, "sw.js")
	// self.postMessage(data);
	return
	// return
};

function getMessage(apiUrl) {
	console.log(10, apiUrl)
	//定时调用接口
	fetch(apiUrl)
		.then(response => response.json())
		.then(data => {
			// 将接口返回的数据发送回主线程
			// self.postMessage(data);
			if (data.data) {
				let messageList = data.data
				for (let i = 0; i < messageList.length; i++) {
					//let body = messageList[i].f_Content
					let body = messageList[i]
					console.log(42, Notification.permission)
					if (Notification.permission === 'granted') {
						self.registration.showNotification('【CSP提醒】', {
							// var notification = new Notification('【提醒】', {
							body: body,
							dir: 'rtl', // 文字方向
							icon: '/static/icons/icon.png', // 通知图标
						});
					} else if (Notification.permission !== 'denied') {
						Notification.requestPermission().then(function(permission) {
							if (permission === 'granted') {
								self.registration.showNotification('【CSP提醒】', {
									// var notification = new Notification('【提醒】', {
									body: body,
									dir: 'ltr', // 从左到右
									icon: '/static/icons/icon.png',
								});
							}
						});
					}
				}
			}
		})
		.catch(error => {
			console.error('Error fetching data:', error);
		});
}

self.addEventListener('install', function(event) {
	//console.log(53, "install", event)
}, false);
self.addEventListener('activate', function(event) {
	//console.log(56, "activate", event)
});
// 在service worker中监听message事件
self.addEventListener('message', (event) => {
	console.log(60, "message")
	// 接收主线程发送的消息
	let interval = event.data.interval; // 定时器间隔
	let apiUrl = event.data.apiUrl; // 需要调用的接口URL
	let token = event.data.token; // token

	if (interval == undefined) {
		interval = 300000 // 定时器间隔，单位为毫秒
	}

	apiUrl = apiUrl + getTime3() + "?token=" + token
	console.log(12, apiUrl)
	if (token != null) {
		//立即获取一次
		getMessage(apiUrl)

		setInterval(function() {
			apiUrl = event.data.apiUrl + getTime3() + "?token=" + token
			// console.log(86, apiUrl)
			getMessage(apiUrl)
		}, interval);
	}
	// self.registration.showNotification('Hello World12345');
	if (Notification.permission === 'granted') {
		// self.postMessage(1);
		// ServiceWorkerRegistration.showNotification('Hello World')
		// self.registration.showNotification("title", {
		//       body: "body",
		//       // icon: "icon",
		//       // tag: "tag",
		//       // data: "data"
		//     })
		// var notification = new Notification('【提醒】', {
		// 	body: "body",
		// 	dir: 'rtl', // 文字方向
		// 	icon: '/static/icons/icon.png' ,// 通知图标
		// });
	}
});

function getTime3() {
	let time = new Date(new Date() - 3 * 60 * 1000);
	let timeString = time.getFullYear() + "-" + (time.getMonth() + 1) + "-" + time.getDate() + " " +
		time.getHours() + ":" + time.getMinutes() + ":" + time.getSeconds()
	// console.log(104, new Date(), timeString)
	return timeString
}