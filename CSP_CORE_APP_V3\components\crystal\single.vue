<template>
	<view :style="showLabel?'margin-bottom: 10px;':''">
		<!-- 手写签名 canvas 区 -->
		<template v-if="Number(taskParam.isSign) === 1">
			<view class="cu-form-group" v-show="showLabel">
				<text>
					<text style="color: red" v-if="required">*</text>
					{{titleLabel}}
					<learun-icon :type="showCanvas?'learun-icon-square-minus':'learun-icon-square-add'" color="#2979ff" @click="showTag" v-if="editMode" v-show="!screenFul"></learun-icon>
				</text>
			</view>

			<view v-if="screenFul">
				<view class="sign-button bg-white" @click="showSign" :style="buttonStyle">
					<view v-show="showPlaceholder" class="sign-placeholder">
						<text>{{placeholder}}</text>
						<learun-icon v-if="disabled" type="learun-icon-circle-ban" color="#999999;"></learun-icon>
					</view>
					<image v-show="!showPlaceholder" :src="prefix + signUrl"></image>
				</view>
				<uni-popup v-model="showProp" mode="left" width="100%" height="100%" border-radius="0" :mask-close-able="false"
					ref="single_popup">
					<crystal-single-write2 @signEnd="fulSignEnd" @close="fulClose"></crystal-single-write2>
				</uni-popup>
			</view>

			<view v-else-if="!screenFul">
				<view class="sign-area bg-white" v-show="showCanvas">
					<canvas v-if="canvas" @touchmove="signMove" @touchstart="signStart($event)" @touchend="signEnd"
						@touchcancel="signEnd" disable-scroll="true" canvas-id="sign-canvas" id="sign-canvas"
						class="sign-canvas"></canvas>
					<view class="signImage" v-if="isWrite">
						<image :src="prefix + signUrl"></image>
					</view>
					<view v-if="showPlaceholder" class="sign-placeholder">{{placeholder}}</view>
				</view>
				<view class="sign-action text-right button-group" v-if="editMode" v-show="showCanvas">
					<button @click="clearSign" style="margin: 15px; width: 30%;">{{$t('重新签名')}}</button>
					<button @click="submit(true)" style="margin: 15px; width: 30%;">{{$t('确定签名')}}</button>
				</view>
				<!-- <view v-show="!showCanvas && isWrite">
					<image :src="prefix + signUrl"></image>
				</view> -->
			</view>
			<view class="validate" v-if="isvalidate && !isWrite">{{validateMessage0}}</view>
			<image :src="prefix + oldSignUrl" v-if="isDebug"></image>
		</template>
	</view>
</template>

<script>
	let context = null
	let touchs = []

	export default {
		name: 'crystal-single',
		props: {
			refName: {
				type: String,
				default: 'crystal'
			},
			titleLabel: {
				type: String,
				default: 'Single Write：'
			},
			id: {
				type: String,
				default: 'single-write'
			},
			validateMessage: {
				type: String
			},
			isvalidate: {
				type: Boolean,
				default: false
			},
			isReady: {
				type: Boolean,
				default: false
			},
			data: {
				type: String
			},
			value: {
				type: String
			},
			editMode: {
				typs: String
			},
			required: {
				type: Boolean
			},
			isShowWrite: {
				type: Boolean
			},
			screenFul: {
				type: Boolean
			},
			//showLabel = true, 电子请假，showLabel = false, 在线表单
			showLabel: {
				type: Boolean,
				default: false
			},
			//是否是电子请假
			isLeave: {
				type: Boolean,
				default: false
			},
			disabled: {
				type: Boolean
			},
			isDebug: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				staff: null,
				remark: '',
				taskParam: {
					type: "sign",
					isSign: 1
				},
				canvas: true,
				signUrl: "",
				oldSignUrl: "",
				prefix: "data:image/png;base64,",
				validateMessage0: "",
				isWrite: false,
				placeholder: "",
				showPlaceholder: true,
				showCanvas: true,
				showProp: true,
				buttonStyle: ""
			}
		},

		async created() {
			await this.canvasInit()
		},

		async onLoad() {
			await this.init()
		},

		methods: {
			// 页面初始化
			async init() {
				this.canvasInit()
			},

			// 初始化签名区 canvas
			canvasInit() {
				this.canvas = true
				context = uni.createCanvasContext('sign-canvas')
				context.setStrokeStyle('#000')
				context.setLineWidth(5)
				context.setLineCap('round')
				context.setLineJoin('round')
				touchs = []
				this.validateMessage0 = this.$t("请") + this.titleLabel
				this.placeholder = this.$t("请在此处签名");
				this.signUrl = this.value;
				if (this.signUrl) {
					this.isWrite = true;
					this.showPlaceholder = false;
				}

				//
				if (!this.showLabel) {
					this.buttonStyle = "margin:0px;width:99%;";
					if (this.disabled) {
						this.buttonStyle += "background-color:#f8f8f8;color: #999999;";
						this.placeholder = this.$t("")
					}
				}

				// this.showCanvas = this.isShowWrite;
			},

			// 点击「提交」按钮
			async submit(isSubmit) {
				// 需要手写签名时，将 canvas 导出为 base64 格式
				// 各个平台写法均不相同，需要注意
				if (Number(this.taskParam.isSign) === 1) {
					// H5 平台，canvasToTempFilePath 的结果直接为画布的 base64
					// #ifdef H5
					const [err, {
						tempFilePath
					}] = await uni.canvasToTempFilePath({
						canvasId: 'sign-canvas'
					})
					this.signUrl = tempFilePath
					// #endif

					// App 平台，canvasToTempFilePath 输出文件，上传后台转为 base64 格式
					// #ifdef APP-VUE
					const [err, {
						tempFilePath
					}] = await uni.canvasToTempFilePath({
						canvasId: 'sign-canvas'
					})
					const signBase64 = await this.HTTP_UPLOAD('/annexes/tobase64', tempFilePath)
					this.signUrl = this.prefix + signBase64
					// #endif

					// 微信小程序，canvasToTempFilePath 输出文件，使用文件管理器以 base64 格式读取文件即可
					// #ifdef MP-WEIXIN
					const [err, {
						tempFilePath
					}] = await uni.canvasToTempFilePath({
						canvasId: 'sign-canvas'
					})
					this.signUrl = this.prefix + uni.getFileSystemManager().readFileSync(tempFilePath,
						'base64')
					// #endif

					// #ifdef MP-ALIPAY
					// 钉钉小程序，context.toTempFilePath 输出文件，上传后台转为 base64 格式
					// #ifdef MP-DINGTALK
					const filePath = await new Promise((res, rej) => {
						context.toTempFilePath({
							success: ({
								filePath
							}) => {
								res(filePath)
							},
							fail: () => {
								rej()
							}
						})
					})

					const signBase64 = await this.HTTP_UPLOAD('/annexes/tobase64', filePath)
					this.signUrl = this.prefix + signBase64
					// #endif

					// 支付宝小程序，context.toDataURL 直接输出 base64 字符串
					// #ifndef MP-DINGTALK
					this.signUrl = await context.toDataURL('image/png', 1)
					// #endif
					// #endif
				}
				if (this.signUrl.substr(0, this.prefix.length) == this.prefix) {
					this.signUrl = this.signUrl.substr(this.prefix.length, this.signUrl.length);
				}
				this.$emit('setValue', {
					path: this.id,
					value: this.signUrl
				});
				this.isWrite = isSubmit;
				this.showPlaceholder = false;
			},

			// 手写板事件（开始拖动）
			signStart(e) {
				touchs.push({
					x: e.changedTouches[0].x,
					y: e.changedTouches[0].y
				});
				this.showPlaceholder = false;
			},

			// 手写板事件（拖动签名）
			signMove(e) {
				touchs.push({
					x: e.touches[0].x,
					y: e.touches[0].y
				})
				this.drawLine()
			},

			// 手写板事件（签名结束）
			signEnd(e) {
				touchs = [];
				this.submit(false);
			},

			// 手写板事件（绘出线型）
			drawLine() {
				if (touchs.length < 2) {
					return
				}

				const [p1, p2] = touchs
				touchs.shift()
				context.moveTo(p1.x, p1.y)
				context.lineTo(p2.x, p2.y)
				context.stroke()
				context.draw(true)
			},

			// 清除手写板
			// 阿里小程序无法使用 clearRect 来清空，因此直接重新渲染 canvas
			clearSign() {
				// #ifndef MP-ALIPAY
				context.clearRect(0, 0, 9999, 9999)
				context.draw()
				context.setStrokeStyle('#000')
				context.setLineWidth(5)
				context.setLineCap('round')
				context.setLineJoin('round')
				// #endif

				// #ifdef MP-ALIPAY
				// 阿里系小程序无法 clearRect 清空画布，必须重新渲染 canvas
				this.canvas = false
				this.$nextTick(() => {
					this.canvasInit()
				})
				// #endif
				this.signUrl = "";
				this.$emit('setValue', {
					path: this.id,
					value: this.signUrl
				});
				this.isWrite = false;
				this.showPlaceholder = true;
			},

			showSign() {
				// if (editMode)
				if (this.editMode && !this.disabled) {
					this.$refs.single_popup.open()
				}
			},

			fulClose() {
				this.$refs.single_popup.close()
			},
			fulSignEnd(signImg) {
				let base64Img = signImg.base64Img;
				this.signUrl = base64Img.substr(this.prefix.length, base64Img.length);
				let base64Img2 = signImg.base64Img2;
				this.oldSignUrl = base64Img2.substr(this.prefix.length, base64Img2.length);
				// signImg.base64Img,signImg.base64Img2

				if (this.isLeave) {
					this.$emit('setValue', {
						path: this.id,
						value: this.signUrl,
					});
				} else {
					this.$emit('setValue', this.signUrl);
				}
				this.$emit('change', this.signUrl);
				this.isWrite = true;
				this.showPlaceholder = false;
				this.$refs.single_popup.close()
			},
		}
	}
</script>

<style lang="less" scoped>
	.sign-area {
		position: relative;

		// min-height: 500rpx;
		.sign-canvas {
			width: 670rpx;
			height: 500rpx;
			margin: 23rpx;
			// border: 2rpx dashed #444444;
			border: 1px solid #E2E2E2;
			border-radius: 4px;
			width: 94%;
		}

		.signImage {
			width: 670rpx;
			height: 500rpx;
			margin: 23rpx;
			// border: 2rpx dashed #444444;
			border: 1px solid #E2E2E2;
			border-radius: 4px;
			width: 94%;
			position: absolute;
			top: -23rpx;

			// left: 30rpx;
			// background-color: ;
			image {
				width: 675rpx !important;
				height: 500rpx !important;
				background-color: white;
			}
		}

		.sign-placeholder {
			position: absolute;
			top: 200rpx;
			left: 260rpx;
			color: #ababab;
			font-size: 30rpx;
		}
	}

	.button-group {
		display: flex;
		justify-content: space-around;

		button {
			display: inline-flex !important
		}
	}

	.title {
		font-size: 13px;
		color: #666666;
		display: flex;
		align-items: center;
		width: 100%;
		height: 32px;
	}

	.validate {
		line-height: 22px;
		color: #dd524d;
		font-size: 13px;
	}

	.show-or-noshow {
		border: 1px solid #E8E7E7;
		;
	}

	.sign-button {
		height: 400rpx;
		margin: 23rpx;
		border: 1px solid #E2E2E2;
		border-radius: 4px;
		width: 94%;
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 100%;
			height: 100%;
			background-color: white;
		}
	}
</style>