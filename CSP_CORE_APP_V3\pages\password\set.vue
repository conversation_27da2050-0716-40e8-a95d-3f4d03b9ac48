<template>
	<view style="padding: 8px;">
		<!-- 页面标题，使用多语言翻译，蓝色加粗居中显示 -->
		<view
			style="margin-bottom: 15px; font-size: 30px; color: rgb(0, 144, 255); text-align: center; font-weight: 600;">
			{{$t("设置查询密码")}}
		</view>

		<!-- 表单容器，使用uni-forms进行表单验证，绑定数据、验证规则和标签宽度 -->
		<uni-forms :modelValue="formData" :rules="rules" :label-width="100" ref="myForm">
			<!-- 旧密码表单项，仅在非首次设置且需要旧密码时显示（v-if条件控制） -->
			<uni-forms-item :label="$t('旧密码')" name="oldPwd" v-if="!isFrist && this.needOldPw" required>
				<uni-easyinput v-model="formData.oldPwd" :placeholder="$t('请输入字母+数字')" :inputBorder="false"
					type="password" class="form-item learun-flex-box" />
			</uni-forms-item>
			<!-- 新密码表单项，必填项 -->
			<uni-forms-item :label="$t('新密码')" name="newPwd" required>
				<uni-easyinput v-model="formData.newPwd" :placeholder="$t('请输入字母+数字(至少6位)')" :inputBorder="false"
					type="password" class="form-item learun-flex-box" />
			</uni-forms-item>
			<!-- 确认密码表单项，必填项 -->
			<uni-forms-item :label="$t('确认密码')" name="confirmPwd" required>
				<uni-easyinput v-model="formData.confirmPwd" :placeholder="$t('请输入字母+数字(至少6位)')" :inputBorder="false"
					type="password" class="form-item learun-flex-box" />
			</uni-forms-item>
		</uni-forms>
		<!-- 提交按钮，点击触发表单提交处理 -->
		<view class="padding">
			<button @click="submit" type="primary">{{$t('确认')}}</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					oldPwd: '', // 旧密码输入值（非首次修改时使用）
					newPwd: '', // 新密码输入值
					confirmPwd: '' // 确认密码输入值
				},
				rules: {
					// 旧密码验证规则（仅在需要时生效）
					'oldPwd': {
						rules: [{
							required: true,
							errorMessage: this.$t('请填写旧密码'), // 错误提示信息
						}]
					},
					// 新密码验证规则
					'newPwd': {
						rules: [{
							required: true,
							errorMessage: this.$t('请填写新密码'), // 错误提示信息
						}]
					},
					// 确认密码验证规则
					'confirmPwd': {
						rules: [{
							required: true,
							errorMessage: this.$t('请再次输入新密码'), // 错误提示信息
						}]
					},
				},
				company_Code: '', // 公司代码（从路由参数获取）
				emp_No: '', // 工号（从路由参数获取）
				idCardCode: '', // 身份证号码（从路由参数获取）
				RID: '', // 密码记录ID（用于更新时的唯一标识）
				isFrist: true, // 是否为首次设置密码（默认true）
				needOldPw: true, // 是否需要输入旧密码（默认true，可通过路由参数修改）
			}
		},

		async onLoad() {
			// 获取路由参数
			const params = this.GET_PARAM();
			// 解析公司代码、工号、身份证号码（用于密码设置接口参数）
			if (params.Company_Code) {
				this.company_Code = params.Company_Code;
				this.emp_No = params.Emp_No;
				this.idCardCode = params.idCardCode;
				// 首次设置时获取或生成RID（记录唯一标识）
				await this.getPasswordRID();
			} else {
				// 非首次设置时获取已有密码记录信息
				await this.getPassword();
			}
			// 根据路由参数更新是否需要旧密码标志
			if (params.needOldPw !== null) {
				this.needOldPw = params.needOldPw;
			}
			// 显示路由传递的提示信息（如密码过期提示）
			if (params.showTip) {
				this.TOAST(params.showTip);
			}
			// 设置页面标题（支持路由参数自定义标题）
			this.SET_TITLE(this.$t(params.title) || this.$t('设置查询密码'));
		},

		methods: {
			// 获取或生成密码记录RID（首次设置时生成，非首次时从后端获取）
			async getPasswordRID() {
				// 调用后端接口获取RID
				const success = await this.HTTP_GET({
					url: '/hrattf008/password/getRID',
					params: {
						Company_Code: this.company_Code,
						Emp_No: this.emp_No
					}
				});
				if (success) {
					// 已有RID，非首次设置
					this.RID = success;
					this.isFrist = false;
				} else {
					// 无RID，首次设置，生成唯一标识并移除旧密码验证规则
					this.RID = this.GUID();
					this.isFrist = true;
					delete this.rules.oldPwd; // 首次设置无需旧密码，从规则中删除旧密码验证
				}
			},
			// 获取已有密码记录信息（非首次设置时调用）
			async getPassword() {
				// 调用后端接口获取密码记录
				const res = await this.HTTP_GET({
					url: '/hrattf008/password'
				});
				if (res) {
					// 填充公司代码、工号、身份证号码和RID
					this.RID = res.RID;
					this.company_Code = res.Company_Code;
					this.emp_No = res.Emp_No;
					this.idCardCode = res.ID_NO;
					this.isFrist = false; // 标记为非首次设置
				}
			},
			// 表单提交处理方法
			async submit() {
				// 验证表单有效性
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm);
				if (err) return; // 验证失败则返回

				const {
					oldPwd,
					newPwd,
					confirmPwd
				} = this.formData;

				// 检查新密码与确认密码是否一致
				if (newPwd !== confirmPwd) {
					this.CONFIRM(this.$t('操作失败'), this.$t('新密码和确认密码输入不一致，请修改。'));
					return;
				}

				// 验证密码格式：至少6位，包含字母和数字
				const regex = /^(?=.*\d)(?=.*[a-zA-Z]).{6,}$/;
				if (!regex.test(newPwd) || this.idCardCode.includes(newPwd)) {
					this.CONFIRM(this.$t('密码格式错误'), this.$t('密码需字母+数字至少6位，且不能包含在身份证号码内！'));
					return;
				}

				this.LOADING(this.$t('提交…')); // 显示加载提示

				let success;
				// 根据是否首次设置选择不同的接口（新增或更新）
				if (this.isFrist) {
					// 首次设置密码，使用POST接口
					success = await this.HTTP_POST({
						url: '/hrattf008/password',
						data: {
							company_Code: this.company_Code,
							emp_No: this.emp_No,
							password: this.MD5(newPwd) // 密码加密后传输
						},
						errorTips: this.$t('修改失败')
					});
				} else {
					// 非首次修改密码，使用PUT接口，并根据needOldPw决定是否包含旧密码
					const data = {
						company_Code: this.company_Code,
						emp_No: this.emp_No,
						password: this.MD5(newPwd) // 新密码加密
					};
					if (this.needOldPw) {
						data.oldPassword = this.MD5(oldPwd); // 旧密码加密后添加到请求数据
					}
					success = await this.HTTP_PUT({
						url: `/hrattf008/password/${this.RID}`, // 使用RID作为资源标识符
						data,
						errorTips: this.$t('修改失败')
					});
				}

				this.HIDE_LOADING(); // 隐藏加载提示
				if (!success) return; // 接口调用失败则返回

				// 密码修改成功，返回上一页并提示
				this.NAV_BACK();
				this.TOAST(this.$t('密码修改成功'));
			}
		}
	}
</script>

<style lang="less" scoped>
	.form-item {
		/* 表单项公共样式：边框和圆角 */
		border: 1px solid #F0F0F0;
		border-radius: 4px;
	}
</style>