<template>
	<uni-forms-item v-if="component.display !== false" :label="$t(component.label)" :name="component.prop"
		:required="component.required" :key="component.key">

		<view v-if="component.type == 'number'" class="learun-flex-box">
			<uni-number-box :value="value" @input="setValue(component.prop, $event)" :min="component.min || -10000000"
				:max="component.max || 10000000" :step="component.step || 1" :disabled="isNotEdit"
				@change="handleChange" />
		</view>

		<learun-radio v-else-if="component.type == 'radio'" :value="value" @input="setValue(component.prop, $event)"
			:options="getOptions(component)" :disabled="isNotEdit" @change="handleChange" />
		<learun-checkbox v-else-if="component.type == 'checkbox'" :value="value"
			@input="setValue(component.prop, $event)" :options="getOptions(component)" :disabled="isNotEdit"
			@change="handleChange" />
		<learun-picker v-else-if="['select'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :options="getOptions(component)"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" @change="handleChange" />

		<learun-multiple-picker v-else-if="['selectMultiple'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :options="getOptions(component)"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" @change="handleChange" />

		<learun-tree-picker v-else-if="['treeselect'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :isTreeData="component.dataType == '1'"
			:options="getOptions(component)" :idKey="component.dataIdKey" :pIdKey="component.dataPIdKey"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" @change="handleChange" />
		<learun-layer-picker v-else-if="['layerselect'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :options="getOptions(component)"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" :columns="component.columns"
			@change="handleChange" />



		<learun-time-picker v-else-if="['time'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :format="component.format" :disabled="isNotEdit"
			:clearable="component.clearable" :placeholder="$t(component.placeholder)"
			:selectableRange="component.selectableRange" @change="handleChange" />
		<learun-time-picker v-else-if="['timerange'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :format="component.format" :disabled="isNotEdit"
			:clearable="component.clearable" :startPlaceholder="component.startPlaceholder"
			:endPlaceholder="component.endPlaceholder" :isRange="true" @change="handleChange" />


		<learun-datetime-picker v-else-if="['datetime','datetimerange'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :type="component.dateType" :format="component.format"
			:clearable="component.clearable" :placeholder="$t(component.placeholder)" :disabled="isNotEdit"
			@change="handleChange" />


		<learun-area-picker v-else-if="['areaselect'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :clearable="component.clearable"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" @change="handleChange" />

		<learun-map-picker v-else-if="['layerbmap'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :clearable="component.clearable"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" @change="handleChange" />

		<learun-upload-file v-else-if="['upload','uploadimg'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" @change="handleChange" :accept="component.accept"
			:limit="component.limit" :maxSize="component.maxSize" :sizeType="component.sizeType"
			:isTip="component.isTip" :listType="component.listType" :disabled="isNotEdit" />



		<learun-company-picker :getDataSource="getDataSource" v-else-if="['companySelect'].includes(component.type)"
			:value="value" :disabled="isNotEdit" @input="setValue(component.prop, $event)"
			:placeholder="$t(component.placeholder)" @change="handleChange" />

		<learun-department-picker :getDataSource="getDataSource"
			v-else-if="['departmentSelect'].includes(component.type)" :value="value" :disabled="isNotEdit"
			:placeholder="$t(component.placeholder)" @input="setValue(component.prop, $event)" @change="handleChange" />

		<learun-user-picker v-else-if="['userSelect'].includes(component.type)" :value="value" :disabled="isNotEdit"
			@input="setValue(component.prop, $event)" :getDataSource="getDataSource"
			:placeholder="$t(component.placeholder)" @change="handleChange" />

		<learun-code v-else-if="['encode'].includes(component.type)" :value="value" :code="component.code"
			@input="setValue(component.prop, $event)" @change="handleChange" />

		<learun-label
			v-else-if="['company','department','createuser','modifyuser','createuser2'].includes(component.type)"
			:value="value" :type="component.type" />

		<learun-icon-picker v-else-if="['icon'].includes(component.type)" :value="value" :disabled="isNotEdit"
			@input="setValue(component.prop, $event)" @change="handleChange" />

		<view v-else-if="['rate'].includes(component.type)" style="padding-top:4px;">
			<uni-rate :value="value" :disabled="isNotEdit" @input="setValue(component.prop, $event)"
				@change="handleChange($event.value)" />
		</view>

		<learun-switch v-else-if="['switch'].includes(component.type)" :value="value" :valueType="component.valueType"
			:activeValue="component.activeValue" :inactiveValue="component.inactiveValue" :disabled="isNotEdit"
			@input="setValue(component.prop, $event)" @change="handleChange" />
		<learun-slider v-else-if="['slider'].includes(component.type)" :value="value" :min="component.min"
			:max="component.max" :step="component.step" :showTooltip="component.showTooltip" :disabled="isNotEdit"
			@input="setValue(component.prop, $event)" @change="handleChange" />


		<learun-color-picker v-else-if="['color'].includes(component.type)" :value="value" :disabled="isNotEdit"
			@input="setValue(component.prop, $event)" @change="handleChange" />



		<uni-easyinput v-else-if="['createtime','modifytime','guid'].includes(component.type)" :value="value" disabled
			:clearable="false" />


		<uni-easyinput v-else-if="component.type == 'password'" type="password" :value="value"
			@input="setValue(component.prop, $event)" :placeholder="$t(component.placeholder)"
			:prefixIcon="component.prefixIcon" :suffixIcon="component.suffixIcon"
			:maxlength="component.maxlength?component.maxlength:-1" :disabled="isNotEdit"
			@blur="handleChange($event.detail.value)" />


		<uni-easyinput v-else-if="['textarea','texteditor'].includes(component.type)" type="textarea" :value="value"
			@input="setValue(component.prop, $event)" @blur="handleChange($event.detail.value)"
			:placeholder="$t(component.placeholder) || ''" :maxlength="component.maxlength?component.maxlength:-1"
			:disabled="isNotEdit" autoHeight />

		<crystal-layer-picker2 v-else-if="['layerselect2'].includes(component.type)" :value="value"
			@input="setValue(component.prop, $event)" :options="getOptions(component)"
			:placeholder="$t(component.placeholder)" :disabled="isNotEdit" :columns="component.columns"
			@change="handleChange" />

		<uni-easyinput v-else-if="!['viewtable'].includes(component.type)" type="text" :value="value"
			@input="setValue(component.prop, $event)" @blur="handleChange($event.detail.value)"
			:placeholder="$t(component.placeholder) || ''" :prefixIcon="component.prefixIcon || ''"
			:suffixIcon="component.suffixIcon || ''" :maxlength="component.maxlength?component.maxlength:-1"
			:disabled="isNotEdit" />

	</uni-forms-item>
</template>

<script>
	export default {
		name: 'crystal-customform-item',
		emits: ['change'],
		props: {
			value: {},
			component: {},
			getDataSource: Function,
			editMode: {
				default: true
			},
			tableId: String,
			tableIndex: Number,
			formId: String,
			isEdit: Boolean,
			showLable: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {}
		},
		computed: {
			isNotEdit() {
				if (this.isEdit) {
					return false
				}
				return !(this.editMode && !this.component.readonly && !this.component.disabled && this.component.edit !=
					false)
			}
		},
		methods: {
			handleChange(obj) {
				if (this.component.type != 'number') {
					this.$emit('change', {
						data: obj,
						component: this.component
					})
				}

			},
			// 设置表单数据的方法
			setValue(path, value) {
				this.$emit('input', {
					path,
					value
				})
				if (this.component.type == 'number') {
					this.$emit('change', {
						data: value,
						component: this.component
					})
				}
			},
			getOptions(component) {
				const formData = this.GET_DATA(`learun_form_data_${this.formId}`)
				let options = []
				if (this.tableId) {
					options = this.getDataSource(component, formData[this.tableId][this.tableIndex])
				} else {
					options = this.getDataSource(component, formData)
				}
				return options
			}
		}
	}
</script>
