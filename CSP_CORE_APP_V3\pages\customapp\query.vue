<template>
    <!-- 页面容器，设置最小高度为屏幕高度，背景颜色为白色，底部内边距为 40px -->
    <view class="page" :style="{
        'min-height': SCREENHEIGHT() + 'px',
        'background-color': '#fff',
        'padding-bottom': '40px',
    }">
        <!-- 导航栏，固定在顶部，带有关闭图标，点击关闭图标调用 closePage 方法，标题为“筛选条件” -->
        <uni-nav-bar fixed statusBar leftIcon="closeempty" @clickLeft="closePage" :title="$t('筛选条件')"></uni-nav-bar>
        <!-- 当 ready 为 true 时渲染表单，绑定表单数据，标签位置在顶部，标签宽度为 320px -->
        <uni-forms v-if="ready" :modelValue="formData" label-position="top" :label-width="320" ref="myForm">
            <!-- 表单内容容器，设置内边距为 8px -->
            <view style="padding: 8px">
                <!-- 循环渲染表单组件 -->
                <view v-for="component in components" :key="component.key">
                    <!-- 自定义表单组件，传入组件配置、数据源获取方法、表单 ID、值，绑定输入和变化事件 -->
                    <learun-customform-item :component="component" :getDataSource="learun_form_getDataSource"
                        :formId="formId" :value="getValue(component.id)" @input="setValue" @change="handleChange"
                        :isEdit="true">
                    </learun-customform-item>
                </view>
            </view>
        </uni-forms>
        <!-- 当 ready 为 true 时显示底部按钮 -->
        <view class="learun-bottom-btns" v-if="ready">
            <!-- 清除按钮，点击调用 handleClear 方法 -->
            <button @click.stop="handleClear" style="flex: 1">
                {{ $t("清除") }}
            </button>
            <!-- 确定按钮，点击调用 handleOk 方法 -->
            <button @click.stop="handleOk" style="flex: 2" type="primary">
                {{ $t("确定") }}
            </button>
        </view>
    </view>
</template>

<script>
// 引入 lodash 的 set 和 get 方法，用于设置和获取对象属性
import set from "lodash/set";
import get from "lodash/get";
// 引入自定义表单的混入文件
import customFormMixins from "@/common/customform.js";

export default {
    // 使用自定义表单的混入
    mixins: [customFormMixins],
    data() {
        return {
            // 是否为弹窗层模式
            isLayer: true,
            // 数据是否准备好
            ready: false,
            // 表单的唯一 ID
            formId: this.GUID(),
            // 表单数据
            formData: {},
            // 表单组件列表
            components: [],
        };
    },
    async onLoad() {
        // 检查页面是否可以启动
        if (await this.PAGE_LAUNCH()) {
            // 初始化页面数据
            await this.init();
        }
    },
    onBackPress() {
        // 移除自定义事件监听
        this.OFF("learun-customapp-query");
    },
    onUnload() {
        // 移除自定义事件监听
        this.OFF("learun-customapp-query");
    },
    methods: {
        async init() {
            // 显示加载提示
            this.LOADING("加载数据中…");
            // 获取页面传递的表单方案、页面方案和表单数据
            const {
                formScheme,
                pageScheme,
                formData
            } = this.GET_PARAM();
            console.log(formScheme, pageScheme, formData, "formData");

            // 将表单数据存储到本地
            this.SET_DATA(`learun_form_data_${this.formId}`, this.formData);

            // 存储查询组件的数组
            const queryComponents = [];
            // 存储组件映射的对象
            const componentMap = {};

            // 遍历表单方案中的组件
            for (
                let i = 0, len = formScheme.formInfo.components.length; i < len; i++
            ) {
                const component = formScheme.formInfo.components[i];
                // 排除子表组件
                if (!component.config.isSubTable) {
                    componentMap[component.id] = component;
                }
            }

            // 遍历页面方案中的查询条件
            for (const item of pageScheme.table.querys) {
                // 复制对应的组件配置
                const component = this.COPY(componentMap[item.id]);
                // 设置组件为非必填、非禁用、非只读
                component.required = false;
                component.disabled = false;
                component.readonly = false;

                // 根据组件类型进行转换
                if (["password", "guid", "encode", "count"].includes(component.type)) {
                    component.type = "input";
                } else if (["textEditor"].includes(component.type)) {
                    component.type = "textarea";
                } else if (["date", "time"].includes(component.type)) {
                    component.dateType = `${component.dateType}range`;
                } else if (["company"].includes(component.type)) {
                    component.type = `companySelect`;
                } else if (["department"].includes(component.type)) {
                    component.type = `departmentSelect`;
                } else if (["createUser", "modifyUser"].includes(component.type)) {
                    component.type = `userSelect`;
                } else if (["createTime", "modifyTime"].includes(component.type)) {
                    component.type = `dateRange`;
                    component.dateType = `dateRange`;
                }

                // 设置部分组件为多选
                if (
                    [
                        "userSelect",
                        "departmentSelect",
                        `companySelect`,
                        "select",
                    ].includes(component.type)
                ) {
                    component.isMultiSelect = true;
                }

                // 加载组件的选择数据
                await this.learun_form_fetchDataSource(component, {});

                // 设置表单数据中的组件值
                this.$set(this.formData, component.id, formData[component.id]);
                // 将组件添加到查询组件数组中
                queryComponents.push(component);
            }

            // 更新组件列表
            this.components = queryComponents;

            // 隐藏加载提示
            this.HIDE_LOADING();

            // 标记数据准备好
            this.ready = true;
        },
        // 组件数据值改变时的处理方法
        async handleChange({
            data,
            component
        }) {
            // 对于部分组件类型，清除子组件的值
            if (
                [
                    "checkbox",
                    "radio",
                    "select",
                    "selectMultiple",
                    "treeSelect",
                    "inputLayer",
                    "buttonLayer",
                    "companySelect",
                    "departmentSelect",
                    "userSelect",
                ].includes(component.type)
            ) {
                await this.clearSubValue(component.id);
            }
        },

        // 递归清除子组件的值
        async clearSubValue(upProp) {
            for (const component of this.components) {
                if (component.upCtrl == upProp) {
                    // 获取数据值
                    await this.learun_form_fetchDataSource(component, this.formData);
                    // 更新组件的 key，触发重新渲染
                    component.key = this.GUID();
                    // 设置组件的值为 undefined
                    this.setValue({
                        path: component.id,
                        value: undefined,
                    });
                    // 递归清除子组件的值
                    await this.clearSubValue(component.id);
                }
            }
        },

        // 设置表单数据的方法
        setValue({
            path,
            value
        }) {
            set(this.formData, path, value);
        },

        // 获取表单数据的方法
        getValue(path) {
            return get(this.formData, path);
        },

        // 关闭页面的方法
        closePage() {
            uni.navigateBack();
        },
        // 清除表单数据的方法
        handleClear() {
            // 遍历组件，将表单数据中的组件值设置为 undefined
            for (const component of this.components) {
                this.$set(this.formData, component.id, undefined);
            }
            // 更新本地存储的表单数据
            this.SET_DATA(`learun_form_data_${this.formId}`, this.formData);
        },
        // 确定按钮的处理方法
        handleOk() {
            // 复制表单数据
            const formData = this.COPY(this.formData);
            // 触发自定义事件，传递表单数据
            this.EMIT("learun-customapp-query", formData);
            // 返回上一页
            uni.navigateBack();
        },
    },
};
</script>