<template>
	<view class="learun-edit-table-item">
		<!-- 用v-show而不是使用v-if modify by kyle on 2024-01-25-->
		<view class="learun-edit-table-item__content" > 
			<uni-table border stripe emptyText="暂无更多数据" >
				<uni-tr>
					<uni-th v-for="(col,index) in columns" align="center">{{col.label}}</uni-th>
				</uni-tr>
				<uni-tr>
					<uni-td v-for="(col,index) in columns">
						<learun-customform-item
							:key="col.key"
							:value="get(this.row, col.prop)"
							@input="setValue"
							@change="handleChange"
							:formId="formId"
							:tableId="tableId"
							:tableIndex="num - 1"
							:editMode="isEdit(col)"
							:component="col" 
							:getDataSource="getDataSource" 
							
						/>
					</uni-td>
				</uni-tr>			
			</uni-table>
		</view>
	</view>
</template>

<script>
	import set from "lodash/set"
	import get from "lodash/get"
	export default {
		name:'learun-edit-table-item',
		props:{
			columns: {
				type: Array,
				default:()=>[] 
			},
			num:Number,
			rowData:{},
			getDataSource:Function,
			editMode: {
				type: Boolean, 
				default: true ,
			},
			hasRemoveBtn: {
				type: Boolean, 
				default: true ,
			},
			formId:String,
			tableId:String,
		},
		created(){
			this.myRowData = this.COPY(this.rowData)
		},
		
		data(){
			return {
				myRowData:{},
				isOpen:true,
			}
		},
		computed:{
			hasDeleteBtn(){
				if(this.getValue('hasNoDeleteBtn')){
					return false
				}
				else if(this.editMode && this.hasRemoveBtn){
					return true
				}
				else{
					return false
				}
			},
			rules(){
				const rules = {}
				for(let j = 0,len = this.columns.length;j < len; j++ ){
					const component = this.columns[j]
					if(component.required){
						rules[component.prop] =rules[component.prop] || {rules:[]}
						rules[component.prop].rules.push({required:true,errorMessage: `${this.$t('请输入')}${component.label}`})
					}
					if(component.patterns && component.patterns.length > 0){
						rules[component.prop] = rules[component.prop] || {rules:[]}
						component.patterns.forEach(pattern =>{
							if(pattern.reg){
								let reg = pattern.reg.substring(1,pattern.reg.length-1)								
								rules[component.prop].rules.push({ pattern:reg, errorMessage: pattern.msg })
							}
						})
					}
				}
				return rules
			},
		},
		methods:{
			
			isEdit(col){
				if(this.editMode && (!this.rowData.disabled || this.getValue('abledList').indexOf(col.prop) > -1 )){
					return true
				}
				return false
			},
			handleClickTitle(){
				// this.isOpen = !this.isOpen
				//modify by kyle
			// this.myRowData = this.COPY(this.rowData)
				if (this.editMode) {
					this.isOpen = !this.isOpen
				}
			},
			handleDelete(){
				this.$emit('deleteRow')
			},
			handleChange({data,component}){
				this.$emit('change',{index:this.num - 1,rowData:this.myRowData,data:data,prop:component.prop,component:component})
			},
			
			
			// 设置表单数据的方法
			setValue({path, value}) {		
			  set(this.myRowData, path, value)
			},
			
			// 获取表单数据的方法
			getValue(path) {
			  return get(this.myRowData, path)
			},
			async validate(){
				const {err} =await this.VALIDATEFORM(this.$refs.myForm)
				if(err){
					return false
				}
				return true
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	.learun-edit-table-item{
		
		&__title{
			padding:0 8px;
			font-size: 12px;
			color: $uni-base-color;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 32px;
			background-color: $uni-info-light;
			box-sizing: border-box;
			
			.lefticon{
				margin-right: 4px;
			}
			
			.delete-btn{
				color: $uni-error;
			}
		}
		
		&__content{
			padding: 16px 8px 0 8px;
			background-color: #fdfdfd;
		}
	}
</style>
