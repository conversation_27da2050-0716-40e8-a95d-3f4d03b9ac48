{"scripts": {"build:dev": "vue-cli-service build --target app --dest dist-dev", "build:cn": "vue-cli-service build --target app --dest dist-cn", "build:yida": "vue-cli-service build --target app --dest dist-yida", "build:regent": "vue-cli-service build --target app --dest dist-regent", "build:cetvn": "vue-cli-service build --target app --dest dist-cetvn"}, "dependencies": {"@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^1.8.4", "cherry-markdown": "^0.8.20", "eval5": "^1.4.7", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "moment": "^2.24.0", "qr-scanner": "^1.4.2", "quagga": "^0.12.1", "vue": "^2.7.16", "webrtc-adapter": "^9.0.1"}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}}}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@vue/cli": "^4.5.19", "@vue/cli-service": "^5.0.8", "babel-loader": "^10.0.0", "sass": "^1.32.13", "sass-loader": "^10.2.0", "vue-template-compiler": "^2.7.16"}}