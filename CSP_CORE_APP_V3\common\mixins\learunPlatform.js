/**
 * 和框架后端相关的方法集合
 */
import mapValues from "lodash/mapValues";
import keyBy from "lodash/keyBy";
import values from "lodash/values";
// 即时通讯模块
import {
	imInit,
	imClose
} from './im.js'
import {
	onEvent,
	onOnceEvent,
	emitEvent,
	offEvent
} from "./eventBus";
export default {
	methods: {
		async PAGE_LAUNCH() {
			const isLaunch = this.GET_GLOBAL("isLaunch");
			if (isLaunch !== false) {
				// 刷新后执行
				if (this.isLayer) {
					this.RELAUNCH_TO("/pages/home");
					return false;
				}
				this.SET_GLOBAL("isLaunch", false);
				const pages = getCurrentPages();
				const pagePath = pages ? "/" + pages.slice(-1)[0].route : "/";

				if (!["/pages/login"].includes(pagePath)) {
					const loginState = await this.CHECK_LOGIN_STATE();
					if (!loginState) {
						this.RELAUNCH_TO("/pages/login");
						return false;
					}
				}

				// 同时发出请求，获取所有功能按钮、我的列表、数据字典中的功能名称表
				const [modules] = await Promise.all([
					this.HTTP_GET({
						url: "/mapp/modules"
					}),
					this.HTTP_GET({
						url: "/mapp/module/mylist"
					}),
					this.FETCH_DATAITEM("function").then((result) =>
						mapValues(keyBy(values(result), "f_ItemValue"), "f_ItemName")
					),
				]);
				const loginUser = this.GET_GLOBAL("loginUser");
				// 功能区按钮需要处理 icon
				const myModules = [];
				modules.forEach((item) => {
					if (
						item.f_EnabledMark == 1 &&
						item.f_IsSystem != 3 &&
						(loginUser.f_SecurityLevel == 1 ||
							(loginUser.moduleAuthIds || []).indexOf(item.f_Id) > -1)
					) {
						const icon = item.f_Icon ? item.f_Icon : "";
						myModules.push({
							...item,
							icon: icon
						});
					}
				});
				this.SET_GLOBAL("learun_modules", myModules);
				this.HIDE_LOADING();

				// 判断是否是流程表单页面,是的话就跳转回标记的页面
				const routeOptions = pages ? pages.slice(-1)[0].options : {};
				if (routeOptions.routeFrom) {
					this.JUMP_TO(routeOptions.routeFrom, routeOptions);
				}
				return true;
			}
			return true;
		},
		// 拉取多语言类型
		async FETCH_LANG_TYPE() {
			const types = (
				(await this.HTTP_GET({
					url: `/language/types`,
				})) || []
			).map((t) => ({
				...t,
				value: t.f_Code,
				label: t.f_Name,
			}));
			this.SET_GLOBAL("lang_types", types);
		},

		GET_LANG_MAIN_TYPE() {
			const langConfig = this.LEARUN_CONFIG("language");
			return langConfig.mainType;
		},
		GET_LANG_TYPE() {
			const langConfig = this.LEARUN_CONFIG("language");
			return this.GET_STORAGE("learun_lang_type") || langConfig.type;
		},
		GET_LANG_TYPES() {
			return this.GET_GLOBAL("lang_types") || [];
		},

		async FETCH_LANG_DATA() {
			await this.FETCH_LANG_DATA_NOT_RELAUNCH_TO();
			try {
				const currentLang = this.GET_LANG_TYPE();
				const mainLang = this.GET_LANG_MAIN_TYPE();
				let langData = {};

				if (currentLang !== mainLang) {
					const response = await this.HTTP_GET({
						url: `/language/mapping/${mainLang}/${currentLang}`
					});
					langData = response || {};
				}

				this.SET_GLOBAL('lang_data', langData);

				// 设置tabBar多语言
				let home = this.$t("首页");
				let message = this.$t("工作台");
				let addressBook = this.$t("新闻");
				let my = this.$t("我的");

				uni.setTabBarItem({
					index: 0,
					text: home
				});
				uni.setTabBarItem({
					index: 1,
					text: message
				});
				uni.setTabBarItem({
					index: 2,
					text: addressBook
				});
				uni.setTabBarItem({
					index: 3,
					text: my
				});

				// 重新加载当前页面以应用新的语言
				const pages = getCurrentPages();
				if (pages && pages.length > 0) {
					const path = pages[0].route;
					if (pages.length > 1) {
						if (pages[1].route === 'pages/signup') {
							this.$nextTick(() => {
								this.RELAUNCH_TO(`/${pages[1].route}`);
							});
							return;
						}
						if (pages[1].route === 'pages/changePassword') {
							this.$nextTick(() => {
								this.RELAUNCH_TO(`/${pages[1].route}`);
							});
							return;
						}
					}
					this.$nextTick(() => {
						this.RELAUNCH_TO(`/${path}`);
					});
				}
			} catch (error) {
				console.error('获取语言数据失败:', error);
			}
			if (getCurrentPages()[0]) {
				const path = getCurrentPages()[0].route;
				this.$nextTick(() => {
					this.RELAUNCH_TO(`/${path}`);
				});
			}
		},

		async FETCH_LANG_DATA_NOT_RELAUNCH_TO() {
			if (this.GET_LANG_MAIN_TYPE() != this.GET_LANG_TYPE()) {
				const data = await this.HTTP_GET({
					url: `/language/mapping/${this.GET_LANG_MAIN_TYPE()}/${this.GET_LANG_TYPE()}`,
				});
				this.SET_GLOBAL("lang_data", data);
			} else {
				this.SET_GLOBAL("lang_data", {});
			}
		},
		// 获取图标
		async FETCH_ICONS() {
			try {
				const iconVer = this.GET_STORAGE("learun_icon_ver");
				let iconList = JSON.parse(this.GET_STORAGE("learun_icon_list") || "[]");
				this.SET_GLOBAL("learun_icon_list", iconList);

				const {
					ver,
					list
				} = (await this.HTTP_GET({
					url: `/data/icons`,
					params: {
						ver: iconVer,
					}
				})) || {};
				if (ver && ver != iconVer) {
					list.forEach((item) => {
						const point = {
							code: item.f_Code,
							name: item.f_Name,
							body: item.f_Body,
							width: item.f_Width,
							height: item.f_Height,
						};
						iconList.push(point);
					});
					this.SET_STORAGE("learun_icon_list", JSON.stringify(iconList));
					this.SET_STORAGE("learun_icon_ver", ver);
					this.SET_GLOBAL("learun_icon_list", iconList);

					if (getCurrentPages()[0]) {
						const path = getCurrentPages()[0].route;
						this.$nextTick(() => {
							this.RELAUNCH_TO(`/${path}`);
						});
					}
				}
			} catch (error) {
				console.log(error, "error");
			}
		},

		GET_ICON(code) {
			let learun_icon_list = this.GET_GLOBAL("learun_icon_list") 
			if (!learun_icon_list) {
				learun_icon_list = JSON.parse(this.GET_STORAGE("learun_icon_list"))
			}
			return learun_icon_list.find(item => item.code == code)
		},
		GET_SVG_DATAURL(type, color = '#333333') {
			const icon = this.GET_ICON(type);
			if (icon) {
				const iconBody = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img"  viewBox="0 0 ${
          icon.width
        } ${icon.height}" >${icon.body.replace(
          /currentColor/g,
          color
        )}</svg>`;
				return `data:image/svg+xml,${encodeURIComponent(iconBody)}`;
			} else {
				return ''
			}
		},


		$t(label) {
			const data = this.GET_GLOBAL("lang_data") || {};
			return data[label] || label;
		},

		// 判断用户登录状态
		// 验证登录状态
		async CHECK_LOGIN_STATE() {
			// 判断 token 字段，没有 token 则必定无登录态
			const token = this.GET_GLOBAL("token") || this.GET_STORAGE("token");
			if (!token || token === "null" || token === "undefined") {
				this.HIDE_LOADING();
				return false;
			}
			this.SET_GLOBAL("token", token);

			// 判断是否有 loginUser 对象
			if (this.GET_GLOBAL("loginUser")) {
				return true;
			}

			this.LOADING("用户数据...");

			// 拉取用户信息以验证登录态
			const success = await this.FETCH_CURRENT_USERINFO();
			this.HIDE_LOADING();
			if (!success) {
				this.TOAST("获取用户/部门/公司信息时出现错误，登录失败");

				this.SET_GLOBAL("token", null);
				this.SET_STORAGE("token", null);

				return false;
			}

			return true;
		},

		// 拉取数据字典
		async FETCH_DATAITEM(code) {
			if (!code) {
				return [];
			}
			return await this.HTTP_GET({
				url: `/data/dataitem/details/${code}`,
			});
		},

		// 拉取数据源
		async FETCH_DATASOURCE(code, param, params) {
			if (!code) {
				return [];
			}
			if (!param) {
				return await this.HTTP_POST({
					url: `/data/dbsource/${code}/list`,
					data: {
						ParamsJson: params ? JSON.stringify(params) : "",
					},
				});
			} else {
				return await this.HTTP_POST({
					url: `/data/dbsource/${code}/list`,
					data: {
						ParamsJson: JSON.stringify({
							param,
						}),
					},
				});
			}
		},

		// 拉取一个或多个用户信息
		// 传入参数为 ID 字符串时拉取单个用户信息
		// 传入参数为数组时拉取多个用户信息
		async FETCH_USER(userId) {
			const isMultiple = Array.isArray(userId);

			if (!isMultiple) {
				if (!userId) {
					return null;
				}
				const result = await this.HTTP_POST({
					url: "/organization/users",
					data: {
						ids: userId,
					},
				});

				const res = result ?
					result.map((t) => ({
						...t,
						value: t.f_UserId,
						label: t.f_RealName,
						account: t.f_Account,
					})) :
					[];

				return res.length > 0 ? res[0] : null;
			} else {
				if (userId.length <= 0) {
					return [];
				}
				const result = await this.HTTP_POST({
					url: "/organization/users",
					data: {
						ids: userId.join(","),
					},
				});
				return result ?
					result.map((t) => ({
						...t,
						value: t.f_UserId,
						label: t.f_RealName,
						account: t.f_Account,
					})) :
					[];
			}
		},

		// 拉取一个或多个部门信息
		//   传入参数为 ID 字符串时拉取单个部门信息
		//   传入参数为数组时拉取多个部门信息
		async FETCH_DEPARTMENT(departmentId) {
			const isMultiple = Array.isArray(departmentId);
			if (!isMultiple) {
				if (!departmentId) {
					return null;
				}
				const result = await this.HTTP_GET({
					url: `/organization/department/${departmentId}`,
				});

				return result ?
					{
						...result,
						value: result.f_DepartmentId,
						label: result.f_FullName,
					} :
					null;
			} else {
				if (departmentId.length <= 0) {
					return [];
				}
				const result = await this.HTTP_POST({
					url: `/organization/departments`,
					data: {
						ids: departmentId.join(","),
					},
				});
				return result ?
					result.map((t) => ({
						...t,
						value: t.f_DepartmentId,
						label: t.f_FullName,
					})) :
					[];
			}
		},

		// 拉取部门信息
		async FETCH_DEPARTMENTS(companyId) {
			const list = await this.HTTP_GET({
				url: `/organization/departments/${companyId}`,
			});
			return list || [];
		},

		// 拉取公司信息
		async FETCH_COMPANYS() {
			const list = await this.HTTP_GET({
				url: `/organization/companys`,
			});
			return (list || []).map((t) => ({
				...t,
				value: t.f_CompanyId,
				label: t.f_FullName,
			}));
		},

		// 拉取行政区域信息
		async FETCH_AREAS(pid) {
			const list = await this.HTTP_GET({
				url: `/data/areas/${pid}`,
			});
			return list || [];
		},

		// 拉取用户信息数据直接存入全局数据
		// 用户信息拉取失败则直接返回 false
		async FETCH_CURRENT_USERINFO() {
			// loginUser 中合并 role、post 两个字段
			const userInfo = await this.HTTP_GET({
				url: "/login/app",
			});
			if (!userInfo) {
				return false;
			}
			const user = userInfo;

			user.id = user.f_UserId;
			user.name = user.f_RealName;
			user.account = user.f_Account;
			user.postIds = user.postIds ? user.postIds?.split(',') : [];
			user.roles = [];

			// 加入角色信息
			if (user.roleIds) {
				user.roles = await this.HTTP_POST({
					url: `/organization/roles`,
					data: {
						ids: user.roleIds,
					},
				});
			}
			// 加入部门信息
			if (user.f_DepartmentId) {
				user.department = await this.FETCH_DEPARTMENT(user.f_DepartmentId);
			}

			// 岗位

			const postIds = user.postIds || [];
			const currentPost = this.GET_STORAGE('LOCALE_POST_KEY') || ''
			const type = currentPost;
			const typeList = type.split('|');
			let postId = '';
			if (typeList.length > 1) {
				postId = typeList[1];
				if (typeList[0] === user?.id && postIds.includes(typeList[1])) {
					userInfo.postId = typeList[1];
				}
			}

			if (!userInfo.postId && postIds.length > 0) {
				userInfo.postId = postIds[0];
			}

			if (!userInfo.postId && postId) {
				userInfo.postId = postId;
			}
			this.SET_GLOBAL("loginUser", user);

			if (user) {
				let hasImFunction = this.GET_GLOBAL("SetImFunction")
				if (!hasImFunction) {
					this.SET_GLOBAL("SetImFunction", true)
					// 监听消息事件
					onEvent("learun-im-system-message", (data) => {
						console.log('245', 'learun-im-system-message')
						console.log(245, "加载即时通讯信息", data.userId, data.msg, data.dateTime, data.id, data.isSystem)
						if (data.msg != "" && data.msg != null) {
							var notification = new Notification('【CSP】', {
								body: data.msg,
								dir: 'rtl', // 文字方向
								icon: '/static/icons/icon.png', // 通知图标
							});
						}
					});
					// 加载即时通讯信息
					await imInit(this.API, user.f_UserId)
				}
			}

			return true;
		},
		async GET_USER_POSTS(ids) {
			if (this.VALIDATENULL(ids)) {
				return [];
			}
			const labelKey = 'f_Name';
			const idList = ids.split(',');
			const list = [];
			const postList = await this.HTTP_POST({
				url: `/organization/posts`,
				data: {
					ids: ids
				},
			});
			const getPost = {};
			postList.forEach((item) => {
				getPost[item.f_PostId] = item;
			});
			idList.forEach((id) => {
				const post = getPost[id];
				if (post) {
					if (post.deptName) {
						list.push({
							value: id,
							label: `${post.deptName}-${post[labelKey]}`,
						});
					} else {
						list.push({
							value: id,
							label: `${post[labelKey]}`,
						});
					}
				}
			});
			return list;
		},
		SET_CURRENT_POST(postId) {
			const user = this.GET_GLOBAL('loginUser')
			const currentPost = `${user?.id}|${postId}`;
			this.SET_STORAGE('LOCALE_POST_KEY', currentPost)

		},
		async FETCH_POST_DATA() {
			await this.FETCH_CURRENT_USERINFO()
			if (getCurrentPages()[0]) {
				const path = getCurrentPages()[0].route;
				this.$nextTick(() => {
					this.RELAUNCH_TO(`/${path}`);
				});
			}
		},

		async GET_CURRENT_COMPANY() {
			let company = this.GET_GLOBAL("currentCompany");
			if (!company) {
				const user = this.GET_GLOBAL("loginUser");
				(company = user.f_CompanyId ?
					await this.HTTP_GET({
						url: `/organization/company/${user.f_CompanyId}`,
					}) :
					{}),
				this.SET_GLOBAL("currentCompany", company);
			}
			return company;
		},

		async GET_CURRENT_DEPARTMENT() {
			let department = this.GET_GLOBAL("currentDepartment");
			if (!department) {
				const user = this.GET_GLOBAL("loginUser");
				department = user.f_DepartmentId ?
					await this.HTTP_GET({
						url: `/organization/department/${user.f_DepartmentId}`,
					}) :
					{};
				this.SET_GLOBAL("currentDepartment", department);
			}

			return department;
		},

		// 文件上传
		async UPLOAD_FILE(fileList) {
			if (!fileList) {
				return
			}
			let folderId = this.GUID();
			for (const fileData of fileList) {
				if (fileData.folderId) {
					folderId = fileData.folderId;
				}
				if (fileData.uploadType == "add") {
					await this.HTTP_UPLOAD(
						`/system/annexesfile/${fileData.fileID}`,
						fileData.path, {
							fileName: fileData.name,
							folderId,
						}
					);
				} else if (fileData.uploadType == "remove") {
					await this.HTTP_DELETE({
						url: `/system/annexesfile/${fileData.fileID}`,
					});
				}
			}

			const hasFile =
				fileList.filter((t) => t.uploadType != "remove").length > 0;

			return hasFile ? folderId : "";
		},
		// 文件下载
		async DOWNLOAD_FILE(folderId) {
			let res = [];
			if (!this.VALIDATENULL(folderId)) {
				const list =
					(await this.HTTP_GET({
						url: `/system/annexesfile/${folderId}/list`,
					})) || [];

				res = list.map((t) => ({
					fileID: t.f_Id,
					folderId: t.f_FolderId,
					name: t.f_FileName,
					extname: t.f_FileExtensions,
					size: t.f_FileSize,
					uploadType: "old",
					url: `${this.API}/system/annexesfile/${
            t.f_Id
          }?token=${this.GET_GLOBAL("token")}`,
				}));
			}

			return res;
		},

		// 获取表单数据
		GET_FORMDATA(formData, tableName) {
			const res = {};
			for (var key in formData) {
				if (key.indexOf(tableName + "_") == 0) {
					let formProp = key.replace(tableName + "_", "");
					res[formProp] = formData[key];
				}
			}
			return res;
		},

		// 功能菜单跳转
		TO_MODULE_URL(url, param) {
			const modules = this.GET_GLOBAL("learun_modules") || [];
			const module = modules.find((t) => t.f_Url == url);
			this.TO_MODULE(module, param);
		},
		TO_MODULE_ID(id) {
			const modules = this.GET_GLOBAL("learun_modules") || [];
			const module = modules.find((t) => t.f_Id == id);
			this.TO_MODULE(module);
		},
		TO_MODULE(module, param) {
			if (module != null) {
				if (module.f_IsSystem === 2) {
					this.NAV_TO(
						`/pages/customapp/list?formId=${module.f_FormVerison}`,
						module,
						true
					);
					return;
				} else if (module.f_IsSystem === 4) {
					this.NAV_TO(`/pages/home/<USER>
						id: module.f_Id,
						title: module.f_Name,
					});
					return;
				}
				this.NAV_TO(`${module.f_Url}`, param, true);
			} else {
				this.TOAST("你没有该功能权限！");
			}
		},

		GET_MODULEID() {
			const path = this.PATH();
			const modules = this.GET_GLOBAL("learun_modules") || [];
			const module = modules.find((t) => t.f_Url == path);
			return module.f_Id;
		},

		// 加载功能页面的权限信息
		async FETCH_AUTH(id) {
			const loginUser = this.GET_GLOBAL("loginUser");
			if (loginUser.f_SecurityLevel == 1) {
				this.SET_DATA(`learun_auth_${id}`, {
					isAuth: false,
				});
			} else {
				const data = await this.HTTP_GET({
					url: `/permission/module/app/${id}`,
				});
				const authData = {
					isAuth: true,
					buttons: data.buttons || [],
					columns: (data.columns || []).map((t) => t.toLowerCase()),
					forms: {},
				};
				const forms = data.forms || [];
				forms.forEach((item) => {
					const itemList = item.split(",");
					const propList = itemList[0].split("|");
					if (propList.length > 1) {
						// 表格
						authData.forms[propList[0].toLowerCase()] =
							authData.forms[propList[0].toLowerCase()] || {};
						authData.forms[propList[0].toLowerCase()][
							propList[1].toLowerCase()
						] = itemList[1];
					} else {
						// 表单字段
						authData.forms[itemList[0].toLowerCase()] = itemList[1];
					}
				});

				this.SET_DATA(`learun_auth_${id}`, authData);
			}
		},
		// 列权限
		GET_COLUMN_AUTH(columnId, moduleId) {
			if (!columnId) {
				return false;
			}

			const authData = this.GET_DATA(`learun_auth_${moduleId}`);
			if (authData.isAuth) {
				// 全选处理
				if (authData.columns.includes("learun_auth_all")) {
					return true;
				}
				if (authData.columns.indexOf(columnId.toLowerCase()) == -1) {
					return false;
				}
			}

			return true;
		},
		// 按钮权限
		GET_BUTTON_AUTH(buttonId, moduleId) {
			if (!buttonId) {
				return false;
			}

			const authData = this.GET_DATA(`learun_auth_${moduleId}`);
			if (authData.isAuth) {
				if (authData.buttons.includes("learun_auth_all")) {
					return true;
				}
				if (authData.buttons.indexOf(buttonId) == -1) {
					return false;
				}
			}

			return true;
		},
		// 表单查看权限
		GET_FORM_LOOK_AUTH(formId, moduleId) {
			let isWorkflow = false;
			if (!moduleId) {
				isWorkflow = true;
				moduleId = "wfform";
			}

			if (!formId) {
				return false;
			}

			const authData = this.GET_DATA(`learun_auth_${moduleId}`);
			if (authData.isAuth) {
				if (isWorkflow) {
					if (
						authData.forms[formId.toLowerCase()] &&
						authData.forms[formId.toLowerCase()] == "3"
					) {
						return false;
					}
				} else {
					// 全选处理
					const authForms = authData.forms;
					if (authForms["learun_auth_all"]) {
						return true;
					}
					if (!authData.forms[formId.toLowerCase()]) {
						return false;
					}
				}
			}
			return true;
		},
		// 表单编辑权限
		GET_FORM_EDIT_AUTH(formId, moduleId) {
			let isWorkflow = false;
			if (!moduleId) {
				isWorkflow = true;
				moduleId = "wfform";
			}

			if (!formId) {
				return false;
			}

			const authData = this.GET_DATA(`learun_auth_${moduleId}`);
			if (authData.isAuth) {
				if (isWorkflow) {
					if (
						authData.forms[formId.toLowerCase()] &&
						authData.forms[formId.toLowerCase()] != "2"
					) {
						return false;
					}
				} else {
					// 全选处理
					const authForms = authData.forms;
					if (authForms["learun_auth_all"]) {
						return true;
					}
					if (authData.forms[formId.toLowerCase()] != "2") {
						return false;
					}
				}
			}
			return true;
		},
		// 子表查看权限
		GET_FORMTABLE_LOOK_AUTH(formId, prop, moduleId) {
			let isWorkflow = false;
			if (!moduleId) {
				isWorkflow = true;
				moduleId = "wfform";
			}

			if (!formId || !prop) {
				return false;
			}
			const authData = this.GET_DATA(`learun_auth_${moduleId}`);
			if (authData.isAuth) {
				if (isWorkflow) {
					if (
						authData.forms[formId.toLowerCase()] &&
						authData.forms[formId.toLowerCase()][prop.toLowerCase()] &&
						authData.forms[formId.toLowerCase()][prop.toLowerCase()] == "3"
					) {
						return false;
					}
				} else {
					const authForms = authData.forms;
					if (authForms["learun_auth_all"]) {
						return true;
					}
					if (
						!authData.forms[formId.toLowerCase()] ||
						!authData.forms[formId.toLowerCase()][prop.toLowerCase()]
					) {
						return false;
					}
				}
			}
			return true;
		},
		// 子表编辑权限
		GET_FORMTABLE_EDIT_AUTH(formId, prop, moduleId) {
			let isWorkflow = false;
			if (!moduleId) {
				isWorkflow = true;
				moduleId = "wfform";
			}
			if (!formId || !prop) {
				return false;
			}

			const authData = this.GET_DATA(`learun_auth_${moduleId}`);
			if (authData.isAuth) {
				if (isWorkflow) {
					if (
						authData.forms[formId.toLowerCase()] &&
						authData.forms[formId.toLowerCase()][prop.toLowerCase()] &&
						authData.forms[formId.toLowerCase()][prop.toLowerCase()] != "2"
					) {
						return false;
					}
				} else {
					const authForms = authData.forms;
					if (authForms["learun_auth_all"]) {
						return true;
					}
					if (
						!authData.forms[formId.toLowerCase()] ||
						authData.forms[formId.toLowerCase()][prop.toLowerCase()] != "2"
					) {
						return false;
					}
				}
			}
			return true;
		},

		// 流程
		CREATE_WORKFLOW({
			code,
			name,
			formData,
			processId,
			isLoadFormData
		}) {
			this.NAV_TO("/pages/workflow/releasetask/single?type=create", {
				f_Code: code,
				f_Name: name,
				processId,
				formData,
				isLoadFormData,
			});
		},
	},
};