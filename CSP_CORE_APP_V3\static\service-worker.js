// 添加notificationclick事件监听器，在点击notification时触发
self.addEventListener('notificationclick', function(event) {
  // 关闭当前的弹窗
  event.notification.close();
  // 在新窗口打开页面
  event.waitUntil(
    clients.openWindow('https://www.baidu.com')
  );
});

// 触发一条通知
self.registration.showNotification('您有新消息', {
  body: 'Hello Service Worker',
});

// 在Service Worker中每分钟调用一个函数
self.addEventListener('install', function(event) {
  setInterval(function() {
    // 调用你的函数
    getMessage();
  }, 10000); // 每分钟执行一次，单位为毫秒
});

// self.addEventListener('activate', event => {
//   setInterval(function() {
// 		console.log(29)
//   }, 10000); // 每分钟执行一次，单位为毫秒
// });

// 定义你的函数
function getMessage() {
	console.log(32, this, this.API, this.GET_GLOBAL('token'))
  // 这里是你的函数逻辑
	
	// var xhr = new XMLHttpRequest()
	// xhr.open('GET', "https://tstsystem04.crystal-csc.cn/csp_core_api/message/msg/sys/page?rows=100&page=1&sidx=F_IsRead,F_CreateDate+DESC&keyword=&isDelete=0", true);
	// xhr.onreadystatechange = function() {
	// 	if (xhr.readyState === 4) {
	// 		if (xhr.status === 200) {
	// 			console.log(40, '请求成功：', xhr.responseText);
	// 		} else {
	// 			console.log(40, '请求失败：', xhr.status);
	// 		}
	// 	}
	// }
	
	
	//https://tstsystem04.crystal-csc.cn/csp_core_api/message/msg/sys/page?rows=100&page=1&sidx=F_IsRead,F_CreateDate+DESC&keyword=&isDelete=0
	// self.registration.showNotification('test27');
}

function test(registration) {
	registration.showNotification('Hello World123');
}