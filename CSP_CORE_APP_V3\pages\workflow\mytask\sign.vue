<template>
	<!-- 页面主容器 -->
	<view>
		<!-- 显示流程名称 -->
		<l-label :title="$t('流程名称：')">{{ taskParam.taskName }}</l-label>
		<!-- 显示当前操作，若为加签操作则仅显示加签，否则显示操作类型及名称 -->
		<l-label :title="$t('当前操作：')">{{ typeText }}{{ type === 'sign' ? '' : ` [${taskParam.name}]` }}</l-label>
		<!-- 若当前操作是加签，显示加签人员选择器 -->
		<l-organize-picker v-if="type === 'sign'" v-model="staff" :title="$t('加签人员')" type="user" required />

		<!-- 手写签名 canvas 区 -->
		<template v-if="Number(taskParam.isSign) === 1">
			<!-- 手写签名标题 -->
			<view class="cu-form-group">
				<view class="title">{{$t('手写签名：')}}</view>
			</view>
			<!-- 签名区域 -->
			<view class="sign-area bg-white">
				<!-- 签名画布，绑定触摸事件 -->
				<canvas v-if="canvas" @touchmove="signMove" @touchstart="signStart($event)" @touchend="signEnd"
					@touchcancel="signEnd" disable-scroll="true" canvas-id="sign-canvas" id="sign-canvas"
					class="sign-canvas"></canvas>
			</view>
			<!-- 签名操作区域，包含清空签名板按钮 -->
			<view class="sign-action padding-bottom text-right">
				<l-button @click="clearSign" color="red" style="margin-right: 15px;">{{$t('清空签名板')}}</l-button>
			</view>
		</template>

		<!-- 备注输入框 -->
		<l-textarea v-model="remark" :placeholder="`${$t('输入')}${typeText}${$t('备注')}`" :title="$t('备注：')" formMode />

		<!-- 提交按钮区域 -->
		<view class="padding margin-top bg-white">
			<!-- 提交流程按钮 -->
			<l-button @click="submit" class="block" size="lg" color="green"
				block>{{$t('提交流程')}}{{ typeText }}</l-button>
		</view>
	</view>
</template>

<script>
	// 定义全局变量，用于存储画布上下文和触摸点信息
	let context = null
	let touchs = []

	export default {
		data() {
			return {
				// 当前操作类型，默认为加签
				type: 'sign',
				// 当前操作类型的文本描述
				typeText: '',
				// 加签人员信息
				staff: null,
				// 备注信息
				remark: '',
				// 任务参数对象
				taskParam: {},
				// 控制签名画布的显示与隐藏
				canvas: true
			}
		},

		// 页面加载时执行初始化操作
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("流程加签与审核")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 执行初始化方法
				await this.init()
			}
		},

		methods: {
			// 页面初始化
			async init() {
				// 获取页面传递的任务参数
				this.taskParam = this.GET_PARAM()
				// 获取当前操作类型
				this.type = this.taskParam.type
				// 根据操作类型设置操作类型文本描述
				this.typeText = this.taskParam.type === 'sign' ? this.$t('加签') : this.$t('审核')

				// 如果需要手写签名，初始化签名画布
				if (Number(this.taskParam.isSign) === 1) {
					this.canvasInit()
				}
			},

			// 初始化签名区 canvas
			canvasInit() {
				// 显示签名画布
				this.canvas = true
				// 创建画布上下文
				context = uni.createCanvasContext('sign-canvas')
				// 设置笔触颜色为黑色
				context.setStrokeStyle('#000')
				// 设置线条宽度为 5
				context.setLineWidth(5)
				// 设置线条端点为圆形
				context.setLineCap('round')
				// 设置线条连接处为圆形
				context.setLineJoin('round')
				// 清空触摸点信息
				touchs = []
			},

			// 点击「提交」按钮
			async submit() {
				// 如果是加签操作且未选择加签人员，提示用户补全必填项
				if (this.type === 'sign' && (!this.staff || !this.staff.id)) {
					this.CONFIRM(this.$t('请补全必填项'), this.$t('必须指定一个加签用户。'))
					return
				}

				// 构建提交数据对象
				const postData = {
					// 流程 ID
					processId: this.taskParam.processId,
					// 任务 ID
					taskId: this.taskParam.taskId,
					// 备注信息
					des: this.remark
				}

				// 如果存在操作代码或操作名称，添加到提交数据中
				if (this.taskParam.code || this.taskParam.name) {
					postData.operationCode = this.taskParam.code
					postData.operationName = this.taskParam.name
				}

				// 如果存在表单请求信息，添加到提交数据中
				if (this.taskParam.formreq) {
					postData.formreq = this.taskParam.formreq
				}

				// 如果是加签操作，添加加签人员 ID 到提交数据中
				if (this.type === 'sign') {
					postData.userId = this.staff.id
				} else {
					// 否则添加审核人员信息到提交数据中
					postData.auditors = this.taskParam.auditors
				}

				// 需要手写签名时，将 canvas 导出为 base64 格式
				// 各个平台写法均不相同，需要注意
				if (Number(this.taskParam.isSign) === 1) {
					// H5 平台，canvasToTempFilePath 的结果直接为画布的 base64
					// #ifdef H5
					const [err, {
						tempFilePath
					}] = await uni.canvasToTempFilePath({
						canvasId: 'sign-canvas'
					})
					postData.signUrl = tempFilePath
					// #endif

					// App 平台，canvasToTempFilePath 输出文件，上传后台转为 base64 格式
					// #ifdef APP-VUE
					const [err, {
						tempFilePath
					}] = await uni.canvasToTempFilePath({
						canvasId: 'sign-canvas'
					})
					const signBase64 = await this.HTTP_UPLOAD('/annexes/tobase64', tempFilePath)
					postData.signUrl = 'data:image/png;base64,' + signBase64
					// #endif

					// 微信小程序，canvasToTempFilePath 输出文件，使用文件管理器以 base64 格式读取文件即可
					// #ifdef MP-WEIXIN
					const [err, {
						tempFilePath
					}] = await uni.canvasToTempFilePath({
						canvasId: 'sign-canvas'
					})
					postData.signUrl = 'data:image/png;base64,' + uni.getFileSystemManager().readFileSync(tempFilePath,
						'base64')
					// #endif

					// #ifdef MP-ALIPAY
					// 钉钉小程序，context.toTempFilePath 输出文件，上传后台转为 base64 格式
					// #ifdef MP-DINGTALK
					const filePath = await new Promise((res, rej) => {
						context.toTempFilePath({
							success: ({
								filePath
							}) => {
								res(filePath)
							},
							fail: () => {
								rej()
							}
						})
					})

					const signBase64 = await this.HTTP_UPLOAD('/annexes/tobase64', filePath)
					postData.signUrl = 'data:image/png;base64,' + signBase64
					// #endif

					// 支付宝小程序，context.toDataURL 直接输出 base64 字符串
					// #ifndef MP-DINGTALK
					postData.signUrl = await context.toDataURL('image/png', 1)
					// #endif
					// #endif
				}

				// 根据操作类型选择提交的 URL
				const url =
					this.type === 'sign' ?
					'/newwf/signflow' :
					this.taskParam.isFromSignAudit ?
					'/newwf/signauditflow' :
					'/newwf/auditflow'

				// 发送 POST 请求提交数据
				const success = await this.HTTP_POST(url, postData,
					`${this.$t("提交")}[${this.typeText}]${this.$t("时发生错误")}`)

				// 如果提交失败，直接返回
				if (!success) {
					return
				}

				// 触发任务列表更新事件
				this.EMIT('task-list-change')
				// 返回上两级页面
				this.NAV_BACK(2)
				// 提示用户提交成功
				this.TOAST(`已成功提交${this.typeText}`, 'success')
			},

			// 手写板事件（开始拖动）
			signStart(e) {
				// 将触摸点的坐标信息添加到触摸点数组中
				touchs.push({
					x: e.changedTouches[0].x,
					y: e.changedTouches[0].y
				})
			},

			// 手写板事件（拖动签名）
			signMove(e) {
				// 将触摸点的坐标信息添加到触摸点数组中
				touchs.push({
					x: e.touches[0].x,
					y: e.touches[0].y
				})
				// 绘制线条
				this.drawLine()
			},

			// 手写板事件（签名结束）
			signEnd(e) {
				// 清空触摸点数组
				touchs = []
			},

			// 手写板事件（绘出线型）
			drawLine() {
				// 如果触摸点数量小于 2，无法绘制线条，直接返回
				if (touchs.length < 2) {
					return
				}

				// 获取前两个触摸点
				const [p1, p2] = touchs
				// 移除第一个触摸点
				touchs.shift()
				// 移动笔触到第一个触摸点
				context.moveTo(p1.x, p1.y)
				// 绘制线条到第二个触摸点
				context.lineTo(p2.x, p2.y)
				// 绘制线条
				context.stroke()
				// 渲染画布
				context.draw(true)
			},

			// 清除手写板
			// 阿里小程序无法使用 clearRect 来清空，因此直接重新渲染 canvas
			clearSign() {
				// #ifndef MP-ALIPAY
				// 非阿里小程序，使用 clearRect 清空画布
				context.clearRect(0, 0, 9999, 9999)
				// 渲染画布
				context.draw()
				// 设置笔触颜色为黑色
				context.setStrokeStyle('#000')
				// 设置线条宽度为 5
				context.setLineWidth(5)
				// 设置线条端点为圆形
				context.setLineCap('round')
				// 设置线条连接处为圆形
				context.setLineJoin('round')
				// #endif

				// #ifdef MP-ALIPAY
				// 阿里系小程序无法 clearRect 清空画布，必须重新渲染 canvas
				this.canvas = false
				// 等待下一次 DOM 更新完成后重新初始化画布
				this.$nextTick(() => {
					this.canvasInit()
				})
				// #endif
			}
		}
	}
</script>

<style lang="less" scoped>
	.sign-area {
		// 签名区域最小高度
		min-height: 500rpx;
		// 签名区域内边距
		margin: 23rpx;
		// 签名区域边框样式
		border: 2rpx dashed #444444;

		.sign-canvas {
			// 签名画布宽度
			width: 700rpx;
			// 签名画布高度
			height: 500rpx;
		}

		.sign-action {
			// 签名操作区域文本右对齐
			text-align: right;
		}
	}
</style>