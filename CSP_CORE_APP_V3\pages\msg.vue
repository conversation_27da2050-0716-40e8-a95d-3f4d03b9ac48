<template>
	<!-- 页面根容器，设置最小高度为屏幕高度 -->
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<!-- uni-list 组件，显示消息列表，带有边框 -->
		<uni-list :border="true">
			<!-- 头像显示角标，使用 uni-list-chat 组件循环渲染消息列表 -->
			<uni-list-chat :avatar-circle="true" v-for="item of msgList" :key="item.f_Id" clickable @click="goTo(item)"
				:avatar="avatarSrc(item)" :title="msgTitle(item)" :note="item.f_Content"
				:time="msgDateTime(item.f_Time).toString()" badge-positon="left" :badge-text="item.f_NoReadNum">
			</uni-list-chat>
		</uni-list>
	</view>
</template>

<script>
	// 引入 moment 库用于时间处理
	import moment from "moment"
	// 引入 lodash 的 mapKeys 方法用于对象键映射
	import mapKeys from "lodash/mapKeys"
	export default {
		data() {
			return {
				// 消息列表
				msgList: [],
				// 用户信息表，存储用户的详细信息
				userTable: {},
				// 系统用户信息表
				sysUserTable: {},
				// 定时器，用于定时拉取消息
				timer: null,
				// 下次拉取消息的时间
				nextTime: '1888-10-10 10:10:10'
			}
		},

		async onLoad() {
			// 页面启动时的逻辑，PAGE_LAUNCH 是自定义方法，返回 true 则继续初始化页面
			if (await this.PAGE_LAUNCH()) {
				await this.init()
			}
		},

		// 本页面开启下拉刷新，用于立即拉取消息
		onPullDownRefresh() {
			// 清空消息列表
			this.msgList = []
			// 重置下次拉取时间
			this.nextTime = '1888-10-10 10:10:10'
			// 拉取消息，拉取成功后提示更新并停止下拉刷新
			this.fetchMsg().then(() => {
				this.TOAST(this.$t('已更新消息列表'))
				uni.stopPullDownRefresh()
			})
		},

		// 页面添加定时器，自动收取消息
		onShow() {
			// 获取配置的定时拉取消息的时间间隔
			const intervalTime = this.LEARUN_CONFIG('pageConfig.msg.fetchMsg')
			// 设置定时器，定时调用 fetchMsg 方法拉取消息
			this.timer = setInterval(this.fetchMsg, intervalTime)
		},

		// 离开页面后移除定时器
		onHide() {
			// 清除定时器
			clearInterval(this.timer)
		},

		// 卸载页面后移除定时器
		onUnload() {
			uni.setNavigationBarTitle({
				title: this.$t("消息")
			})
			// 清除定时器
			clearInterval(this.timer)
		},

		methods: {
			// 页面初始化
			async init() {
				// 显示加载提示
				this.LOADING(this.$t('读取消息列表中…'))
				// 拉取消息
				await this.fetchMsg()
				// 隐藏加载提示
				this.HIDE_LOADING()
			},

			// 拉取消息
			async fetchMsg() {
				// 发送 HTTP 请求拉取消息
				const message = await this.HTTP_GET({
					url: `/message/contacts/${this.nextTime}`,
					errorTips: this.$t('加载信息时通讯出错')
				})
				// 如果没有拉取到消息，则返回
				if (!message || message.length <= 0) {
					return
				}
				// 更新下次拉取消息的时间
				this.nextTime = moment(message[0].f_Time).format('YYYY-MM-DD HH:mm:ss')

				// 不在列表里的消息，则插入；已在列表里的消息，则替换
				// 最后按照时间排序，最后的消息在最上
				const newMsg = message
				const allMsg = this.msgList

				// 提取出消息列表中新增的用户的 ID，拉取这些用户的信息
				const newUserIds = newMsg
					.filter(t => !this.userTable[t.f_OtherUserId])
					.map(t => t.f_OtherUserId)

				// 如果有新增用户 ID，则拉取这些用户的信息
				if (newUserIds.length > 0) {
					await this.fetchUsers(newUserIds)
				}

				// 处理新消息，更新消息列表
				newMsg.forEach(item => {
					const idx = allMsg.findIndex(t => t.f_Id === item.f_Id)
					if (idx === -1) {
						allMsg.push(item)
						return
					}
					allMsg[idx] = item
				})

				// 对消息列表按时间排序，最新消息在最上面
				this.msgList = allMsg.sort((a, b) => moment(b.f_Time).valueOf() - moment(a.f_Time).valueOf())
			},

			// 拉取聊天信息中新增用户的信息
			async fetchUsers(newIds) {
				// 发送 HTTP 请求拉取用户信息
				const result = await this.HTTP_POST({
					url: '/organization/users',
					data: {
						ids: newIds.join(',')
					},
					errorTips: this.$t('拉取用户信息失败')
				})
				// 将拉取到的用户信息合并到 userTable 中
				this.userTable = Object.assign(this.userTable, mapKeys(result || [], 'f_UserId'))
			},

			// 点击后，跳转到详细聊天页
			goTo(item) {
				const otherUserId = item.f_OtherUserId
				// 发送 HTTP 请求更新消息状态
				this.HTTP_PUT({
					url: `/message/contact/state/${otherUserId}`
				})
				const user = this.userTable[otherUserId]

				//const sys = this.sysUserTable[otherUserId] ? '1' : null

				// 跳转到聊天页面，传递用户 ID、名称和头像信息
				this.NAV_TO('/pages/msg/chat', {
					id: otherUserId,
					name: this.$t(user.f_Name) || this.$t(user.f_RealNames) || this.$t('(未知用户)'),
					icon: user.f_HeadIcon,
					sys: null
				})
			},

			// 获取消息发送人名称显示
			msgTitle(item) {
				const user = this.userTable[item.f_OtherUserId] || {}
				// 优先显示用户名称，若不存在则显示真实姓名，若都不存在则显示未知用户
				return this.$t(user.f_Name) || this.$t(user.f_RealNames) || this.$t('(未知用户)')
			},

			// 格式化显示消息时间
			msgDateTime(date) {
				// 调用自定义的日期格式化方法
				return this.TABLEITEM_DATEFORMAT(date)
			},

			// 获取用户头像 url
			avatarSrc(item) {
				const user = item && this.userTable[item.f_OtherUserId]
				if (!user) {
					return null
				}
				const token = this.GET_GLOBAL('token')
				// 如果用户头像存在且不是文件名形式，则拼接头像的完整 URL，否则使用默认头像
				return (user.f_HeadIcon && user.f_HeadIcon.indexOf('.') == -1) ?
					`${this.API}/system/annexesfile/${user.f_HeadIcon}?token=${token}` : `/static/img-avatar/head.png`
			}
		},

		computed: {
			// msgList 消息数组的长度
			msgCount() {
				return this.msgList.length
			},

			// 头像圆形/方形显示参数
			roundAvatar() {
				// 获取配置的头像显示样式
				return this.LEARUN_CONFIG('pageConfig.roundAvatar')
			}
		}
	}
</script>