<template>
  <view class="learun-icon" :style="{display: isBlock? '': 'flex'}">
    <image
      class="learun-icon-image"
	  :style="{width: size + 'px', height: size + 'px'}"
      :src="svgDataurl()"
      mode="aspectFit"
    ></image>
  </view>
</template>
<script>
/**
 * Icons 图标
 * @description 用于展示 icons 图标
 * @tutorial https://ext.dcloud.net.cn/plugin?id=28
 * @property {Number} size 图标大小
 * @property {String} type 图标图案，参考示例
 * @property {String} color 图标颜色
 * @property {String} customPrefix 自定义图标
 * @event {Function} click 点击 Icon 触发事件
 */
export default {
  name: "learun-icon",
  emits: ["click"],
  props: {
    type: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "#333333",
    },
    size: {
      type: [Number, String],
      default: 16,
    },
    isBlock: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {};
  },
  methods: {
    svgDataurl() {
      const icon = this.GET_ICON(this.type);
      if (icon) {
        const iconBody = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img"  viewBox="0 0 ${
          icon.width
        } ${icon.height}" >${icon.body.replace(
          /currentColor/g,
          this.color
        )}</svg>`;
        // console.log(iconBody)
        return `data:image/svg+xml,${encodeURIComponent(iconBody)}`;
      }
	  else{
		return ''
	  }
    },
    _onClick() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss">
.learun-icon {
  text-align: center;
}
</style>
