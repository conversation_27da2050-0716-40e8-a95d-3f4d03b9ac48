<template>
	<uni-data-checkbox multiple v-model="myValue" :localdata="myOptions"></uni-data-checkbox>
</template>

<script>
	export default {
		name:'learun-checkbox',
		props:{
			value:[String],
			labelKey:{
				type:String,
				default:'label'
			},
			valueKey:{
				type:String,
				default:'value'
			},
			options:{
				type:Array,
				default:()=>[]
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		computed:{
			myValue:{
				get(){
					if(this.VALIDATENULL(this.value)){
						return []
					}
					if(!this.value.split){
						return this.value
					}
					return this.value.split(',')
				},
				set(val){
					this.$emit('input', val.join(','))
					
					const objs = []
					if(val.length == 0){
						this.$emit('change', undefined)
					}
					else{
						val.forEach(item => {
							const objone = this.options.find(t=>t[this.valueKey] == item)
							objs.push(objone)
						})
						this.$emit('change', objs)
					}
				}
			},
			myOptions(){
				return this.options.map(t=>({value:t[this.valueKey],text:t[this.labelKey],disable:this.disabled}))
			}
		}
	}
</script>


