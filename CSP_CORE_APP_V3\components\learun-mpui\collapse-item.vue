<template>
	<view class="learun-collapse-item" ref="learun-collapse-item">
		<view class="learun-collapse-item__title" @click.stop="handleClickTitle" >
			<view>
				<uni-icons class="lefticon"  :type="isOpen? 'bottom':'right'" size="14" color="#c0c4cc" ></uni-icons>
				<text>{{$t(myRowData.label)}}</text>
			</view>
		</view>
		<view class="learun-collapse-item__content" v-show="isOpen" >
			<view v-for="col in myRowData.children" :key="col.id" >
				<learun-divider v-if="col.type=='divider'" :text="col.html" />
				<button style="margin-bottom: 8px;" v-else-if="col.type=='btn'" :disabled="col.config.disabled"  :type="col.config.myType"  
				@click="handleBtnClick(component)"
				>{{$t(col.config.label)}}</button>
				<learun-customform-card
					v-else-if="col.type=='card'"
					:component="col"
					:getDataSource="getDataSource"
					:editMode="editMode"
					:formId="formId"
					@addRow="addTableRow"
					@deleteRow="deleteTableRow($event.event,$event.col)"
					@rowChange="rowChange($event.event,$event.col)"
					@input="setValue"
					@change="handleChange"
					@btnClick="handleBtnClick"
					ref="gridtable"
					/>
				<learun-edit-table
					v-else-if="col.type=='gridtable'"
					:value="getValue(col.id)"
					:columns="col.children"
					:getDataSource="getDataSource"
					:title="$t(col.config.label)"
					:editMode="editMode"
					:formId="formId"
					:tableId="col.id"
					:hasAddBtn="col.config.isAddBtn"
					:addBtnText="$t(col.config.addBtnText)"
					:hasRemoveBtn="col.config.isRemoveBtn" 
					:required="col.config.required"
					@addRow="addTableRow(col)"
					@deleteRow="deleteTableRow($event,col)"
					@rowChange="rowChange($event,col)"
					ref="gridtable"
					/>
				<learun-view-table
						v-else-if="col.type=='viewTable'" 
						
						:paramFiled="getValue(col.config.paramFiled)"
						:columns="col.config.columns"
						:code="col.config.dataCode"
						:title="$t(col.config.label)"
						/>
				<view class="learun-collapse" v-else-if="col.type=='collapse'">
					<view  v-if="col.config.label" class="learun-edit-table__title" >
						<text>{{$t(col.config.label)}}</text>
					</view>
					<view class="learun-edit-table__body" >
							
						<learun-collapse-item
							v-for="(row,index) in  col.children"
							:key="index"
							:num="index + 1"
							:rowData="row"
								
							:getDataSource="getDataSource"
							:formId="formId"
							:tableId="tableId"
							:editMode="editMode"
								
							@addRow="addTableRow"
							@deleteRow="deleteTableRow"
							@rowChange="rowChange"
							@input="setValue"
							@change="handleChange"
							@btnClick="handleBtnClick"
								
							ref="gridtable"
								
						/>
					</view>
				</view>
				<view style="margin-bottom: 15px;" v-else-if="col.type=='label'" :disabled="col.config.disabled"  
					:style="{fontSize:col.config.size+'px',color:col.config.color,textAlign:col.config.align}"
				>{{col.config.content}}</view>
				<learun-customform-item 
					v-else
					:component="col"
					:getDataSource="getDataSource" 
					:editMode="editMode"
					:formId="formId"
					
					:value="getValue(col.id)"
					@input="setValue"
					@change="handleChange"
					/>
			</view>
		</view>
	</view>
</template>

<script>
	import set from "lodash/set"
	import get from "lodash/get"
	export default {
		name:'learun-collapse-item',
		props:{
			num:Number,
			rowData:{},
			getDataSource:Function,
			editMode: {
				type: Boolean, 
				default: true ,
			},
			formId:String,
			tableId:String
		},
		
		data(){
			return {
				isOpen:true,
			}
		},
		computed: {
			myRowData() {
				return this.rowData
			}
		},
		methods:{
			handleClickTitle(){
				this.isOpen = !this.isOpen
			},
			handleBtnClick(event){
					this.$emit('btnClick',event)
			},
			handleChange(event){
				this.$emit('change',event)
			},
			
			setValue(event) {	
				this.$emit('input',event)
			},
			getValue(path) {
			  return get(this.GET_DATA(`learun_form_data_${this.formId}`), path)
			},
			rowChange(event,col){
				this.$emit('rowChange',{event,col})
			},
			addTableRow(event){
				this.$emit('addRow',event)
			},
			deleteTableRow(event,col){
				this.$emit('deleteRow',{event,col})
			},
			async validate(){
				if(this.$refs.gridtable){
					for(let i = 0,len =  this.$refs.gridtable.length;i<len;i++){
						const res = await this.$refs.gridtable[i].validate()
						if(!res){
							return false
						}
					}
				}
				
				return true
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	.learun-collapse{
		&__title{
			font-size: 13px;
			color: #666666;
			display: flex;
			align-items: center;
			width: 100%;
			height: 32px;
		}
		
		margin-bottom: 16px;
	}
	.learun-collapse-item{
		
		&__title{
			padding:0 8px;
			font-size: 12px;
			color: $uni-base-color;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 32px;
			background-color: $uni-info-light;
			box-sizing: border-box;
			
			.lefticon{
				margin-right: 4px;
			}
			
			.delete-btn{
				color: $uni-error;
			}
		}
		
		&__content{
			padding: 16px 8px 0 8px;
			background-color: #fdfdfd;
		}
	}
</style>
