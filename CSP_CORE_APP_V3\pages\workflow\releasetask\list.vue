<template>
  <!-- 页面容器，设置最小高度和顶部内边距 -->
  <view class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-top':'64px'}">
    <!-- 顶部搜索栏，当 appUrl 为空时显示 -->
    <view class="bg-white fixed" v-if="!appUrl">
      <!-- 搜索栏组件，绑定搜索文本，设置占位符和取消按钮样式 -->
      <uni-search-bar :placeholder="$t('搜索流程')" cancelButton="none" v-model="searchText"></uni-search-bar>
    </view>
    <!-- 流程列表展示区域，当 appUrl 为空时显示 -->
    <view v-if="!appUrl">
      <!-- 遍历分组列表 -->
      <template v-for="(group, title) in groupList">
        <!-- 卡片组件，展示分组信息 -->
        <uni-card padding="8px" margin="8px" :title="title" :key="title" :is-shadow="false" :border="false"  >
          <!-- 网格布局组件，设置列数和点击事件 -->
          <uni-grid
            :showBorder="false"
            :column="3"
            @change="taskClick($event,group)"
          >
            <!-- 遍历分组内的流程项 -->
            <uni-grid-item
              v-for="(listItem,listIndex) in group"
              :key="listIndex"
              :index="listIndex"
            >
              <!-- 流程项容器 -->
              <view class="learun-grid-item" >
                <!-- 流程项图标容器 -->
                <view class="learun-grid-icon"  >
                  <!-- 自定义图标组件，显示流程图标 -->
                  <learun-icon :type="listItem.f_Icon" :color="listItem.f_Color" size="24" ></learun-icon>
                </view>
                <!-- 流程项名称容器，处理名称过长显示省略号 -->
                <view class="learun-text-ellipsis"  ><text class="learun-grid-text">{{listItem.f_Name}}</text></view>
              </view>
            </uni-grid-item>
          </uni-grid>
        </uni-card>
      </template>
    </view>
    <!-- 网页视图组件，当 appUrl 存在时显示，用于加载外部网页 -->
    <web-view :webviewStyles="webviewStyles" :src="appUrl"  v-if="appUrl"></web-view>
  </view>
</template>

<script>
// 引入 lodash 工具函数
import keyBy from 'lodash/keyBy'
import mapValues from 'lodash/mapValues'
import values from 'lodash/values'
import groupBy from 'lodash/groupBy'
import mapKeys from 'lodash/mapKeys'

export default {
  data() {
    return {
      // 搜索框中的文本
      searchText: '',
      // 分组名称映射对象
      groupNames: {},
      // 流程列表数据
      list: [],
      // 外部网页的 URL
      appUrl: '',
      // 网页视图的样式配置
      webviewStyles: {
        progress: {
          color: '#FF3333'
        },
      }
    }
  },
  computed: {
    // 计算属性，获取经过筛选和分组后的流程列表
    groupList() {
      // 过滤出包含搜索文本的流程项，按分类分组，然后映射分组名称
      return mapKeys(
        groupBy(this.list.filter(item => item.f_Name.includes(this.searchText) || item.f_Code.includes(this.searchText)), 'f_Category'),
        (v, k) => this.groupNames[k]
      )
    }
  },
  async onLoad() {
	  uni.setNavigationBarTitle({
	  	title: this.$t("发起流程")
	  })
    // 检查页面是否可以启动
    if (await this.PAGE_LAUNCH()) {
      // 初始化页面数据
      await this.init()
    }
  },
  methods: {
    // 初始化页面数据
    async init() {
      // 拉取流程列表数据
      await this.fetchList()
    },
    // 拉取流程列表数据
    async fetchList() {
      // 显示加载提示
      this.LOADING(this.$t('加载流程信息…'))
      // 同时发起两个请求，获取流程列表和分组名称映射
      const [list, groupNames] = await Promise.all([
        // 获取我的流程列表，过滤出显示在移动应用发起的流程
        this.HTTP_GET(
          {
            url: '/workflow/scheme/mylist',
            errorTips: this.$t('加载数据时出错')
          }
        ).then(data => {
          if (!data) {
            return []
          }
          return data.filter(t => t.f_IsInApp == 1)
        }),
        // 获取数据字典中的功能名称表，并转换为映射对象
        this.FETCH_DATAITEM('FlowSort').then(result => mapValues(keyBy(values(result), 'f_ItemValue'), 'f_ItemName'))
      ])
      // 更新流程列表数据
      this.list = list
      // 更新分组名称映射对象
      this.groupNames = groupNames
      // 隐藏加载提示
      this.HIDE_LOADING()
    },
    // 点击流程列表项的处理函数
    async taskClick({ detail }, group) {
      // 获取点击的流程项
      const item = group[detail.index]
      // 判断是否为外部流程
      if (item.f_IsOther === 1) {
        // 获取外部流程的详细信息
        const data = (await this.HTTP_GET({ url: `/workflow/scheme/${item.f_Code}`, })) || { scheme: { f_Content: '' } }
        // 解析流程配置信息
        const scheme = JSON.parse(data.scheme.f_Content);
        // 检查是否有 appUrl
        if (scheme.pcUrl) {
          // 设置 appUrl
          this.appUrl = scheme.appUrl
          // 设置页面标题
          this.SET_TITLE(item.f_Name)
        }
      } else {
        // 非外部流程，跳转到创建页面
        this.NAV_TO('./single?type=create', group[detail.index])
      }
    }
  }
}
</script>