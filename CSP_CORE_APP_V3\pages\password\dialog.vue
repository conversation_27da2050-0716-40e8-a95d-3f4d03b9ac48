<template>
	<!-- 自定义对话框组件，根据visible属性控制显示/隐藏 -->
	<view style="padding: 8px;" class="custom-dialog" v-if="visible">
		<!-- 透明遮罩层，在数据准备完成前显示 -->
		<view style="background-color:#fff;width: 120%;height: 120%;margin: -8px;" v-show="!isPrepare"></view>
		<!-- 对话框内容区域，数据准备完成后显示 -->
		<view class="dialog-content" v-show="isPrepare">
			<!-- 对话框标题，使用多语言翻译显示"输入查询密码" -->
			<view
				style="margin-bottom: 15px; font-size: 15px; color: rgb(0, 144, 255); text-align: center; font-weight: 600;">
				{{$t("输入查询密码")}}
			</view>
			<!-- 表单区域，使用uni-forms组件进行表单验证 -->
			<view class="dialog-message">
				<uni-forms :modelValue="formData" :rules="rules" :label-width="100" ref="myForm">
					<uni-forms-item :label="$t('查询密码')" name="password" required>
						<!-- 密码输入框，使用uni-easyinput组件 -->
						<uni-easyinput v-model="formData.password" :placeholder="$t('请输入查询密码')" :inputBorder="true"
							:clearable="false" type="password">
							<!-- 右侧插槽，目前显示空状态（密码为空时的图标预留位置） -->
							<template v-slot:right>
								<uni-icons v-if="formData.password == ''" class="content-clear-icon"
									:type="'eye-filled'" :size="18" color="#c0c4cc"></uni-icons>
							</template>
						</uni-easyinput>
					</uni-forms-item>
				</uni-forms>
				<!-- 提示信息区域，显示密码相关说明 -->
				<view style="font-size: 12px; color: red; text-align:left;">
					<view>{{ $t("提示：") }}</view>
					<view>{{ $t("1、请输入您之前设置的工资单查询密码。") }}</view>
					<view>{{ $t("2、若您忘记了密码，请点击【忘记密码】重新设置密码。") }}</view>
				</view>
			</view>
			<!-- 按钮区域，包含取消和确认按钮 -->
			<view class="dialog-buttons">
				<!-- 取消按钮，点击关闭对话框 -->
				<button @click="cancel" type="default" style="margin-left: 0px;">{{$t('取消')}}</button>
				<!-- 确认按钮，点击提交表单 -->
				<button @click="submit" type="primary" style="margin-right: 0px;">{{$t('确认')}}</button>
			</view>
			<!-- 忘记密码按钮，点击导航到密码重置页面 -->
			<view style="width: 100px;">
				<button @click="resetPassword" class="reset-password">{{$t('忘记密码')}}</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		// 接收父组件传递的属性
		props: {
			visible: {
				type: Boolean, // 对话框显示状态
				default: false
			},
			emp_No: '', // 员工编号（从父组件获取）
		},
		data() {
			return {
				formData: { // 表单数据对象
					password: '', // 输入的查询密码
				},
				rules: { // 表单验证规则
					'password': {
						rules: [{
							required: true, // 密码为必填项
							errorMessage: this.$t('请输入查询密码'), // 错误提示信息
						}]
					},
				},
				company_Code: '', // 公司代码（从用户信息中解析）
				user_ID: '', // 用户ID（从用户信息中解析）
				isPrepare: false, // 数据准备状态（控制内容显示）
			}
		},

		async created() {
			// 从全局变量获取登录用户信息
			let loginUser = this.GET_GLOBAL('loginUser');
			// 解析钉钉OpenId，格式为"公司代码_用户ID"
			let f_DDOpenId = loginUser.f_DDOpenId;
			if (loginUser.f_DDOpenId) {
				let DDOpenIdArray = f_DDOpenId.split("_");
				// 提取公司代码和用户ID
				if (DDOpenIdArray.length > 0) {
					this.company_Code = DDOpenIdArray[0];
				}
				if (DDOpenIdArray.length > 1) {
					this.user_ID = DDOpenIdArray[1];
				}
				// 检查是否存在查询密码
				await this.hasPassword();
			} else {
				// 检查是否存在查询密码
				await this.hasPassword();
			}
			
		},

		methods: {
			// 检查是否已有查询密码，没有则引导设置
			async hasPassword() {
				// 判断本地存储中是否记录过密码状态
				if (this.GET_STORAGE("hasPw4Pay") != 1 && this.visible) {
					this.LOADING('…')

					// 发送HTTP请求检查密码是否存在
					const success = await this.HTTP_GET({
						url: '/hrattf008/hasPassword',
						params: {
							"Company_Code": this.company_Code,
							"Emp_No": this.user_ID,
						},
						errorTips: 'error'
					});
					// 根据返回结果更新本地存储和导航
					if (success == "1") {
						this.SET_STORAGE("hasPw4Pay", 1);
					} else {
						this.SET_STORAGE("hasPw4Pay", 0);
						// 导航到密码设置页面（首次设置场景）
						this.JUMP_TO('/pages/password/reset', {
							title: this.$t("设置查询密码"),
							fristMessage: this.$t("提示：首次查询请先设置您的工资单查询密码。")
						}, true);
					}
					this.HIDE_LOADING()
				}
				// 标记数据准备完成，显示对话框内容
				this.isPrepare = true
			},
			// 表单提交处理
			async submit() {
				// 验证表单有效性
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm)

				if (err) {
					return
				}

				const {
					password
				} = this.formData

				this.LOADING(this.$t('提交…'))

				// 发送密码验证请求
				const success = await this.HTTP_POST2({
					url: '/hrattf008/checkPassword',
					data: {
						"Company_Code": this.company_Code,
						"User_ID": this.user_ID,
						"Emp_No": this.emp_No == "" ? this.user_ID : this.emp_No, // 优先使用传入的emp_No，否则用user_ID
						"Password": this.MD5(password) // 对密码进行MD5加密
					},
					errorTips: this.$t('失败')
				});
				this.HIDE_LOADING()

				if (!success) {
					return
				}
				// 向父组件发送密码确认后的回调，传递加密后的密码和用户信息
				this.$emit('afterPasswordConfirm', {
					Password: this.MD5(password),
					Company_Code: this.company_Code,
					Emp_No: this.user_ID
				});
			},
			// 取消按钮回调，返回上一页
			cancel() {
				this.NAV_BACK();
			},
			// 忘记密码按钮回调，导航到密码重置页面
			resetPassword() {
				this.NAV_TO('/pages/password/reset')
			},
			// 封装的HTTP POST请求方法（带错误处理）
			async HTTP_POST2({
				url,
				data,
				params,
				errorTips,
				callback
			}) {
				url = this.learun_params_url(url, params)
				const [err, res] = await this.learun_requestBase(url, data, null, 'POST')

				// 处理密码过期情况
				if (res.data.code == 400 && res.data.info.indexOf("密码已过期") >= 0) {
					// 导航到密码设置页面（密码过期场景）
					this.JUMP_TO('/pages/password/set', {
						title: this.$t("设置查询密码"),
						showTip: this.$t("查询密码已过期，请重新设置密码后方可查看工资"),
						needOldPw: true
					}, true);
					return
				}
				// 处理通用请求结果
				const result = this.learun_handleResult(err, res, errorTips)
				callback && callback(result)
				return result
			},
		}
	}
</script>

<style lang="less" scoped>
	.form-item {
		border: 1px solid #F0F0F0;
		border-radius: 4px;
	}

	.custom-dialog {
		/* 对话框容器样式：覆盖整个屏幕的固定定位 */
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.7);
		/* 半透明黑色背景 */
	}

	.dialog-content {
		/* 对话框内容样式：白色背景，圆角，内边距 */
		background-color: #fff;
		border-radius: 10px;
		padding: 20px;
		text-align: center;
	}

	.dialog-buttons {
		/* 按钮区域样式：水平分布，两端对齐 */
		display: flex;
		justify-content: space-between;
	}

	.dialog-buttons button {
		/* 按钮通用样式：圆角，内边距，光标样式 */
		margin: 10px;
		padding: 10px 20px;
		border: none;
		border-radius: 5px;
		cursor: pointer;
		width: 39%;
		/* 控制按钮宽度，留出间距 */
	}

	.reset-password {
		/* 忘记密码按钮样式：左对齐，蓝色文字，无背景 */
		justify-content: start;
		background-color: #fff;
		color: blue;
	}

	.reset-password:after {
		/* 移除按钮默认边框 */
		border: none
	}
</style>