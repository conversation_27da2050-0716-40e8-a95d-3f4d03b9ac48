/**
 * 表单数据处理相关方法
 */
export default {
	methods: {
		// 根据组件模板加载数据值
		async learun_form_fetchDataSource(schemeItem, formData) {
			switch (schemeItem.dataType) {
				case '1': // 静态数据
					this.SET_DATA(schemeItem.prop, schemeItem.options)
					break;
				case '2': // 数据字典
					if (schemeItem.dataCode && !this.GET_DATA(schemeItem.dataCode)) {
						const dataItems = await this.FETCH_DATAITEM(schemeItem.dataCode) || []
						this.SET_DATA(schemeItem.dataCode, dataItems.map(t => ({
							...t,
							value: t.f_ItemValue,
							label: t.f_ItemName
						})))
					}
					break;
				case '3': // 远端数据(数据源)			
					if (schemeItem.dataCode && schemeItem.upCtrl && formData) {
						if (formData[schemeItem.upCtrl]) {
							const upCtrlVal = formData[schemeItem.upCtrl]
							const upCtrlValList = upCtrlVal.split(',') // 考虑数据多选的问题
							for (let v of upCtrlValList) {
								if (!this.GET_DATA(`${schemeItem.dataCode}_${v}`)) {
									this.SET_DATA(`${schemeItem.dataCode}_${v}`, await this.FETCH_DATASOURCE(
										schemeItem.dataCode, v) || [])
								}
							}
						} else if (schemeItem.upShowAll) {
							if (schemeItem.dataCode && !this.GET_DATA(schemeItem.dataCode)) {
								this.SET_DATA(schemeItem.dataCode, await this.FETCH_DATASOURCE(schemeItem
									.dataCode) || [])
							}
						}
					} else {
						if (schemeItem.dataCode && !this.GET_DATA(schemeItem.dataCode)) {
							this.SET_DATA(schemeItem.dataCode, await this.FETCH_DATASOURCE(schemeItem.dataCode) ||
							[])
						}
					}
					break;
			}
			if (['companySelect', 'company', 'departmentSelect'].includes(schemeItem.type)) {
				if (!this.GET_DATA('learun_company_list')) {
					this.SET_DATA('learun_company_list', await this.FETCH_COMPANYS())
				}
			} else if (['areaselect'].includes(schemeItem.type)) {
				let areas = this.GET_DATA('learun_areas_0')
				if (!areas) {
					areas = (await this.FETCH_AREAS('0')).map(t => ({
						value: t.f_AreaCode,
						label: t.f_AreaName
					}))
					this.SET_DATA('learun_areas_0', areas)
				}


				if (formData && formData[schemeItem.prop]) {
					const areaValues = formData[schemeItem.prop].split(',')
					if (!this.GET_DATA(`learun_areas_${areaValues[0]}`)) {
						this.SET_DATA(`learun_areas_${areaValues[0]}`, (await this.FETCH_AREAS(areaValues[0])).map(
							t => ({
								value: t.f_AreaCode,
								label: t.f_AreaName
							})))
					}
					if (!this.GET_DATA(`learun_areas_${areaValues[1]}`)) {
						this.SET_DATA(`learun_areas_${areaValues[1]}`, (await this.FETCH_AREAS(areaValues[1])).map(
							t => ({
								value: t.f_AreaCode,
								label: t.f_AreaName
							})))
					}
				} else {
					let areaPid = areas[0].value
					areas = this.GET_DATA(`learun_areas_${areaPid}`)
					if (!areas) {
						areas = (await this.FETCH_AREAS(areaPid)).map(t => ({
							value: t.f_AreaCode,
							label: t.f_AreaName
						}))
						this.SET_DATA(`learun_areas_${areaPid}`, areas)
					}
					areaPid = areas[0].value
					areas = this.GET_DATA(`learun_areas_${areaPid}`)
					if (!areas) {
						areas = (await this.FETCH_AREAS(areaPid)).map(t => ({
							value: t.f_AreaCode,
							label: t.f_AreaName
						}))
						this.SET_DATA(`learun_areas_${areaPid}`, areas)
					}
				}

			}
		},

		// 加载行政区域信息
		async learun_form_fetchAreaInfo(list) {
			for (const item of list) {
				const items = item.split(',')
				if (!this.GET_DATA(`learun_areas_${items[0]}`)) {
					this.SET_DATA(`learun_areas_${items[0]}`, (await this.FETCH_AREAS(items[0])).map(t => ({
						value: t.f_AreaCode,
						label: t.f_AreaName
					})))
				}
				if (!this.GET_DATA(`learun_areas_${items[1]}`)) {
					this.SET_DATA(`learun_areas_${items[1]}`, (await this.FETCH_AREAS(items[1])).map(t => ({
						value: t.f_AreaCode,
						label: t.f_AreaName
					})))
				}
			}
		},

		// 加载组织信息
		async learun_form_fetchOrganizeInfo(userIdList, departmentIdList) {
			const userMap = this.GET_DATA('learun_users_map') || {}
			const departmentMap = this.GET_DATA('learun_departments_map') || {}


			const _userIdList = userIdList.filter(t => !userMap[t])
			const _departmentIdList = departmentIdList.filter(t => !departmentMap[t])

			await Promise.all([
				this.FETCH_USER(_userIdList).then(result => {
					result.forEach(t => {
						userMap[t.value] = t
					})
				}),
				this.FETCH_DEPARTMENT(_departmentIdList).then(result => {
					result.forEach(t => {
						departmentMap[t.value] = t
					})
				})
			])
			this.SET_DATA('learun_users_map', userMap)
			this.SET_DATA('learun_departments_map', departmentMap)
		},

		// 获取表单数据源和数据字典数据
		learun_form_getDataSource(schemeItem, formData) {
			if (['companySelect', 'company'].includes(schemeItem.type)) {
				return this.GET_DATA('learun_company_list')
			}

			switch (schemeItem.dataType) {
				case '1': // 静态数据
					return this.GET_DATA(schemeItem.prop)
				case '2': // 数据字典
					return this.GET_DATA(schemeItem.dataCode).filter((t) => {
						return t.f_EnabledMark == 1;
					})
				case '3': // 远端数据(数据源)
					// 获取带参数值的数据源数据值
					if (schemeItem.upCtrl && formData) {
						if (formData[schemeItem.upCtrl]) {
							const res = []
							const upCtrlVal = formData[schemeItem.upCtrl]
							const upCtrlValList = upCtrlVal.split(',') // 考虑数据多选的问题
							upCtrlValList.forEach(v => {
								const vList = this.GET_DATA(`${schemeItem.dataCode}_${v}`) || []
								res.push(...vList.filter(t => !res.some(t2 => t2[schemeItem.dataValueKey] === t[
									schemeItem.dataValueKey])))
							})
							return res.map(t => ({
								...t,
								value: t[schemeItem.dataValueKey],
								label: t[schemeItem.dataLabelKey]
							}))
						} else if (schemeItem.upShowAll) {
							return (this.GET_DATA(schemeItem.dataCode) || []).map(t => ({
								...t,
								value: t[schemeItem.dataValueKey],
								label: t[schemeItem.dataLabelKey]
							}))
						} else {
							return []
						}
					} else {
						return (this.GET_DATA(schemeItem.dataCode) || []).map(t => ({
							...t,
							value: t[schemeItem.dataValueKey],
							label: t[schemeItem.dataLabelKey]
						}))
					}
			}

			return []
		},

		// 显示数据
		learun_form_displayText(value, schemeItem, formData) {
			if (this.VALIDATENULL(value)) {
				return ''
			}

			let name = value
			switch (schemeItem.type) {
				case 'radio':
				case 'checkbox':
				case 'select':
				case 'selectMultiple':
				case 'treeselect':
				case 'layerselect':
					if (schemeItem.dataType == '1') { // 静态数据
						const data = []
						const options = this.COPY(schemeItem.options)
						this.TREE_TO_ARRAY(options, data) //静态数据
						name = this.FORMAT_NAME(data, value, 'value', 'label')
					} else if (schemeItem.dataType == '2') { // 数据字典
						name = this.FORMAT_NAME(this.GET_DATA(schemeItem.dataCode), value, 'f_ItemValue', 'f_ItemName')
					} else {
						if (schemeItem.upCtrl && formData) {
							if (formData[schemeItem.upCtrl]) {
								const res = []
								const upCtrlVal = formData[schemeItem.upCtrl]
								const upCtrlValList = upCtrlVal.split(',') // 考虑数据多选的问题
								upCtrlValList.forEach(v => {
									const vList = this.GET_DATA(`${schemeItem.dataCode}_${v}`) || []
									res.push(...vList.filter(t => !res.some(t2 => t2[schemeItem
										.dataValueKey] === t[schemeItem.dataValueKey])))
								})
								name = this.FORMAT_NAME(res, value, schemeItem.dataValueKey, schemeItem.dataLabelKey)
							} else {
								name = value
							}
						} else {
							name = this.FORMAT_NAME(this.GET_DATA(schemeItem.dataCode), value, schemeItem.dataValueKey,
								schemeItem.dataLabelKey)
						}
					}
					break;
				case 'datetime':
				case 'createtime':
				case 'modifytime':
					name = this.DATEFORMAT(value, schemeItem.format)
					break;
				case 'companySelect':
				case 'company':
					name = this.FORMAT_NAME(this.GET_DATA('learun_company_list'), value, 'f_CompanyId', 'f_FullName')
					break;
				case 'departmentSelect':
				case 'department':
					const department = this.GET_DATA('learun_departments_map')[value]
					name = department ? department.label : ''
					break;
				case 'userSelect':
				case 'createuser':
				case 'modifyuser':
					const user = this.GET_DATA('learun_users_map')[value]
					name = user ? user.label : ''
					break;
				case 'areaselect':
					const valueList = value.split(',')
					const nameList = []
					const obj1 = this.GET_DATA(`learun_areas_0`).find(t => t.value == valueList[0])
					if (obj1) {
						nameList.push(obj1.label)
						const obj2 = this.GET_DATA(`learun_areas_${valueList[0]}`).find(t => t.value == valueList[1])
						if (obj2) {
							nameList.push(obj2.label)
							const obj3 = this.GET_DATA(`learun_areas_${valueList[1]}`).find(t => t.value == valueList[
								2])
							if (obj3) {
								nameList.push(obj3.label)
							}
						}
					}
					name = nameList.join(',')
					break;
			}

			return name
		},

		// 获取表单数据
		async learun_form_getFormData(formScheme, postData = {}, isUpdate) {
			const res = {}
			const loginUser = this.GET_GLOBAL('loginUser')
			for (let i = 0, len = formScheme.formInfo.tabList.length; i < len; i++) {
				const components = formScheme.formInfo.tabList[i].components
				for (let j = 0, jlen = components.length; j < jlen; j++) {
					const component = components[j]
					switch (component.type) {
						case 'guid':
							res[component.prop] = await this.learun_form_getComponentValue(component, postData, this
								.GUID())
							break
						case 'input':
						case 'textarea':
						case 'texteditor':
						case 'password':
							res[component.prop] = await this.learun_form_getComponentValue(component, postData, '')
							break
						case 'number':
							res[component.prop] = await this.learun_form_getComponentValue(component, postData, 0)
							break
						case 'upload':
						case 'uploadimg':
							res[component.prop] = await this.DOWNLOAD_FILE(await this.learun_form_getComponentValue(
								component, postData, ''))
							break
						case 'companySelect':
							let companySValue = ''
							if (!isUpdate && component.isLogin) {
								companySValue = loginUser.f_CompanyId
							} else {
								companySValue = await this.learun_form_getComponentValue(component, postData, '')
							}
							if (companySValue != '') {
								if (!this.GET_DATA('learun_company_list')) {
									this.SET_DATA('learun_company_list', await this.FETCH_COMPANYS())
								}
							}
							res[component.prop] = companySValue
							break
						case 'company':
							let companyValue = ''
							if (!isUpdate) {
								companyValue = loginUser.f_CompanyId
							} else {
								companyValue = await this.learun_form_getComponentValue(component, postData, '')
							}
							if (companyValue != '') {
								if (!this.GET_DATA('learun_company_list')) {
									this.SET_DATA('learun_company_list', await this.FETCH_COMPANYS())
								}
							}
							res[component.prop] = companyValue
							break
						case 'department':
						case 'departmentSelect':
							let departmentValue = ''
							if (component.type == 'department' && !isUpdate) {
								departmentValue = loginUser.f_DepartmentId
							} else if (component.type == 'departmentSelect' && !isUpdate && component.isLogin) {
								departmentValue = loginUser.f_DepartmentId
							} else {
								departmentValue = await this.learun_form_getComponentValue(component, postData, '')
							}
							await this.learun_form_getDepartment(departmentValue)
							res[component.prop] = departmentValue
							break
						case 'createuser':
						case 'modifyuser':
						case 'userSelect':

							let userValue = ''
							if ((component.type == 'createuser' && !isUpdate) || component.type == 'modifyuser') {
								userValue = loginUser.f_UserId
							} else if (component.type == 'userSelect' && !isUpdate && component.isLogin) {
								userValue = loginUser.f_UserId
							} else {
								userValue = await this.learun_form_getComponentValue(component, postData, '')
							}
							await this.learun_form_getUser(userValue)
							res[component.prop] = userValue
							break
						case 'createtime':
						case 'modifytime':
							if ((component.type == 'createtime' && !isUpdate) || component.type == 'modifytime') {
								res[component.prop] = this.DATENOW()
							} else {
								res[component.prop] = await this.learun_form_getComponentValue(component, postData,
									'')
								if (res[component.prop] != '') {
									res[component.prop] = this.DATEFORMAT(res[component.prop])
								}
							}
							break
						case 'gridtable':
							res[component.prop] = await this.learun_form_getComponentValue(component, postData)
							break
						case 'areaselect':
							res[component.prop] = await this.learun_form_getComponentValue(component, postData, '')
							if (!res[component.prop] || !res[component.prop].split || res[component.prop].split(',')
								.length < 3) {
								res[component.prop] = ''
							}
							break
						default:
							if (!['card', 'divider', 'btn', 'viewtable'].includes(component.type)) {
								res[component.prop] = await this.learun_form_getComponentValue(component, postData,
									'')
							}
							break
					}
				}
			}
			return res
		},

		// 获取表单组件数据
		async learun_form_getComponentValue(component, data, defaultValue) {
			if (component.type == 'gridtable') {
				const tableData = data[component.table]
				const res = []
				if (!this.VALIDATENULL(tableData)) {
					for (let row of tableData) {
						const rowTmp = {}
						for (let cell of component.children) {
							const cellData = this.GET_VALUE(row, cell.field)
							if (['createuser', 'modifyuser', 'userSelect'].includes(cell.type)) {
								await this.learun_form_getUser(cellData)
								rowTmp[cell.prop] = cellData
							} else if (['department', 'departmentSelect'].includes(cell.type)) {
								await this.learun_form_getDepartment(cellData)
								rowTmp[cell.prop] = cellData
							} else if (['createtime', 'modifytime'].includes(cell.type) && !this.VALIDATENULL(
									cellData)) {
								rowTmp[cell.prop] = this.DATEFORMAT(cellData)
							} else {
								rowTmp[cell.prop] = cellData
							}
						}

						rowTmp.abledList = []
						rowTmp.disabled = false
						rowTmp.hasNoDeleteBtn = false
						res.push(rowTmp)
					}
				}
				return res
			} else {
				let _formdata = data[component.table]
				if (_formdata && this.lr_getObjType(_formdata) != 'object') {
					_formdata = _formdata[0]
				}

				if (_formdata) {
					const value = this.GET_VALUE(_formdata, component.field)
					if (this.VALIDATENULL(value)) {
						return ''
					}
					return value
				} else if ((this.isAuth && this.GET_FORM_LOOK_AUTH(component.prop, this.moduleId)) || !component
					.display) {
					return component.default || defaultValue
				} else {
					return undefined
				}
			}
		},

		// 获取提交表单数据
		async learun_form_convertToPostData(formData, components, isUpdate) {
			/*需要进行一次数据转化，主要是针对文件上传组件*/
			for (const component of components) {

				if (!component.subfield) {
					//console.log(component,isUpdate,'learun_form_convertToPostData')
					if (['upload', 'uploadimg'].includes(component.type)) {
						//如果是文件上传组件需要将文件进行上传
						const fileList = formData[component.prop]
						if (typeof fileList !== 'string') {
							formData[component.prop] = await this.UPLOAD_FILE(fileList)
						}
					} else if (['modifyuser'].includes(component.type)) {
						//if (isUpdate) {
						const loginUser = this.GET_GLOBAL('loginUser')
						formData[component.prop] = loginUser.f_UserId
						//}
					} else if (['modifytime'].includes(component.type)) {
						//if (isUpdate) {
						formData[component.prop] = this.DATENOW()
						//}
					} else if (['gridtable'].includes(component.type)) {
						//转化子表文件上传组件 modify by kyle on 20230-10-20
						// for (let i = 0; i < component.children.length; i ++){
						for (const children of component.children) {
							if (['upload', 'uploadimg'].includes(children.type)) {
								for (let i = 0; i < formData[component.prop].length; i++) {
									//如果是文件上传组件需要将文件进行上传
									const fileList = formData[component.prop][i][children.prop];
									if (typeof fileList !== 'string') {
										formData[component.prop][i][children.prop] = await this.UPLOAD_FILE(
											fileList)
									}
								}
							}
						}
						formData[component.prop].forEach(row => {
							delete row.hasNoDeleteBtn
							delete row.disabled
							delete row.abledList
						})
					}
				}
			}
			return formData
		},

		// 获取部门数据
		async learun_form_getDepartment(value) {
			if (!this.VALIDATENULL(value)) {
				const departmentMap = this.GET_DATA('learun_departments_map') || {}
				let department = departmentMap[value]
				if (!department) {
					department = await this.FETCH_DEPARTMENT(value)
					if (department) {
						departmentMap[department.value] = department
						this.SET_DATA('learun_departments_map', departmentMap)
					}
				}
				return department
			} else {
				return null
			}
		},

		// 获取用户数据
		async learun_form_getUser(value) {
			const loginUser = this.GET_GLOBAL('loginUser')
			const userMap = this.GET_DATA('learun_users_map') || {}
			if (!userMap[loginUser.f_UserId]) {
				const userInfo = this.COPY(loginUser)
				delete userInfo.roleIds
				delete userInfo.postIds
				delete userInfo.moduleAuthIds

				userInfo.value = userInfo.f_UserId
				userInfo.label = userInfo.f_RealName
				userInfo.account = userInfo.f_Account

				userMap[userInfo.f_UserId] = userInfo
				this.SET_DATA('learun_users_map', userMap)
			}


			if (!this.VALIDATENULL(value)) {
				let userObj = userMap[value]
				if (!userObj) {
					userObj = await this.FETCH_USER(value)
					if (userObj) {
						userMap[userObj.value] = userObj
						this.SET_DATA('learun_users_map', userMap)
					}
				}
				return userObj
			} else {
				return null
			}
		}


	}
}