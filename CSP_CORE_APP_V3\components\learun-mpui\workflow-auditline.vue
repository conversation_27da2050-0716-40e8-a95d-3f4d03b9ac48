<template>
	<learun-timeline-wraper>
		<learun-timeline-item v-for="(item,index) in list" 
			:key="index"
			:isFirst="index == 0"
			:isLast="index == list.length -1"
			
			:color="item.current?'#2979ff':''"
			>
			<view style="font-size: 12px;" >
				<text>{{$t(item.nodeName)}}</text>
			</view>
			<view style="color: #6a6a6a;" >
				<text style="color: #2979ff;" >{{$t(item.nodeUser)}}</text>
			</view>
		</learun-timeline-item>
	</learun-timeline-wraper>
</template>

<script>
export default {
  name: 'learun-workflow-auditline',
  props: {
    list: { default: () => [] }
  }
}
</script>
