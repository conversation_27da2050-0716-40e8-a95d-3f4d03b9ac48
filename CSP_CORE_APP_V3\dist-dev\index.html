<!doctype html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport"
			content="width=device-width,height=device-height,initial-scale=1,user-scalable=0,minimum-scale=1,maximum-scale=1,viewport-fit=cover">
		<link rel="shortcut icon" type="image" href="/static/logo.png">
		<link rel="manifest" href="/static/pwa-manifest.json" />
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
		<title></title>
		<link rel="stylesheet" href="styles.css?v=asxga454">
		<script defer="defer" src="/js/chunk-vendors.b066fcd5.js"></script>
		<script defer="defer" src="/js/app.b2efd6ba.js"></script>
		<link href="/css/app.71600318.css" rel="stylesheet">
	</head>
	<body><noscript><strong>您当前使用的浏览器已关闭或不支持 Javascript 功能，因此框架无法正常初始化，请检查设置或重新安装浏览器。</strong></noscript>
		<div id="app"></div>
	</body>
	<script src="/static/signalr/signalr.min.js" charset="utf-8"></script>
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
		})
		// fetch('/static/pwa-manifest.json')
		//   .then(response => {
		//     if (response.ok) {
		//       return response.json();
		//     }
		//     throw new Error('Network response was not ok.');
		//   })
		//   .then(manifest => {
		//     console.log('Manifest file content:', manifest);
		//   })
		//   .catch(error => {
		//     console.error('Error fetching manifest:', error);
		//   });
		// let deferredPrompt
		// // 阻止默认弹出安装事件
		// window.addEventListener('beforeinstallprompt', (event) => {
		// 	console.log('beforeinstallprompt-------')
		//   // // 阻止默认行为
		//   // event.preventDefault()
		//   // // 保存事件，稍后触发安装
		//   deferredPrompt = event
		//   // // 显示自定义安装按钮
		//   // document.getElementById('installBtn').style.display = 'block'
		// })

		// 监听按钮点击事件
		// document.getElementById('installBtn').addEventListener('click', () => {
		// 	console.log('点击安装pwa', deferredPrompt)
		//   if (deferredPrompt) {
		// 		// 触发PWA安装
		//     deferredPrompt.prompt()

		//     // 监听安装结果
		//     deferredPrompt.userChoice.then((choiceResult) => {
		// 			if (choiceResult.outcome === 'accepted') {
		// 				console.log('用户安装了PWA')
		//       } else {
		// 				console.log('用户拒绝安装PWA')
		// 			}

		//       // 重置事件
		// 			deferredPrompt = null
		// 		})
		//   }
		// })

		// 判断安装环境
		// const isInStandaloneMode = () =>
		// 	window.matchMedia('(display-mode: standalone)').matches ||
		//   window.navigator.standalone ||
		//   document.referrer.includes('android-app://')
		//   console.log('panduan env:', isInStandaloneMode())
		//   if (isInStandaloneMode()) {
		// 		document.getElementById('content').innerHTML = '在pwa中打开了'
		//     window.location.href = 'https://www.baidu.com/'
		//     // document.body.innerHTML = '是在pwa中打开的'
		//   } else {
		// 		document.getElementById('content').innerHTML =
		// 			'在桌面快捷打开会自动跳转到baidu'
		//       // document.body.innerHTML = '请在pwa中打开'
		//   }
		// if ('serviceWorker' in navigator) {
		// 	navigator.serviceWorker.register('/static/service-worker.js')
		// 		.then(function (registration) {
		// 			registration.pushManager.getSubscription()
		// 			  .then(function(subscription) {
		// 			    if (subscription) {
		// 			      console.log(JSON.stringify(subscription));
		// 			    } else {
		// 			      console.log('没有订阅');
		// 			      subscribeUser(registration);
		// 			    }
		// 					// 直接调用showNotification方法，显示消息
		// 					registration.showNotification('Hello World');
		// 			  });
		//         console.log('Service Worker注册成功');
		//         })
		//     .catch(function (error) {
		//      	console.log('Service Worker注册失败：', error);
		//     });
		// }
		// function subscribeUser(swReg) {
		// 	let applicationServerPublicKey = "BLbSQAt6xGIykqCgV2Hqgw8Ch9BpQpFJc9UGuTteI2mBifDXaLccTcw-958UXE8T-JlgAnM-Giz0Qcsx85DEL3A"
		//   const applicationServerKey = urlB64ToUint8Array(applicationServerPublicKey);
		//   swReg.pushManager.subscribe({
		//     userVisibleOnly: true,
		//     applicationServerKey: applicationServerKey
		//   })
		//   .then(function(subscription) {
		//     console.log(JSON.stringify(subscription));
		//   })
		//   .catch(function(err) {
		//     console.log('订阅失败: ', err);
		//   });
		// }
		// function urlB64ToUint8Array(base64String) {
		//   const padding = '='.repeat((4 - base64String.length % 4) % 4);
		//   const base64 = (base64String + padding)
		//     .replace(/\-/g, '+')
		//     .replace(/_/g, '/');

		//   const rawData = window.atob(base64);
		//   const outputArray = new Uint8Array(rawData.length);

		//   for (let i = 0; i < rawData.length; ++i) {
		//     outputArray[i] = rawData.charCodeAt(i);
		//   }
		//   return outputArray;
		// }
	</script>
</html>