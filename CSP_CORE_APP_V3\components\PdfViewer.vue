<template>
  <view class="document-viewer-container">
    <!-- H5和APP端使用web-view方式 -->
    <!-- #ifdef H5 || APP-PLUS -->
    <web-view
      v-if="previewUrl"
      :src="previewUrl"
      class="document-webview"
      @message="handleWebViewMessage"
      @error="handleWebViewError"
    >
    </web-view>
    <!-- #endif -->

    <!-- 微信小程序端特殊处理 -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="mp-preview-container">
      <view class="preview-info">
        <view class="file-icon">
          <text class="icon">{{ fileIcon }}</text>
        </view>
        <text class="file-name">{{ fileName }}</text>
        <text class="file-type">{{ fileTypeText }}</text>
        <text class="file-tip">点击下方按钮打开{{ fileTypeText }}文档</text>
      </view>
      <button
        class="preview-btn"
        @click="previewInMiniProgram"
        :loading="downloading"
      >
        {{ downloading ? "下载中..." : "预览文档" }}
      </button>
    </view>
    <!-- #endif -->

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" color="#007AFF"></uni-load-more>
      <text class="loading-text">正在加载{{ fileTypeText }}...</text>
    </view>

    <!-- 错误状态 -->
    <view v-if="error" class="error-container">
      <text class="error-text">{{ errorMessage }}</text>
      <button class="retry-btn" @click="retry">重新加载</button>
    </view>

    <!-- 不支持的文件类型 -->
    <view v-if="!supportedType && !loading" class="unsupported-container">
      <view class="unsupported-info">
        <text class="unsupported-icon">📄</text>
        <text class="unsupported-title">暂不支持此文件格式预览</text>
        <text class="unsupported-desc"
          >文件类型：{{ fileType.toUpperCase() }}</text
        >
        <text class="unsupported-desc"
          >支持格式：PDF、Word、Excel、PowerPoint</text
        >
      </view>
      <button class="download-btn" @click="downloadFile">下载文件</button>
    </view>
  </view>
</template>

<script>
export default {
  name: "DocumentViewer",
  props: {
    // 文档文件URL
    src: {
      type: String,
      required: true,
    },
    // 文件名（可选）
    fileName: {
      type: String,
      default: "文档",
    },
    // 是否使用本地预览器（可选）
    useLocal: {
      type: Boolean,
      default: false,
    },
    // 是否禁用编辑工具（可选）
    disableEdit: {
      type: Boolean,
      default: false,
    },
    // 是否显示工具栏（可选）
    showToolbar: {
      type: Boolean,
      default: false,
    },
    // 是否允许下载（可选）
    allowDownload: {
      type: Boolean,
      default: false,
    },
    // 是否允许打印（可选）
    allowPrint: {
      type: Boolean,
      default: false,
    },
    // 预览服务提供商 ('microsoft', 'google', 'custom')
    previewProvider: {
      type: String,
      default: "microsoft",
    },
    // 手动指定文件类型（可选）
    manualFileType: {
      type: String,
      default: "",
      validator: function (value) {
        return [
          "",
          "pdf",
          "doc",
          "docx",
          "xls",
          "xlsx",
          "ppt",
          "pptx",
        ].includes(value);
      },
    },
    // 通用预览模式（不检测文件类型，直接尝试预览）
    universalMode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      previewUrl: "",
      loading: true,
      error: false,
      errorMessage: "",
      downloading: false,
      fileType: "",
      supportedType: false,
      currentPreviewAttempt: 0, // 当前预览尝试次数
      previewAttempts: ["pdf", "office"], // 预览尝试顺序
    };
  },
  computed: {
    // 文件类型文本
    fileTypeText() {
      const typeMap = {
        pdf: "PDF",
        doc: "Word",
        docx: "Word",
        xls: "Excel",
        xlsx: "Excel",
        ppt: "PowerPoint",
        pptx: "PowerPoint",
      };
      return typeMap[this.fileType] || "文档";
    },

    // 文件图标
    fileIcon() {
      const iconMap = {
        pdf: "📄",
        doc: "📝",
        docx: "📝",
        xls: "📊",
        xlsx: "📊",
        ppt: "📋",
        pptx: "📋",
      };
      return iconMap[this.fileType] || "📄";
    },
  },
  mounted() {
    this.initDocumentViewer();
  },
  watch: {
    src: {
      handler(newVal) {
        if (newVal) {
          this.initDocumentViewer();
        }
      },
      immediate: true,
    },
  },
  methods: {
    initDocumentViewer() {
      if (!this.src) {
        this.error = true;
        this.errorMessage = "请提供文档文件地址";
        this.loading = false;
        return;
      }

      this.loading = true;
      this.error = false;

      // 检测文件类型
      this.detectFileType();

      // #ifdef H5 || APP-PLUS
      this.setupWebViewDocument();
      // #endif

      // #ifdef MP-WEIXIN
      this.loading = false;
      // #endif
    },

    // 检测文件类型
    detectFileType() {
      // 0. 如果开启通用预览模式，直接尝试预览
      if (this.universalMode) {
        this.useUniversalPreview();
        return;
      }

      // 1. 如果手动指定了文件类型，直接使用
      if (this.manualFileType) {
        this.fileType = this.manualFileType;
        this.supportedType = true;
        return;
      }

      const url = this.src.toLowerCase();

      // 1. 首先检查URL中的文件扩展名
      const fileExtensions = [
        "pdf",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "ppt",
        "pptx",
      ];

      for (let ext of fileExtensions) {
        if (url.includes(`.${ext}`)) {
          this.fileType = ext;
          this.supportedType = true;
          return;
        }
      }

      // 2. 检查文件名prop中的扩展名
      if (this.fileName) {
        const fileName = this.fileName.toLowerCase();
        for (let ext of fileExtensions) {
          if (fileName.includes(`.${ext}`)) {
            this.fileType = ext;
            this.supportedType = true;
            return;
          }
        }
      }

      // 3. 根据URL关键字判断文件类型
      const urlKeywords = {
        pdf: ["pdf", "acrobat"],
        doc: ["word", "document", "doc"],
        docx: ["word", "document", "docx"],
        xls: ["excel", "spreadsheet", "xls"],
        xlsx: ["excel", "spreadsheet", "xlsx"],
        ppt: ["powerpoint", "presentation", "ppt"],
        pptx: ["powerpoint", "presentation", "pptx"],
      };

      for (let [type, keywords] of Object.entries(urlKeywords)) {
        for (let keyword of keywords) {
          if (url.includes(keyword)) {
            this.fileType = type;
            this.supportedType = true;
            return;
          }
        }
      }

      // 4. 检查URL路径中的特殊标识
      const pathIndicators = {
        pdf: ["/pdf/", "/documents/pdf/", "/files/pdf/"],
        doc: ["/word/", "/documents/word/", "/files/doc/"],
        docx: ["/word/", "/documents/word/", "/files/docx/"],
        xls: ["/excel/", "/spreadsheets/", "/files/xls/"],
        xlsx: ["/excel/", "/spreadsheets/", "/files/xlsx/"],
        ppt: ["/powerpoint/", "/presentations/", "/files/ppt/"],
        pptx: ["/powerpoint/", "/presentations/", "/files/pptx/"],
      };

      for (let [type, paths] of Object.entries(pathIndicators)) {
        for (let path of paths) {
          if (url.includes(path)) {
            this.fileType = type;
            this.supportedType = true;
            return;
          }
        }
      }

      // 5. 如果都无法识别，尝试通过请求头检测（仅在支持的环境下）
      this.detectFileTypeByRequest();
    },

    // 通过HTTP请求头检测文件类型
    async detectFileTypeByRequest() {
      try {
        // #ifdef H5
        const response = await fetch(this.src, { method: "HEAD" });
        const contentType = response.headers.get("content-type");

        if (contentType || "") {
          const typeMapping = {
            "application/UNKNOWN": "UNKNOWN",
            "application/pdf": "pdf",
            "application/msword": "doc",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
              "docx",
            "application/vnd.ms-excel": "xls",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
              "xlsx",
            "application/vnd.ms-powerpoint": "ppt",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation":
              "pptx",
          };

          const detectedType = typeMapping[contentType];
          if (detectedType) {
            this.fileType = detectedType;
            this.supportedType = true;
            return;
          }
        }
        // #endif
      } catch (error) {
        console.warn("无法通过请求头检测文件类型:", error);
      }

      // 如果所有方法都失败，启用通用预览模式
      this.useUniversalPreview();
    },

    // 使用通用预览模式
    useUniversalPreview() {
      console.log(
        "启用通用预览模式，尝试第",
        this.currentPreviewAttempt + 1,
        "种方式"
      );

      if (this.currentPreviewAttempt < this.previewAttempts.length) {
        const attemptType = this.previewAttempts[this.currentPreviewAttempt];
        this.fileType = attemptType;
        this.supportedType = true;

        // 根据尝试类型设置显示文本
        if (attemptType === "pdf") {
          // 先尝试PDF预览
          console.log("尝试PDF预览模式");
        } else if (attemptType === "office") {
          // 再尝试Office预览
          console.log("尝试Office预览模式");
        }
      } else {
        // 所有尝试都失败了
        this.fileType = "unknown";
        this.supportedType = false;
      }
    },

    // 切换到下一种预览方式
    tryNextPreviewMode() {
      this.currentPreviewAttempt++;
      console.log("当前预览方式失败，尝试下一种方式");

      if (this.currentPreviewAttempt < this.previewAttempts.length) {
        this.useUniversalPreview();
        this.setupWebViewDocument();
      } else {
        // 所有方式都尝试过了
        this.error = true;
        this.errorMessage = "无法预览此文档，请检查文件格式";
        this.loading = false;
      }
    },

    // 设置文档预览URL
    setupWebViewDocument() {
      try {
        if (!this.supportedType) {
          this.loading = false;
          return;
        }

        const encodedUrl = encodeURIComponent(this.src);

        if (this.fileType === "pdf") {
          // PDF文件预览
          this.setupPdfPreview(encodedUrl);
        } else {
          // Office文档预览
          this.setupOfficePreview(encodedUrl);
        }

        // 延迟取消加载状态，等待web-view加载
        setTimeout(() => {
          this.loading = false;
        }, 3000);
      } catch (error) {
        console.error("文档预览初始化失败:", error);
        this.error = true;
        this.errorMessage = "文档预览初始化失败";
        this.loading = false;
      }
    },

    // 设置PDF预览
    setupPdfPreview(encodedUrl) {
      const params = new URLSearchParams();
      params.append("file", this.src);

      // 控制工具栏显示
      if (!this.showToolbar) {
        params.append("toolbar", "0");
      }

      // 禁用编辑功能
      if (this.disableEdit) {
        params.append("textLayer", "0");
        params.append("annotationLayer", "0");
      }

      // 控制下载功能
      if (!this.allowDownload) {
        params.append("download", "0");
      }

      // 控制打印功能
      if (!this.allowPrint) {
        params.append("print", "0");
      }

      // 禁用右键菜单和侧边栏
      params.append("contextmenu", "0");
      params.append("sidebar", "0");

      if (this.useLocal) {
        this.previewUrl = `/static/pdfjs/web/viewer.html?${params.toString()}`;
      } else {
        this.previewUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?${params.toString()}`;
      }
    },

    // 设置Office文档预览
    setupOfficePreview(encodedUrl) {
      switch (this.previewProvider) {
        case "microsoft":
          // 使用微软Office Online预览服务
          this.previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
          break;
        case "google":
          // 使用Google Docs预览服务
          this.previewUrl = `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`;
          break;
        default:
          // 默认使用微软服务
          this.previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
      }
    },

    // 微信小程序预览文档
    previewInMiniProgram() {
      // #ifdef MP-WEIXIN
      if (this.downloading) return;

      this.downloading = true;
      uni.showLoading({
        title: "正在下载...",
      });

      uni.downloadFile({
        url: this.src,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              showMenu: !this.disableEdit,
              success: () => {
                console.log("文档预览成功");
                this.$emit("preview-success");
              },
              fail: (err) => {
                console.error("文档预览失败:", err);
                uni.showToast({
                  title: "预览失败",
                  icon: "none",
                });
                this.$emit("preview-error", err);
              },
            });
          } else {
            uni.showToast({
              title: "文件下载失败",
              icon: "none",
            });
          }
        },
        fail: (err) => {
          console.error("文档下载失败:", err);
          uni.showToast({
            title: "下载失败",
            icon: "none",
          });
          this.$emit("download-error", err);
        },
        complete: () => {
          this.downloading = false;
          uni.hideLoading();
        },
      });
      // #endif
    },

    // 下载文件
    downloadFile() {
      // #ifdef H5
      const link = document.createElement("a");
      link.href = this.src;
      link.download = this.fileName;
      link.click();
      // #endif

      // #ifndef H5
      uni.downloadFile({
        url: this.src,
        success: (res) => {
          uni.showToast({
            title: "下载成功",
            icon: "success",
          });
        },
        fail: (err) => {
          uni.showToast({
            title: "下载失败",
            icon: "none",
          });
        },
      });
      // #endif
    },

    handleWebViewMessage(event) {
      console.log("WebView消息:", event);
      this.$emit("message", event);
    },

    handleWebViewError(event) {
      console.error("WebView错误:", event);

      // 如果是通用预览模式或者无法识别文件类型，尝试下一种预览方式
      if (
        this.universalMode ||
        this.fileType === "unknown" ||
        this.currentPreviewAttempt > 0
      ) {
        console.log("预览失败，尝试下一种预览方式");
        this.tryNextPreviewMode();
      } else {
        this.error = true;
        this.errorMessage = "文档加载失败，请检查网络连接";
        this.loading = false;
        this.$emit("error", event);
      }
    },

    retry() {
      this.initDocumentViewer();
    },
  },
};
</script>

<style lang="scss" scoped>
.document-viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f5f5f5;
}

.document-webview {
  width: 100%;
  height: 100%;
}

.mp-preview-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;

  .preview-info {
    text-align: center;
    margin-bottom: 60rpx;

    .file-icon {
      margin-bottom: 20rpx;

      .icon {
        font-size: 80rpx;
      }
    }

    .file-name {
      display: block;
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 10rpx;
    }

    .file-type {
      display: block;
      font-size: 24rpx;
      color: #007aff;
      margin-bottom: 20rpx;
    }

    .file-tip {
      font-size: 28rpx;
      color: #666;
    }
  }

  .preview-btn {
    background: linear-gradient(135deg, #007aff, #5ac8fa);
    color: white;
    border: none;
    padding: 24rpx 60rpx;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);

    &:active {
      transform: translateY(2rpx);
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;

  .error-text {
    color: #ff4d4f;
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .retry-btn {
    background: #007aff;
    color: white;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
  }
}

.unsupported-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;

  .unsupported-info {
    text-align: center;
    margin-bottom: 60rpx;

    .unsupported-icon {
      display: block;
      font-size: 80rpx;
      margin-bottom: 30rpx;
    }

    .unsupported-title {
      display: block;
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 20rpx;
    }

    .unsupported-desc {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 10rpx;
    }
  }

  .download-btn {
    background: #52c41a;
    color: white;
    border: none;
    padding: 24rpx 60rpx;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: 500;
  }
}
</style>
