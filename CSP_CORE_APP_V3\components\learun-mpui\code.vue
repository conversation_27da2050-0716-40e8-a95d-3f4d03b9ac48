<template>
	<uni-easyinput :value="value2" disabled :clearable="false" />
</template>

<script>
export default {
	name: 'learun-code',
	props: {
		value:[String],
		code:String
	},
	watch: {
		value:{
			handler(newVal){
				if(this.VALIDATENULL(newVal)){
					this.value2 = `learun_code_${this.code}|${this.GUID()}`
				}
			},
			immediate: true
		}
	},
	computed:{
		value2:{
			get(){
				if(this.VALIDATENULL(this.value) || this.value.indexOf('learun_code_') != -1){
					return this.$t('保存时生成')
				}
				return this.value
			},
			set(val){
				this.$emit('input',val)
			}
		}
	}
}
</script>

<style>
</style>
