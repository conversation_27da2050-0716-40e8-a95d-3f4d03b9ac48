{"name": "CSP of CETVN", "private": true, "version": "1.1.0", "description": "CSP of CETVN", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"start": "zmp start", "deploy": "zmp deploy", "build:css": "postcss src/css/tailwind.css -o src/css/styles.css"}, "dependencies": {"@react-spring/web": "^9.6.1", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.14.191", "@vitejs/plugin-react": "^1.3.2", "crypto-js": "^4.2.0", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-router-dom": "^6.8.2", "recoil": "^0.7.7", "swiper": "^9.1.0", "zmp-sdk": "^2.39.9", "zmp-ui": "^1.11.5"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "postcss": "^8.4.21", "postcss-aspect-ratio-polyfill": "^2.0.0", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "sass": "^1.58.3", "tailwindcss": "^3.4.10", "vite": "^2.9.18", "vite-tsconfig-paths": "^4.0.5"}}