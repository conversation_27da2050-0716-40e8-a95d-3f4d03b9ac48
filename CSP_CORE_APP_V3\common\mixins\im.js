import {
	emitEvent,
	onEvent
} from "./eventBus"

let connection = null;
let isStop = false

const imInit = async (api, userId) => {
	console.log(7, api, userId)
	isStop = false
	// 初始化im即时通讯-SignalR
	if (connection == null) {
		connection = new window.signalR.HubConnectionBuilder()
			.withUrl(`${api}/ChatsHub`)
			.withAutomaticReconnect({
				nextRetryDelayInMilliseconds: retryContext => {
					console.log(14, 'im 重连', retryContext.elapsedMilliseconds)
					if (isStop) {
						return false
					}
					console.log("im.js", 18, retryContext.elapsedMilliseconds)
					if (retryContext.elapsedMilliseconds < 60000) {
						return 10000; // 10s重连一次
					} else {
						return 60000; // 60s重连一次
					}
				}
			})
			.build()

		connection.on("revMsg", (userId, msg, dateTime, id, isSystem) => {
			console.log(29, "im.js", userId, msg, dateTime, id, isSystem, 'revMsg')
			emitEvent('learun-im-system-message', {
				userId,
				msg,
				dateTime,
				id
			})
		})

		connection.onclose(async () => {
			//console.log("Connection closed!", e)
			if (!isStop) {
				await start()
			}
		})

		async function start() {
			try {
				await connection.start()
				connection.invoke("SendInfo", userId)
				//console.log("SignalR Connected.")
			} catch (err) {
				console.log(err)
				setTimeout(start, 5000)
			}
		}

		// 监听发送事件
		// onEvent('learun-im-send', ({ sendUserId, recvUserId, msg, id, isSystem }) => {
		//   //console.log(sendUserId, recvUserId, msg, id, isSystem)
		//   if (isSystem == undefined) {
		//     isSystem = 0
		//   }
		//   if (connection.state == 'Connected') {
		//     connection.invoke(
		//       "SendMsg",
		//       sendUserId,
		//       recvUserId,
		//       msg,
		//       id,
		//       isSystem
		//     );
		//   }
		// })

		await start()

		//console.log(80, connection.connectionId)
	}
}


const imClose = async () => {
	if (connection) {
		isStop = true
		await connection.stop();
	}

}


export {
	imClose,
	imInit
}