<template>
	<!-- 当页面准备好时，渲染页面内容 -->
	<view v-if="ready" class="page" :style="{'padding-bottom':'40px'}">
		<!-- 顶部固定栏，显示数据总数和筛选按钮 -->
		<view class="learun-top-bar fixed" style="padding-right: 8px;">
			<!-- 显示数据总数 -->
			<view class="learun-total-bar">
				<text>{{$t("共")}} {{total}} {{$t("条数据")}}</text>
			</view>
			<!-- 筛选按钮，点击触发筛选操作 -->
			<view class="learun-search-btn" @click.stop="searchClick">
				<text>{{$t("筛选")}}</text>
				<view class="learun-search-btn__flag" />
			</view>
		</view>
		<!-- 顶部留白区域 -->
		<view :style="{'padding-top':'40px'}"></view>
		<!-- 循环渲染请假列表项 -->
		<view v-for="(item,index) in list" :key="index" @click.stop="handleRowClick({row: item, index: index})"
			class="table-view" @scrolltolower="loadMore">
			<!-- 显示列表项序号 -->
			<view class="table-index">{{index+1}}</view>
			<!-- 显示列表项具体内容 -->
			<view class="table-content">
				<view>{{$t("开始日期：")}}{{ formatDate(item.from_Date) }} {{item.from_Time}}</view>
				<view>{{$t("结束日期：")}}{{ formatDate(item.to_Date) }} {{item.to_Time}}</view>
				<view>{{$t("请假类型：")}}{{item.leave_Type_Name}}</view>
				<view>{{$t("批核状态：")}}{{item.approve_Status_Name}}</view>
			</view>
		</view>
		<!-- 加载更多组件，根据加载状态显示不同提示 -->
		<uni-load-more :status="statusLoadMore" v-show="statusLoadMore != 'nomore'" />
		<!-- 新增按钮，当有新增权限时显示 -->
		<view v-if="isAdd()" class="learun-bottom-btns" :style="{'margin-top':isAdd() ?'40px':''}">
			<button @click.stop="handleAdd" type="primary">{{$t("新增")}}</button>
		</view>
		<!-- 弹出按钮组件，用于显示编辑和删除按钮 -->
		<learun-popup-buttons ref="popup" :buttons="rowBtns" @click="BUTTONS_CLICK" />
	</view>
</template>

<script>
	// 导入自定义表单混入
	import customFormMixins from '@/common/customform.js'
	// 导入 lodash 工具函数
	import keyBy from 'lodash/keyBy'
	import mapValues from 'lodash/mapValues'
	import values from 'lodash/values'
	import groupBy from 'lodash/groupBy'
	import mapKeys from 'lodash/mapKeys'

	export default {
		// 使用混入
		mixins: [customFormMixins],
		data() {
			return {
				// 模块 ID
				moduleId: '',
				// 行操作按钮配置
				rowBtns: [{
						prop: 'Edit',
						label: '编辑',
						type: 'primary'
					},
					{
						prop: 'Delete',
						label: '删除',
						type: 'warn'
					}
				],
				// 表格列配置
				columns: [{
						label: '单号',
						rowid: 'leave_Note_NO',
						minWidth: 120,
						align: 'left'
					},
					{
						label: '填单人',
						rowid: 'submit_UserID',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "createuser"
						}
					},
					{
						label: '提交日期',
						rowid: 'leave_Submit_Date',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "createtime"
						}
					},
					{
						label: '请假类型',
						rowid: 'leave_Type',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "select",
							"dataType": "dataSource",
							"dataCode": "FHIS_Leave_Type",
							"dataValueKey": "leave_type",
							"dataLabelKey": "leave_description",
							"upCtrl": "",
							"upShowAll": false,
							"required": true
						}
					},
					{
						label: '请假方式',
						rowid: 'leave_Way',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "radio",
							"dataType": "dataItem",
							"dataCode": "Leave_Way",
							"required": true
						}
					},
					{
						label: '请假日期',
						rowid: 'from_Date',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "datetime",
							"format": "yyyy-MM-dd",
							"required": true
						}
					},
					{
						label: '请假结束日期',
						rowid: 'to_Date',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "datetime",
							"format": "yyyy-MM-dd",
							"labelWidth": 0,
							"required": true
						}
					},
					{
						label: '请假时段',
						rowid: 'leave_Shift_Item',
						minWidth: 120,
						align: 'left',
						config: {
							"type": "radio",
							"dataType": "dataItem",
							"dataCode": "Leave_Shift_Item",
							"required": true
						}
					},
					{
						label: '请假时间',
						rowid: 'from_Time',
						minWidth: 120,
						align: 'left'
					},
					{
						label: '请假结束时间',
						rowid: 'to_Time',
						minWidth: 120,
						align: 'left'
					},
					{
						label: '备注',
						rowid: 'remark',
						minWidth: 120,
						align: 'left'
					}
				],
				// 每页显示的记录数
				rows: 20,
				// 当前页码
				page: 1,
				// 总记录数
				total: 0,
				// 列表数据
				list: [],
				// 查询参数
				queryParams: {},
				// 主键
				primaryKey: '',
				// 当前编辑的行数据
				editRow: null,
				// 页面是否准备好的标志
				ready: false,
				// 当前登录用户
				loginUser: "",
				// 我的草稿数据
				my_draft_data: null,
				// 未完成任务数据
				unCompletedTask: null,
				// 加载更多状态，'more'、'loading' 或 'nomore'
				statusLoadMore: "more",
				// 是否在 onLoad 生命周期
				isOnLoad: false
			}
		},
		async onLoad() {
			// 标记处于 onLoad 生命周期
			this.isOnLoad = true
			// 初始化页面
			await this.init()
		},
		async onShow() {
			// 重置页码为 1
			this.page = 1
			// 如果不是在 onLoad 生命周期，重新获取数据列表
			if (!this.isOnLoad) {
				await this.getDataList()
			}
			// 标记处于 onLoad 生命周期
			this.isOnLoad = true
		},
		onUnload() {
			// 移除列表数据变化事件监听
			this.OFF(`custom-list-change-${this.moduleId}`)
		},
		methods: {
			// 初始化页面
			async init(formId) {
				// 显示加载提示
				this.LOADING('加载中...')
				// 获取当前登录用户
				this.loginUser = this.GET_GLOBAL('loginUser')
				// 获取模块 ID
				this.moduleId = this.GET_MODULEID()
				// 获取页面权限信息
				await this.FETCH_AUTH(this.moduleId)
				// 监听列表数据变化事件，触发刷新列表操作
				this.ON(`custom-list-change-${this.moduleId}`, this.getDataList)
				// 拉取数据源数据
				await this.fetchDataSource(this.columns)
				// 获取数据列表
				await this.getDataList()
				// 隐藏加载提示
				this.HIDE_LOADING()
				// 标记页面准备好
				this.ready = true
			},
			// 拉取列表数据
			async fetchList(update) {
				// 设置查询参数
				this.queryParams.rows = this.rows
				this.queryParams.page = this.page
				this.queryParams.sidx = "Leave_Note_No desc"
				this.queryParams.emp_no = this.loginUser.f_DDOpenId
				// 发送 HTTP 请求获取列表数据
				const result = await this.HTTP_GET({
					url: `/hrattf/hrattF001/page`,
					params: this.queryParams,
					errorTips: '加载数据时出错'
				})
				// 如果请求结果为空，返回
				if (!result) {
					return
				}
				// 获取请假类型名称列表
				const leaveTypeNameList = await this.FETCH_DATASOURCE("FHIS_Leave_Type_Com_All");
				// 为每条记录添加请假类型名称
				for (let i = 0; i < result.rows.length; i++) {
					for (let j = 0; j < leaveTypeNameList.length; j++) {
						if (result.rows[i].leave_Type == leaveTypeNameList[j].leave_type) {
							result.rows[i].leave_Type_Name = leaveTypeNameList[j].leave_description
						}
					}
				}
				const LeaveStatus = await this.FETCH_DATASOURCE("FHIS_Leave_Status");
				// 为每条记录添加批核状态名称
				for (let i = 0; i < result.rows.length; i++) {
					for (let j = 0; j < LeaveStatus.length; j++) {
						if (result.rows[i].approve_Status == LeaveStatus[j].value) {
							result.rows[i].approve_Status_Name = this.$t(LeaveStatus[j].local_name)
						}
					}
				}
				// 更新总记录数
				this.total = result.records
				// 更新当前页码
				this.page = result.page
				// 如果是更新数据，将新数据追加到列表中；否则，替换列表数据
				if (update) {
					this.list.push(...result.rows)
				} else {
					this.list = result.rows
				}
				// 如果列表数据数量等于总记录数，设置加载状态为 'nomore'
				if (this.total == this.list.length) {
					this.statusLoadMore = "nomore"
				}
				// 拉取记录中的组织结构信息
				await this.fetchOrganizeInfo(result.rows)
			},
			// 处理页码变化事件
			async handlePageChange({
				current
			}) {
				// 显示加载提示
				this.LOADING('加载数据中...')
				// 更新当前页码
				this.page = current
				// 拉取列表数据
				await this.fetchList()
				// 隐藏加载提示
				this.HIDE_LOADING()
			},
			// 处理列表项点击事件
			handleRowClick({
				row,
				index
			}) {
				// 记录当前编辑的行数据
				this.editRow = row
				let item
				let type = "lookmy"
				// 查找我的任务数据中对应的记录
				for (let i = 0; i < this.look_my_data.rows.length; i++) {
					if (this.look_my_data.rows[i].f_Id == this.editRow.rid) {
						item = this.look_my_data.rows[i]
					}
				}
				// 如果批核状态符合条件，查找未完成任务数据中对应的记录
				if (['23', '32', '10', '22', '12'].includes(this.editRow.approve_Status)) {
					for (let i = 0; i < this.unCompletedTask.rows.length; i++) {
						if (this.unCompletedTask.rows[i].f_ProcessId == this.editRow.rid) {
							item = this.unCompletedTask.rows[i]
							type = "audit"
						}
					}
				}
				// 如果批核状态为空或第二位为 1，显示操作按钮；否则，跳转到详情页面
				if (!row.approve_Status || (row.approve_Status && row.approve_Status.substring(1, 2) == "1")) {
					this.$refs.popup.open(`操作第${index + 1}行`, this.myBtns())
				} else {
					console.log(342, type, item)
					this.NAV_TO('/pages/workflow/mytask/single?type=' + type, item)
				}
			},
			// 处理新增按钮点击事件
			async handleAdd() {
				// 并行获取可发起的流程列表和流程分类数据
				const [list, groupNames] = await Promise.all([
					this.HTTP_GET({
						url: '/workflow/scheme/mylist',
						errorTips: '加载数据时出错'
					}).then(data => {
						if (!data) {
							return []
						}
						return data.filter(t => t.f_IsInApp == 1) // 显示移动发起流程
					}),
					this.FETCH_DATAITEM('FlowSort').then(result => mapValues(keyBy(values(result), 'f_ItemValue'),
						'f_ItemName'))
				])
				let item
				// 查找指定流程代码的流程
				for (let i = 0; i < list.length; i++) {
					if (list[i].f_Code == "HRATTF001") {
						item = list[i]
					}
				}
				// 跳转到发起任务页面
				this.NAV_TO('/pages/workflow/releasetask/single?type=create', item)
			},
			// 处理编辑按钮点击事件
			handleEdit() {
				// 关闭弹出按钮组件
				this.$refs.popup.close()
				let item
				let data_List = this.my_draft_data.rows
				let ridStirng = "f_Id"
				let url = '/pages/workflow/releasetask/single?type=draft'
				// 如果批核状态符合条件，切换数据列表和跳转地址
				if (this.editRow.approve_Status.substring(1, 2) == "1" && this.editRow.approve_Status != "01") {
					data_List = this.unCompletedTask.rows
					ridStirng = "f_ProcessId"
					url = '/pages/workflow/mytask/single?type=again'
				}
				// 查找对应的记录
				for (let i = 0; i < data_List.length; i++) {
					if (data_List[i][ridStirng] == this.editRow.rid) {
						item = data_List[i]
					}
				}
				// 如果未找到记录，再次查找
				if (!item) {
					data_List = this.unCompletedTask.rows
					ridStirng = "f_ProcessId"
					url = '/pages/workflow/mytask/single?type=again'
					for (let i = 0; i < data_List.length; i++) {
						if (data_List[i][ridStirng] == this.editRow.rid) {
							item = data_List[i]
						}
					}
				}
				// 跳转到编辑页面
				this.NAV_TO(url, item)
			},
			// 处理删除按钮点击事件
			async handleDelete() {
				// 确认是否删除
				if (!(await this.CONFIRM(this.$t("删除项目"), this.$t("确定要删除该项吗？"), true))) {
					return
				}
				// 关闭弹出按钮组件
				this.$refs.popup.close()
				// 显示加载提示
				this.LOADING('提交删除中…')
				// 如果流程已发起，作废流程
				if (this.editRow.approve_Status.substring(1, 2) == "1" && this.editRow.approve_Status != "01") {
					await this.HTTP_DELETE({
						url: `/workflow/process/${this.editRow.rid}`,
						errorTips: '作废时发生错误'
					})
				}
				// 发送 HTTP 请求删除记录
				const success = await this.HTTP_DELETE({
					url: "/hrattf/hrattF001/" + this.editRow.rid,
					errorTips: '删除失败'
				}).then(success => {
					// 隐藏加载提示
					this.HIDE_LOADING()
					if (success) {
						// 显示删除成功提示
						this.TOAST('删除成功', 'success')
						// 重置页码为 1
						this.page = 1
						// 拉取列表数据
						this.fetchList()
					}
				})
			},
			// 拉取自定义应用需要数据源的字段的数据源
			async fetchDataSource(columns) {
				for (let i = 0, len = columns.length; i < len; i++) {
					if (columns[i].config) {
						await this.learun_form_fetchDataSource(columns[i])
					}
				}
			},
			// 拉取结果中的组织结构信息
			async fetchOrganizeInfo(list) {
				const departmentIdList = []
				const userIdList = []
				const areaList = []
				// 收集部门 ID、用户 ID 和区域信息
				for (const row of list) {
					for (const key in row) {
						if (!row[key]) {
							continue
						}
						const column = this.columns.find(t => t.rowid == key)
						if (!column || !column.scheme) {
							continue
						}
						switch (column.scheme.type) {
							case 'userSelect':
							case 'createuser':
							case 'modifyuser':
								if (userIdList.findIndex(t => t == row[key]) == -1) {
									userIdList.push(row[key])
								}
								break
							case 'departmentSelect':
							case 'department':
								if (departmentIdList.findIndex(t => t == row[key]) == -1) {
									departmentIdList.push(row[key])
								}
								break
							case 'areaselect':
								if (row[key]) {
									areaList.push(row[key])
								}
								break
						}
					}
				}
				// 拉取用户和部门的组织结构信息
				await this.learun_form_fetchOrganizeInfo(userIdList, departmentIdList)
				// 拉取区域信息
				await this.learun_form_fetchAreaInfo(areaList)
			},
			// 处理筛选按钮点击事件
			searchClick() {
				// 监听筛选条件返回事件
				this.ONCE('learun-customapp-query', (data) => {
					this.queryParams = data
					setTimeout(async () => {
						// 显示加载提示
						this.LOADING('加载中...')
						// 重置页码为 1
						this.page = 1
						// 重置总记录数为 2
						this.total = 2
						// 清空列表数据
						this.list = []
						// 拉取列表数据
						await this.fetchList()
						// 隐藏加载提示
						this.HIDE_LOADING()
					})
				})
				// 跳转到筛选页面
				this.NAV_TO(`./query`, {
					formScheme: this.formScheme,
					pageScheme: this.pageScheme,
					formData: this.queryParams
				}, true)
			},
			// 获取可用的行操作按钮
			myBtns() {
				return this.rowBtns.filter(t => this.GET_BUTTON_AUTH(t.prop, this.moduleId))
			},
			// 判断是否有新增权限
			isAdd() {
				return this.GET_BUTTON_AUTH('Add', this.moduleId)
			},
			// 获取数据列表，包括列表数据、待办任务数据、我的任务数据和草稿任务数据
			async getDataList(update) {
				await this.fetchList(update)
				await this.fetchMyTask(update)
				await this.fetchDraftTask(update)
				await this.fetchUnCompletedTask(update)
			},
			// 加载待办任务数据
			async fetchUnCompletedTask(update) {
				const result = await this.HTTP_POST({
					url: `/workflow/process/uncompleted/mypage?rows=${this.rows}&page=${this.page}&sidx=f_IsUrge DESC,t.F_CreateDate DESC`,
					params: this.searchData,
					data: {
						keyWord: "",
						code: "HRATTF001"
					},
					errorTips: '加载任务时出错'
				})
				// 如果是更新数据，将新数据追加到列表中；否则，替换列表数据
				if (update) {
					this.unCompletedTask.rows.push(...result.rows)
				} else {
					this.unCompletedTask = result
				}
			},
			// 加载我的任务数据
			async fetchMyTask(update) {
				const result = await this.HTTP_POST({
					url: `/workflow/process/mypage?rows=${this.rows}&page=${this.page}&sidx=F_CreateDate DESC`,
					params: this.searchData,
					data: {
						keyWord: "",
						code: "HRATTF001"
					},
					errorTips: '加载任务时出错'
				})
				// 如果是更新数据，将新数据追加到列表中；否则，替换列表数据
				if (update) {
					this.look_my_data.rows.push(...result.rows)
				} else {
					this.look_my_data = result
				}
			},
			// 加载草稿任务数据
			async fetchDraftTask(update) {
				const result = await this.HTTP_GET({
					url: `/workflow/process/mydraftpage?rows=${this.rows}&page=${this.page}&code=HRATTF001&sidx=F_CreateDate%20DESC`,
					params: this.searchData,
					errorTips: '加载草稿任务时出错'
				})
				// 如果是更新数据，将新数据追加到列表中；否则，替换列表数据
				if (update) {
					this.my_draft_data.rows.push(...result.rows)
				} else {
					this.my_draft_data = result
				}
			},
			// 格式化日期
			formatDate(data) {
				if (data) {
					var dt = new Date(data)
					let year = dt.getFullYear()
					let month = dt.getMonth() + 1
					let date = dt.getDate()
					month = '00' + month
					month = month.substring(month.length - 2, month.length)
					date = '00' + date
					date = date.substring(date.length - 2, date.length)
					return `${year}-${month}-${date}`
				}
				return ""
			},
			/**
			 * 上拉加载更多
			 */
			async onReachBottom() {
				console.log("上拉加载更多")
				// 显示加载中
				this.statusLoadMore = "loading"
				// 页码加 1
				this.page = this.page + 1
				// 获取数据列表
				await this.getDataList(true)
				// 根据列表数据数量和总记录数更新加载状态
				if (this.list.length < this.total) {
					this.statusLoadMore = "more"
				} else {
					this.statusLoadMore = "nomore"
				}
			}
		}
	}
</script>
<style lang="less" scoped>
	.table-view {
		display: flex;
		align-items: center;
		margin: 20rpx;
		border-bottom: solid 2rpx #ededed;
		padding-bottom: 10px;

		padding-top: 10px;
		padding-right: 10px;
		padding-left: 20px;
		background-color: #ffffff;
		border-radius: 10px;
		box-shadow: 3px 3px 5px 0px rgba(0, 0, 0, 0.05);

		.table-index {
			width: 10%;
		}
	}
</style>