<template>
  <view :style="[{ bottom: inputBottom + 'px' }]" class="learun-chat-input-bar">
    <input
      @focus="inputFocus"
      @blur="inputBlur"
      @input="input"
      @confirm="sendClick"
      :value="value"
      :adjust-position="false"
      :enableNative="false"
      :focus="false"
      :placeholder="$t(placeholder)"
      :disabled="inputDisabled"
      :confirm-hold="confirmHold"
      cursor-spacing="10"
      class="learun-chat-input"
      confirm-type="send"
      type="text"
    />
		<button class="learun-chat-input-btn" type="primary"  @click="sendClick" :disabled="buttonDisabled" >发送</button>
  </view>
</template>

<script>
export default {
  name: 'learun-chat-input',

  props: {
    value: {},
    placeholder: {},
    inputDisabled: {},
    buttonDisabled: {},
    confirmHold: {}
  },

  data() {
    return { inputBottom: 0 }
  },

  methods: {
    input(e) {
      this.$emit('input', e.detail.value)
      this.$emit('change', e.detail.value)
    },
    inputFocus(e) {
      this.inputBottom = e.detail.height
      this.$emit('focus')
    },
    inputBlur(e) {
      this.inputBottom = 0
      this.$emit('blur')
    },
    sendClick(e) {
      this.$emit('sendMsg', this.value)
    }
  }
}
</script>
<style lang="scss" >
	.learun-chat-input-bar{
		display: flex;
		align-items: center;
		min-height: 48px;
		justify-content: space-between;
		
		position: fixed;
		width: 100%;
		bottom: constant(safe-area-inset-bottom);
		bottom: env(safe-area-inset-bottom);
		z-index: 1024;
		
		background-color: #fff;
		
		box-sizing: border-box;
		
		padding: 0 16px;
	}
	
	.learun-chat-input{
		flex-grow: 1;
		border-bottom:1px solid $uni-border-1;
	}
	
	.learun-chat-input-btn{
		width: 64px;
		margin-left: 16px;
	}
</style>
