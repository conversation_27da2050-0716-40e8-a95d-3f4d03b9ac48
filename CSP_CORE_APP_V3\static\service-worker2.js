// 表示缓存的名称。在 Service Worker 的生命周期中，缓存名称用于标识不同版本的缓存，以便在更新缓存策略时能够区分新旧缓存。
const CACHE_NAME = 'my-site-cache-v1';
// 该数组包含了一系列需要被缓存的文件路径, 就是没有网络时能够加载的文件，从而实现离线加载的效果
// 这个是 重点 重点 重点
const urlsToCache = [
    // '/',
    // '/index.html',
    // 'assets/uniicons.b6d3756e.ttf',
    // 'assets/iconfont.cfbfb53e.ttf',
    // 'static/wx-qrcode.jpg',
    // 'static/upload.html',
    // 'static/logo.png',
    // 'static/js',
    // 'static/index.2da1efab.css',
    // 'static/img-bar',
    // 'static/img-avatar',
    // 'static/banner.png',
    // 'static/72x72.png',
    // 'static/192x192.png',
    // 'static/.DS_Store',
    // 'static/img-avatar/head.png',
    // 'static/img-bar/tab-my-active.png',
    // 'static/img-bar/tab-my.png',
    // 'static/img-bar/tab-msg-active.png',
    // 'static/img-bar/tab-msg.png',
    // 'static/img-bar/tab-home-active.png',
    // 'static/img-bar/tab-home.png',
    // 'static/img-bar/tab-contact-active.png',
    // 'static/img-bar/tab-contact.png',
    // 'static/js/pages-wxloginTest.51d95ac2.js',
    // 'static/js/pages-wxlogin.253d8dc4.js',
    // 'static/js/pages-workflow-releasetask-single.8a915b09.js',
    // 'static/js/pages-workflow-releasetask-list.e2f0a381.js',
    // 'static/js/pages-workflow-mytask-submit~pagesDemo-clock-setting~pagesHRATTF-hrattF001-list~pagesTest-test-list.eafeaa8e.js',
    // 'static/js/pages-workflow-mytask-submit.2e5b9d8f.js',
    // 'static/js/pages-workflow-mytask-single.a258dd5c.js',
    // 'static/js/pages-workflow-mytask-sign.97c73699.js',
    // 'static/js/pages-workflow-mytask-list.91040a71.js',
    // 'static/js/pages-workflow-common-learun-wfsubmit-info.2afd5b8c.js',
    // 'static/js/pagesTest-test-single.363d5ffe.js',
    // 'static/js/pagesTest-test-query.e5269840.js',
    // 'static/js/pagesTest-test-list.b87312bd.js',
    // 'static/js/pages-test.f3ba99ad.js',
    // 'static/js/pages-signup.c1c172ca.js',
    // 'static/js/pages-sign-signForPC.71f66341.js',
    // 'static/js/pages-password-set.1460e51d.js',
    // 'static/js/pages-password-reset.faedccbe.js',
    // 'static/js/pages-password-dialog.4a42d5e5.js',
    // 'static/js/pages-navToPages.4d907351.js',
    // 'static/js/pagesMy-qrcode.1f840ea6.js',
    // 'static/js/pagesMy-password.ff2a22fd.js',
    // 'static/js/pagesMy-learun.3377826b.js',
    // 'static/js/pagesMy-info.ed7ea2dd.js',
    // 'static/js/pagesMy-framework.37eb7349.js',
    // 'static/js/pagesMy-contact.9f3cbd7c.js',
    // 'static/js/pages-my.b8da9a43.js',
    // 'static/js/pages-msg-chat.1dd2d046.js',
    // 'static/js/pages-msg.bf6467d3.js',
    // 'static/js/pages-login.5f5b9850.js',
    // 'static/js/pages-loadWeb.a6a09fb3.js',
    // 'static/js/pagesHRATTF-hrattf004-single.42c2356a.js',
    // 'static/js/pagesHRATTF-hrattF001-single.90b8c49f.js',
    // 'static/js/pagesHRATTF-hrattF001-query.e94b2b0a.js',
    // 'static/js/pagesHRATTF-hrattF001-list.0d200dd3.js',
    // 'static/js/pagesHRATTF-hrattF001-layer-picker.d915237d.js',
    // 'static/js/pages-home-more.47353bdf.js',
    // 'static/js/pages-home~pages-home-more~pages-workflow-releasetask-list.e6c7432f.js',
    // 'static/js/pages-home.edd15cf8.js',
    // 'static/js/pagesDemo-clock-setting.bfbe1543.js',
    // 'static/js/pagesDemo-clock-selectTime.b9e2a92c.js',
    // 'static/js/pagesDemo-clock-selectScope.5899ecad.js',
    // 'static/js/pagesDemo-clock-index.e61178b2.js',
    // 'static/js/pagesDemo-clock-addSetting.4746fde4.js',
    // 'static/js/pages-customapp-single.56ccdb86.js',
    // 'static/js/pages-customapp-query.753f7930.js',
    // 'static/js/pages-customapp-list~pages-customapp-query~pages-customapp-single~pages-password-dialog~pages-passwo~670fbffb.076e62d5.js',
    // 'static/js/pages-customapp-list~pages-customapp-query~pages-customapp-single~pages-login~pages-my~pages-passwor~715f1d38.009d442e.js',
    // 'static/js/pages-customapp-list.1d228917.js',
    // 'static/js/pages-contact.d77b0fb6.js',
    // 'static/js/pages-common-learun-user-picker~pages-contact.0194acef.js',
    // 'static/js/pages-common-learun-user-picker.040c4c69.js',
    // 'static/js/pages-common-learun-layer-picker~pages-common-learun-user-picker~pages-contact~pages-home~pages-my~p~b7e57fe4.f378b4d7.js',
    // 'static/js/pages-common-learun-layer-picker~pages-common-learun-user-picker~pages-contact~pages-customapp-list~~c0a3ce63.d91c6add.js',
    // 'static/js/pages-common-learun-layer-picker.911645d4.js',
    // 'static/js/pages-common-learun-icon-picker~pages-common-learun-user-picker~pages-contact~pages-customapp-list~p~bef23a5d.04d89831.js',
    // 'static/js/pages-common-learun-icon-picker.fd92d73c.js',
    // 'static/js/pages-appLogin.39405e0c.js',
    // 'static/js/index.10580524.js',
    // 'static/js/chunk-vendors.927627d0.js',
    // 'uni_modules/qiun-data-charts',
    // 'uni_modules/qiun-data-charts/static',
    // 'uni_modules/qiun-data-charts/static/h5',
    // 'uni_modules/qiun-data-charts/static/h5/echarts.min.js',
];
// Service Worker 安装时，将指定的文件列表缓存起来，以便在后续的网络请求中使用
// self.addEventListener('install', function (event) {
//     // 安装Service Worker时，缓存文件
//     event.waitUntil(
//         caches.open(CACHE_NAME).then(function (cache) {
//             return cache.addAll(urlsToCache);
//         })
//     );
// });
// // Service Worker 接收到网络请求时，尝试从缓存中获取资源，如果缓存中没有，则向服务器发起请求，
// self.addEventListener('fetch', function (event) {
//     // 拦截网络请求，尝试从缓存中获取资源
//     event.respondWith(
//         caches.match(event.request).then(function (response) {
//             if (response) {
//                 return response;
//             }
//             return fetch(event.request);
//         })
//     );
// });
