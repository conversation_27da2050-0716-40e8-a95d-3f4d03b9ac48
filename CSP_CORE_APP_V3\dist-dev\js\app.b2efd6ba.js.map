{"version": 3, "file": "js/app.b2efd6ba.js", "mappings": "0FACC,SAIC,cAAMA,CAASC,GACd,MAAMC,EAAOC,OAAOC,SAASF,KAC7B,GAAIA,EAAM,CACT,MAAMG,EAAQH,EAAKI,MAAM,UACzB,GAAa,MAATD,GAAiBA,EAAME,OAAS,EAAG,CACtC,MAAMC,EAAOH,EAAM,GACnBI,KAAKC,WAAW,iBAAkBF,EACnC,CACD,OAGML,OAAOQ,iBAAiB,uBAAwBC,IAIrD,IAAIC,EAAiBD,EACrBH,KAAKC,WAAW,oBAAqBG,GACrCJ,KAAKC,WAAW,kBAAkB,GAElC,IAAII,EAAQC,kBAERC,EAAcF,EAAMA,EAAMP,OAAS,GAEP,MAA5BS,EAAYC,cACfD,EAAYC,cACb,IAO+B,YAA5BC,aAAaC,YAChBD,aAAaE,oBAGd,IAAIC,EAAQZ,KAAKa,WAAW,SACf,MAATD,IACHA,EAAQE,aAAaC,QAAQ,UAE9B,IAAIC,EAAS,GAAGhB,KAAKiB,4BAEjB,kBAAmBC,WActBA,UAAUC,eAAeC,SAAS,wCAAyC,IAAIC,MAAOC,eACpFC,MAAKC,IACL,MAAMC,EAAKD,EAAIE,YAAcF,EAAIG,SAAWH,EAAII,OAChDH,EAAGI,YAAY,CACdb,OAAQA,EACRJ,MAAOA,GACP,IAEDkB,OAAM,IAAMC,QAAQC,MAAM,yBA4C7B,MAAMC,EAAW,IAAMzC,EAAM0C,KAGxB,CAAC,eAAgB,cAAe,gBAAiB,iBAAkB,mCACtE,qCAAsC,oBAAqB,kBAAmB,sBAC7EC,SAASF,IACXjC,KAAKoC,WAAU,KACdpC,KAAKqC,YAAY,cAAa,UAM1BrC,KAAKsC,wBACLtC,KAAKuC,kBAKX,MAAMC,EAAgBC,IAAIC,mBAC1BF,EAAcG,eAAc,KAC3B3C,KAAK4C,eACL5C,KAAK6C,QAAQ,OAAQ,sBAAsB,GAAMtB,MAAKuB,IACjDA,GACHN,EAAcO,aACf,GACA,GAGH,GC9I0H,I,ICAxHC,EAAQC,E,UAQRC,GAAY,OACd,EACAF,EACAC,GACA,EACA,KACA,KACA,MAIF,QAAeC,EAAiB,QCbhC,WAAc,SACdT,IAAIU,QAAU,CACbC,KAAM,CACLC,kBAAmB,CAAC,IAGtB,KAAIC,MAAM,cAEV,KAAIC,OAAOC,eAAgB,EAM3B,IAAI,KAAI,CACPR,OAAQS,GAAKA,EAAEC,KACbC,OAAO,QACV,IAAI,KAAI,IACJD,EACHE,OAAQ,QACND,Q,iBC1BH,IAAIE,EAAM,CACT,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,KACb,eAAgB,KAChB,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,QAAS,KACT,aAAc,KACd,gBAAiB,KACjB,WAAY,KACZ,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,YAAa,KACb,eAAgB,KAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,KAIf,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOG,EAAoBF,EAC5B,CACA,SAASC,EAAsBF,GAC9B,IAAIG,EAAoBC,EAAEN,EAAKE,GAAM,CACpC,IAAIK,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAErE,KAAO,mBACHqE,CACP,CACA,OAAOP,EAAIE,EACZ,CACAD,EAAeQ,KAAO,WACrB,OAAOC,OAAOD,KAAKT,EACpB,EACAC,EAAeU,QAAUP,EACzBQ,EAAOC,QAAUZ,EACjBA,EAAeE,GAAK,I,GClRhBW,EAA2B,CAAC,EAGhC,SAAST,EAAoBU,GAE5B,IAAIC,EAAeF,EAAyBC,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaH,QAGrB,IAAID,EAASE,EAAyBC,GAAY,CACjDZ,GAAIY,EACJG,QAAQ,EACRL,QAAS,CAAC,GAUX,OANAM,EAAoBJ,GAAUK,KAAKR,EAAOC,QAASD,EAAQA,EAAOC,QAASR,GAG3EO,EAAOM,QAAS,EAGTN,EAAOC,OACf,CAGAR,EAAoBgB,EAAIF,E,MC5BxB,IAAIG,EAAW,GACfjB,EAAoBkB,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAASrF,OAAQ6F,IAAK,CAGzC,IAFA,IAAKL,EAAUC,EAAIC,GAAYL,EAASQ,GACpCC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAASxF,OAAQ+F,MACpB,EAAXL,GAAsBC,GAAgBD,IAAajB,OAAOD,KAAKJ,EAAoBkB,GAAGU,OAAOC,GAAS7B,EAAoBkB,EAAEW,GAAKT,EAASO,MAC9IP,EAASU,OAAOH,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbT,EAASa,OAAOL,IAAK,GACrB,IAAIM,EAAIV,SACET,IAANmB,IAAiBZ,EAASY,EAC/B,CACD,CACA,OAAOZ,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAASrF,OAAQ6F,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,EAqBjB,C,WCzBdtB,EAAoBgC,EAAKzB,IACxB,IAAI0B,EAAS1B,GAAUA,EAAO2B,WAC7B,IAAO3B,EAAO,WACd,IAAM,EAEP,OADAP,EAAoBmC,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,C,WCLdjC,EAAoBmC,EAAI,CAAC3B,EAAS6B,KACjC,IAAI,IAAIR,KAAOQ,EACXrC,EAAoBC,EAAEoC,EAAYR,KAAS7B,EAAoBC,EAAEO,EAASqB,IAC5ExB,OAAOiC,eAAe9B,EAASqB,EAAK,CAAEU,YAAY,EAAMC,IAAKH,EAAWR,IAE1E,C,WCND7B,EAAoByC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO5G,MAAQ,IAAI6G,SAAS,cAAb,EAChB,CAAE,MAAOzC,GACR,GAAsB,kBAAX1E,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,WCAxBwE,EAAoBC,EAAI,CAAC2C,EAAKC,IAAUxC,OAAOyC,UAAUC,eAAehC,KAAK6B,EAAKC,E,WCClF7C,EAAoB+B,EAAKvB,IACH,qBAAXwC,QAA0BA,OAAOC,aAC1C5C,OAAOiC,eAAe9B,EAASwC,OAAOC,YAAa,CAAEC,MAAO,WAE7D7C,OAAOiC,eAAe9B,EAAS,aAAc,CAAE0C,OAAO,GAAO,C,WCL9DlD,EAAoBmD,IAAO5C,IAC1BA,EAAO6C,MAAQ,GACV7C,EAAO8C,WAAU9C,EAAO8C,SAAW,IACjC9C,E,WCER,IAAI+C,EAAkB,CACrB,IAAK,GAaNtD,EAAoBkB,EAAES,EAAK4B,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4BvE,KACvD,IAGIwB,EAAU6C,GAHTnC,EAAUsC,EAAaC,GAAWzE,EAGhBuC,EAAI,EAC3B,GAAGL,EAASwC,MAAM9D,GAAgC,IAAxBwD,EAAgBxD,KAAa,CACtD,IAAIY,KAAYgD,EACZ1D,EAAoBC,EAAEyD,EAAahD,KACrCV,EAAoBgB,EAAEN,GAAYgD,EAAYhD,IAGhD,GAAGiD,EAAS,IAAIxC,EAASwC,EAAQ3D,EAClC,CAEA,IADGyD,GAA4BA,EAA2BvE,GACrDuC,EAAIL,EAASxF,OAAQ6F,IACzB8B,EAAUnC,EAASK,GAChBzB,EAAoBC,EAAEqD,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOvD,EAAoBkB,EAAEC,EAAO,EAGjC0C,EAAqBC,KAAK,gBAAkBA,KAAK,iBAAmB,GACxED,EAAmBE,QAAQP,EAAqBQ,KAAK,KAAM,IAC3DH,EAAmBI,KAAOT,EAAqBQ,KAAK,KAAMH,EAAmBI,KAAKD,KAAKH,G,KC7CvF,IAAIK,EAAsBlE,EAAoBkB,OAAEN,EAAW,CAAC,MAAM,IAAOZ,EAAoB,QAC7FkE,EAAsBlE,EAAoBkB,EAAEgD,E", "sources": ["webpack:///src/App.vue", "webpack:///./src/App.vue?6a4a", "webpack:///./src/App.vue", "webpack:///./src/main.js", "webpack:///./node_modules/moment/locale/ sync ^\\.\\/.*$", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/chunk loaded", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/global", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///webpack/runtime/node module decorator", "webpack:///webpack/runtime/jsonp chunk loading", "webpack:///webpack/startup"], "sourcesContent": ["<script>\r\n\texport default {\r\n\t\t// 小程序：onLaunch 仅启动时调用一次\r\n\t\t// H5/App：onLaunch 打开网页/用户点刷新/代码热更新时均会调用；\r\n\t\t// 考虑到用户刷新网页时会丢失全局数据、页面栈、页面数据等，因此直接跳回首页即可\r\n\t\tasync onLaunch(param) {\r\n\t\t\tconst href = window.location.href\r\n\t\t\tif (href) {\r\n\t\t\t\tconst codes = href.split(\"#code=\")\r\n\t\t\t\tif (codes != null && codes.length > 1) {\r\n\t\t\t\t\tconst code = codes[1]\r\n\t\t\t\t\tthis.SET_GLOBAL(\"azureTokenCode\", code)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// PWA 阻止默认弹出安装事件\r\n\t\t\tawait window.addEventListener('beforeinstallprompt', (event) => {\r\n\t\t\t\t// 阻止默认行为\r\n\t\t\t\t// event.preventDefault()\r\n\t\t\t\t// 保存事件，稍后触发安装\r\n\t\t\t\tlet deferredPrompt = event\r\n\t\t\t\tthis.SET_GLOBAL(\"PWAdeferredPrompt\", deferredPrompt)\r\n\t\t\t\tthis.SET_GLOBAL(\"showPWAInstall\", true)\r\n\t\t\t\t// 获取当前页面栈\r\n\t\t\t\tlet pages = getCurrentPages();\r\n\t\t\t\t// 获取数组中最后一个，即当前页面\r\n\t\t\t\tlet currentPage = pages[pages.length - 1];\r\n\t\t\t\t// 调用页面的方法\r\n\t\t\t\tif (currentPage.onPWAInstall != null) {\r\n\t\t\t\t\tcurrentPage.onPWAInstall();\r\n\t\t\t\t}\r\n\t\t\t\t// 显示自定义安装按钮\r\n\t\t\t\t// document.getElementById('installBtn').style.display = 'block'\r\n\t\t\t})\r\n\r\n\t\t\t// 创建Web Worker\t\r\n\t\t\t// if (window.Worker) {\r\n\t\t\tif (Notification.permission !== 'granted') {\r\n\t\t\t\tNotification.requestPermission()\r\n\t\t\t}\r\n\r\n\t\t\tlet token = this.GET_GLOBAL('token');\r\n\t\t\tif (token == null) {\r\n\t\t\t\ttoken = localStorage.getItem('token')\r\n\t\t\t}\r\n\t\t\tlet apiUrl = `${this.API}/message/msg/list/big/`\r\n\t\t\t// this.worker = new Worker('/csp_core_app/static/worker/sw.js'); // 修改为worker.js的实际路径\r\n\t\t\tif ('serviceWorker' in navigator) {\r\n\t\t\t\t//  navigator.serviceWorker?.register('/csp_core_app/static/worker/sw.js?v=' + new Date().toISOString())\r\n\t\t\t\t// .then(function(registration) {\r\n\t\t\t\t//     const sw = reg.installing || reg.waiting || reg.active\r\n\t\t\t\t// \t// 向service worker发送消息\r\n\t\t\t\t// \tsw.postMessage({\r\n\t\t\t\t// \t\tapiUrl: apiUrl, // 需要调用的接口URL\r\n\t\t\t\t// \t\ttoken: token\r\n\t\t\t\t// \t})\r\n\t\t\t\t// \tconsole.log('Service Worker registered with scope:', registration.scope);\r\n\t\t\t\t//  }).catch(function(error) {\r\n\t\t\t\t//      console.log('Service Worker registration failed:', error);\r\n\t\t\t\t//  });\r\n\r\n\t\t\t\tnavigator.serviceWorker?.register('/csp_core_app/static/worker/sw.js?v=' + new Date().toISOString())\r\n\t\t\t\t\t.then(reg => {\r\n\t\t\t\t\t\tconst sw = reg.installing || reg.waiting || reg.active\r\n\t\t\t\t\t\tsw.postMessage({\r\n\t\t\t\t\t\t\tapiUrl: apiUrl,\r\n\t\t\t\t\t\t\ttoken: token\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(() => console.error('registration failed'))\r\n\r\n\t\t\t\t// await navigator.serviceWorker.register(\"/csp_core_app/static/worker/sw.js?v=\" + new Date().toISOString());\r\n\t\t\t\t// console.log(53,navigator.serviceWorker)\r\n\t\t\t\t// navigator.serviceWorker.ready.then((registration) => {\r\n\t\t\t\t// \tconsole.log(54, \"app\")\r\n\t\t\t\t//   registration.active.postMessage(\r\n\t\t\t\t//     \"Test message sent immediately after creation\",\r\n\t\t\t\t//   );\r\n\t\t\t\t// });\r\n\r\n\t\t\t\t// console.log(this.worker.onmessage)\r\n\t\t\t\t// 监听来自Worker的消息\r\n\t\t\t\t// navigator.serviceWorker.controller.postMessage(\"123456\")\r\n\t\t\t\t// this.worker.onmessage = (event) => {\r\n\t\t\t\t// \tthis.apiData = event.data;\r\n\t\t\t\t// \tconsole.log('Received data from worker:', this.apiData);\r\n\t\t\t\t// \tvar notification = new Notification('【提醒】', {\r\n\t\t\t\t// \t\tbody: this.apiData,\r\n\t\t\t\t// \t\tdir: 'rtl', // 文字方向\r\n\t\t\t\t// \t\ticon: '/static/icons/icon.png' ,// 通知图标\r\n\t\t\t\t// \t});\r\n\t\t\t\t// \tconsole.log(47)\r\n\t\t\t\t// };\r\n\r\n\t\t\t\t// 向Worker发送启动消息，包含定时器间隔和接口URL\r\n\t\t\t\t// this.worker.postMessage({\r\n\t\t\t\t// \t//interval: 60000, // 定时器间隔，单位为毫秒\r\n\t\t\t\t// \tapiUrl: apiUrl, // 需要调用的接口URL\r\n\t\t\t\t// \ttoken: token\r\n\t\t\t\t// });\r\n\t\t\t\t// this.worker.then(function(registration) {\r\n\t\t\t\t//        console.log('Service Worker registered with scope:', registration.scope);\r\n\t\t\t\t//    }).catch(function(error) {\r\n\t\t\t\t//        console.log('Service Worker registration failed:', error);\r\n\t\t\t\t//    });\r\n\t\t\t} else {\r\n\t\t\t}\r\n\t\t\t// } else {\r\n\t\t\t// \tconsole.error('Your browser does not support Web Workers.');\r\n\t\t\t// }\r\n\r\n\t\t\t// #ifdef H5 || APP-VUE\r\n\t\t\t// H5 刷新时获取当前页面路径\r\n\t\t\tconst pagePath = '/' + param.path\r\n\t\t\t// console.log(14,param,pagePath)\r\n\t\t\t// 如果 H5 刷新后访问的不是首页/登录页/注册页，直接跳转回首页\r\n\t\t\tif (!['/pages/login', '/pages/home', '/pages/signup', '/pages/wxlogin', '/pages/workflow/releasetask/list',\r\n\t\t\t\t\t'/pages/workflow/releasetask/single', '/pages/navToPages', '/pages/appLogin', '/pages/wxloginTest'\r\n\t\t\t\t].includes(pagePath)) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.RELAUNCH_TO('/pages/home')\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\r\n\t\t\t// 加载语言类型\r\n\t\t\tawait this.FETCH_LANG_TYPE()\r\n\t\t\tawait this.FETCH_LANG_DATA()\r\n\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t// 小程序端，处理更新\r\n\t\t\t// 目前只有微信小程序有更新管理器 getUpdateManager\r\n\t\t\tconst updateManager = uni.getUpdateManager()\r\n\t\t\tupdateManager.onUpdateReady(() => {\r\n\t\t\t\tthis.HIDE_LOADING()\r\n\t\t\t\tthis.CONFIRM('更新提示', '小程序新版本已准备好，是否更新应用？', true).then(confirm => {\r\n\t\t\t\t\tif (confirm) {\r\n\t\t\t\t\t\tupdateManager.applyUpdate()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t@import '@/uni_modules/uni-scss';\r\n\t@import \"@/components/learun-mpui/styles/index.scss\";\r\n\t@import \"@/common/style/index.scss\";\r\n</style>", "import mod from \"-!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "var render, staticRenderFns\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=78d61762&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\r\nimport moment from 'moment'\r\nimport App from '@/App.vue'\r\n//import store from '@/common/store.js'\r\nimport mixins from '@/common/mixins/index.js'\r\n\r\nmoment.locale('zh-cn')\r\nuni.$learun = {\r\n\tdata: {\r\n\t\tlearun_datasource: {}\r\n\t}\r\n}\r\nVue.mixin(mixins)\r\n//Vue.config.productionTip = process.env.NODE_ENV === 'development'\r\nVue.config.productionTip = process.env.NODE_ENV === 'prd'\r\n//Vue.prototype.$store = store\r\n\r\n\r\n\r\n//new Vue({ ...App, mpType: 'app', store }).$mount()\r\nnew Vue({\r\n\trender: h => h(App),\r\n}).$mount('#app');\r\nnew Vue({\r\n\t...App,\r\n\tmpType: 'app'\r\n}).$mount()", "var map = {\n\t\"./af\": 5177,\n\t\"./af.js\": 5177,\n\t\"./ar\": 1509,\n\t\"./ar-dz\": 1488,\n\t\"./ar-dz.js\": 1488,\n\t\"./ar-kw\": 8676,\n\t\"./ar-kw.js\": 8676,\n\t\"./ar-ly\": 2353,\n\t\"./ar-ly.js\": 2353,\n\t\"./ar-ma\": 4496,\n\t\"./ar-ma.js\": 4496,\n\t\"./ar-sa\": 2682,\n\t\"./ar-sa.js\": 2682,\n\t\"./ar-tn\": 9756,\n\t\"./ar-tn.js\": 9756,\n\t\"./ar.js\": 1509,\n\t\"./az\": 5533,\n\t\"./az.js\": 5533,\n\t\"./be\": 8959,\n\t\"./be.js\": 8959,\n\t\"./bg\": 7777,\n\t\"./bg.js\": 7777,\n\t\"./bm\": 4903,\n\t\"./bm.js\": 4903,\n\t\"./bn\": 1290,\n\t\"./bn.js\": 1290,\n\t\"./bo\": 1545,\n\t\"./bo.js\": 1545,\n\t\"./br\": 1470,\n\t\"./br.js\": 1470,\n\t\"./bs\": 4429,\n\t\"./bs.js\": 4429,\n\t\"./ca\": 7306,\n\t\"./ca.js\": 7306,\n\t\"./cs\": 6464,\n\t\"./cs.js\": 6464,\n\t\"./cv\": 3635,\n\t\"./cv.js\": 3635,\n\t\"./cy\": 4226,\n\t\"./cy.js\": 4226,\n\t\"./da\": 3601,\n\t\"./da.js\": 3601,\n\t\"./de\": 7853,\n\t\"./de-at\": 6111,\n\t\"./de-at.js\": 6111,\n\t\"./de-ch\": 4697,\n\t\"./de-ch.js\": 4697,\n\t\"./de.js\": 7853,\n\t\"./dv\": 708,\n\t\"./dv.js\": 708,\n\t\"./el\": 4691,\n\t\"./el.js\": 4691,\n\t\"./en-SG\": 2748,\n\t\"./en-SG.js\": 2748,\n\t\"./en-au\": 3872,\n\t\"./en-au.js\": 3872,\n\t\"./en-ca\": 8298,\n\t\"./en-ca.js\": 8298,\n\t\"./en-gb\": 6195,\n\t\"./en-gb.js\": 6195,\n\t\"./en-ie\": 6584,\n\t\"./en-ie.js\": 6584,\n\t\"./en-il\": 5543,\n\t\"./en-il.js\": 5543,\n\t\"./en-nz\": 9402,\n\t\"./en-nz.js\": 9402,\n\t\"./eo\": 2934,\n\t\"./eo.js\": 2934,\n\t\"./es\": 7650,\n\t\"./es-do\": 838,\n\t\"./es-do.js\": 838,\n\t\"./es-us\": 6575,\n\t\"./es-us.js\": 6575,\n\t\"./es.js\": 7650,\n\t\"./et\": 3035,\n\t\"./et.js\": 3035,\n\t\"./eu\": 3508,\n\t\"./eu.js\": 3508,\n\t\"./fa\": 119,\n\t\"./fa.js\": 119,\n\t\"./fi\": 527,\n\t\"./fi.js\": 527,\n\t\"./fo\": 2477,\n\t\"./fo.js\": 2477,\n\t\"./fr\": 5498,\n\t\"./fr-ca\": 6435,\n\t\"./fr-ca.js\": 6435,\n\t\"./fr-ch\": 7892,\n\t\"./fr-ch.js\": 7892,\n\t\"./fr.js\": 5498,\n\t\"./fy\": 7071,\n\t\"./fy.js\": 7071,\n\t\"./ga\": 1734,\n\t\"./ga.js\": 1734,\n\t\"./gd\": 217,\n\t\"./gd.js\": 217,\n\t\"./gl\": 7329,\n\t\"./gl.js\": 7329,\n\t\"./gom-latn\": 3383,\n\t\"./gom-latn.js\": 3383,\n\t\"./gu\": 5050,\n\t\"./gu.js\": 5050,\n\t\"./he\": 1713,\n\t\"./he.js\": 1713,\n\t\"./hi\": 3861,\n\t\"./hi.js\": 3861,\n\t\"./hr\": 6308,\n\t\"./hr.js\": 6308,\n\t\"./hu\": 609,\n\t\"./hu.js\": 609,\n\t\"./hy-am\": 7160,\n\t\"./hy-am.js\": 7160,\n\t\"./id\": 4063,\n\t\"./id.js\": 4063,\n\t\"./is\": 9374,\n\t\"./is.js\": 9374,\n\t\"./it\": 8383,\n\t\"./it-ch\": 1827,\n\t\"./it-ch.js\": 1827,\n\t\"./it.js\": 8383,\n\t\"./ja\": 3827,\n\t\"./ja.js\": 3827,\n\t\"./jv\": 9722,\n\t\"./jv.js\": 9722,\n\t\"./ka\": 1794,\n\t\"./ka.js\": 1794,\n\t\"./kk\": 7088,\n\t\"./kk.js\": 7088,\n\t\"./km\": 6870,\n\t\"./km.js\": 6870,\n\t\"./kn\": 4451,\n\t\"./kn.js\": 4451,\n\t\"./ko\": 3164,\n\t\"./ko.js\": 3164,\n\t\"./ku\": 8174,\n\t\"./ku.js\": 8174,\n\t\"./ky\": 8474,\n\t\"./ky.js\": 8474,\n\t\"./lb\": 9680,\n\t\"./lb.js\": 9680,\n\t\"./lo\": 5867,\n\t\"./lo.js\": 5867,\n\t\"./lt\": 5766,\n\t\"./lt.js\": 5766,\n\t\"./lv\": 9532,\n\t\"./lv.js\": 9532,\n\t\"./me\": 8076,\n\t\"./me.js\": 8076,\n\t\"./mi\": 1848,\n\t\"./mi.js\": 1848,\n\t\"./mk\": 306,\n\t\"./mk.js\": 306,\n\t\"./ml\": 3739,\n\t\"./ml.js\": 3739,\n\t\"./mn\": 9053,\n\t\"./mn.js\": 9053,\n\t\"./mr\": 6169,\n\t\"./mr.js\": 6169,\n\t\"./ms\": 3386,\n\t\"./ms-my\": 2297,\n\t\"./ms-my.js\": 2297,\n\t\"./ms.js\": 3386,\n\t\"./mt\": 7075,\n\t\"./mt.js\": 7075,\n\t\"./my\": 2264,\n\t\"./my.js\": 2264,\n\t\"./nb\": 2274,\n\t\"./nb.js\": 2274,\n\t\"./ne\": 8235,\n\t\"./ne.js\": 8235,\n\t\"./nl\": 2572,\n\t\"./nl-be\": 3784,\n\t\"./nl-be.js\": 3784,\n\t\"./nl.js\": 2572,\n\t\"./nn\": 4566,\n\t\"./nn.js\": 4566,\n\t\"./pa-in\": 9849,\n\t\"./pa-in.js\": 9849,\n\t\"./pl\": 4418,\n\t\"./pl.js\": 4418,\n\t\"./pt\": 9834,\n\t\"./pt-br\": 8303,\n\t\"./pt-br.js\": 8303,\n\t\"./pt.js\": 9834,\n\t\"./ro\": 4457,\n\t\"./ro.js\": 4457,\n\t\"./ru\": 2271,\n\t\"./ru.js\": 2271,\n\t\"./sd\": 1221,\n\t\"./sd.js\": 1221,\n\t\"./se\": 3478,\n\t\"./se.js\": 3478,\n\t\"./si\": 7538,\n\t\"./si.js\": 7538,\n\t\"./sk\": 5784,\n\t\"./sk.js\": 5784,\n\t\"./sl\": 6637,\n\t\"./sl.js\": 6637,\n\t\"./sq\": 6794,\n\t\"./sq.js\": 6794,\n\t\"./sr\": 5719,\n\t\"./sr-cyrl\": 3322,\n\t\"./sr-cyrl.js\": 3322,\n\t\"./sr.js\": 5719,\n\t\"./ss\": 6000,\n\t\"./ss.js\": 6000,\n\t\"./sv\": 1011,\n\t\"./sv.js\": 1011,\n\t\"./sw\": 748,\n\t\"./sw.js\": 748,\n\t\"./ta\": 1025,\n\t\"./ta.js\": 1025,\n\t\"./te\": 1885,\n\t\"./te.js\": 1885,\n\t\"./tet\": 8861,\n\t\"./tet.js\": 8861,\n\t\"./tg\": 6571,\n\t\"./tg.js\": 6571,\n\t\"./th\": 5802,\n\t\"./th.js\": 5802,\n\t\"./tl-ph\": 9231,\n\t\"./tl-ph.js\": 9231,\n\t\"./tlh\": 1052,\n\t\"./tlh.js\": 1052,\n\t\"./tr\": 5096,\n\t\"./tr.js\": 5096,\n\t\"./tzl\": 9846,\n\t\"./tzl.js\": 9846,\n\t\"./tzm\": 1765,\n\t\"./tzm-latn\": 7711,\n\t\"./tzm-latn.js\": 7711,\n\t\"./tzm.js\": 1765,\n\t\"./ug-cn\": 8414,\n\t\"./ug-cn.js\": 8414,\n\t\"./uk\": 6618,\n\t\"./uk.js\": 6618,\n\t\"./ur\": 158,\n\t\"./ur.js\": 158,\n\t\"./uz\": 7609,\n\t\"./uz-latn\": 2475,\n\t\"./uz-latn.js\": 2475,\n\t\"./uz.js\": 7609,\n\t\"./vi\": 1135,\n\t\"./vi.js\": 1135,\n\t\"./x-pseudo\": 4051,\n\t\"./x-pseudo.js\": 4051,\n\t\"./yo\": 2218,\n\t\"./yo.js\": 2218,\n\t\"./zh-cn\": 2648,\n\t\"./zh-cn.js\": 2648,\n\t\"./zh-hk\": 1632,\n\t\"./zh-hk.js\": 1632,\n\t\"./zh-tw\": 304,\n\t\"./zh-tw.js\": 304\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 5358;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunk\"] = self[\"webpackChunk\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], () => (__webpack_require__(5002)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["onLaunch", "param", "href", "window", "location", "codes", "split", "length", "code", "this", "SET_GLOBAL", "addEventListener", "event", "deferred<PERSON>rompt", "pages", "getCurrentPages", "currentPage", "onPWAInstall", "Notification", "permission", "requestPermission", "token", "GET_GLOBAL", "localStorage", "getItem", "apiUrl", "API", "navigator", "serviceWorker", "register", "Date", "toISOString", "then", "reg", "sw", "installing", "waiting", "active", "postMessage", "catch", "console", "error", "pagePath", "path", "includes", "$nextTick", "RELAUNCH_TO", "FETCH_LANG_TYPE", "FETCH_LANG_DATA", "updateManager", "uni", "getUpdateManager", "onUpdateReady", "HIDE_LOADING", "CONFIRM", "confirm", "applyUpdate", "render", "staticRenderFns", "component", "$learun", "data", "learun_datasource", "mixin", "config", "productionTip", "h", "App", "$mount", "mpType", "map", "webpackContext", "req", "id", "webpackContextResolve", "__webpack_require__", "o", "e", "Error", "keys", "Object", "resolve", "module", "exports", "__webpack_module_cache__", "moduleId", "cachedModule", "undefined", "loaded", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "every", "key", "splice", "r", "n", "getter", "__esModule", "d", "a", "definition", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "value", "nmd", "paths", "children", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "for<PERSON>ach", "bind", "push", "__webpack_exports__"], "sourceRoot": ""}