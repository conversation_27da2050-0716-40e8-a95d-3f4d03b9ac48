<template>
  <view class="learun-user-fullname">
    <learun-icon
      :type="
        isFull ? 'learun-icon-circle-minus-base' : 'learun-icon-circle-add-base'
      "
      size="16"
      color="#2979ff"
      @click="handleClick"
    ></learun-icon>
    <text>{{ fullName }}</text>
  </view>
</template>

<script>
import customFormMixins from "@/common/customform.js";
export default {
  mixins: [customFormMixins],
  name: "learun-user-picker",
  props: {
    value: {
      default: null,
    },
  },
  watch: {
    value: {
      handler(val) {
        if (!this.value) {
          return;
        }
        this.getUserFullName(this.value, this.isFull);
      },
      immediate: true,
    },
  },
  data() {
    return {
      isFull: false,
      loading: false,
      fullName: "",
    };
  },
  methods: {
    async handleClick() {
      if (this.loading) {
        return;
      }
      if (!this.isFull) {
        this.loading = true;
        this.isFull = true;
        this.loading = false;
      } else {
        this.isFull = false;
      }
      await this.getUserFullName(this.value, this.isFull);
    },
    async getUserFullName(userIdAndPostId, isFull = false) {
      if (!userIdAndPostId) {
        return "";
      }
      const values = userIdAndPostId.split("|");
      const userValue = values[0];
      let postId = values.length > 1 ? values[1] : "";

      const user = await this.loadUser(userValue);
      if (user) {
        let res = user.f_RealName;

        postId = postId || user.f_Post;

        if (postId) {
          const postName = await this.getPostName(postId, "", isFull);
          if (postName) {
            res = `${postName}-${res}`;
          }
        } else {
          if (user.f_DepartmentId) {
            const departmentname = await this.loadDepartmentFullName(
              user.f_DepartmentId
            );
            if (departmentname) {
              res = `${departmentname}-${res}`;
            }
          }
          if (isFull && user.f_CompanyId) {
            const getCompanyList = await this.FETCH_COMPANYS();
            let companyName = "";
            for (let i = 0; i < getCompanyList.length; i++) {
              if (getCompanyList[i].f_CompanyId == user.f_CompanyId) {
                companyName = getCompanyList[i].f_FullName;
                break;
              }
            }
            if (companyName) {
              res = `${companyName}-${res}`;
            }
          }
        }

        this.fullName = res;
      } else {
        this.fullName = " ";
      }
    },
    async getPostName(id, labelKey, isFull) {
      if (this.VALIDATENULL(id)) {
        return "";
      }
      labelKey = labelKey || "f_Name";
      const postList = await this.HTTP_POST({
        url: `/organization/posts`,
        data: { ids: id },
      });
      const post = postList ? postList[0] : undefined;
      if (post) {
        if (labelKey == "f_CompanyId") {
          return `${post.companyName}`;
        } else if (labelKey == "f_Name" && post.deptName) {
          const deptFullName =
            (await this.loadDepartmentFullName(post.f_DepartmentId)) || "";
          if (isFull && deptFullName) {
            return `${post.companyName}-${deptFullName}-${post[labelKey]}`;
          } else {
            return `${post.deptName}-${post[labelKey]}`;
          }
        } else {
          return `${post[labelKey]}`;
        }
      } else {
        return id;
      }
    },
    async loadUser(value) {
      if (!this.VALIDATENULL(value)) {
        const userMap = this.GET_DATA("learun_users_map") || {};
        let userObj = userMap[value];
        if (!userObj) {
          userObj = await this.FETCH_USER(value);
          if (userObj) {
            userMap[userObj.value] = userObj;
            this.SET_DATA("learun_users_map", userMap);
          }
        }
        return userObj;
      }
    },
    async loadUserDepartmentFullName(userIdAndPostId) {
      if (!userIdAndPostId) {
        return;
      }
      const values = userIdAndPostId.split("|");
      const userValue = values[0];
      let postId = values.length > 1 ? values[1] : "";
      const userMap = this.GET_DATA("learun_users_map") || {};
      const user = userMap[userValue];
      if (user) {
        postId = postId || user.f_Post;
        if (postId) {
          const getPost = this.GET_DATA("learun_post_map") || {};
          const post = getPost[postId];
          if (post) {
            await this.loadDepartmentFullName(post.f_DepartmentId);
            return;
          }
        }
        if (user.f_DepartmentId) {
          await this.loadDepartmentFullName(user.f_DepartmentId);
        }
      }
    },
    async loadDepartmentFullName(departmentId) {
      if (!this.VALIDATENULL(departmentId)) {
        const departmentFullNameMap =
          this.GET_DATA("learun_departments_fullname_map") || {};
        let name = departmentFullNameMap[departmentId];
        if (!name) {
          name = await this.HTTP_GET({
            url: `/organization/department/name/${departmentId}`,
          });
          if (name) {
            departmentFullNameMap[departmentId] = name;
            this.SET_DATA(
              "learun_departments_fullname_map",
              departmentFullNameMap
            );
          }
        }
        return name;
      } else {
        return "";
      }
    },
  },
};
</script>
<style>
.learun-user-fullname {
  padding-right: 4px;
  color: #2979ff;
  display: inline-block;
}
</style>