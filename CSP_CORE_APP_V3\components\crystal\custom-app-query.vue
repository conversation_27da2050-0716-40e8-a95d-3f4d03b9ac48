<template>
	<uni-popup style="z-index: 999999999;" ref="popup" type="bottom">
		<view class="page" :style="{'min-height':SCREENHEIGHT()/4*3+'px','background-color':'#fff','padding-bottom':'40px'}">
			<uni-nav-bar fixed statusBar leftIcon="closeempty" @clickLeft="close" :title="$t('筛选条件')"></uni-nav-bar>
			<text :key="testKey"></text>
			<!-- 渲染表单 查询条件 -->
			<uni-forms
				v-if="ready"
				:modelValue="formData" 
				label-position="left"
				:label-width="70"
				ref="myForm">
				<view style="padding:8px;" >
					<view 
						v-for="component in components" 
						:key="component.key"  >
						<learun-customform-item
							:component="component"
							:getDataSource="learun_form_getDataSource" 
							:formId="formId"
							:value="getValue(component.prop)"
							@input="setValue"
							@change="handleChange"
							
							:isEdit="true"
						 >
						 </learun-customform-item>
					</view>
				</view>
			</uni-forms>
			<!--新增按钮-->
			<view class="learun-bottom-btns" v-if="ready">
				<button @click.stop="handleClear" style="flex:1;" >{{$t('清除')}}</button>
				<button @click.stop="handleOk" style="flex:2;"  type="primary" >{{$t('确定')}}</button>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	import set from "lodash/set"
	import get from "lodash/get"
	import customFormMixins from '@/common/customform.js'
	export default {
		mixins: [customFormMixins],
		name:'custom-app-query',
		props: {
			formScheme:{ default: () => [] },
			pageScheme:{ default: () => [] },
			formData:{ default: () => [] },
		},
		data() {
			return {
				ready:false,
				formId:this.GUID(),
				// formData:{},
				components:[],
				testKey:0
			}
		},
		async onLoad() {
			await this.init()
		},
		onBackPress() {
		  this.OFF('learun-customapp-query')
		},
		onUnload() {
		  this.OFF('learun-customapp-query')
		},
		async created() {
			// this.formData = this.queryParams;
			await this.init();
		},
		methods:{
			async init(){
				this.LOADING(this.$t('加载数据中…'))
				this.SET_DATA(`learun_form_data_${this.formId}`,this.formData)
				
				const queryComponents = []
				const componentMap = {}
				
				
				for (let i = 0, len = this.formScheme.formInfo.components.length; i < len; i++) {
					const component = this.formScheme.formInfo.components[i]
					componentMap[component.id] = component
				}
				
				for(const item of this.pageScheme.table.querys){
					const component = this.COPY(componentMap[item.prop])
					component.required = false
					component.disabled = false
					component.readonly = false
					
					if(['password','guid','encode'].includes(component.type)){
						component.type = 'input'
					}
					else if(['texteditor'].includes(component.type)){
						component.type = 'textarea'
					}
					else if(['datetime','time'].includes(component.type)){
						component.dateType =`${component.dateType}range`
					}
					else if(['company'].includes(component.type)){
						component.type =`companySelect`
					}
					else if(['department'].includes(component.type)){
						component.type =`departmentSelect`
					}
					else if(['createuser','modifyuser'].includes(component.type)){
						component.type =`userSelect`
					}
					else if(['createtime','modifytime'].includes(component.type)){
						component.type =`datetimerange`
						component.dateType =`datetimerange`
					}

					if(['userSelect','departmentSelect',`companySelect`,'select'].includes(component.type)){
						component.multiple = true
					}
					
					// 加载选择数据
					await this.learun_form_fetchDataSource(component,{})
					
					// this.$set(this.formData,component.prop,this.formData[component.prop])
					queryComponents.push(component)
					
				}
				
				this.components = queryComponents
				
				this.HIDE_LOADING()
				
				this.ready = true
			},
			// 组件数据值改变
			async handleChange({data,component}){
				if(["checkbox","radio","select","selectMultiple","treeselect","layerselect",'companySelect','departmentSelect','userSelect'].includes(component.type)){
					await this.clearSubValue(component.prop)
				}
				//刷新子组件
				this.testKey = this.GUID();
				// this.$forceUpdate()
			},
			
			async clearSubValue(upProp){
				for (const component of this.components) {
					if (component.upCtrl == upProp) {	
						// 获取数据值
						await this.learun_form_fetchDataSource(component,this.formData)
						component.key = this.GUID()
						this.setValue({path:component.prop,value:undefined})
						await this.clearSubValue(component.prop)
					}
				}
			},
				
			
			// 设置表单数据的方法
			setValue({path, value}) {
				set(this.formData, path, value)
			},
			
			// 获取表单数据的方法
			getValue(path) {
			  return get(this.formData, path)
			},
			
			handleClear(){
				//this.formData = {}
				for(const component of this.components){
					this.$set(this.formData,component.prop,undefined)
				}
				this.SET_DATA(`learun_form_data_${this.formId}`,this.formData)
			},
			handleOk(){
				const formData = this.COPY(this.formData)
				this.EMIT('learun-customapp-query', formData)
				this.close();
			},
			open(value){
				// if(!this.VALIDATENULL(value)){
				// 	const index = this.options.findIndex(t=>t[this.valueKey] == value)
				// 	this.myValue[0] = index
				// }
				// else{
				// 	this.myValue[0] = 0
				// }
				// this.tmpValue = this.myValue[0]
				
				this.$refs.popup.open()
				this.isopen = true
			},
			close(){
				this.isopen = false
				this.$refs.popup.close()
			},
		}
	}
</script>