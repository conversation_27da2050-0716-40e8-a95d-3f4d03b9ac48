<template>
  <view class="customlist-banner-action">
    <u-button
      v-if="showDelete"
      @click="$emit('delete')"
      class="customlist-banner-action-btn"
			type="error"
			size="small"
    >
      {{$t('删除')}}
    </u-button>
    <u-button
      v-if="showEdit"
      @click="$emit('edit')"
      class="customlist-banner-action-btn"
			type="primary"
			size="small"
    >
      {{$t('编辑')}}
    </u-button>
    <u-button
      @click="$emit('view')"
      class="customlist-banner-action-btn"
			type="success"
			size="small"
    >
      {{$t('查看')}}
      
    </u-button>
  </view>
</template>

<script>
export default {
  name: 'learun-customlist-action',

  props: {
    showDelete: {},
    showEdit: {}
  }
}
</script>

<style lang="scss" scoped>
.customlist-banner-action {
  text-align: right;
	display: flex;
	justify-content:flex-end;
	margin-top: 16rpx;
  .customlist-banner-action-btn {
    margin: 0 8rpx;
		width: 80rpx;
  }
}
</style>
