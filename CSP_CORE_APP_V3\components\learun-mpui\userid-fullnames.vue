<template>
<view style="display:flex;align-items:center;min-height: 36px;">
	<view>
        <learun-user-fullname v-for="(userId, i) of userIds"
            :key="i"
            :value="userId">
        </learun-user-fullname>
        
    </view>
</view>
    
</template>

<script>
	import customFormMixins from '@/common/customform.js'
	export default {
		mixins: [customFormMixins],
		name: 'learun-user-picker',
		props: {
			value: {
				default: null
			},
			userIds: {
				type: Array,
				default: () =>[]
			},
			
			disabled: <PERSON><PERSON>an,
		},
	}
</script>