<template>
	<!-- 当页面准备好时显示主容器 -->
	<view v-if="ready" class="page" :style="{
        'min-height':SCREENHEIGHT()+'px',
        'background-color':'#fff',
        'padding-bottom':'40px'
    }">
		<!-- 表单组件，绑定表单数据和验证规则 -->
		<uni-forms ref="form" :modelValue="config" :rules="configRules" label-width="96">
			<!-- 列表组件，用于包裹表单各项 -->
			<uni-list class="learun-forms-list">
				<!-- 考勤组名称表单项 -->
				<uni-list-item>
					<template v-slot:body>
						<uni-forms-item required name="f_Name" :label="$t('考勤组名称')">
							<!-- 输入框，用于输入考勤组名称 -->
							<uni-easyinput :placeholder="$t('请输入')" placeholderStyle="text-align: right;margin-right:4px;"
								:inputBorder="false" v-model="config.f_Name" />
						</uni-forms-item>
					</template>
				</uni-list-item>
				<!-- 参与人员表单项 -->
				<uni-list-item>
					<template v-slot:body>
						<uni-forms-item required name="f_Person" :label="$t('参与人员')">
							<!-- 用户选择器，支持多选 -->
							<learun-user-picker isMulti v-model="config.f_Person" :placeholder="$t('请输入')" />
						</uni-forms-item>
					</template>
				</uni-list-item>
				<!-- 一天班次表单项 -->
				<uni-list-item>
					<template v-slot:body>
						<uni-forms-item required name="f_Count" :label="$t('一天班次')">
							<!-- 选择器，显示一天班次的选项 -->
							<learun-picker :options="options" v-model="config.f_Count" learun-multiple-picker />
						</uni-forms-item>
					</template>
				</uni-list-item>
				<!-- 班次时间表单项 -->
				<uni-list-item>
					<template v-slot:body>
						<uni-forms-item required name="f_Time" :label="$t('班次时间')">
							<!-- 自定义层选择器，用于设置班次时间 -->
							<learun-custom-layer-picker :placeholder="$t('请设置')" :params="{count:config.f_Count}"
								nav="/pagesDemo/clock/selectTime" :getLabel="getTimeLabel" :validate="timeValidate"
								v-model="config.f_Time" />
						</uni-forms-item>
					</template>
				</uni-list-item>
				<!-- 考勤日期表单项 -->
				<uni-list-item>
					<template v-slot:body>
						<uni-forms-item required name="f_Date" :label="$t('考勤日期')">
							<!-- 多选选择器，显示考勤日期的选项 -->
							<learun-multiple-picker :options="dayOptions" v-model="config.f_Date" />
						</uni-forms-item>
					</template>
				</uni-list-item>
				<!-- 考勤范围表单项 -->
				<uni-list-item>
					<template v-slot:body>
						<uni-forms-item required name="f_Scope" :label="$t('考勤范围')">
							<!-- 地图选择器，用于选择考勤范围 -->
							<learun-map-picker v-model="config.f_Scope" />
						</uni-forms-item>
					</template>
				</uni-list-item>
			</uni-list>
		</uni-forms>

		<!-- 底部操作按钮区域 -->
		<view class="learun-bottom-btns">
			<!-- 删除按钮，仅在非创建模式下显示 -->
			<button v-if="mode !== 'create'" @click.stop="handleDelete" type="warn">{{$t('删除')}}</button>
			<!-- 保存按钮，点击触发保存表单操作 -->
			<button @click.stop="saveForm" type="primary">{{$t('保存')}}</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 表单验证规则
				configRules: {
					f_Name: {
						rules: [{
							// 考勤组名称为必填项
							required: true,
							// 验证失败时的错误提示
							errorMessage: this.$t('请输入')
						}]
					},
					f_Person: {
						rules: [{
							// 参与人员为必填项
							required: true,
							// 验证失败时的错误提示
							errorMessage: this.$t('请选择')
						}]
					},
					f_Count: {
						rules: [{
							// 一天班次为必填项
							required: true,
							// 验证失败时的错误提示
							errorMessage: this.$t('请选择')
						}]
					},
					f_Date: {
						rules: [{
							// 考勤日期为必填项
							required: true,
							// 验证失败时的错误提示
							errorMessage: this.$t('请选择')
						}]
					},
					f_Time: {
						rules: [{
							// 班次时间为必填项
							required: true,
							// 验证失败时的错误提示
							errorMessage: this.$t('请选择')
						}]
					},
					f_Scope: {
						rules: [{
							// 考勤范围为必填项
							required: true,
							// 验证失败时的错误提示
							errorMessage:this.$t('请选择')
						}]
					},
				},
				// 考勤日期的选项列表
				dayOptions: [{
					label: this.$t('星期一'),
					value: '1'
				}, {
					label: this.$t('星期二'),
					value: '2'
				}, {
					label: this.$t('星期三'),
					value: '3'
				}, {
					label: this.$t('星期四'),
					value: '4'
				}, {
					label: this.$t('星期五'),
					value: '5'
				}, {
					label: this.$t('星期六'),
					value: '6'
				}, {
					label: this.$t('星期天'),
					value: '0'
				}],
				// 一天班次的选项列表
				options: [{
					value: '1',
					label: this.$t('一天一班')
				}, {
					value: '2',
					label: this.$t('一天两班')
				}, {
					value: '3',
					label: this.$t( '一天三班')
				}],
				// 表单数据对象
				config: {
					f_Name: '',
					f_Person: '',
					f_Count: '',
					f_Date: '',
					f_Time: '',
					f_Scope: '',
				},
				// 页面模式，如创建或编辑
				mode: '',
				// 模块 ID
				moduleId: '',
				// 数据的主键值
				keyValue: '',
				// 页面是否准备好的标志
				ready: false,
				// 是否为弹窗层模式的标志
				isLayer: true,
			}
		},
		onShow() {},
		async onLoad({
			type
		}) {
			uni.setNavigationBarTitle({
				title: this.$t("添加考勤组")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面
				await this.init(type)
			}
		},
		methods: {
			async init(type) {
				// 标记页面未准备好
				this.ready = false
				// 显示加载提示
				this.LOADING(this.$t('加载数据中…'))

				// 设置页面模式
				this.mode = type
				// 获取页面参数
				const {
					title,
					moduleId,
					keyValue
				} = this.GET_PARAM()
				// 设置页面标题
				this.SET_TITLE(title)
				// 保存模块 ID
				this.moduleId = moduleId
				// 保存主键值
				this.keyValue = keyValue

				if (this.mode == 'edit') {
					// 编辑模式下，获取表单数据
					const formData = await this.HTTP_GET({
						url: `/demoApp/apprule/${keyValue}`,
						errorTips: this.$t('获取数据失败')
					})
					// 更新表单数据
					this.config = formData;
				} else {
					// 创建模式下，初始化表单数据
					this.config = {
						f_Name: '',
						f_Person: '',
						f_Count: '',
						f_Date: '',
						f_Time: '',
						f_Scope: '',
					}
				}

				// 隐藏加载提示
				this.HIDE_LOADING()
				// 标记页面准备好
				this.ready = true
			},
			// 班次时间验证函数
			timeValidate(param) {
				if (param.count) {
					return true;
				}
				// 提示用户选择一天班次
				this.TOAST(this.$t('请选择一天班次'), 'error')
				return false;
			},
			// 获取班次时间标签的函数
			getTimeLabel(value) {
				// 替换 | 为空格
				return value.replace(/\|/g, ' ')
			},
			// 表单验证函数
			async validate() {
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.form)
				if (err) {
					return false
				} else {
					return true
				}
			},
			// 获取表单数据的函数
			getForm() {
				// 复制表单数据
				const postData = this.COPY(this.config)
				return postData
			},
			// 保存表单的函数
			async saveForm() {
				// 显示加载提示
				this.LOADING(this.$t('正在提交…'))
				if (await this.validate()) {
					// 表单验证通过，调用保存处理函数
					const res = await this.handleSave({
						keyValue: this.keyValue,
						isEdit: this.mode !== 'create'
					})
					// 隐藏加载提示
					this.HIDE_LOADING()
					if (res) {
						// 保存成功，触发列表更新事件并返回上一页
						this.EMIT(`custom-list-change-${this.moduleId}`)
						this.NAV_BACK()
					}
				} else {
					// 表单验证失败，隐藏加载提示
					this.HIDE_LOADING()
				}
			},
			// 处理保存逻辑的函数
			async handleSave({
				keyValue,
				isEdit
			}) {
				// 获取表单数据
				const postData = this.getForm()
				let res
				if (isEdit) {
					// 编辑模式下，使用 PUT 请求更新数据
					res = await this.HTTP_PUT({
						url: `/demoApp/apprule/${this.keyValue}`,
						data: postData,
						errorTips: this.$t('表单提交保存失败')
					})
				} else {
					// 创建模式下，使用 POST 请求创建数据
					res = await this.HTTP_POST({
						url: `/demoApp/apprule`,
						data: postData,
						errorTips: this.$t('表单提交保存失败')
					})
				}

				return res
			},
			// 处理删除操作的函数
			async handleDelete(row) {
				// 确认是否删除
				if (!(await this.CONFIRM(this.$t('删除项目', '确定要删除该项吗？'), true))) {
					return
				}

				// 显示加载提示
				this.LOADING(this.$t('提交删除中…'))

				// 发送 DELETE 请求删除数据
				const success = await this.HTTP_DELETE({
					url: "/demoApp/apprule/" + this.keyValue,
					errorTips: this.$t('删除失败')
				})
				// 隐藏加载提示
				this.HIDE_LOADING()
				if (success) {
					// 删除成功，提示用户并触发列表更新事件，返回上一页
					this.TOAST(this.$t('删除成功'), 'success')
					this.EMIT(`custom-list-change-${this.moduleId}`)
					this.NAV_BACK()
				}
			},
		}
	}
</script>