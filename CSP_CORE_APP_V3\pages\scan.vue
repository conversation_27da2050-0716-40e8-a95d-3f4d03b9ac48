<template>
	<!-- 页面容器 -->
	<view class="container">
		<!-- 自定义扫码组件，绑定成功和失败回调事件，设置持续扫码和手电筒开关状态 -->
		<mumu-get-qrcode @success="qrcodeSuccess" @error="qrcodeError" :continue="true" :torch="isFlashlightOn">
			<!-- 扫码组件报错时显示的提示内容 -->
			<template v-slot:error>
				<view class="scan-error-tip">{{ $t('摄像头启动失败') }}</view>
			</template>
		</mumu-get-qrcode>
	</view>
</template>

<script>
	// 引入自定义扫码组件
	import mumuGetQrcode from '@/uni_modules/mumu-getQrcode/components/mumu-getQrcode/mumu-getQrcode.vue'
	// 引入 lodash 工具函数
	import {
		keyBy,
		mapValues
	} from 'lodash'

	export default {
		// 注册组件
		components: {
			mumuGetQrcode
		},
		data() {
			return {
				// 手电筒开关状态
				isFlashlightOn: false,
				// 存储获取的流程节点信息
				getProcessNode: null
			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("扫描")
			})
		},
		methods: {
			// 整合老组件的业务逻辑，获取流程列表
			async getProcessList(codeState) {
				try {
					// 并行发送两个请求，一个获取工作流列表，一个获取流程分类数据
					const [list, groupNames] = await Promise.all([
						// 获取工作流列表，过滤出在 App 中显示的项
						this.HTTP_GET({
							url: '/workflow/scheme/mylist',
							errorTips: this.$t('加载数据时出错'),
						}).then(data => data.filter(t => t.f_IsInApp === 1)),
						// 获取流程分类数据，并转换为键值对形式
						this.FETCH_DATAITEM('FlowSort').then(result =>
							mapValues(keyBy(result, 'f_ItemValue'), 'f_ItemName')
						)
					])

					// 查找与扫码结果匹配的流程节点
					const targetNode = list.find(res => res.f_Code === codeState)
					return targetNode || null
				} catch (error) {
					// 显示错误提示
					this.showToast(this.$t('获取流程列表数据出错'), 'none')
					// 打印错误信息
					console.error('获取流程列表数据出错:', error)
					return null
				}
			},

			// 新组件扫码成功回调
			async qrcodeSuccess(data) {
				try {
					// 获取扫码结果
					const scanResult = data
					// 验证扫码结果是否为有效的 URL
					const isValidUrl = /^(https?:\/\/)/.test(scanResult)
					if (!isValidUrl) {
						// 显示错误提示
						this.showToast(this.$t('扫描结果不是有效的 URL'), 'none')
						return
					}

					// 从 URL 中解析出流程标识
					const codeState = this.getStateFromUrl(scanResult)
					if (!codeState) {
						// 显示错误提示
						this.showToast(this.$t('未发现有效的流程标识'), 'none')
						return
					}

					// 获取对应的流程配置
					const processNode = await this.getProcessList(codeState)
					if (!processNode) {
						// 显示错误提示
						this.showToast(this.$t('未找到对应的流程配置'), 'none')
						return
					}

					// 显示加载提示
					this.LOADING(this.$t('前往中…'))
					// 延迟 1.5 秒后跳转到指定页面，并传递相关参数
					setTimeout(() => {
						this.JUMP_TO('./workflow/releasetask/single?type=create', {
							...processNode,
							scanResult: scanResult
						}, true)
					}, 1500)
				} catch (error) {
					// 调用扫码失败处理方法
					this.qrcodeError(error)
				}
			},

			// 老组件的 URL 解析方法，从 URL 中提取流程标识
			getStateFromUrl(url) {
				// 查找 URL 中问号的位置
				const queryIndex = url.indexOf('?')
				if (queryIndex === -1) return null

				// 获取查询字符串
				const queryString = url.slice(queryIndex + 1)
				// 解析查询字符串
				const params = new URLSearchParams(queryString)
				// 获取 state 参数的值
				const stateValue = params.get('state')

				if (stateValue) {
					// 使用正则表达式匹配 create 后面的内容
					const regex = /create(.*)/
					const match = stateValue.match(regex)
					return match?.[1] || stateValue
				}
				return null
			},

			// 扫码失败处理
			qrcodeError(err) {
				// 显示错误提示
				this.showToast(this.$t('扫码失败，请重试'), 'none')
				// 打印错误信息
				console.error('扫码失败:', err)

				// 显示模态框提示用户检查摄像头权限，并在用户确认后返回上一页
				uni.showModal({
					title: this.$t('摄像头授权失败'),
					content: this.$t('请检测当前浏览器是否有摄像头权限'),
					success: () => uni.navigateBack()
				})
			},

			// 通用提示方法，用于显示提示信息
			showToast(title, icon = 'none') {
				uni.showToast({
					title,
					icon,
					duration: 2000
				})
			}
		},

		// 整合老组件的生命周期
		onLoad() {
			// 页面加载时的逻辑，当前为空
		},

		onUnload() {
			// 清理资源，当前为空
		}
	}
</script>

<style scoped lang="scss">
	// .container {
	//     // 设置容器高度为视口高度，背景颜色为黑色
	//     height: 100vh;
	//     background-color: #000;

	//     // 继承老组件的样式
	//     .scan-error-tip {
	//         // 设置提示文字颜色为白色，字体大小为 16px，内边距为 20px，文本居中显示
	//         color: #fff;
	//         font-size: 16px;
	//         padding: 20px;
	//         text-align: center;
	//     }

	//     // 保留必要的样式覆盖
	//     .mumu-get-qrcode {
	//         // 设置扫码组件宽度和高度为 100%
	//         width: 100%;
	//         height: 100%;

	//         // 扫码框样式（根据 UI 调整）
	//         .scan-box {
	//             // 设置扫码框宽度为 80%，最大宽度为 350px，高度为 350px，边框为 2px 绿色实线，圆角为 10px，阴影为绿色
	//             width: 80%;
	//             max-width: 350px;
	//             height: 350px;
	//             border: 2px solid #0f0;
	//             border-radius: 10px;
	//             box-shadow: 0 0 20px #0f0;
	//         }
	//     }
	// }
</style>