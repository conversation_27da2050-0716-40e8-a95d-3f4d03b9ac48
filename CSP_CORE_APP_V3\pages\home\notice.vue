<template>
	<view id="notice" class="page">
		<!-- 主要内容区域，用于显示通知具体内容（当前为空，可能通过HTML转换填充） -->
		<view class="padding text-lg">

		</view>
		<!-- 通知信息区，显示发布时间和日期，右对齐，浅灰色文本 -->
		<view class="padding-sm text-grey notice-info">
			<!-- 发布时间（时分格式） -->
			<view class="text-right">{{$t('本页内容发布于')}} {{ time }}</view>
			<!-- 发布日期（年月日格式） -->
			<view class="text-right">{{ date }}</view>
		</view>
	</view>
</template>

<script>
	import moment from "moment" // 引入时间处理库moment.js
	export default {
		data() {
			return {
				isLayer: true, // 标记是否以层叠方式显示（可能用于控制界面样式或布局）
				ready: false, // 数据加载完成标志（用于控制加载状态显示）
				content: '', // 通知内容（通过HTML转换后存储）
				time: '', // 格式化后的发布时间（时分）
				date: '' // 格式化后的发布日期（年月日）
			}
		},

		async onLoad() {
			// 页面加载时触发，先进行页面启动验证（如登录状态等）
			if (await this.PAGE_LAUNCH()) {
				await this.init() // 调用初始化方法
			}
			await this.init() // 直接调用初始化（可能重复，实际以PAGE_LAUNCH为准）
		},

		methods: {
			async init() {
				this.LOADING('加载中…') // 显示加载进度提示
				const noticeItem = this.GET_PARAM() // 获取路由传递的通知参数（包含标题、内容、时间等）

				// 将通知内容转换为HTML（可能进行安全过滤或格式处理）
				this.content = this.CONVERT_HTML(noticeItem.f_content)

				// 格式化时间为HH:mm格式
				this.time = moment(noticeItem.f_time).format('HH : mm')
				// 格式化日期为YYYY年 M月 D日格式
				this.date = moment(noticeItem.f_time).format('YYYY年 M月 D日')
				// 设置页面标题为通知标题（使用多语言翻译可能在SET_TITLE中处理）
				this.SET_TITLE(noticeItem.f_title)

				this.ready = true // 标记数据加载完成
				this.HIDE_LOADING() // 隐藏加载进度提示
			}
		}
	}
</script>