<template>
	<view>
		<uni-datetime-picker
			v-if="isDatetime"
			:type="type"
			v-model="myValue"
			:clear-icon="clearable"
			
			:disabled="disabled"
			:placeholder="$t(placeholder)"
			:start-placeholder="$t(startPlaceholder)"
			:end-placeholder="$t(endPlaceholder)"
			
			:key="i"
		/>
		
		<learun-year-month-picker 
			v-else-if="type == 'monthrange'"
			:disabled="disabled"
			:startPlaceholder="$t(startPlaceholder)"
			:endPlaceholder="$t(endPlaceholder)"
			:value="myValue"
			@input="handleInput"
		/>
		
		<picker v-else 
			mode="date"
			:fields="type"
			:value="myValue"
			@change="change"
			>
			<view class="learun-input learun-input-border" :class="{'learun-input-placeholder':myValue == -1}" >
				<view class="learun-input__content" ><text>{{$t(myValue)}}</text></view>
				<view class="learun-input-icon-right">
					<uni-icons v-if="VALIDATENULL(myValue)"  type="bottom" size="14" color="#c0c4cc" ></uni-icons>
					<view  v-else  @tap.stop="handleClear" >
						<uni-icons type="clear" size="14" color="#c0c4cc" ></uni-icons>
					</view>
				</view>
			</view>
		</picker>
	</view>
</template>

<script>
	export default {
	  name: 'learun-datetime-picker',
		props:{
			value:{
				type:[String],
				default:''
			},
			type:{
				default:'date'
			},
			clearable:{
				type:Boolean,
				default:true
			},
			format:{
				type:String,
				default:'yyyy-MM-dd'
			},
			disabled:Boolean,
			placeholder:String,
			startPlaceholder:{
				type:String,
				default:'开始日期'
			},
			endPlaceholder:{
				type:String,
				default:'结束日期'
			}
		},
		computed:{
			isDatetime(){	
				return ['date','daterange','date','dateRange'].includes(this.type)
			},
			isRange(){
				return ['daterange','dateRange'].includes(this.type)
			},
			myValue:{
				get(){
					if(this.type == 'monthrange'){
						return this.value
					}
					
					if(this.isRange){
						if(this.VALIDATENULL(this.value)){
							return ["",""];
						}
						else{
							return this.value.split(' - ');
						}
					}
					else{
						if(this.VALIDATENULL(this.value)){
							return this.value
						}
						else{		
							return this.DATEFORMAT(this.value,this.format)
						}
					}
				},
				set(val){
					this.$nextTick(() =>{
						if(this.isRange){
							if(this.VALIDATENULL(val)){
								this.$emit('input', undefined)
								this.$emit('change', undefined)
							}
							else{
								if(this.VALIDATENULL(this.format)){
									this.$emit('input',`${val[0]} - ${val[1]}`)
									this.$emit('change',`${val[0]} - ${val[1]}`)
								}
								else{
									this.$emit('input',`${this.DATEFORMAT(val[0],this.format)} - ${this.DATEFORMAT(val[1],this.format) }`)
									this.$emit('change',`${this.DATEFORMAT(val[0],this.format)} - ${this.DATEFORMAT(val[1],this.format) }`)
								}
							}
						}
						else{
							if(this.VALIDATENULL(val) || this.VALIDATENULL(this.format)){
								this.$emit('input', val)
								this.$emit('change', val)
							}
							else{
								this.$emit('input', this.DATEFORMAT(val,this.format))
								this.$emit('change', this.DATEFORMAT(val,this.format))
							}
						}
						this.i++
					})
				}
			},
		},
		data(){
			return {
				i:0
			}
		},
		methods:{
			change(e){
				this.myValue = e.target.value
			},
			handleInput(e){
				this.$emit('input', e)
				this.$emit('change', e)
			},
			handleClear(){
				this.myValue = ''
			}
		}
	}
</script>

