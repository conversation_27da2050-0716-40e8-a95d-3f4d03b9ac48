﻿<template>
    <!-- 页面视图，设置最小高度、背景颜色和底部内边距 -->
    <view
        class="page"
        :style="{'min-height':SCREENHEIGHT()+'px','background-color':'#fff','padding-bottom':'40px'}"
    >
        <!-- 导航栏组件，固定在顶部，显示关闭图标和标题，点击关闭图标触发 closePage 方法 -->
        <uni-nav-bar
            fixed
            statusBar
            leftIcon="closeempty"
            @clickLeft="closePage"
            title="筛选条件"
        ></uni-nav-bar>
        <!-- 表单组件，当页面准备好时渲染，绑定表单数据 -->
        <uni-forms
            v-if="ready"
            :modelValue="formData"
            label-position="top"
            :label-width="320"
            ref="myForm"
        >
            <!-- 表单内容容器，设置内边距 -->
            <view style="padding:8px;">
                <!-- 循环渲染表单组件 -->
                <view
                    v-for="component in components"
                    :key="component.key"
                >
                    <!-- 自定义表单组件，传入组件配置、数据源获取方法、表单 ID、值，监听输入和变化事件 -->
                    <learun-customform-item
                        :component="component"
                        :getDataSource="learun_form_getDataSource"
                        :formId="formId"
                        :value="getValue(component.prop)"
                        @input="setValue"
                        @change="handleChange"
                        :isEdit="true"
                    >
                    </learun-customform-item>
                </view>
            </view>
        </uni-forms>
        <!-- 底部操作按钮区域，当页面准备好时显示 -->
        <view
            class="learun-bottom-btns"
            v-if="ready"
        >
            <!-- 清除按钮，点击触发 handleClear 方法 -->
            <button
                @click.stop="handleClear"
                style="flex:1;"
            >清除</button>
            <!-- 确定按钮，点击触发 handleOk 方法 -->
            <button
                @click.stop="handleOk"
                style="flex:2;"
                type="primary"
            >确定</button>
        </view>
    </view>
</template>

<script>
// 导入 lodash 的 get 和 set 方法
import get from 'lodash/get'
import set from 'lodash/set'
// 导入自定义表单混入
import customFormMixins from '@/common/customform.js'

export default {
    // 使用混入
    mixins: [customFormMixins],
    data() {
        return {
            // 页面是否准备好的标志
            ready: false,
            // 表单 ID，使用 GUID 生成唯一标识
            formId: this.GUID(),
            // 表单数据
            formData: {},
            // 表单组件配置
            components: [
                {
                    // 输入框组件
                    "type": "input",
                    "label": "单号",
                    "code": "FHIS Leave Note",
                    "prop": "leave_Note_NO"
                },
                {
                    // 下拉选择框组件，支持多选
                    "type": "select",
                    "label": "请假类型",
                    "placeholder": "请选择",
                    "dataType": "3",
                    "dataCode": "FHIS_Leave_Type",
                    "dataValueKey": "leave_type",
                    "dataLabelKey": "leave_description",
                    "upCtrl": "",
                    "upShowAll": false,
                    "prop": "leave_Type",
                    "multiple": true
                },
                {
                    // 日期范围选择组件
                    "type": "datetime",
                    "label": "请假开始日期",
                    "dateType": "daterange",
                    "format": "yyyy-MM-dd",
                    "clearable": true,
                    "placeholder": "选择日期",
                    "prop": "From_DateQRange"
                },
                {
                    // 日期范围选择组件
                    "type": "datetime",
                    "label": "请假结束日期",
                    "dateType": "daterange",
                    "format": "yyyy-MM-dd",
                    "clearable": true,
                    "placeholder": "选择日期",
                    "prop": "To_DateQRange"
                }
            ]
        }
    },
    async onLoad() {
        // 页面加载时初始化
        await this.init()
    },
    onBackPress() {
        // 页面返回时，移除事件监听
        this.OFF('learun-customapp-query')
    },
    onUnload() {
        // 页面卸载时，移除事件监听
        this.OFF('learun-customapp-query')
    },
    methods: {
        async init() {
            // 显示加载提示
            this.LOADING('加载数据中…')
            // 获取页面传递的表单数据
            const {
                formData
            } = this.GET_PARAM()
            // 将表单数据存储到全局数据中
            this.SET_DATA(`learun_form_data_${this.formId}`, this.formData)
            // 遍历表单组件
            for (const component of this.components) {
                // 加载组件的数据源
                await this.learun_form_fetchDataSource(component, formData)
                // 设置表单数据中对应组件的值
                this.$set(this.formData, component.prop, formData[component.prop])
            }
            // 隐藏加载提示
            this.HIDE_LOADING()
            // 标记页面准备好
            this.ready = true
        },
        // 组件数据值改变时的处理方法
        async handleChange({
            data,
            component
        }) {
            // 如果组件类型是复选框、单选框、下拉选择框等
            if (["checkbox", "radio", "select", "selectMultiple", "treeselect", "layerselect", 'companySelect', 'departmentSelect', 'userSelect'].includes(component.type)) {
                // 清空相关子组件的值
                await this.clearSubValue(component.prop)
            }
        },
        // 清空相关子组件的值
        async clearSubValue(upProp) {
            // 遍历表单组件
            for (const component of this.components) {
                // 如果组件的上级控制属性等于传入的属性
                if (component.upCtrl == upProp) {
                    // 加载组件的数据源
                    await this.learun_form_fetchDataSource(component, this.formData)
                    // 生成新的组件 key
                    component.key = this.GUID()
                    // 设置组件的值为 undefined
                    this.setValue({
                        path: component.prop,
                        value: undefined
                    })
                    // 递归清空子组件的值
                    await this.clearSubValue(component.prop)
                }
            }
        },
        // 设置表单数据的方法
        setValue({
            path,
            value
        }) {
            // 使用 lodash 的 set 方法设置表单数据
            set(this.formData, path, value)
        },
        // 获取表单数据的方法
        getValue(path) {
            // 使用 lodash 的 get 方法获取表单数据
            return get(this.formData, path)
        },
        // 关闭页面，返回上一页
        closePage() {
            uni.navigateBack()
        },
        // 清除表单数据
        handleClear() {
            // 清空表单数据
            this.formData = {}
            // 更新全局数据中的表单数据
            this.SET_DATA(`learun_form_data_${this.formId}`, this.formData)
        },
        // 确定筛选条件，返回上一页并传递表单数据
        handleOk() {
            // 复制表单数据
            let formData = this.COPY(this.formData)
            // 打印表单数据
            console.log(171, formData)
            // 触发自定义事件，传递表单数据
            this.EMIT('learun-customapp-query', formData)
            // 返回上一页
            uni.navigateBack()
        }
    }
}
</script>