<template>
	<!-- 页面容器 -->
	<view class="page">
		<!-- 图标展示区域，使用 flex 布局，允许换行 -->
		<view class="learun-flex icon-content">
			<!-- 循环遍历图标列表，渲染每个图标 -->
			<view @click.stop="handleClick(item)" class="icon-content-item" v-for="(item,index) in icons" :key="index">
				<!-- 自定义图标组件，传入图标类型和大小 -->
				<learun-icon :type="item" :size="24"></learun-icon>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 是否以弹窗层形式显示
				isLayer: true,
				// 图标列表，包含大量的图标类型
				icons: [
					"learun-icon-data-access1", "learun-icon-choose-drop-down-box", "learun-icon-choose-date-range",
					"learun-icon-input-editor", "learun-icon-input-multiline-text",
					"learun-icon-choose-nextmap-choose",
					"learun-icon-layout-card-layout", "learun-icon-choose-popup-window", "learun-icon-input-counter",
					"learun-icon-layout-design-child-table", "learun-icon-choose-tree-selection",
					"learun-icon-other-slider",
					"learun-icon-choose-time-range", "learun-icon-choose-date-selection",
					"learun-icon-choose-pull-down-box",
					"learun-icon-choose-single-selection-box", "learun-icon-handle-button",
					"learun-icon-choose-multiple-selection-box",
					"learun-icon-layout-divider", "learun-icon-upload-image-upload", "learun-icon-system-Modify-time",
					"learun-icon-system-guid", "learun-icon-input-password",
					"learun-icon-system-subordinate-companies",
					"learun-icon-system-companies-choose", "learun-icon-other-color-choices", "learun-icon-other-icon",
					"learun-icon-system-create-personnel", "learun-icon-other-on-off",
					"learun-icon-system-personnel-selection",
					"learun-icon-choose-time-selection", "learun-icon-other-score", "learun-icon-system-modify-staff",
					"learun-icon-input-textfield", "learun-icon-upload-file-upload",
					"learun-icon-commissioned-by-the-process",
					"learun-icon-system-creation-time", "learun-icon-view-view-form", "learun-icon-system-department",
					"learun-icon-choose-provinces", "learun-icon-language-type-maintenance",
					"learun-icon-menu-function-translation",
					"learun-icon-process-design", "learun-icon-excel-configuration", "learun-icon-ubrta",
					"learun-icon-data-dictionary", "learun-icon-system-log", "learun-icon-data-sheet-management",
					"learun-icon-task-designing", "learun-icon-process-tasks", "learun-icon-multilingual-management",
					"learun-icon-form-design", "learun-icon-message-the-user", "learun-icon-rights-management",
					"learun-icon-mobile-desktop", "learun-icon-message-strategy", "learun-icon-task-scheduling",
					"learun-icon-job-log", "learun-icon-system-management", "learun-icon-company-management",
					"learun-icon-administrative-areas", "learun-icon-data-access",
					"learun-icon-department-of-management",
					"learun-icon-data-source", "learun-icon-documents-coding", "learun-icon-data-application",
					"learun-icon-data-dictionary-translation", "learun-icon-form-post", "learun-icon-message-center",
					"learun-icon-desktop-design", "learun-icon-database-connection", "learun-icon-online-development",
					"learun-icon-functional-authority", "learun-icon-menu-management",
					"learun-icon-mobile-capabilities",
					"learun-icon-node", "learun-icon-safety", "learun-icon-tick", "learun-icon-ranking",
					"learun-icon-up-arrow", "learun-icon-shopping-bag", "learun-icon-undo", "learun-icon-support",
					"learun-icon-upload", "learun-icon-template", "learun-icon-user", "learun-icon-transfer",
					"learun-icon-video", "learun-icon-qq", "learun-icon-wallet", "learun-icon-store",
					"learun-icon-close-eye", "learun-icon-open-eye", "learun-icon-pie", "learun-icon-repository",
					"learun-icon-magnifying-glass", "learun-icon-subtract", "learun-icon-stars", "learun-icon-sina",
					"learun-icon-want", "learun-icon-integrated", "learun-icon-van", "learun-icon-bluetooth",
					"learun-icon-up", "learun-icon-validatingprofile", "learun-icon-address-m", "learun-icon-ashcan",
					"learun-icon-tag", "learun-icon-projection", "learun-icon-site", "learun-icon-assess-m",
					"learun-icon-append-m", "learun-icon-briefcase-m", "learun-icon-ashcan-m", "learun-icon-call-m",
					"learun-icon-camera-m", "learun-icon-clock-m", "learun-icon-card-m", "learun-icon-alarm",
					"learun-icon-book-m", "learun-icon-close-m", "learun-icon-cloud-m", "learun-icon-circle-m",
					"learun-icon-cloud-upload-m", "learun-icon-collect-m", "learun-icon-cloud-download-m",
					"learun-icon-close-eye-m",
					"learun-icon-alarm-clock-m", "learun-icon-volume", "learun-icon-calendar-m",
					"learun-icon-component-m",
					"learun-icon-address-book-m", "learun-icon-wechat", "learun-icon-cup-m", "learun-icon-alarm-m",
					"learun-icon-scanning", "learun-icon-customize-m", "learun-icon-copy-m", "learun-icon-database-m",
					"learun-icon-down-m", "learun-icon-customize", "learun-icon-down-arrow-m",
					"learun-icon-computer-m",
					"learun-icon-download-m", "learun-icon-add-contacts-m", "learun-icon-coupon-m",
					"learun-icon-emoji-m",
					"learun-icon-explore-m", "learun-icon-certificate-m", "learun-icon-qr", "learun-icon-bluetooth-m",
					"learun-icon-code-m", "learun-icon-document-m", "learun-icon-exit-m", "learun-icon-confirm-m",
					"learun-icon-cooperation-m", "learun-icon-correct-m", "learun-icon-file-m", "learun-icon-flag-m",
					"learun-icon-forwarding-m", "learun-icon-facility-m", "learun-icon-error-m",
					"learun-icon-folder-m",
					"learun-icon-edit-m", "learun-icon-goods-m", "learun-icon-integrated-m", "learun-icon-left-m",
					"learun-icon-document-search-m", "learun-icon-home-m", "learun-icon-mail-m",
					"learun-icon-counter-m",
					"learun-icon-menology-m", "learun-icon-mobile-m", "learun-icon-data-bank-m",
					"learun-icon-microphone-m",
					"learun-icon-left-arrow-m", "learun-icon-member-m", "learun-icon-nav-m", "learun-icon-more-m",
					"learun-icon-histogram-m", "learun-icon-list-m", "learun-icon-open-eye-m", "learun-icon-node-m",
					"learun-icon-magnifying-glass-m", "learun-icon-learn-m", "learun-icon-paperairplane-m",
					"learun-icon-pie-m",
					"learun-icon-gather-m", "learun-icon-gift-m", "learun-icon-money-m", "learun-icon-projection-m",
					"learun-icon-product-m", "learun-icon-read-m", "learun-icon-navigation-m", "learun-icon-pending-m",
					"learun-icon-pic-m", "learun-icon-safety-m", "learun-icon-project-m", "learun-icon-repository-m",
					"learun-icon-laptop-m", "learun-icon-qq-m", "learun-icon-shopping-bag-m", "learun-icon-house-m",
					"learun-icon-position-m", "learun-icon-lock-m", "learun-icon-question-m",
					"learun-icon-right-arrow-m",
					"learun-icon-share-m", "learun-icon-right-m", "learun-icon-support-m", "learun-icon-site-m",
					"learun-icon-skin-m", "learun-icon-stars-m", "learun-icon-mute-m", "learun-icon-paper-m",
					"learun-icon-subtract-m", "learun-icon-tick-m", "learun-icon-plan-m", "learun-icon-store-m",
					"learun-icon-qr-m", "learun-icon-template-m", "learun-icon-internet-m", "learun-icon-refresh-m",
					"learun-icon-scanning-m", "learun-icon-module-m", "learun-icon-up-arrow-m",
					"learun-icon-printer-m",
					"learun-icon-shopping-cart-m", "learun-icon-user-m", "learun-icon-undo-m", "learun-icon-van-m",
					"learun-icon-validating-profile-m", "learun-icon-fingerprint-m", "learun-icon-add",
					"learun-icon-video-m",
					"learun-icon-about", "learun-icon-sina-m", "learun-icon-add-contacts", "learun-icon-volume-m",
					"learun-icon-address", "learun-icon-record-m", "learun-icon-address-book", "learun-icon-tag-m",
					"learun-icon-append", "learun-icon-wallet-m", "learun-icon-alarm-clock", "learun-icon-transfer-m",
					"learun-icon-briefcase", "learun-icon-want-m", "learun-icon-book", "learun-icon-upload-m",
					"learun-icon-assess", "learun-icon-like-m", "learun-icon-calendar", "learun-icon-ranking-m",
					"learun-icon-camera", "learun-icon-up-m", "learun-icon-card", "learun-icon-love-m",
					"learun-icon-certificate", "learun-icon-map-m", "learun-icon-close", "learun-icon-wechat-m",
					"learun-icon-clock", "learun-icon-message-m", "learun-icon-cloud-upload", "learun-icon-mine-m",
					"learun-icon-circle", "learun-icon-about-m", "learun-icon-cloud-download", "learun-icon-call",
					"learun-icon-code", "learun-icon-confirm", "learun-icon-collect", "learun-icon-computer",
					"learun-icon-component", "learun-icon-cooperation", "learun-icon-correct", "learun-icon-coupon",
					"learun-icon-cloud", "learun-icon-copy", "learun-icon-database", "learun-icon-cup",
					"learun-icon-counter", "learun-icon-down", "learun-icon-download", "learun-icon-down-arrow",
					"learun-icon-document", "learun-icon-document-search", "learun-icon-exit", "learun-icon-emoji",
					"learun-icon-data-bank", "learun-icon-file", "learun-icon-explore", "learun-icon-error",
					"learun-icon-edit", "learun-icon-facility", "learun-icon-folder", "learun-icon-gift",
					"learun-icon-forwarding", "learun-icon-flag", "learun-icon-home", "learun-icon-gather",
					"learun-icon-fingerprint", "learun-icon-left", "learun-icon-histogram", "learun-icon-laptop",
					"learun-icon-left-arrow", "learun-icon-mail", "learun-icon-link", "learun-icon-love",
					"learun-icon-learn", "learun-icon-list", "learun-icon-like", "learun-icon-mine",
					"learun-icon-menology", "learun-icon-lock", "learun-icon-internet", "learun-icon-microphone",
					"learun-icon-member", "learun-icon-money", "learun-icon-map", "learun-icon-message",
					"learun-icon-nav", "learun-icon-mobile", "learun-icon-goods", "learun-icon-paper-airplane",
					"learun-icon-more", "learun-icon-module", "learun-icon-pic", "learun-icon-plan",
					"learun-icon-mute", "learun-icon-position", "learun-icon-project", "learun-icon-navigation",
					"learun-icon-pending", "learun-icon-product", "learun-icon-paper", "learun-icon-question",
					"learun-icon-right-arrow", "learun-icon-house", "learun-icon-read", "learun-icon-record",
					"learun-icon-share", "learun-icon-refresh", "learun-icon-printer", "learun-icon-right",
					"learun-icon-skin", "learun-icon-shopping-cart"
				]
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("图标选择")
			})
			// 页面加载时，检查页面是否可以启动
			await this.PAGE_LAUNCH()
		},
		methods: {
			// 图标点击事件处理函数
			handleClick(item) {
				// 触发自定义事件，传递选中的图标类型
				this.EMIT('select-learun-icon', item)
				// 返回上一页
				this.NAV_BACK()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.icon-content {
		// 允许换行
		flex-wrap: wrap;
		// 水平排列
		flex-direction: row;
		// 背景颜色为白色
		background-color: #fff;

		&-item {
			// 弹性布局
			display: flex;
			// 盒模型计算方式
			box-sizing: border-box;
			// 宽度占比 25%
			width: 25%;
			// 垂直居中对齐
			align-items: center;
			// 内边距
			padding: 24px 10px;
			// 文本居中
			text-align: center;
			// 垂直排列
			flex-direction: column;
		}
	}
</style>