<template>
	<uni-popup ref="popup" type="bottom">
		<view class="bg-white" style="width: 100%;" >
			<view class="learun-popup-button__title" >
				<text>{{$t(title)}}</text>
			</view>
			<view class="learun-popup-button__box" @click.stop="handleClick(btn)" v-for="(btn,index) in buttons" :key="index" >
				<button style="flex: 1;font-size: 16px;" :type="btn.type" >{{$t(btn.label)}}</button>
			</view>
			<view class="learun-popup-button__box">
				<button style="flex: 1;font-size: 16px;" @click.stop="handleCancel"  >{{$t("取消")}}</button>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		name:'learun-popup-buttons',
		data(){
			return {
				title:'',
				buttons:[]
			}
		},
		
		methods:{
			open(title,buttons){
				this.title = title || ''
				this.buttons = buttons || []
				this.$refs.popup.open()
			},
			close(){
				this.$refs.popup.close()
			},
			handleCancel(){
				this.$refs.popup.close()
			},
			handleClick(btn){
				this.$emit('click',btn)
			}
		}
	}
</script>

<style>
</style>
