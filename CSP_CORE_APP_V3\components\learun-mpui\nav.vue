<template>
  <scroll-view
		style="z-index: 10;"
    :scroll-left="scrollLeft"
    scroll-with-animation
    scroll-x
  >
    <view class="learun-nav-wraper">
      <view
        @tap="tabSelect(idx)"
        v-for="(item, idx) in items"
        :key="idx"
        :class="value === idx ? 'active' : ''"
        class="learun-nav-item"
      >
        {{ item }}
				<view class="learun-popup-line" ></view>
				<view class="learun-nav-item-move" ></view>
				
      </view>
			
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'learun-nav',
  props: {
    color: { default: 'white' },
    items: { default: () => [] },
    value: {}
  },

  data() {
    return {
      scrollLeft: 0
    }
  },

  methods: {
    tabSelect(idx) {			
      this.scrollLeft = (idx - 1) * 60
      this.$emit('input', idx)
      this.$emit('change', idx)
    }
  }
}
</script>
<style lang="scss" scoped>
	.learun-nav
	{

		
		&-wraper{
			display: flex;
			height: 32px;
			
			position: relative;
			padding-bottom: 2px;
			
			.learun-popup-line{
				position: absolute;
				bottom: -2px;
				left: 0;
				width: 100%;
			}
		}
		&-item{
			flex:1;
			display: flex;
			align-items: center;
			text-align: center;
			justify-content: center;
			white-space: nowrap;
			padding: 0 8px;
			font-size: 14px;
			color: $uni-base-color;
			position: relative;
			height: 100%;
			
			&-move{
				position:absolute;
				width: 100%;
				height: 2px;
				bottom: -2px;
				left: 0;
				background-color: $uni-primary;
				display: none;
			}
			&.active{
				color: $uni-primary;
			}
			&.active &-move{
				display: block;
			}
		}
		
		
	}
</style>
