<template>
	<!-- 登录页面容器，设置最小高度为屏幕高度 -->
	<view class="page login-page" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<view class="content">
			<!-- 头部横幅区域 -->
			<view class="head-banner">
				<!-- logo显示区域，背景图为静态资源logo.png -->
				<view mode="aspectFit" class="logo" style="background-image: url('/csp_core_app/static/logo.png')">
				</view>
				<!-- 等待提示标题 -->
				<view class="title">
					<text>Please Waiting...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 数据区域，当前无本地状态数据
			}
		},
		async onLoad(option) {
			// 接收页面跳转参数，option包含上个页面传递的参数
			let page = option.page; // 提取目标页面参数
			const HOME_URL = "/pages/home"; // 首页路由地址

			// 1. 基础参数验证
			// 1.1 若参数为空或无效，直接跳转至首页
			if (!page || page === 'null' || page === 'undefined') {
				this.RELAUNCH_TO(HOME_URL); // 重新启动应用并跳转
				return
			}

			// 保存导航回退地址到全局变量，用于登录后返回原页面
			this.SET_GLOBAL("NavToPagesUrl", "/pages/navToPages?page=" + page);

			// 1.2 验证登录状态，确保用户已登录
			const stateValid = await this.checkLoginState();
			if (!stateValid) { // 登录状态无效时跳转登录页
				this.RELAUNCH_TO('/pages/login');
				return
			}

			// 登录有效时清空导航回退地址，避免循环跳转
			this.SET_GLOBAL("NavToPagesUrl", null);

			// 2. 参数清洗处理
			// 2.1 转义URL编码字符
			page = page.replace("%26", "&").replace("%3F", "?").replace("%27", "'");
			// 2.2 移除前后可能存在的引号
			if (page.substring(0, 1) == "'") {
				page = page.substring(1, page.length - 1);
			}
			if (page.substring(page.length - 2, page.length - 1) == "'") {
				page = page.substring(0, page.length - 2);
			}

			// 初始化参数存储变量
			var parmas = page.split("/"); // 按路径拆分参数数组
			var goToUrl = page; // 目标跳转URL
			var toUrlParam = null; // 传递给目标页面的参数
			var getDataUrl = null; // 业务数据获取URL

			// 4. 业务场景处理 - 工作流明细页面
			if (parmas[0] == "workflowDetail") {
				// 校验参数完整性，参数不足时跳转首页
				if (parmas.length < 3) {
					this.RELAUNCH_TO(HOME_URL);
					return
				}
				// 定义路径前缀变量
				let task = "task/";
				let mytask = "mytask";
				let type = parmas[1]; // 提取操作类型（audit/again/draft等）
				// 根据不同操作类型调整路径前缀
				if (type == "lookmy") task = "";
				if (type == "draft") {
					task = "draft/";
					mytask = "releasetask";
				}

				// 构建数据获取URL和目标跳转URL
				getDataUrl = 'workflow/process/' + task + parmas[2];
				goToUrl = "/pages/workflow/" + mytask + "/single?type=" + type;

				// 处理待办任务场景（审核/重新处理）
				if (["audit", "again"].includes(type)) {
					// 拉取待办任务列表验证当前任务是否存在
					const result = await this.HTTP_POST({
						url: `/workflow/process/uncompleted/mypage?rows=100&page=1&sidx=f_IsUrge DESC,t.F_CreateDate DESC`,
						params: this.searchData,
						data: {
							keyWord: ""
						},
						errorTips: '加载任务时出错'
					});
					// 检查任务是否存在于待办列表
					let isUnCompleted = false;
					for (let i = 0; i < result.rows.length; i++) {
						if (result.rows[i].f_Id == parmas[2]) isUnCompleted = true;
					}
					// 任务不存在时提示并跳转任务列表
					if (!isUnCompleted) {
						this.RELAUNCH_TO("/pages/workflow/mytask/list");
						return
					}
				}
			}

			// 处理其他业务场景
			if (parmas[0] == "sign") { // 签名场景
				goToUrl = "/pages/workflow/mytask/sign";
				toUrlParam = {
					type: "sign",
					isSign: 1
				}; // 传递签名参数
			} else if (parmas[0] == "signForPC") { // PC端签名场景
				goToUrl = "/pages/sign/signForPC?id=" + option.code; // 携带code参数
			}
			if (page == "create") { // 创建流程场景
				goToUrl = "/pages/workflow/releasetask/single?type=create";
				// 初始化创建流程所需参数
				toUrlParam = {
					f_Code: option.code,
					f_Name: option.code,
					processId: '',
					formData: {},
					isLoadFormData: false
				};
			} else { // 通用模块导航处理
				// 从配置项获取导航映射关系
				const data = await this.FETCH_DATAITEM("APPNavTo");
				const APPNavTo = data.find(item => item.f_ItemValue == page);
				if (APPNavTo) {
					// 匹配模块信息并构建跳转参数
					const modules = await this.HTTP_GET({
						url: '/mapp/modules'
					});
					modules.forEach(item => {
						if (item.f_Name == APPNavTo.f_ItemName) {
							toUrlParam = item;
							goToUrl = `/pages/customapp/list?formId=${item.f_FormVerison}`;
						}
					});
				}
			}

			// 5. 数据获取与校验（针对需要数据的场景）
			var result;
			if (getDataUrl != null) {
				result = await this.HTTP_GET({
					url: getDataUrl
				});
				// 数据获取失败时跳转任务列表
				if (result == null) {
					this.RELAUNCH_TO("/pages/workflow/mytask/list");
					return
				}
			}

			// 特殊处理工作流明细场景的参数
			if (parmas[0] == "workflowDetail") {
				toUrlParam = result.task; // 提取任务数据
				// 草稿/查看场景使用流程数据
				if (parmas[1] == "draft" || parmas[1] == "lookmy") {
					toUrlParam = result.process;
				}
			}

			// 6. 带参数跳转至目标页面（携带toUrlParam参数）
			this.JUMP_TO(goToUrl, toUrlParam, true); // true表示关闭当前页面
		},

		methods: {
			// 页面初始化方法（当前无实际逻辑）
			async init() {
				this.ready = true // 标记页面准备完成
			},
			// 登录状态验证方法
			async checkLoginState() {
				// 检查token有效性
				const token = this.GET_GLOBAL('token') || this.GET_STORAGE('token');
				if (!token || token === 'null' || token === 'undefined') {
					this.HIDE_LOADING();
					return false; // token无效时返回false
				}
				this.SET_GLOBAL('token', token); // 同步token到全局变量

				// 检查是否已获取用户信息
				if (this.GET_GLOBAL('loginUser')) return true; // 已有用户信息则登录有效

				// 拉取用户信息验证登录态
				const success = await this.FETCH_CURRENT_USERINFO();
				if (!success) { // 用户信息获取失败时清除登录态
					this.TOAST('获取用户/部门/公司信息时出现错误，登录失败');
					this.SET_GLOBAL('token', null);
					this.SET_STORAGE('token', null);
					return false;
				}
				return true; // 所有验证通过则登录有效
			},
		}
	}
</script>

<style lang="scss" scoped>
	.login-page {
		/* 弹性布局，垂直居中内容 */
		display: flex;
		justify-content: center;
		flex-direction: column;

		.head-banner {
			margin-bottom: 32px; // 底部边距

			.title {
				/* 标题样式 */
				display: block;
				margin: 8px 0;
				font-size: 10px;
				margin-bottom: 16px;
				color: $uni-main-color; // 使用uni-ui主色
			}

			.logo {
				/* logo样式，背景图自适应 */
				background-size: contain;
				height: 36px;
				width: 50px;
				text-align: center;
				display: inline-block;
				border-radius: 2px;
			}
		}

		.content {
			/* 内容区域样式，水平居中，内边距 */
			text-align: center;
			width: 100%;
			padding: 0 24px;
			box-sizing: border-box;
		}
	}
</style>