<template>
  <view class="learun-chat">
    <slot></slot>
    <view v-if="empty" class="padding text-gray text-center">{{ $t(emptyTips) }}</view>
    <view v-if="!empty && nomore" class="padding text-gray text-center">{{ $t(nomoreTips) }}</view>
  </view>
</template>

<script>
export default {
  name: 'learun-chat-wraper',

  props: {
    empty: {},
    emptyTips: { default: '暂无消息' },
    nomore: { default: true },
    nomoreTips: { default: '没有更多消息了' }
  }
}
</script>

<style lang="scss" >
	.learun-chat {
	  display: flex;
	  flex-direction: column;
	}
</style>
