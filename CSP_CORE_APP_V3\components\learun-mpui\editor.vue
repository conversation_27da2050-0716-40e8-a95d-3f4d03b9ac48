<template>
	<view class="learun-editor">
        <editor id="editor" class="ql-container" :placeholder="$t('开始输入...')"
            show-img-resize @statuschange="onStatusChange" :read-only="true" @ready="onEditorReady">
        </editor>
		<button v-if="!disabled" type="primary" size="mini" style="width: 70px;margin-right: 0px;margin-top: 8px;" @click="handleClick">{{$t('编辑')}}</button>
	</view>
</template>


<script>
	export default {
		name:'learun-editor',
		props:{
			text: { default: '' },
            disabled:Boolean,
            value: String
		},
        data() {
			return {
				readOnly: false,
				formats: {},
			}
		},
		onLoad() {
			
		},
		computed: {
			myValue() {
				// 图片格式转化
				let value = this.value || '';
				value = value
					.replace(/{learun_img_api}/g, `${this.API}/system/annexesfile/`)
					.replace(/{learun_img_token}/g, `token=${this.GET_GLOBAL("token")}`);
				return value
			}
		},
		methods: {
			onEditorReady() {
				// #ifdef MP-BAIDU
				this.editorCtx = requireDynamicLib('editorLib').createEditorContext('editor');
				this.editorCtx.setContents({
					html: this.myValue,
				})
				// #endif

				// #ifdef APP-PLUS || MP-WEIXIN || H5
				uni.createSelectorQuery().select('#editor').context((res) => {
					this.editorCtx = res.context
                    this.editorCtx.setContents({
						html: this.myValue,
					})
				}).exec()
				// #endif
			},
			onStatusChange(e) {
				const formats = e.detail
				this.formats = formats
			},
			handleClick() {// 编辑
				this.ONCE('edit-learun-editor', data => {
					this.editorCtx.setContents({
						html: data.value,
					})
					// 转化
					 const len = data.data?.delta?.ops?.length || 0
					for (let i = 0; i < len; i++) {
						const deltaItem = data.data.delta.ops[i]
						if (deltaItem.insert && deltaItem.insert.image && deltaItem.insert.image.indexOf(this.API) > -1) {
						const imageUrl = `${deltaItem.insert.image
							.replace(this.API + '/system/annexesfile/', '{learun_img_api}')
							.replace(`token=${this.GET_GLOBAL("token")}`, '{learun_img_token}')}`
						data.value = data.value.replace(deltaItem.insert.image, imageUrl)
						}
					}
					this.$emit('input', data.value)
					this.$emit('change', data)
				})
				this.NAV_TO_LAYER('/pages/common/learun-editor-picker', {
					value: this.myValue,
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import "/static/editor/editor-icon.css";

	.ql-container {
		box-sizing: border-box;
		padding: 12px 15px;
		width: 100%;
		min-height: 20px;
		height: auto;
		font-size: 16px;
		line-height: 1.5;
		border: 1px solid #F0F0F0;
	}
	.ql-container .ql-image-overlay .ql-image-toolbar{
		display: none;
	}

	.ql-active {
		color: #06c;
	}
</style>
