<template>
	<view>
		<view  class="learun-box learun-input learun-input-border" :class="{'learun-input-placeholder':VALIDATENULL(value)}"  @tap.stop="handleOpen">
			<view class="learun-input__content" ><text>{{myValue}}</text></view>
			<view class="learun-input__color-content" :style="{ background: myValue }"></view>
			<view class="learun-input-icon-right">
				<uni-icons v-if="VALIDATENULL(value)"  type="bottom" size="14" color="#c0c4cc" ></uni-icons>
				<view  v-else-if="!disabled"  @tap.stop="handleClear" >
					<uni-icons type="clear" size="14" color="#c0c4cc" ></uni-icons>
				</view>
			</view>
		</view>
		
		<learun-color-popup ref="colorPicker" @confirm="confirm"  ></learun-color-popup>
	</view>
</template>

<script>
	export default {
		name:'learun-color-picker',
		props:{
			value:{},
			placeholder:{
				type:String,
				default:'请选择'
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		computed:{
			myValue:{
				get(){
					if(this.VALIDATENULL(this.value)){
						return this.$t(this.placeholder)
					}
					
					return this.value
				},
				set(val){
					this.$emit('input',val)
					this.$emit('change',val)
				}
			}
		},
		methods:{
			handleOpen(){
				if(this.disabled){
					return
				}
				// 打开颜色选择器
				this.$refs.colorPicker.open(this.myValue)
			},
			handleClear(){
				if(this.disabled){
					return
				}
				this.myValue = ''
			},
			confirm({rgba}){
				this.myValue = `rgba(${rgba.r}, ${rgba.g},${rgba.b}, ${rgba.a})`
			}
		}
	}
</script>
<style lang="scss" scoped >
	.learun-input__color-content{
		position: absolute;
		top:5px;
		right: 24px;
		height: 24px;
		width: 24px;
	}
</style>
