<template>
	<view id="app">
		<!-- 顶部导航栏区域，包含返回按钮和页面标题 -->
		<view class="header">
			<!-- 返回按钮，点击触发goBack方法返回上一页 -->
			<button class="back-button" @click="goBack">
				<!-- 通过边框绘制向左箭头图标 -->
				<view class="arrow-left"></view>
			</button>
			<!-- 页面标题，使用计算属性truncatedTitle进行长度截断处理 -->
			<view class="title">{{ truncatedTitle }}</view>
		</view>
		<!-- 内容显示区域，使用v-html动态渲染后端返回的HTML内容 -->
		<view class="content" v-html="htmlContent"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 用于存储后端返回的HTML内容，将在页面中动态渲染
				htmlContent: '',
				// 页面原始标题，从后端数据中获取
				title: '',
				// 标题最大显示长度，超过则截断并显示省略号
				maxTitleLength: 20,
				// 存储从后端获取的完整数据列表
				listALL: [],
			};
		},
		// 页面加载时触发数据获取，参数e包含路由传递的查询参数
		onLoad(e) {
			uni.setNavigationBarTitle({
				title: this.$t("新闻详情")
			})
			this.fetchData(e);
		},
		computed: {
			// 计算属性：根据maxTitleLength对标题进行截断处理
			// 若标题长度超过阈值，显示前20个字符+省略号，否则显示完整标题
			truncatedTitle() {
				return this.title.length > this.maxTitleLength ?
					`${this.title.slice(0, this.maxTitleLength)}...` :
					this.title;
			},
		},
		methods: {
			// 返回上一页的方法，调用uni.navigateBack实现路由回退
			goBack() {
				uni.navigateBack();
			},
			// 异步获取数据的核心方法，接收路由参数e
			async fetchData(e) {
				try {
					// 发送HTTP GET请求获取新闻列表数据
					const result = await this.HTTP_POST({
						url: `/data/dbsource/newsList/list`,
						data: {
							paramsJson: "",
						}
					});
					// 将返回的完整数据存储到listALL
					this.listALL = result;
					// 根据路由参数e.id查找对应的目标数据项
					const targetItem = this.listALL.find((res) => res.id === e.id);
					// 若找到目标数据，更新页面显示的HTML内容和标题
					if (targetItem) {
						this.htmlContent = targetItem.content;
						this.title = targetItem.title;
					}
				} catch (error) {
					// 捕获请求过程中的错误并打印到控制台
					console.error('数据请求出错:', error);
				}
			},
		},
	};
</script>

<style scoped>
	/* 页面整体样式：设置字体、背景色 */
	page {
		font-family: 'Inter', sans-serif;
		/* 使用现代无衬线字体 */
		margin: 0;
		padding: 0;
		background-color: #f4f4f4;
		/* 浅灰色背景 */
	}

	/* 顶部导航栏样式：蓝色背景、弹性布局、阴影效果 */
	.header {
		background-color: #0096FF;
		/* 柔和的蓝色背景 */
		color: white;
		display: flex;
		/* 弹性布局实现水平排列 */
		align-items: center;
		/* 垂直居中内容 */
		padding: 16px 24px;
		/* 内边距 */
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
		/* 淡阴影提升层次感 */
	}

	/* 返回按钮样式：圆形背景、透明渐变效果、点击反馈 */
	.back-button {
		background-color: rgba(255, 255, 255, 0.15);
		/* 半透明白色背景 */
		border: none;
		border-radius: 50%;
		/* 圆形按钮 */
		width: 36px;
		height: 36px;
		color: white;
		font-size: 22px;
		cursor: pointer;
		margin-right: 16px;
		/* 右侧边距 */
		display: flex;
		/* 子元素居中 */
		justify-content: center;
		align-items: center;
		transition: background-color 0.3s ease;
		/* 背景色渐变过渡 */
	}

	/* 返回按钮悬停状态 */
	.back-button:hover {
		background-color: rgba(255, 255, 255, 0.25);
		/* 稍透明背景 */
	}

	/* 返回按钮点击激活状态 */
	.back-button:active {
		background-color: rgba(255, 255, 255, 0.35);
		/* 更透明背景 */
	}

	/* 向左箭头图标样式：通过边框绘制三角形 */
	.arrow-left {
		width: 0;
		height: 0;
		border-top: 10px solid transparent;
		/* 上边框透明 */
		border-bottom: 10px solid transparent;
		/* 下边框透明 */
		border-right: 14px solid white;
		/* 右边框白色形成箭头 */
	}

	/* 标题样式：弹性布局、单行显示、溢出隐藏 */
	.title {
		font-size: 16px;
		font-weight: 500;
		/* 中等粗细字体 */
		white-space: nowrap;
		/* 禁止换行 */
		overflow: hidden;
		/* 溢出隐藏 */
		text-overflow: ellipsis;
		/* 显示省略号 */
		flex-grow: 1;
		/* 占据剩余空间 */
		margin: 0;
		color: white;
		/* 白色文字 */
	}

	/* 内容区域样式：白色背景、内边距、阴影、圆角 */
	.content {
		padding: 24px;
		/* 内边距 */
		line-height: 1.6;
		/* 行高提升可读性 */
		background-color: white;
		/* 白色背景 */
		margin: 16px;
		/* 外边距 */
		border-radius: 8px;
		/* 圆角 */
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
		/* 淡阴影 */
	}
</style>