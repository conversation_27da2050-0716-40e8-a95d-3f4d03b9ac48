<template>
	<view v-if="ready" :class="['learun-form','learun-customform',{'learun-customform__noTab':navList.length <= 1}]">
		<view v-if="navList.length > 1" class="learun-customform-tabbar fixed" :style="{'top':windowTop}">
			<learun-nav v-model="tab" :items="navList" />
		</view>
		<view class="learun-customform-tabcontent">
			<uni-forms :modelValue="formData" :label-position="scheme.formInfo.labelPosition === 'top'?'top':'left'"
				:label-width="scheme.formInfo.labelPosition === 'top'?320:scheme.formInfo.labelWidth" :rules="rules"
				ref="myForm" style="height: 100%;">
				<view style="height: 100%;" v-show="tab == index" v-for="(item, index) of tabList" :key="index">
					<!--<scroll-view scroll-with-animation scroll-y style="height: 100%;">-->
					<view style="padding:8px;">
						<view v-for="component in item.components" :key="component.key">
							<learun-divider v-if="component.type=='divider'" :text="component.html" />
							<button style="margin-bottom: 8px;" v-else-if="component.type=='btn'"
								:disabled="component.disabled" :type="component.myType"
								@click="handleBtnClick(component)">
								{{component.label}}
							</button>

							<view style="margin-bottom: 15px;" v-else-if="component.type=='lable'"
								:disabled="component.disabled"
								:style="{fontSize:component.fontSize+'px',color:component.color,textAlign:component.contentPosition,fontWeight:component.fontWeight}">
								{{component.label}}</view>

							<learun-customform-card v-else-if="component.type=='card'" :component="component"
								:getDataSource="learun_form_getDataSource" :editMode="editMode" :formId="formId"
								@addRow="addTableRow" @deleteRow="deleteTableRow($event.event,$event.col)"
								@rowChange="rowChange($event.event,$event.col)" @input="setValue" @change="handleChange"
								ref="gridtable" @btnClick="handleBtnClick" />

							<learun-edit-table v-else-if="component.type=='gridtable'" :value="getValue(component.prop)"
								:formId="formId" :columns="component.children" :tableId="component.prop"
								:getDataSource="learun_form_getDataSource" :title="component.label"
								:editMode="editMode1" :hasAddBtn="component.isAddBtn" :addBtnText="component.addBtnText"
								:hasRemoveBtn="component.isRemoveBtn" :required="component.required"
								:classType="component.classType" :isShowNum="component.isShowNum"
								@addRow="addTableRow(component)" @deleteRow="deleteTableRow($event,component)"
								@rowChange="rowChange($event,component)" ref="gridtable" />

							<learun-view-table v-else-if="component.type=='viewtable'"
								:paramFiled="getValue(component.paramFiled)" :columns="component.columns"
								:code="component.dataCode" :title="component.label" />

							<crystal-single v-else-if="component.type=='singleWrite'" :titleLabel="component.label"
								:id="component.prop" :isvalidate="component.isvalidate"
								:value="getValue(component.prop)" :style="{width: 70}" :editMode="editMode"
								:required="component.required" @setValue="setValue" :screenFul="component.screenFul"
								:isLeave="true" />

							<view v-else-if="component.type=='crystalText'" v-html="component.html"
								style="margin-bottom: 10px;" v-show="component.isShow" />

							<view v-else-if="component.type=='hidden'" />

							<crystal-customform-item v-else :component="component"
								:getDataSource="learun_form_getDataSource" :editMode="editMode" :formId="formId"
								:value="getValue(component.prop)" @input="setValue" @change="handleChange" />
						</view>
					</view>
					<!--</scroll-view>-->
				</view>
			</uni-forms>
		</view>
		<passwordDialog :visible="passwordDialogVisible" @afterPasswordConfirm="afterPasswordConfirm"
			:emp_No="passwordEmp_No"></passwordDialog>
	</view>
</template>

<script>
	import get from "lodash/get"
	import set from "lodash/set"
	import customFormMixins from '@/common/customform.js'
	import {
		forEach
	} from "lodash"
	import passwordDialog from "../../pages/password/dialog.vue"
	export default {
		name: 'learun-customform-wraper',
		mixins: [customFormMixins],
		props: {
			scheme: {
				default: () => {}
			},
			editMode: {
				default: true
			},
			isUpdate: {
				default: false
			},
			isWorkflow: {
				default: false
			},
			moduleId: String,
			formId: String,
			loadParams: {},

			isSystem: { // 代码开发表单（系统表单）
				default: false
			},
			isNotLoadData: { // 自定义表单从外部赋值
				default: false
			},

			initFormValue: {},

			top: {
				type: Number,
				default: 0
			},

			isAuth: {
				type: Boolean,
				default: true
			},
			isChlidTable: {
				type: Boolean,
				default: true
			},

		},
		data() {
			return {
				tab: 0,
				myScheme: {},
				formData: {},
				oldFormData: {},
				rules: {},
				components: [],
				passwordDialogVisible: false,
				tabList: [],
				editMode1: false,
				ready: false,
				isFormUpdate: false,
				passwordEmp_No: "",
				afterPassword: Function,
				screenWidth: 750,
			}
		},
		computed: {
			navList() {
				return this.tabList.map(t => t.text)
			},
			windowTop() {
				return (this.top + this.WINDOWTOP()) + 'px'
			}
		},

		created() {
			this.init()
		},

		methods: {
			async init(isSet, _setData) {
				if (!isSet) {
					this.isFormUpdate = this.isUpdate
				}
				let initFormValue = {}
				if (isSet) {
					initFormValue = _setData
				} else if (!this.isNotLoadData && this.isFormUpdate && !this.isSystem) {
					if (this.scheme.formType == 0) {
						initFormValue = await this.HTTP_GET({
							url: `/custmerform/data/${this.formId}`,
							params: this.loadParams,
							errorTips: this.$t('加载表单失败')
						})
					} else {
						var mainTable = this.scheme.db.find(t => t.type == 'main')
						initFormValue[mainTable.name] = [this.initFormValue]
						if (this.scheme.db.length > 1) {
							const mainDataQuery = {}
							this.scheme.db.forEach(db => {
								mainDataQuery[db.relationField] = this.initFormValue[db.relationField]
							})
							const childDatas = await this.HTTP_GET({
								url: `/custmerform/viewdata/${this.formId}`,
								params: {
									mainData: JSON.stringify(mainDataQuery)
								},
								errorTips: this.$t('加载表单失败')
							})
							initFormValue = {
								...initFormValue,
								...childDatas
							}
						}
					}

					if (this.hasFormData(initFormValue)) {
						this.isFormUpdate = true
					} else {
						this.isFormUpdate = false
					}
				} else if (this.isSystem || this.isNotLoadData) {
					initFormValue = this.initFormValue
				}

				this.formData = await this.learun_form_getFormData(this.scheme, this.isFormUpdate ? initFormValue : {},
					this.isFormUpdate)
				if (this.isFormUpdate) {
					this.oldFormData = this.COPY(this.formData)
				} else {
					this.oldFormData = null
				}

				this.SET_DATA(`learun_form_data_${this.formId}`, this.formData)

				// 加载选择数据
				for (let i = 0, len = this.scheme.formInfo.tabList.length; i < len; i++) {
					const components = this.scheme.formInfo.tabList[i].components
					for (let j = 0, jlen = components.length; j < jlen; j++) {
						if (!this.isAuth || this.GET_FORM_LOOK_AUTH(components[j].prop, this.moduleId)) {
							if (components[j].type == 'gridtable') {
								for (let componentItem of components[j].children) {
									await this.learun_form_fetchDataSource(componentItem, this.formData)
								}
							} else {
								await this.learun_form_fetchDataSource(components[j], this.formData)
							}
						}
					}
				}
				this.tabList = this.toShowTabs(this.scheme.formInfo.tabList)

				/*设置校验规则*/
				this.setRules(this.scheme.formInfo.tabList)

				// 赋值前脚本
				await this.beforeEvent()

				this.ready = true
				this.$emit('ready')

			},

			// 给表单赋值
			async setForm(data, isUpdate) {
				this.ready = false
				this.isFormUpdate = isUpdate
				await this.init(true, data)
			},

			toShowTabs(data) { // 转化成表单显示数据结构
				const tabList = this.COPY(data)
				const components = []
				const pMap = {}
				const showTabList = []
				const hideComponentList = []
				tabList.forEach(tab => {
					const componentList = []
					tab.components.forEach(component => {
						if (!this.isAuth || this.GET_FORM_LOOK_AUTH(component.prop, this.moduleId) || [
								'card'
							].includes(
								component.type) || !component.display) {
							components.push(component)
							component.key = component.prop

							if (this.isAuth) {
								component.edit = this.GET_FORM_EDIT_AUTH(component.prop, this.moduleId)
							}

							if (['gridtable'].includes(component.type)) {
								if (this.isAuth) {
									component.isAddBtn = this.GET_FORM_EDIT_AUTH(component.prop + '_add',
										this
										.moduleId)
									component.isRemoveBtn = this.GET_FORM_EDIT_AUTH(component.prop +
										'_remove', this.moduleId)
								}



								const children = []
								component.children.forEach(ccomponent => {
									if (!this.isAuth || this.GET_FORMTABLE_LOOK_AUTH(component
											.prop, ccomponent.prop, this.moduleId)) {
										ccomponent.key = ccomponent.prop
										if (this.isAuth) {
											ccomponent.edit = this.GET_FORMTABLE_EDIT_AUTH(
												component.prop, ccomponent.prop, this.moduleId)
										}

										components.push(ccomponent)
										children.push(ccomponent)
									}
								})
								component.children = children
							} else if (['card'].includes(component.type)) {
								if (!pMap[component.prop]) {
									pMap[component.prop] = []
								}
								component.children = pMap[component.prop]
							}


							if (component.ptype == 'card') {
								if (!pMap[component.pid]) {
									pMap[component.pid] = []
								}
								pMap[component.pid].push(component)

								delete component.pid
								delete component.ptype
							} else {
								if (component.display != false) {
									componentList.push(component)
								} else {
									hideComponentList.push(component)
								}
							}
						}
					})
					tab.components = componentList
					if (tab.components.length > 0) {
						showTabList.push(tab)
					}
				})


				showTabList[0].components.push(...hideComponentList)
				this.toFilterCard(showTabList)
				this.components = components
				return showTabList
			},
			toFilterCard(tabList) { // 过滤掉空卡片
				tabList.forEach(tab => {
					const componentList = []
					tab.components.forEach(component => {
						if (['card'].includes(component.type)) {
							component.children = this.toFilterCardSub(component.children)
							if (component.children.length > 0) {
								componentList.push(component)
							}
						} else {
							componentList.push(component)
						}
					})

					tab.components = componentList
				})
			},
			toFilterCardSub(cardList) {
				const list = []
				cardList.forEach(cardComponent => {
					if (cardComponent.type != 'card') {
						list.push(cardComponent)
					} else {
						cardComponent.children = this.toFilterCardSub(cardComponent.children)
						if (cardComponent.children.length > 0) {
							list.push(cardComponent)
						}
					}
				})
				return list
			},

			// 设置校验规则
			setRules(tabList) {
				const rules = {}
				for (let i = 0, len = tabList.length; i < len; i++) {
					const components = tabList[i].components
					for (let j = 0, jlen = components.length; j < jlen; j++) {
						const component = components[j]
						rules[component.prop] = rules[component.prop] || {
							rules: []
						}

						if (component.required) {
							rules[component.prop].rules.push({
								required: true,
								errorMessage: `${this.$t('请输入')}${component.label}`
							})
						}

						if (component.patterns && component.patterns.length > 0) {
							rules[component.prop] = rules[component.prop] || {
								rules: []
							}
							component.patterns.forEach(pattern => {
								if (pattern.reg) {
									let reg = pattern.reg.substring(1, pattern.reg.length - 1)
									rules[component.prop].rules.push({
										pattern: reg,
										errorMessage: pattern.msg
									})
								}

							})
						}

					}
				}
				this.rules = rules
			},
			// 本方法由调用方使用 $refs 的方式调用
			// 表单校验
			async validateTable() {
				if (this.$refs.gridtable) {
					for (let i = 0, len = this.$refs.gridtable.length; i < len; i++) {
						const res = await this.$refs.gridtable[i].validate()
						if (!res) {
							return false
						}
					}
				}
				return true
			},
			async validate() {
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm)
				const tableRes = await this.validateTable()
				if (err || !tableRes) {
					return false
				} else {
					// 校验后执行脚本
					return await this.afterValidateEvent()
				}
			},

			// 本方法由调用方使用 $refs 的方式调用
			// 获取表单值
			async getFormValue() {
				const formData = this.COPY(this.formData)
				return this.learun_form_convertToPostData(formData, this.components, this.isFormUpdate)
			},

			// 保存表单数据
			async saveForm(formId, pkey, pkeyValue, isWf) {
				const formData = await this.getFormValue()
				if (isWf) {
					formData[pkey] = pkeyValue
				}

				const diffFormData = this.getDiffFormData()
				const postData = {
					schemeId: formId,
					isUpdate: this.isFormUpdate,
					pkey: pkey,
					pkeyValue: pkeyValue,
					data: JSON.stringify(formData),
					diffFormData: diffFormData
				}

				const res = await this.HTTP_POST({
					url: '/custmerform/data',
					data: postData,
					errorTips: this.$t('表单提交保存失败')
				})

				if (!res) {
					return false
				}
				this.isFormUpdate = true

				return await this.afterSaveEvent(res)
			},

			// 表单数据值改变后执行脚本
			afterChangeDataEvent(component, data) {
				// 具体某一个组件值改变后执行脚本
				const changeEvent = this.GET_FUNCTION(component.changeCode)
				if (changeEvent.res) {
					changeEvent.fn(this.getEventParams({
						component,
						data
					}))
				}

				// 表单数据改变后执行脚本
				const changeDataEvent = this.GET_FUNCTION(this.scheme.formInfo.changeDataEvent)

				if (changeDataEvent.res) {
					changeDataEvent.fn(this.getEventParams({
						component,
						data
					}))
				}
			},
			// 子表数据改变脚本
			afterTableChangeDataEvent(component, data, tableProp, tableIndex, tableRow, myTable) {
				const changeDataEvent = this.GET_FUNCTION(myTable.changeDataEvent)
				if (changeDataEvent.res) {
					changeDataEvent.fn(this.getEventParams({
						component,
						data,
						tableProp,
						tableIndex,
						tableRow
					}))
				}
			},

			// 表单加载前执行脚本
			beforeEvent() {
				return new Promise((resolve) => {
					const beforeSetData = this.GET_FUNCTION(this.scheme.formInfo.beforeSetData)
					if (beforeSetData.res) {
						const res = beforeSetData.fn(this.getEventParams({
							callback: (res) => {
								// 兼容异步回调方法
								resolve(res)
							}
						}))
						if (res != 'callback') {
							resolve(res)
						}
					} else {
						resolve(true)
					}
				})
			},

			// 校验后执行脚本
			afterValidateEvent() {
				return new Promise((resolve) => {
					const {
						res,
						fn
					} = this.GET_FUNCTION(this.scheme.formInfo.afterValidateForm)
					if (res) {
						const fnRes = fn(this.getEventParams({
							callback: (rs) => {
								// 兼容异步回调方法
								resolve(rs)
							}
						}))

						if (fnRes != 'callback') {
							resolve(fnRes)
						}
					} else {
						resolve(true)
					}
				})
			},
			// 保存后执行脚本
			afterSaveEvent(formData) {
				return new Promise((resolve) => {
					const {
						res,
						fn
					} = this.GET_FUNCTION(this.scheme.formInfo.afterSaveEvent)
					if (res) {
						this.formData = this.COPY(formData)
						const fnRes = fn(this.getEventParams({
							callback: (rs) => {
								// 兼容异步回调方法
								resolve(rs)
							}
						}))

						if (fnRes != 'callback') {
							resolve(fnRes)
						}
					} else {
						resolve(true)
					}
				})
			},

			// 获取脚本参数
			getEventParams({
				component,
				data,
				tableProp,
				tableIndex,
				tableRow,
				callback
			}) {
				const loginUser = this.GET_GLOBAL('loginUser')
				const params = {
					prop: component ? component.prop : undefined,
					data: data,
					tableProp: tableProp,
					tableIndex: tableIndex,
					isUpdate: this.isFormUpdate,
					get: this.getValue,
					set: this.setValue,
					getLabel: this.getLabel,
					setRequired: this.setRequired,
					setDisabled: this.setDisabled,
					setHide: this.setHide,
					httpGet: this.HTTP_GET,
					httpPost: this.HTTP_POST,
					httpDelete: this.HTTP_DELETE,
					httpPut: this.HTTP_PUT,
					loading: this.LOADING,
					hideLoading: this.HIDE_LOADING,
					message: this.TOAST,
					loginUser: loginUser,
					callback: callback,
					getSourceData: this.getSourceData
				}

				if (tableRow) {
					params.getLabel = (id) => {
						return this.getLabel(id, tableRow);
					};
				}
				return params;

			},

			async getSourceData(code, params) {
				if (!code) {
					return []
				}
				return await this.HTTP_POST({
					url: `/data/dbsource/${code}/list`,
					data: {
						ParamsJson: params ? JSON.stringify(params) : ""
					}
				})
			},

			async addTableRow(myTable) {
				this.LOADING('添加行中...')
				let point = {}
				const currentUser = this.GET_GLOBAL('loginUser')
				for (let col of myTable.children) {
					if (!['guid', 'createtime', 'modifytime', 'company', 'department', 'createuser', 'modifyuser',
							'encode'
						].includes(col.type)) {
						point[col.prop] = col.default || ''

						if (["checkbox", "radio", "select", "selectMultiple", "treeselect", "layerselect",
								'companySelect',
								'departmentSelect', 'userSelect'
							].includes(col.type) && point[col.prop]) {
							await this.clearSubValue(col.prop, myTable.children, point)
						}
					} else {
						point[col.prop] = ''

						switch (col.type) {
							case 'encode':
								if (col.code) {
									point[col.prop] = `learun_code_${col.code}|${this.GUID()}`
								} else {
									point[col.prop] = this.$t('未设置单据编码')
								}

							case 'companySelect':
								if (col.isLogin) {
									point[col.prop] = currentUser.f_CompanyId
								}
								break
							case 'departmentSelect':
								if (col.isLogin) {
									point[col.prop] = currentUser.f_DepartmentId
									await this.learun_form_getDepartment(point[col.prop])
								}
								break
							case 'userSelect':
								if (col.isLogin) {
									point[col.prop] = currentUser.f_UserId
									await this.learun_form_getUser(point[col.prop])
								}
								break
							case 'guid':
								point[col.prop] = this.GUID()
								break
							case 'company':
								point[col.prop] = currentUser.f_CompanyId
								break
							case 'department':
								point[col.prop] = currentUser.f_DepartmentId
								await this.learun_form_getDepartment(point[col.prop])
								break
							case 'createuser':
								point[col.prop] = currentUser.f_UserId
								await this.learun_form_getUser(point[col.prop])
								break
							case 'modifyuser':
								point[col.prop] = currentUser.f_UserId
								await this.learun_form_getUser(point[col.prop])
								break
							case 'createtime':
								point[col.prop] = this.DATENOW()
								break
							case 'modifytime':
								point[col.prop] = this.DATENOW()
								break
						}
					}

				}

				point.abledList = []
				point.disabled = false
				point.hasNoDeleteBtn = false
				this.formData[myTable.prop].push(point)

				this.HIDE_LOADING()
			},
			deleteTableRow(event, myTable) {
				this.formData[myTable.prop].splice(event.index, 1)
			},
			async rowChange({
				index,
				rowData,
				prop,
				data,
				component
			}, myTable) {
				set(this.formData, `${myTable.prop}.${index}`, rowData)

				// 根据右侧赋值字段给表单赋值地图信息
				if (component.type == "layerbmap") {
					if (component.bindaddr) {
						if (data) {
							set(this.formData, `${myTable.prop}.${index}.${component.bindaddr}`, data.address) //设置绑定地址
						} else {
							set(this.formData, `${myTable.prop}.${index}.${component.bindaddr}`, '') //设置绑定地址
						}
					}
					if (component.bindaddrpoint) {
						if (data) {
							set(this.formData, `${myTable.prop}.${index}.${component.bindaddrpoint}`,
								`${data.lng},${data.lat}`) //设置绑定经纬度
						} else {
							set(this.formData, `${myTable.prop}.${index}.${component.bindaddrpoint}`, '') //设置绑定地址
						}
					}
				}
				// 弹窗赋值
				if (component.type == 'layerselect') {
					component.columns.forEach(col => {
						if (col.valueKey) {
							if (data) {
								set(this.formData, `${myTable.prop}.${index}.${col.valueKey}`, data[col.prop])
							} else {
								set(this.formData, `${myTable.prop}.${index}.${col.valueKey}`, undefined)
							}
						}
					})
				}

				if (["checkbox", "radio", "select", "selectMultiple", "treeselect", "layerselect", 'companySelect',
						'departmentSelect', 'userSelect'
					].includes(component.type)) {
					await this.clearSubValue(component.prop, this.components.find(t => t.prop == myTable.prop)
						.children, rowData)
				}

				this.$nextTick(() => {
					this.afterTableChangeDataEvent(component, data, myTable.prop, index, rowData, myTable)
					this.$emit('myAfterChangeDataEvent', {
						component,
						data,
						rowData: rowData,
						table: myTable.prop,
						tableIndex: index
					})
				})

			},
			// 组件数据值改变
			async handleChange({
				data,
				component
			}) {
				// 根据右侧赋值字段给表单赋值地图信息
				if (component.type == "layerbmap") {
					if (component.bindaddr) {
						if (data) {
							set(this.formData, component.bindaddr, data.address) //设置绑定地址
						} else {
							set(this.formData, component.bindaddr, '') //设置绑定地址
						}
					}
					if (component.bindaddrpoint) {
						if (data) {
							set(this.formData, component.bindaddrpoint, `${data.lng},${data.lat}`) //设置绑定经纬度
						} else {
							set(this.formData, component.bindaddrpoint, '') //设置绑定地址
						}
					}
				}
				// 弹窗赋值
				if (component.type == 'layerselect') {
					component.columns.forEach(col => {
						if (col.valueKey) {
							if (data) {
								this.setValue({
									path: col.valueKey,
									value: data[col.prop]
								})
							} else {
								this.setValue({
									path: col.valueKey,
									value: undefined
								})
							}
						}
					})
				}

				if (["checkbox", "radio", "select", "selectMultiple", "treeselect", "layerselect", 'companySelect',
						'departmentSelect', 'userSelect'
					].includes(component.type)) {
					await this.clearSubValue(component.prop)
				}


				this.afterChangeDataEvent(component, data)
				this.$emit('myAfterChangeDataEvent', {
					component,
					data
				})
			},

			async clearSubValue(upProp, list, data) {
				const components = list || this.components
				const formData = data || this.formData
				for (const component of components) {
					if (component.upCtrl == upProp) {
						// 获取数据值
						await this.learun_form_fetchDataSource(component, formData)
						set(formData, component.prop, undefined)
						//this.setValue({path:component.prop,value:undefined})
						this.$set(component, 'key', this.GUID())
						await this.clearSubValue(component.prop, list, data)
					}
				}
			},

			setRequired(prop, isRequired = true) {
				const rule = this.rules[prop]
				const component = this.components.find(t => t.prop == prop)
				if (rule) {
					const ruleIndex = rule.rules.findIndex(t => t.required)
					if (ruleIndex == -1 && isRequired) {
						rule.rules.push({
							required: true,
							errorMessage: `${this.$t('请输入')}${component.label}`
						})
						component.required = true;
					} else if (ruleIndex != -1 && !isRequired) {
						rule.rules.splice(ruleIndex, 1)
						component.required = false;
					}
					component.key = this.GUID()
				}
			},
			setDisabled(prop, isDisabled = true) {
				const component = this.components.find(t => t.prop == prop)
				if (component) {
					component.disabled = isDisabled
					component.key = this.GUID()
				}
			},
			setHide(prop, isHide = true) {
				const component = this.components.find(t => t.prop == prop)
				if (component) {
					component.display = !isHide
					component.key = this.GUID()
				}
			},

			getLabel(prop, formData) {
				if (formData == undefined || formData == null) {
					formData = this.formData
				}
				if (prop.indexOf('.') != -1) { // 子表获取
					const propList = prop.split('.')
					if (propList.length != 3) {
						return ''
					}
					prop = propList[2]
					formData = formData[propList[0]][propList[1]]
				}

				const value = formData[prop]
				const component = this.components.find(t => t.prop == prop)
				return this.learun_form_displayText(value, component, formData)
			},

			// 设置表单数据的方法
			setValue({
				path,
				value,
				type
			}) {
				//add by kyle on 2024-01-12
				// let {a, b} = this.$emit('beforeInput', {
				// 	path,
				// 	value
				// });
				if (type == 'addTable') {
					this.formData[path].push(value)
				} else if (type == 'deleteTable') {
					const paths = path.split('.')
					this.formData[paths[0]].splice(paths[1], 1)
				} else {
					set(this.formData, path, value)
				}
			},
			// 获取表单数据的方法
			getValue(path) {
				return get(this.formData, path)
			},

			// 按钮点击
			async handleBtnClick(component) {
				if (component.clickEvent) {
					await component.clickEvent(this.getEventParams({
						component
					}))
				} else {
					const {
						res,
						fn
					} = this.GET_FUNCTION(component.clickCode)
					if (res) {
						fn(this.getEventParams({
							component
						}))
					}
				}
			},

			// 比对数据修改
			getDiffFormData() {
				const diffFormData = []
				if (this.oldFormData == null) {
					return diffFormData
				}
				const currentUser = this.GET_GLOBAL('loginUser')
				for (let key in this.oldFormData) {
					const componentM = this.components.find(t => t.prop == key)
					const oldValue = this.oldFormData[key]
					const newValue = this.formData[key]

					if (componentM && !['gridtable', 'divider', 'upload', 'uploadimg', 'company',
							'department', 'createuser', 'modifyuser', 'createtime', 'modifytime', 'viewtable', 'card',
							'btn', 'lable'
						].includes(componentM.type) &&
						oldValue != newValue) {
						const dataPoint = { // 数据变动
							table_Name: componentM.table,
							tableField: componentM.field,
							formColumn: componentM.label,
							oldVal: oldValue,
							newVal: newValue,
							oldShowVal: '',
							newShowVal: '',
							creator: currentUser.f_RealName + '/' + currentUser.f_Account
						}
						dataPoint.oldShowVal = this.getLabel(key, this.oldFormData)
						dataPoint.newShowVal = this.getLabel(key, this.formData)

						diffFormData.push(dataPoint)
					}
				}
				return diffFormData
			},

			// 判断是否有表单值
			hasFormData(data) {
				if (!data) {
					return false
				}

				for (let key in data) {
					if (data[key].length > 0) {
						return true
					}
				}

				return false

			},

			//设置组件属性
			setComponent(prop, data) {
				const component = this.components.find(t => t.prop == prop);
				if (component) {
					for (let key in data) {
						component[key] = data[key];
						component.key = this.GUID();
					}
				}
			},
			afterPasswordConfirm(res) {
				this.passwordDialogVisible = false
				this.afterPassword(res);
			},
		}
	}
</script>


<style lang="scss" scoped>
	.learun-customform {
		box-sizing: border-box;
		position: relative;
		//height: 100%;
		//width: 100%;
		background-color: #fff;
		padding-top: 34px;

		&__noTab {
			padding-top: 0;
		}

		.learun-customform-tabbar {
			background-color: #fff;
			width: 100%;
			height: 34px;
		}

		.learun-customform-tabcontent {
			box-sizing: border-box;
			position: relative;
			height: 100%;
			width: 100%;
		}
	}
</style>