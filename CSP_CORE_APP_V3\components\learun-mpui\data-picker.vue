<template>
	<view>
		<uni-data-picker v-slot:default="{ data, error, options }" :popup-title="$t('请选择所在地区')" :readonly="disabled"
			:localdata="myOptions" v-model="value2" @nodeclick="handleNodeClick" @popupclosed="handlePopupclosed"
			ref="picker" :map="{text:'label',value:'value'}">
			<view class="input-value input-value-border">
				<view v-if="error" class="error">
					<text>{{ error }}</text>
				</view>
				<scroll-view v-else-if="data.length" class="selected-area" scroll-x="true">
					<view class="selected-list">
						<view v-if="showFormat == 'mostChild'" class="selected-item">{{ data[data.length - 1].text}}
						</view>
						<template v-else>
							<view class="selected-item" v-for="(item,index) in data" :key="index">
								<text v-if="index === data.length - 1">{{ item.text }}</text>
								<text v-else>{{ item.text }}{{ separator }}</text>
							</view>
						</template>
					</view>
				</scroll-view>
				<view v-else>
					<text class="placeholder">{{ $t(placeholder)|| $t('请选择') }}</text>
				</view>
			</view>

		</uni-data-picker>
	</view>
</template>

<script>
	export default {
		name: 'learun-data-picker',
		props: {
			value: [String, Number],
			placeholder: {
				type: String,
				default: '请选择'
			},
			labelKey: {
				type: String,
				default: 'label'
			},
			valueKey: {
				type: String,
				default: 'value'
			},
			idKey: {
				type: String,
				default: 'id'
			},
			pIdKey: {
				type: String,
				default: 'pid'
			},
			options: {
				type: Array,
				default: () => []
			},
			isTreeData: Boolean,
			disabled: Boolean,
			showFormat: {
				type: String,
				default: 'all' // all, mostChild
			},
			separator: {
				type: String,
				default: '/'
			},
			changeOnSelect: {
				type: String,
				default: 'mostChild', // mostChild, any
			}
		},
		data() {
			return {
				currentValue: '',
			}
		},
		computed: {
			myOptions() {
				if (this.isTreeData) {
					return this.options || []
				} else {
					return this.TOTREE(this.options, this.idKey, this.pIdKey, this.valueKey, this.labelKey)
				}
			},
			valDic() {
				const options2 = this.isTreeData ? this.options : this.TOTREE(this.options, this.idKey, this.pIdKey, this
					.valueKey, this.labelKey);
				let dic = {};
				this.getOptionsDic(options2, dic)
				return dic;
			},
			value2: {
				get() {
					if (!this.VALIDATENULL(this.value)) {
						const valueArr = this.value.split(',')
						return valueArr[valueArr.length - 1]
					}
					return this.value
				},
				set(val) {
					const vals = this.valDic[val]
					this.$emit('input', vals)
					this.$emit('change', {
						data: this.options.find(item => item[this.valueKey] == val),
						value: vals
					})
				}
			}
		},
		methods: {
			handleNodeClick(data) {
				if (this.changeOnSelect == 'any') {
					this.currentValue = data[this.valueKey]
				}
			},
			handlePopupclosed() {
				if (this.changeOnSelect == 'any') {
					this.value2 = this.currentValue;
				}
			},
			getOptionsDic(options, dic, parentPath = []) {
				options.forEach(item => {
					const currentPath = [...parentPath, item[this.valueKey]];
					dic[item[this.valueKey]] = currentPath.join(',');
					if (item.children) {
						this.getOptionsDic(item.children, dic, currentPath);
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.input-value {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		flex-wrap: nowrap;
		font-size: 14px;
		/* line-height: 35px; */
		padding: 0 10px;
		padding-right: 5px;
		overflow: hidden;
		height: 35px;
		/* #ifndef APP-NVUE */
		box-sizing: border-box;
		/* #endif */
	}

	.input-value-border {
		border: 1px solid #e5e5e5;
		border-radius: 5px;
	}

	.selected-area {
		flex: 1;
		overflow: hidden;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	.selected-list {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex-wrap: nowrap;
		/* padding: 0 5px; */
	}

	.selected-item {
		flex-direction: row;
		/* padding: 0 1px; */
		/* #ifndef APP-NVUE */
		white-space: nowrap;
		/* #endif */
	}

	.placeholder {
		color: grey;
		font-size: 12px;
	}
</style>