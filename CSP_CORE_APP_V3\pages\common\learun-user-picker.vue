<template>
    <!-- 页面容器，设置最小高度为屏幕高度，顶部和底部根据条件设置内边距 -->
    <view class="page"
          :style="{'min-height':SCREENHEIGHT()+'px','padding-top':'86px','padding-bottom':isMulti ?'40px':''}">
        <!-- 固定在顶部的白色背景区域，包含搜索框和标签信息 -->
        <view class="bg-white fixed">
            <!-- 包含搜索框和部门图标按钮的横向布局 -->
            <view class="learun-flex" style="padding-right: 10px;">
                <!-- 搜索框组件，绑定搜索文本，监听确认和清空事件 -->
                <uni-search-bar :placeholder="$t('搜索用户名/账号')" cancelButton="none" @confirm="searchChange(false)"
                                @clear="searchChange(true)" v-model="searchText"></uni-search-bar>
                <!-- 部门图标按钮，点击打开右侧抽屉 -->
                <learun-icon type="learun-icon-department" color="#2979ff" @click="searchClick"></learun-icon>
            </view>
            <!-- 包含部门标签、记录总数标签和已选数量标签的区域 -->
            <view style="padding: 0 0 8px 10px;">
                <!-- 显示当前选择的部门名称 -->
                <uni-tag customStyle="margin-right:8px;" size="small" inverted :text="departmentName"></uni-tag>
                <!-- 显示记录总数 -->
                <uni-tag size="small" type="primary" :text="`${$t('共')}${records}${$t('条')}`"></uni-tag>
                <!-- 多选模式下显示已选记录数量 -->
                <uni-tag customStyle="margin-left:8px;" v-if="isMulti" size="small" type="success"
                         :text="`${$t('已选')}${values.length}${$t('条')}`"></uni-tag>
            </view>
        </view>
        <!-- 显示用户列表的组件 -->
        <uni-list>
            <!-- 遍历用户列表，生成可点击的列表项 -->
            <uni-list-item clickable @click="itemClick(item)" v-for="(item,index) in list" :key="item.f_UserId"
                           :title="$t(item.f_RealName)" :note="item.f_Account" :thumb="avatarSrc(item)" thumb-size="lg">
                <!-- 列表项底部插槽，多选模式下显示选中图标 -->
                <template v-slot:footer>
                    <view v-if="isMulti" style="display: flex;align-items: center;">
                        <learun-icon v-if="values.indexOf(item.f_UserId) > -1" type="learun-icon-circle-correct" size="20"
                                     color="#2979ff" />
                    </view>
                </template>
            </uni-list-item>
        </uni-list>
        <!-- 加载更多组件，根据加载状态显示不同内容 -->
        <uni-load-more v-if="loading || status === 'noMore' " :status="status" />
        <!-- 右侧抽屉组件，用于显示搜索条件 -->
        <uni-drawer ref="showRight" mode="right">
            <!-- 抽屉内容区域 -->
            <view class="learun-box padding-top-win ">
                <!-- 抽屉标题 -->
                <view class="learun-title learun-ab ">{{$t('搜索条件')}}</view>
                <!-- 包含滚动视图的区域 -->
                <view class="learun-box">
                    <!-- 可滚动的视图，包含树形视图 -->
                    <scroll-view style="height: 100%;" scroll-with-animation scroll-y>
                        <!-- 树形视图组件，显示公司和部门信息 -->
                        <learun-tree-view :options="companyOptions()" :isTreeData="true" :loadData="loadDepartments"
                                          @change="handleDepartmentChange">
                        </learun-tree-view>
                    </scroll-view>
                </view>
            </view>
        </uni-drawer>
        <!-- 多选模式下显示的底部确定按钮 -->
        <view v-if="isMulti" class="learun-bottom-btns">
            <button @click.stop="handleOk" type="primary">{{$t('确定')}}</button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // 是否以弹窗层形式显示
            isLayer: true,
            // 搜索框中的文本
            searchText: '',
            // 当前选择的部门名称
            departmentName: '',
            // 是否正在加载数据
            loading: true,
            // 加载状态，如 'loading' 或 'noMore'
            status: 'loading',
            // 当前页码
            page: 1,
            // 总页数
            total: 1,
            // 总记录数
            records: 1,
            // 显示的用户列表
            list: [],
            // 公司列表
            companyList: [],
            // 当前选择的公司 ID
            companyId: '',
            // 当前选择的部门 ID
            departmentId: '',
            // 是否为多选模式
            isMulti: false,
            // 多选模式下已选用户的 ID 列表
            values: [],
        }
    },
    // 页面返回时，移除事件监听
    onBackPress() {
        this.OFF('select-learun-user')
    },
    // 页面卸载时，移除事件监听
    onUnload() {
		uni.setNavigationBarTitle({
			title: this.$t("选择用户")
		})
        this.OFF('select-learun-user')
    },
    async onLoad() {
        // 检查页面是否可以启动
        if (await this.PAGE_LAUNCH()) {
            // 初始化页面
            await this.init()
        }
    },
    methods: {
        // 页面初始化方法
        async init() {
            // 获取页面传递的参数
            const {
                isMulti,
                value,
                titleText
            } = this.GET_PARAM()
            // 设置页面标题
            this.SET_TITLE(titleText);
            // 设置是否为多选模式
            this.isMulti = isMulti;
            if (value) {
                // 解析已选用户 ID 列表
                this.values = value.split(",")
            } else {
                this.values = []
            }
            // 获取用户列表数据
            await this.fetchList()
            // 获取公司列表数据
            this.companyList = await this.HTTP_GET({
                url: '/organization/companys'
            }) || []
        },
        // 列表项点击事件处理方法
        itemClick(user) {
            if (this.isMulti) {
                // 多选模式下，处理用户的选中和取消选中
                const index = this.values.indexOf(user.f_UserId)
                if (index > -1) {
                    this.values.splice(index, 1)
                } else {
                    this.values.push(user.f_UserId);
                }
            } else {
                // 单选模式下，触发事件并返回上一页
                this.EMIT('select-learun-user', {
                    label: user.f_RealName,
                    value: user.f_UserId,
                    account: user.f_Account,
                    data: user
                })
                this.NAV_BACK()
            }
        },
        // 获取用户列表数据的方法
        async fetchList() {
            // 如果当前页码大于总页数，不再加载数据
            if (this.page > this.total) {
                return
            }
            // 设置加载状态
            this.loading = true
            // 构建请求参数
            const params = {
                rows: 20,
                page: this.page,
                sidx: 'F_CreateDate DESC',
                keyword: this.searchText,
                companyId: this.companyId,
                departmentId: this.departmentId
            }
            // 发送请求获取用户列表数据
            const result = await this.HTTP_GET({
                url: '/organization/user/page',
                params,
                errorTips: this.$t('获取用户信息失败')
            })
            // 加载完成，设置加载状态为 false
            this.loading = false
            if (!result) {
                return
            }
            // 过滤出新的用户数据
            const newList = result.rows.filter(t => !this.list.some(t2 => t2.f_UserId === t.f_UserId))
            // 更新总页数和总记录数
            this.total = result.total
            this.records = result.records
            // 将新数据追加到用户列表中
            this.list = this.list.concat(newList)
            // 页码加 1
            this.page++
            // 如果没有更多数据，设置状态为 'noMore'
            if (this.page > this.total) {
                this.status = "noMore"
            }
            // 更新用户数据映射
            const userMap = this.GET_DATA('learun_users_map') || {}
            const _userList = result.rows.filter(t => !userMap[t.f_UserId])
            _userList.forEach(t => {
                userMap[t.f_UserId] = t
            })
        },
        // 获取用户头像地址的方法
        avatarSrc(user) {
            const token = this.GET_GLOBAL('token')
            return user.f_HeadIcon ? `${this.API}/system/annexesfile/${user.f_HeadIcon}?token=${token}` :
                `/static/img-avatar/head.png`
        },
        // 搜索框变化事件处理方法
        async searchChange(isClear) {
            if (isClear) {
                // 清空搜索框文本
                this.searchText = ''
            }
            // 设置加载状态
            this.status = 'loading'
            // 清空用户列表
            this.list = []
            // 重置页码和总页数
            this.page = 1
            this.total = 1
            // 重新获取用户列表数据
            await this.fetchList()
        },
        // 点击部门图标按钮，打开右侧抽屉
        searchClick() {
            this.$refs.showRight.open()
        },
        // 树形视图选择变化事件处理方法
        async handleDepartmentChange({
            value,
            label,
            isCompany
        }) {
            // 更新部门名称
            this.departmentName = label
            // 重置公司 ID 和部门 ID
            this.companyId = ''
            this.departmentId = ''
            // 设置加载状态
            this.status = 'loading'
            // 清空用户列表
            this.list = []
            // 重置页码和总页数
            this.page = 1
            this.total = 1
            if (isCompany) {
                // 如果选择的是公司，设置公司 ID
                this.companyId = value
            } else {
                // 如果选择的是部门，设置部门 ID
                this.departmentId = value
            }
            // 重新获取用户列表数据
            await this.fetchList()
            // 关闭右侧抽屉
            this.$refs.showRight.close()
        },
        // 加载部门数据的方法
        async loadDepartments(companyId) {
            // 获取指定公司下的部门列表
            const list = await this.FETCH_DEPARTMENTS(companyId)
            // 将部门列表转换为树形结构
            const res = this.TOTREE(list, 'f_DepartmentId', 'f_ParentId', 'f_DepartmentId', 'f_FullName')
            return res
        },
        // 获取公司树形结构数据的方法
        companyOptions() {
            return this.TOTREE(this.companyList.map(t => ({
                ...t,
                isCompany: true,
                isLoad: false
            })), 'f_CompanyId', 'f_ParentId', 'f_CompanyId', 'f_FullName')
        },
        // 多选模式下点击确定按钮的处理方法
        handleOk() {
            // 触发事件，传递已选用户信息
            this.EMIT('select-learun-user', {
                values: this.values,
                data: this.list.filter(t => this.values.indexOf(t.f_UserId) > -1)
            })
            // 返回上一页
            this.NAV_BACK()
        },
    },
    /**
     * 下拉刷新回调函数
     */
    async onPullDownRefresh() {
        // 重置搜索条件
        this.departmentName = ''
        this.searchText = ''
        this.companyId = ''
        this.departmentId = ''
        // 设置加载状态
        this.status = 'loading'
        // 清空用户列表
        this.list = []
        // 重置页码和总页数
        this.page = 1
        this.total = 1
        // 重新获取用户列表数据
        await this.fetchList()
        // 停止下拉刷新
        uni.stopPullDownRefresh()
    },
    /**
     * 上拉加载回调函数
     */
    onReachBottom() {
        // 上拉加载更多数据
        this.fetchList()
    }
}
</script>