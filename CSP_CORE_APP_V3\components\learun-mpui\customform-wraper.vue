<template>
	<view v-if="ready" :class="['learun-form','learun-customform',{'learun-customform__noTab':navList.length <= 1}]">
		<!-- <view v-if="navList.length > 1" class="learun-customform-tabbar fixed" :style="{'top':windowTop}">
			<learun-nav v-model="tab" :items="navList" />
		</view> -->
		<view class="learun-customform-tabcontent">
			<uni-forms :modelValue="formData"
				:label-position="scheme.formInfo.form.labelPosition === 'top'?'top':'left'"
				:label-width="scheme.formInfo.form.labelPosition === 'top'?320:scheme.formInfo.form.labelWidth"
				:rules="rules" ref="myForm" style="height: 100%;">
				<view style="padding:8px;">
					<view v-for="component in tabList" :key="component.key">
						<learun-divider v-if="component.type=='divider'" :text="component.config.content" />
						<button style="margin-bottom: 8px;" :style="component.config.style || {}"
							v-else-if="component.type=='btn' && component.config.display !== false"
							:disabled="component.disabled"
							:type="component.config.danger ? 'warn' : component.config.type"
							@click="handleBtnClick(component)">
							<learun-icon :type="component.config.iconName" :size="16" :isBlock="false"
								style="margin-right: 2px" v-if="component.config.iconName"
								:color="component.config.type=='default'? '': '#fff'" />
							{{component.config.label}}
						</button>

						<view style="margin-bottom: 15px;" v-else-if="component.type=='label'"
							:disabled="component.config.disabled"
							:style="{fontSize:component.config.size+'px',color:component.config.color,textAlign:component.config.align}">
							{{component.config.content}}
						</view>
						<view style="margin-bottom: 15px;" v-else-if="component.type=='text'" :style="{fontSize:component.config.size+'px',color:component.config.color,textAlign:component.config.align,
								fontWeight: component.config.weight,fontStyle: component.config.fontStyle}" v-show="component.config.display">
							{{component.config.content}}
						</view>

						<learun-customform-card v-else-if="component.type=='card' && component.config.display !== false"
							:component="component" :getDataSource="learun_form_getDataSource" :editMode="editMode"
							:formId="formId" @addRow="addTableRow" @deleteRow="deleteTableRow($event.event,$event.col)"
							@rowChange="rowChange($event.event,$event.col)" @input="setValue" @change="handleChange"
							ref="gridtable" @btnClick="handleBtnClick" />

						<learun-edit-table v-else-if="component.type=='gridtable'" :value="getValue(component.id)"
							:formId="formId" :columns="component.children" :tableId="component.id"
							:getDataSource="learun_form_getDataSource" :title="component.config.label"
							:editMode="editMode" :hasAddBtn="component.config.isAddBtn"
							:addBtnText="component.config.addBtnText" :hasRemoveBtn="component.config.isRemoveBtn"
							:required="component.config.required" :classType="component.config.classType"
							:isShowNum="component.config.isShowNum" @addRow="addTableRow(component)"
							@deleteRow="deleteTableRow($event,component)" @rowChange="rowChange($event,component)"
							ref="gridtable" />

						<learun-view-table v-else-if="component.type=='viewTable'"
							:paramFiled="getValue(component.config.paramFiled)" :columns="component.config.columns"
							:code="component.config.dataCode" :title="component.config.label" />

						<crystal-single v-else-if="component.type=='singleWrite'" :titleLabel="component.label"
							:id="component.prop" :isvalidate="component.isvalidate" :value="getValue(component.prop)"
							:style="{width: 70}" :editMode="editMode" :required="component.required"
							@setValue="setValue" :screenFul="component.screenFul" :isLeave="true" />

						<learun-collapse v-else-if="component.type=='collapse'" :component="component" :formId="formId"
							:getDataSource="learun_form_getDataSource" :title="component.config.label"
							:editMode="editMode" @addRow="addTableRow"
							@deleteRow="deleteTableRow($event.event,$event.col)"
							@rowChange="rowChange($event.event,$event.col)" @input="setValue" @change="handleChange"
							@btnClick="handleBtnClick" ref="gridtable" />

						<learun-customform-tab v-else-if="component.type=='tabs' && component.config.display !== false"
							:component="component" :formId="formId" :getDataSource="learun_form_getDataSource"
							:title="component.config.label" :editMode="editMode" @addRow="addTableRow"
							@deleteRow="deleteTableRow($event.event,$event.col)"
							@rowChange="rowChange($event.event,$event.col)" @input="setValue" @change="handleChange"
							@btnClick="handleBtnClick" ref="gridtable" />
						
						<view v-else-if="component.type=='crystalText'" v-html="component.config.content"
							style="margin-bottom: 10px;" v-show="component.config.display" />
						
						<learun-customform-item v-else-if="!['tableLayout'].includes(component.type)"
							:component="component" :getDataSource="learun_form_getDataSource" :editMode="editMode"
							:formId="formId" :value="getValue(component.id)" @input="setValue" @change="handleChange" />
					</view>
				</view>
			</uni-forms>
		</view>
		<passwordDialog :visible="passwordDialogVisible" @afterPasswordConfirm="afterPasswordConfirm"
			:emp_No="passwordEmp_No"></passwordDialog>
	</view>

</template>

<script>
	import get from "lodash/get"
	import set from "lodash/set"
	import customFormMixins from '@/common/customform.js'
	import passwordDialog from "../../pages/password/dialog.vue"

	export default {
		name: 'learun-customform-wraper',
		mixins: [customFormMixins],
		props: {
			scheme: {
				default: () => {}
			},
			editMode: {
				default: true
			},
			isUpdate: {
				default: false
			},
			isWorkflow: {
				default: false
			},
			moduleId: String,
			formId: String,
			loadParams: {},

			isSystem: { // 代码开发表单（系统表单）
				default: false
			},
			isNotLoadData: { // 自定义表单从外部赋值
				default: false
			},

			initFormValue: {},

			top: {
				type: Number,
				default: 0
			},

			isAuth: {
				type: Boolean,
				default: true
			},
			isChildTableVirtualDel: {
				type: Boolean,
				default: false
			}

		},
		components: {
			passwordDialog
		},
		data() {
			return {
				tab: 0,
				myScheme: {},
				formData: {},
				oldFormData: {},
				rules: {},
				components: [],
				passwordDialogVisible: false, //是否显示密码
				passwordEmp_No: "",
				afterPassword: Function,
				tabList: [],
				ready: false,
				isFormUpdate: false,
				editMode1: false, //用于在线表单中修改
				screenWidth: 750,
			}
		},
		computed: {
			navList() {
				return this.tabList.map(t => t.text)
			},
			windowTop() {
				return (this.top + this.WINDOWTOP()) + 'px'
			}
		},

		created() {
			this.init()
		},

		methods: {
			async init(isSet, _setData) {

				if (!isSet) {
					this.isFormUpdate = this.isUpdate
				}
				let initFormValue = {}
				if (isSet) {
					initFormValue = _setData

				} else if (!this.isNotLoadData && this.isFormUpdate && !this.isSystem) {
					if (this.scheme.formType == 0 || this.scheme.formType == 2) {
						initFormValue = await this.HTTP_GET({
							url: `/custmerform/data/${this.formId}`,
							params: this.loadParams,
							errorTips: this.$t('加载表单失败')
						})
					} else {
						var mainTable = this.scheme.db.find(t => t.type == 'main')
						initFormValue[mainTable.name] = [this.initFormValue]
						//add by kyle on 14 Map 2025 视图表单，如果initFormValue为空，则请求API获取数据
						if (this.initFormValue == undefined) {
							initFormValue = await this.HTTP_GET({
								url: `/custmerform/data/${this.formId}`,
								params: this.loadParams,
								errorTips: this.$t('加载表单失败')
							})
						}
						if (this.scheme.db.length > 1) {
							const mainDataQuery = {}
							this.scheme.db.forEach(db => {
								mainDataQuery[db.relationField] = this.initFormValue[db.relationField]
							})
							const childDatas = await this.HTTP_GET({
								url: `/custmerform/viewdata/${this.formId}`,
								params: {
									mainData: JSON.stringify(mainDataQuery)
								},
								errorTips: this.$t('加载表单失败')
							})
							initFormValue = {
								...initFormValue,
								...childDatas
							}
						}
					}

					if (this.hasFormData(initFormValue)) {
						this.isFormUpdate = true
					} else {
						this.isFormUpdate = false
					}
				} else if (this.isSystem || this.isNotLoadData) {
					initFormValue = this.initFormValue
				}
				this.formData = await this.learun_form_getFormData(this.scheme, this.isFormUpdate ? initFormValue : {},
					this.isFormUpdate)
				if (this.isFormUpdate) {
					this.oldFormData = this.COPY(this.formData)
				} else {
					this.oldFormData = null
				}

				this.SET_DATA(`learun_form_data_${this.formId}`, this.formData)

				// 加载选择数据
				for (let j = 0, jlen = this.scheme.formInfo.components.length; j < jlen; j++) {
					const component = this.scheme.formInfo.components[j]
					if (!this.isAuth || this.GET_FORM_LOOK_AUTH(component.id, this.moduleId)) {
						if (component.type == 'gridtable') {
							for (let componentItem of component.children) {
								await this.learun_form_fetchDataSource(componentItem, this.formData)
							}
						} else {
							await this.learun_form_fetchDataSource(component, this.formData)
						}
					}
				}
				this.tabList = this.toShowTabs(this.scheme.formInfo.components)
				/*设置校验规则*/
				this.setRules(this.scheme.formInfo.components)

				// 赋值前脚本
				await this.beforeEvent()

				this.ready = true
				this.$emit('ready')

			},

			// 给表单赋值
			async setForm(data, isUpdate) {
				this.ready = false
				this.isFormUpdate = isUpdate
				await this.init(true, data)
			},

			toShowTabs(data) { // 转化成表单显示数据结构
				const tabList = this.COPY(data)
				const components = []
				const pMap = {}
				const showTabList = []
				const hideComponentList = []
				const componentList = []
				tabList.forEach(component => {
					if (!this.isAuth || this.GET_FORM_LOOK_AUTH(component.id, this.moduleId) || component.config
						.display == false) {
						components.push(component)
						component.key = component.id

						if (this.isAuth && !['gridtable', 'card', 'collapse', 'tab'].includes(component.type)) {
							component.config.edit = this.GET_FORM_EDIT_AUTH(component.id, this.moduleId)
						}


						if (['gridtable'].includes(component.type)) {
							if (this.isAuth) {
								if (component.config.isAddBtn != false) {
									component.config.isAddBtn = this.GET_FORM_EDIT_AUTH(component.id + '_add', this
										.moduleId)
								}

								if (component.config.isRemoveBtn != false) {
									component.config.isRemoveBtn = this.GET_FORM_EDIT_AUTH(component.id +
										'_remove', this.moduleId)
								}
							}



							const children = []
							component.children.forEach(ccomponent => {
								if (!this.isAuth || this.GET_FORMTABLE_LOOK_AUTH(component
										.id, ccomponent.id, this.moduleId)) {
									ccomponent.key = ccomponent.id
									if (this.isAuth) {
										ccomponent.config.edit = this.GET_FORMTABLE_EDIT_AUTH(
											component.id, ccomponent.id, this.moduleId)
									}

									components.push(ccomponent)
									children.push(ccomponent)
								}
							})
							component.children = children
						} else if (['card'].includes(component.type)) {
							if (!pMap[component.id]) {
								pMap[component.id] = []
							}
							component.children = pMap[component.id]
						} else if (["collapse", "tabs"].includes(component.type)) {
							component.children = this.COPY(component.config.groups)
							component.children.forEach((tab) => {
								const tabProp = `${component.id}.${tab.i}`;
								if (!pMap[tabProp]) {
									pMap[tabProp] = []
								}
								tab.children = pMap[tabProp]
							});
						}
						// else if (["collapse", "tabs"].includes(component.type)) {
						// 	component.config.groups.forEach((tab, index) => {
						// 		const tabProp = `${component.id}.${index}`;
						// 		if (!pMap[tabProp]) {
						// 			pMap[tabProp] = []
						// 		}
						// 		tab.children = pMap[tabProp]
						// 	});
						// }


						// if (['card', 'collapse', 'tabs'].includes(component.ptype)) {
						// 	if (!pMap[component.pid]) {
						// 		pMap[component.pid] = []
						// 	}
						// 	pMap[component.pid].push(component)

						// 	delete component.pid
						// 	delete component.ptype
						// }
						if (component.containerId && component.containerId !== 'form') {

							if (component.groupId) { // tabs, collapse
								if (!pMap[`${component.containerId}.${component.groupId}`]) {
									pMap[`${component.containerId}.${component.groupId}`] = []
								}
								pMap[`${component.containerId}.${component.groupId}`].push(component)
							} else {
								if (!pMap[component.containerId]) {
									pMap[component.containerId] = []
								}
								pMap[component.containerId].push(component)
							}
						} else {
							if (component.display != false) {
								componentList.push(component)
							} else {
								hideComponentList.push(component)
							}
						}
					}
				})

				if (componentList.length > 0) {
					showTabList.push(...componentList)
				}
				showTabList.push(...hideComponentList)
				this.toFilterCard(showTabList)
				this.components = components


				return showTabList
			},
			toFilterCard(tabList) { // 过滤掉空卡片
				const componentList = []
				tabList.forEach(component => {
					if (['card'].includes(component.type)) {
						component.children = this.toFilterCardSub(component.children || [])
						if (component.children.length > 0) {
							componentList.push(component)
						}
					} else {
						componentList.push(component)
					}
				})

				tabList = componentList
			},
			toFilterCardSub(cardList) {
				const list = []
				cardList.forEach(cardComponent => {
					if (cardComponent.type != 'card') {
						list.push(cardComponent)
					} else {
						cardComponent.children = this.toFilterCardSub(cardComponent.children)
						if (cardComponent.children.length > 0) {
							list.push(cardComponent)
						}
					}
				})
				return list
			},

			// 设置校验规则
			setRules(components) {
				const rules = {}
				for (let j = 0, jlen = components.length; j < jlen; j++) {
					const component = components[j]
					rules[component.id] = rules[component.id] || {
						rules: []
					}

					if (component.config.required) {
						rules[component.id].rules.push({
							required: true,
							errorMessage: `${this.$t('请输入')}${component.config.label}`
						})
					}

					if (component.config.patterns && component.config.patterns.length > 0) {
						rules[component.id] = rules[component.id] || {
							rules: []
						}
						component.config.patterns.forEach(pattern => {
							if (pattern.reg) {
								let reg = pattern.reg.substring(1, pattern.reg.length - 1)
								rules[component.id].rules.push({
									pattern: reg,
									errorMessage: pattern.msg
								})
							}

						})
					}

				}
				this.rules = rules
			},
			// 本方法由调用方使用 $refs 的方式调用
			// 表单校验
			async validateTable() {
				if (this.$refs.gridtable) {
					for (let i = 0, len = this.$refs.gridtable.length; i < len; i++) {
						const res = await this.$refs.gridtable[i].validate()
						if (!res) {
							return false
						}
					}
				}
				return true
			},
			async validate() {
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm)
				const tableRes = await this.validateTable()
				if (err || !tableRes) {
					return false
				} else {
					// 校验后执行脚本
					return await this.afterValidateEvent()
				}
			},

			// 本方法由调用方使用 $refs 的方式调用
			// 获取表单值
			async getFormValue() {
				const formData = this.COPY(this.formData)
				return this.learun_form_convertToPostData(formData, this.components, this.isFormUpdate)
			},

			// 保存表单数据
			async saveForm(formId, pkey, pkeyValue, isWf) {
				const formData = await this.getFormValue()
				if (isWf) {
					formData[pkey] = pkeyValue
				}

				const diffFormData = this.getDiffFormData()
				const postData = {
					schemeId: formId,
					isUpdate: this.isFormUpdate,
					pkey: pkey,
					pkeyValue: pkeyValue,
					data: JSON.stringify(formData),
					diffFormData: diffFormData
				}

				const res = await this.HTTP_POST({
					url: '/custmerform/data',
					data: postData,
					errorTips: this.$t('表单提交保存失败')
				})

				if (!res) {
					return false
				}
				this.isFormUpdate = true

				return await this.afterSaveEvent(res)
			},

			// 表单数据值改变后执行脚本
			afterChangeDataEvent(component, data) {
				// 具体某一个组件值改变后执行脚本
				const changeEvent = this.GET_FUNCTION(component.changeCode)
				if (changeEvent.res) {
					changeEvent.fn(this.getEventParams({
						component,
						data
					}))
				}

				// 表单数据改变后执行脚本
				const changeDataEvent = this.GET_FUNCTION(this.scheme.formInfo.form.codeDataChange)

				if (changeDataEvent.res) {
					changeDataEvent.fn(this.getEventParams({
						component,
						data
					}))
				}
			},
			// 子表数据改变脚本
			afterTableChangeDataEvent(component, data, tableProp, tableIndex, tableRow, myTable) {
				const changeDataEvent = this.GET_FUNCTION(myTable.changeDataEvent)
				if (changeDataEvent.res) {
					changeDataEvent.fn(this.getEventParams({
						component,
						data,
						tableProp,
						tableIndex,
						tableRow
					}))
				}
			},

			// 表单加载前执行脚本
			beforeEvent() {
				return new Promise((resolve) => {
					const beforeSetData = this.GET_FUNCTION(this.scheme.formInfo.form.codeInit)
					if (beforeSetData.res) {
						const res = beforeSetData.fn(this.getEventParams({
							callback: (res) => {
								// 兼容异步回调方法
								resolve(res)
							}
						}))
						if (res != 'callback') {
							resolve(res)
						}
					} else {
						resolve(true)
					}
				})
			},

			// 校验后执行脚本
			afterValidateEvent() {
				return new Promise((resolve) => {
					const {
						res,
						fn
					} = this.GET_FUNCTION(this.scheme.formInfo.form.codeBeforeSave)
					if (res) {
						const fnRes = fn(this.getEventParams({
							callback: (rs) => {
								// 兼容异步回调方法
								resolve(rs)
							}
						}))
						if (fnRes == undefined) { // 如果脚本没有返回值就默认为true
							resolve(true);
						} else if (fnRes != 'callback') {
							resolve(fnRes)
						}
					} else {
						resolve(true)
					}
				})
			},
			// 保存后执行脚本
			afterSaveEvent(formData) {
				return new Promise((resolve) => {
					const {
						res,
						fn
					} = this.GET_FUNCTION(this.scheme.formInfo.form.codeAfterSave)
					if (res) {
						this.formData = this.COPY(formData)
						const fnRes = fn(this.getEventParams({
							callback: (rs) => {
								// 兼容异步回调方法
								resolve(rs)
							}
						}))

						if (fnRes != 'callback') {
							resolve(fnRes)
						}
					} else {
						resolve(true)
					}
				})
			},

			// 获取脚本参数
			getEventParams({
				component,
				data,
				tableProp,
				tableIndex,
				tableRow,
				callback
			}) {
				const loginUser = this.GET_GLOBAL('loginUser')
				const params = {
					prop: component ? component.id : undefined,
					data: data,
					tableProp: tableProp,
					tableIndex: tableIndex,
					isUpdate: this.isFormUpdate,
					get: this.getValue,
					set: this.setValue,
					getLabel: this.getLabel,
					setRequired: this.setRequired,
					setDisabled: this.setDisabled,
					setHide: this.setHide,
					httpGet: this.HTTP_GET,
					httpPost: this.HTTP_POST,
					httpDelete: this.HTTP_DELETE,
					httpPut: this.HTTP_PUT,
					loading: this.LOADING,
					hideLoading: this.HIDE_LOADING,
					message: this.TOAST,
					loginUser: loginUser,
					callback: callback,
					getSourceData: this.getSourceData,
					getDateNow: this.getDateNow
				}

				if (tableRow) {
					params.getLabel = (id) => {
						return this.getLabel(id, tableRow);
					};
				}
				return params;

			},

			async getSourceData(code, params) {
				return await this.FETCH_DATASOURCE(code, params)
			},

			getDateNow(format) {
				return this.DATENOW(format)
			},
			async addTableRow(myTable) {
				this.LOADING(this.$t('添加行中...'))
				let point = {}
				const currentUser = this.GET_GLOBAL('loginUser')
				for (let col of myTable.children) {
					if (!['guid', 'createTime', 'modifyTime', 'company', 'department', 'createUser', 'modifyUser',
							'encode'
						].includes(col.type)) {
						point[col.id] = col.defaultValue || ''

						if (["checkbox", "radio", "select", "selectMultiple", "treeSelect", "inputLayer",
								"buttonLayer",
								'companySelect',
								'departmentSelect', 'userSelect'
							].includes(col.type) && point[col.id]) {
							await this.clearSubValue(col.id, myTable.children, point)
						}
					} else {
						point[col.id] = ''

						switch (col.type) {
							case 'encode':
								if (col.config.code) {
									point[col.id] = `learun_code_${col.config.code}|${this.GUID()}`
								} else {
									point[col.id] = this.$t('未设置单据编码')
								}

							case 'companySelect':
								if (col.config.isLogin) {
									point[col.id] = currentUser.f_CompanyId
								}
								break
							case 'departmentSelect':
								if (col.config.isLogin) {
									point[col.id] = currentUser.f_DepartmentId
									await this.learun_form_getDepartment(point[col.id])
								}
								break
							case 'userSelect':
								if (col.config.isLogin) {
									point[col.id] = currentUser.f_UserId
									await this.learun_form_getUser(point[col.id])
								}
								break
							case 'guid':
								point[col.id] = this.GUID()
								break
							case 'company':
								point[col.id] = currentUser.f_CompanyId
								break
							case 'department':
								point[col.id] = currentUser.f_DepartmentId
								await this.learun_form_getDepartment(point[col.id])
								break
							case 'createUser':
								point[col.id] = currentUser.f_UserId
								await this.learun_form_getUser(point[col.id])
								break
							case 'modifyUser':
								point[col.id] = currentUser.f_UserId
								await this.learun_form_getUser(point[col.id])
								break
							case 'createTime':
								point[col.id] = this.DATENOW()
								break
							case 'modifyTime':
								point[col.id] = this.DATENOW()
								break
						}
					}

				}

				point.abledList = []
				point.disabled = false
				point.hasNoDeleteBtn = false
				point.learun_table_flag = 'add'
				this.formData[myTable.id].push(point)

				this.HIDE_LOADING()
			},
			deleteTableRow(event, myTable) {
				let data = this.COPY(this.formData)[myTable.id]
				let newIndex = event.index;
				if (this.isChildTableVirtualDel) {
					let delFlagIndx = 0;
					for (let i = 0; i < data.length; i++) {
						if (data[i].learun_table_flag === 'delete') {
							delFlagIndx += 1
						}
						if (i - delFlagIndx === event.index) {
							newIndex = i
							break
						}

					}
				}
				const row = data[newIndex]
				if (!this.isChildTableVirtualDel || row.learun_table_flag === 'add') {
					data.splice(newIndex, 1)
				}
				row.learun_table_flag = 'delete'
				this.$set(this.formData, myTable.id, data)
				// this.formData[myTable.id].splice(newIndex, 1)
			},
			async rowChange({
				index,
				rowData,
				prop,
				data,
				component
			}, myTable) {
				// set(this.formData, `${myTable.id}.${index}`, rowData)
				console.log(785, index, rowData, prop, data, component)
				// 根据右侧赋值字段给表单赋值地图信息
				if (component.type == "inputMap") {
					if (component.bindaddr) {
						if (data) {
							set(this.formData, `${myTable.id}.${index}.${component.bindaddr}`, data.address) //设置绑定地址
						} else {
							set(this.formData, `${myTable.id}.${index}.${component.bindaddr}`, '') //设置绑定地址
						}
					}
					if (component.bindaddrpoint) {
						if (data) {
							set(this.formData, `${myTable.id}.${index}.${component.bindaddrpoint}`,
								`${data.lng},${data.lat}`) //设置绑定经纬度
						} else {
							set(this.formData, `${myTable.id}.${index}.${component.bindaddrpoint}`, '') //设置绑定地址
						}
					}
				}
				// 弹窗赋值
				if (component.type == 'inputLayer' || component.type == 'buttonLayer') {
					component.columns.forEach(col => {
						if (col.valueKey) {
							if (data) {
								set(this.formData, `${myTable.id}.${index}.${col.valueKey}`, data[col.id])
							} else {
								set(this.formData, `${myTable.id}.${index}.${col.valueKey}`, undefined)
							}
						}
					})
				}

				if (["checkbox", "radio", "select", "selectMultiple", "treeSelect", "inputLayer", "buttonLayer",
						'companySelect',
						'departmentSelect', 'userSelect'
					].includes(component.type)) {
					await this.clearSubValue(component.id, this.components.find(t => t.id == myTable.id)
						.children, rowData)
				}

				this.$nextTick(() => {
					this.afterTableChangeDataEvent(component, data, myTable.id, index, rowData, myTable)
					this.$emit('myAfterChangeDataEvent', {
						component,
						data,
						rowData: rowData,
						table: myTable.id,
						tableIndex: index
					})
				})

			},
			// 组件数据值改变
			async handleChange({
				data,
				component
			}) {
				if (["checkbox", "radio", "select", "selectMultiple", "treeSelect", "inputLayer", "buttonLayer",
						'companySelect',
						'departmentSelect', 'userSelect'
					].includes(component.type)) {
					await this.clearSubValue(component.id)
				}


				// 根据右侧赋值字段给表单赋值地图信息
				if (component.type == "inputMap") {
					if (component.config.bindaddr) {
						if (data) {
							set(this.formData, component.config.bindaddr, data.address) //设置绑定地址
						} else {
							set(this.formData, component.config.bindaddr, '') //设置绑定地址
						}
					}
					if (component.config.bindaddrpoint) {
						if (data) {
							set(this.formData, component.config.bindaddrpoint, `${data.lng},${data.lat}`) //设置绑定经纬度
						} else {
							set(this.formData, component.config.bindaddrpoint, '') //设置绑定地址
						}
					}
				}
				// 弹窗赋值
				if (component.type == 'inputLayer' || component.type == 'buttonLayer') {
					component.config.columns.forEach(col => {
						if (col.valueKey) {
							if (data) {
								this.setValue({
									path: col.valueKey,
									value: data[col.prop]
								})
								this.EMIT(`valueChange_${col.valueKey}`, data[col.prop])
							} else {
								this.setValue({
									path: col.valueKey,
									value: undefined
								})
								this.EMIT(`valueChange_${col.valueKey}`, undefined)
							}
						}
					})
				}
				// 下拉框赋值
				if (component.type == 'select') {
					if (component.config.assignment?.length) {
						component.config.assignment.forEach((item) => {
							if (item.valueKey && item.prop) {
								if (data) {
									this.setValue({
										path: item.valueKey,
										value: data[item.prop]
									})
								} else {
									this.setValue({
										path: item.valueKey,
										value: undefined
									})
								}
							}
						})
					}
				}



				this.afterChangeDataEvent(component, data)
				this.$emit('myAfterChangeDataEvent', {
					component,
					data
				})
			},

			async clearSubValue(upProp, list, data) {
				const components = list || this.components
				const formData = data || this.formData
				for (const component of components) {
					if (component.config.upCtrl == upProp) {
						// 获取数据值
						await this.learun_form_fetchDataSource(component, formData, this.formData)
						if (formData[component.id]) {
							set(formData, component.id, undefined)
						} else if (this.formData[component.id]) {
							set(this.formData, component.id, undefined)
						}

						//this.setValue({path:component.id,value:undefined})
						this.$set(component, 'key', this.GUID())
						await this.clearSubValue(component.id, list, data)
					}
				}
			},

			setRequired(id, isRequired = true) {
				const rule = this.rules[id]
				const component = this.components.find(t => t.id == id)
				if (rule) {
					const ruleIndex = rule.rules.findIndex(t => t.required)
					if (ruleIndex == -1 && isRequired) {
						rule.rules.push({
							required: true,
							errorMessage: `${this.$t('请输入')}${component.config.label}`
						})
						component.config.required = true;
					} else if (ruleIndex != -1 && !isRequired) {
						rule.rules.splice(ruleIndex, 1)
						component.config.required = false;
						this.$refs.myForm.clearValidate(component.id);
					}
					component.key = this.GUID()
					component.key = component.id;
				}
			},
			setDisabled(id, isDisabled = true) {
				const component = this.components.find(t => t.id == id)
				if (component) {
					component.config.disabled = isDisabled
					component.key = this.GUID()
				}
			},
			setHide(id, isHide = true) {
				const component = this.components.find(t => t.id == id)
				if (component) {
					component.config.display = !isHide
					component.key = this.GUID()
				}
			},

			getLabel(id, formData) {
				if (formData == undefined || formData == null) {
					formData = this.formData
				}
				if (id.indexOf('.') != -1) { // 子表获取
					const propList = id.split('.')
					if (propList.length != 3) {
						return ''
					}
					id = propList[2]
					formData = formData[propList[0]][propList[1]]
				}

				const value = formData[id]
				const component = this.components.find(t => t.id == id)
				return this.learun_form_displayText(value, component, formData, this.formData)
			},

			// 设置表单数据的方法
			setValue({
				path,
				value,
				type
			}) {
				if (type == 'addTable') {
					this.formData[path].push(value)
				} else if (type == 'deleteTable') {
					const paths = path.split('.')
					this.formData[paths[0]].splice(paths[1], 1)
				} else {
					set(this.formData, path, value)
				}
			},
			// 获取表单数据的方法
			getValue(path) {
				return get(this.formData, path)
			},

			// 按钮点击
			async handleBtnClick(component) {
				if (component.clickEvent) {
					await component.clickEvent(this.getEventParams({
						component
					}))
				} else {
					const {
						res,
						fn
					} = this.GET_FUNCTION(component.clickCode)
					if (res) {
						fn(this.getEventParams({
							component
						}))
					}
				}
			},

			// 比对数据修改
			getDiffFormData() {
				const diffFormData = []
				if (this.oldFormData == null) {
					return diffFormData
				}
				const currentUser = this.GET_GLOBAL('loginUser')
				for (let key in this.oldFormData) {
					const componentM = this.components.find(t => t.id == key)
					const oldValue = this.oldFormData[key]
					const newValue = this.formData[key]

					if (componentM && !['gridtable', 'divider', 'upload', 'uploadimg', 'company',
							'department', 'createUser', 'modifyUser', 'createTime', 'modifyTime', 'viewTable', 'card',
							'btn'
						].includes(componentM.type) &&
						oldValue != newValue) {
						const dataPoint = { // 数据变动
							table_Name: componentM.table,
							tableField: componentM.field,
							formColumn: componentM.label,
							oldVal: oldValue,
							newVal: newValue,
							oldShowVal: '',
							newShowVal: '',
							creator: currentUser.f_RealName + '/' + currentUser.f_Account
						}
						dataPoint.oldShowVal = this.getLabel(key, this.oldFormData)
						dataPoint.newShowVal = this.getLabel(key, this.formData)

						diffFormData.push(dataPoint)
					}
				}
				return diffFormData
			},

			// 判断是否有表单值
			hasFormData(data) {
				if (!data) {
					return false
				}

				for (let key in data) {
					if (data[key].length > 0) {
						return true
					}
				}

				return false

			},

			// 设置组件属性
			setComponent(id, data) {
				const component = this.components.find(t => t.id == id)
				if (component) {
					for (let key in data) {
						component[key] = data[key]
						component.key = this.GUID()
					}
				}
			},
			afterPasswordConfirm(res) {
				this.passwordDialogVisible = false
				this.afterPassword(res);
			}
		}
	}
</script>


<style lang="scss" scoped>
	.learun-customform {
		box-sizing: border-box;
		position: relative;
		//height: 100%;
		//width: 100%;
		background-color: #fff;
		// padding-top: 34px;

		&__noTab {
			padding-top: 0;
		}

		.learun-customform-tabbar {
			background-color: #fff;
			width: 100%;
			height: 34px;
		}

		.learun-customform-tabcontent {
			box-sizing: border-box;
			position: relative;
			height: 100%;
			width: 100%;
		}
	}
</style>