<template>
    <!-- 当页面准备好时，渲染页面内容 -->
    <view v-if="ready" class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-top':'8px'}">
        <!-- uni-forms 表单组件，绑定表单数据和验证规则 -->
        <uni-forms :modelValue="formData"  ref="myForm" :rules="formRules" label-width="88">
            <!-- 循环渲染表单中的每个班次信息 -->
            <view v-for="item in list" :key="item" class="learun-panel" style="margin-top:8px;">
                <uni-list class="learun-forms-list">
                    <!-- 班次名称输入项 -->
                    <uni-list-item>
                        <template v-slot:body>
                            <uni-forms-item required :name="`name${item}`" :label="$t('班次名称')">
                                <!-- 输入框，用于输入班次名称 -->
                                <uni-easyinput :placeholder="this.$t('请输入')" placeholderStyle="text-align: right;margin-right:4px;"
                                               :inputBorder="false" v-model="formData[`name${item}`]" />
                            </uni-forms-item>
                        </template>
                    </uni-list-item>
                    <!-- 班次时间选择项 -->
                    <uni-list-item>
                        <template v-slot:body>
                            <uni-forms-item required :name="`time${item}`" :label="$t('班次时间')">
                                <!-- 自定义时间选择器，支持选择时间范围 -->
                                <learun-time-picker isRange style="width: 100%;" v-model="formData[`time${item}`]" />
                            </uni-forms-item>
                        </template>
                    </uni-list-item>
                </uni-list>
            </view>
        </uni-forms>

        <!-- 底部操作按钮区域 -->
        <view class="learun-bottom-btns">
            <!-- 保存按钮，点击触发保存操作 -->
            <button @click.stop="handleSave"  type="primary" >{{$t('保存')}}</button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // 表单验证规则
            formRules: {
                // 第一个班次的名称验证规则，为必填项
                name0: {
                    rules: [{
                        required: true,
                        errorMessage: this.$t('请输入')
                    }]
                },
                // 第一个班次的时间验证规则，为必填项
                time0: {
                    rules: [{
                        required: true,
                        errorMessage: this.$t('请选择')
                    }]
                },
                // 第二个班次的名称验证规则，为必填项
                name1: {
                    rules: [{
                        required: true,
                        errorMessage: this.$t('请输入')
                    }]
                },
                // 第二个班次的时间验证规则，为必填项
                time1: {
                    rules: [{
                        required: true,
                        errorMessage: this.$t('请选择')
                    }]
                },
                // 第三个班次的名称验证规则，为必填项
                name2: {
                    rules: [{
                        required: true,
                        errorMessage: this.$t('请输入')
                    }]
                },
                // 第三个班次的时间验证规则，为必填项
                time2: {
                    rules: [{
                        required: true,
                        errorMessage: this.$t('请选择')
                    }]
                },
            },
            // 表单数据
            formData: {
                name0: '',
                time0: '',
                name1: '',
                time1: '',
                name2: '',
                time2: '',
            },
            // 班次列表，存储班次的索引
            list: [],
            // 页面是否准备好的标志
            ready: false,
            // 是否为弹窗层模式的标志
            isLayer: true,
        }
    },
    // 页面返回时，移除事件监听
    onBackPress() {
        this.OFF('learun-custom-layer-picker')
    },
    // 页面卸载时，移除事件监听
    onUnload() {
		uni.setNavigationBarTitle({
			title: this.$t("班次时间")
		})
        this.OFF('learun-custom-layer-picker')
    },
    async onLoad() {
        // 检查页面是否可以启动
        if(await this.PAGE_LAUNCH()){
            // 初始化页面
            this.init()
        }
    },
    methods: {
        init() {
            // 标记页面未准备好
            this.ready = false
            // 获取页面传递的参数
            const {
                params,
                value
            } = this.GET_PARAM()

            // 解析传入的班次值
            let values = [];
            if (value) {
                values = value.split(',')
            }

            // 获取班次数量
            const count = parseInt(params.count)
            const list = [];
            // 生成班次列表
            for (let i = 0; i < count; i++) {
                list.push(i)
                if (values.length > i) {
                    // 解析每个班次的值
                    let valueItem = values[i];
                    let valueItems = valueItem.split('|');
                    // 填充表单数据
                    this.formData['name' + i] = valueItems[0]
                    this.formData['time' + i] = valueItems[1]
                }
            }

            console.log(values, this.formData);

            // 更新班次列表
            this.list = list;
            // 标记页面准备好
            this.ready = true;
        },
        async validate() {
            // 验证表单数据
            const {
                err
            } = await this.VALIDATEFORM(this.$refs.myForm)
            if (err) {
                return false
            } else {
                return true
            }
        },
        async handleSave() {
            // 验证表单数据
            if (await this.validate()) {
                let values = []
                // 收集表单数据
                for (let i = 0; i < this.list.length; i++) {
                    let point = `${this.formData['name' + i]}|${this.formData['time' + i]}`
                    values.push(point)

                    // 清空表单数据
                    this.formData['name' + i] = ''
                    this.formData['time' + i] = ''
                }

                // 触发自定义事件，传递保存的数据
                this.EMIT('learun-custom-layer-picker', {
                    value: String(values)
                })
                // 标记页面未准备好
                this.ready = false

                // 返回上一页
                this.NAV_BACK()

                // console.log(this.formData, values, 'values');
            }
        }
    }
}
</script>

<style>

</style>