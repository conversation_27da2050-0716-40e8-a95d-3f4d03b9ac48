<template>
	<view class="learun-edit-table-item" >
		<view class="learun-edit-table-item__title" @click.stop="handleClickTitle" >
			<view>
				<uni-icons class="lefticon"  :type="isOpen? 'bottom':'right'" size="14" color="#c0c4cc" ></uni-icons>
				<text>{{$t("第")}}{{num}}{{$t("行")}}</text>
			</view>
			<view v-if="hasDeleteBtn" class="delete-btn" @click.stop="handleDelete" ><text>{{$t('删除')}}</text></view>
		</view>
		<view class="learun-edit-table-item__content" v-show="isOpen" >
			<uni-forms
				:modelValue="myRowData" 
				label-position="top" 
				:label-width="320"
				:rules="rules"
				
				ref="myForm"
				>
				<learun-customform-item 
					v-for="(col) in columns"
					:key="col.key"
					:value="getValue(col.id)"
					@input="setValue"
					@change="handleChange"
					:formId="formId"
					:tableId="tableId"
					:tableIndex="num - 1"
					:editMode="isEdit(col)"
					
					:component="col" 
					:getDataSource="getDataSource" 
					
				/>
			</uni-forms>
		</view>
	</view>
</template>

<script>
	import set from "lodash/set"
	import get from "lodash/get"
	export default {
		name:'learun-edit-table-item',
		props:{
			columns: {
				type: Array,
				default:()=>[] 
			},
			num:Number,
			rowData:{},
			getDataSource:Function,
			editMode: {
				type: Boolean, 
				default: true ,
			},
			hasRemoveBtn: {
				type: Boolean, 
				default: true ,
			},
			formId:String,
			tableId:String
		},
		created(){
			// this.myRowData = this.COPY(this.rowData)
		},
		
		data(){
			return {
				// myRowData:{},
				isOpen:true,
			}
		},
		computed:{
			myRowData() {
				return this.rowData
			},
			hasDeleteBtn(){
				if(this.getValue('hasNoDeleteBtn')){
					return false
				}
				else if(this.editMode && this.hasRemoveBtn){
					return true
				}
				else{
					return false
				}
			},
			rules(){
				const rules = {}
				for(let j = 0,len = this.columns.length;j < len; j++ ){
					const component = this.columns[j]
					if(component.config.required){
						rules[component.id] =rules[component.id] || {rules:[]}
						rules[component.id].rules.push({required:true,errorMessage: `${this.$t('请输入')}${component.config.label}`})
					}
					if(component.config.patterns && component.config.patterns.length > 0){
						rules[component.id] = rules[component.id] || {rules:[]}
						component.config.patterns.forEach(pattern =>{
							if(pattern.reg){
								let reg = pattern.reg.substring(1,pattern.reg.length-1)								
								rules[component.id].rules.push({ pattern:reg, errorMessage: pattern.msg })
							}
						})
					}
				}
				return rules
			},
		},
		methods:{
			
			isEdit(col){
				//console.log(this.rowData.disabled,this.rowData.abledList,'this.rowData.abledList')
				if(this.editMode && (!this.rowData.disabled || this.getValue('abledList').indexOf(col.id) > -1 )){
					return true
				}
				return false
			},
			handleClickTitle(){
				this.isOpen = !this.isOpen
			},
			handleDelete(){
				this.$emit('deleteRow')
			},
			handleChange({data,component}){
				this.$emit('change',{index:this.num - 1,rowData:this.myRowData,data:data,prop:component.id,component:component})
			},
			
			
			// 设置表单数据的方法
			setValue({path, value}) {		
				// console.log(this.myRowData,path, value,'setValue');
			  set(this.myRowData, path, value)
			},
			
			// 获取表单数据的方法
			getValue(path) {
				// console.log(path,'getValue');
			  return get(this.myRowData, path)
			},
			async validate(){
				const {err} =await this.VALIDATEFORM(this.$refs.myForm)
				if(err){
					return false
				}
				return true
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	.learun-edit-table-item{
		
		&__title{
			padding:0 8px;
			font-size: 12px;
			color: $uni-base-color;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 32px;
			background-color: $uni-info-light;
			box-sizing: border-box;
			
			.lefticon{
				margin-right: 4px;
			}
			
			.delete-btn{
				color: $uni-error;
			}
		}
		
		&__content{
			padding: 16px 8px 0 8px;
			background-color: #fdfdfd;
		}
	}
</style>
