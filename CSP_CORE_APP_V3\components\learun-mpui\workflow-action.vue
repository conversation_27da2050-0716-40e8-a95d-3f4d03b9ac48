<template>
  <view class="wf-action lr-hidden">
    <!-- 催办/撤销，适用于打开我的任务 -->
    <view v-if="canUrge || canRevoke" class="form-action padding bg-white margin-top lr-hidden" style="padding-top: 0;">
      <l-button v-if="canUrge" @click="$emit('action', 'urge')" size="lg" color="orange" class="block margin-top" block>
        {{$t("催办审核")}}
      </l-button>
      <l-button
        v-if="canRevoke"
        @click="$emit('action', 'revoke')"
        size="lg"
        color="red"
        class="block margin-top"
        block
      >
        {{$t("撤销流程")}}
      </l-button>
    </view>

    <!-- 审批按钮列表，适用于打开待办任务 -->
    <view
      v-if="buttonList.length > 0"
      class="form-action padding bg-white margin-top lr-hidden"
      style="padding-top: 0;"
    >
      <l-button
        v-for="button of buttonList"
        @click="taskAction(button)"
        :key="button.id"
        :color="button.color"
        class="block margin-top"
        size="lg"
        block
      >
        {{ button.name }}
      </l-button>
    </view>

    <!-- 子流程草稿/提交按钮 -->
    <view
      v-if="['child', 'create', 'again'].includes(type)"
      class="form-action padding bg-white margin-top lr-hidden"
      style="padding-top: 0;"
    >
      <l-button @click="$emit('action', 'draft')" size="lg" color="orange" class="block margin-top" block>
        {{$t('保存草稿')}}
      </l-button>
      <l-button @click="$emit('action', 'submit')" size="lg" color="green" class="block margin-top" block>
        {{$t('提交流程')}}
      </l-button>
    </view>

    <!-- 已阅按钮，传阅模式使用 -->
    <view v-if="type === 'refer'" class="form-action padding bg-white margin-top lr-hidden" style="padding-top: 0;">
      <l-button @click="$emit('action', 'refer')" color="blue" class="block margin-top" size="lg" block>
        {{$t('标记已阅')}}
      </l-button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'learun-workflow-action',

  props: {
    currentNode: { default: () => ({}) },
    currentTask: { default: () => ({}) },
    type: {}
  },

  computed: {
    // 按钮列表（通过 isSign 判断可否加签；如果可以加签，则额外添加一个加签按钮）
    buttonList() {
      if (this.openFrom !== 'pre') {
        return []
      }

      const btnList = (this.currentNode.btnList || []).filter(t => Number(t.isHide) !== 1)
      if (this.canMultipleSign) {
        btnList.push({
          id: '__sign__',
          code: '__sign__',
          name: this.$t('加签')
        })
      }

      return btnList.map(t => ({ ...t, color: { agree: 'green', disagree: 'red', end: 'red' }[t.code] || 'blue' }))
    },

    // 获取表单页是从任务列表哪一栏打开，my=我的，pre=待办，maked=已办
    openFrom() {
      return this.currentTask.mark
    },

    // 是否显示催办按钮（仅限我的任务，满足以下条件则可以催办：已开始、未结束、未作废）
    canUrge() {
      if (this.openFrom !== 'my') {
        return false
      }

      return !this.currentTask.F_IsFinished && this.currentTask.F_EnabledMark !== 3
    },

    // 是否显示撤销按钮（仅限我的任务，满足以下条件则可以撤销：未开始）
    canRevoke() {
      if (this.openFrom !== 'my') {
        return false
      }

      return !this.currentTask.F_IsStart
    },

    // 是否允许加签（仅限待办任务，流程中配置允许加签则）
    canMultipleSign() {
      if (this.openFrom !== 'pre') {
        return false
      }

      return Number(this.currentNode.isSign) === 1
    }
  },

  methods: {
    // 点击审批按钮
    taskAction(buttonItem) {
      this.$emit('audit', buttonItem)
    }
  }
}
</script>
