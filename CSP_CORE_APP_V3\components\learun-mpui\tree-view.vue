<template>
	<view class="l-tree-view">
		<learun-tree-item :loadData="loadData"  @select="handleSelect" :data="item" v-for="(item,index) in myOptions" :key="index" >
		</learun-tree-item>
	</view>
</template>

<script>
	export default {
		name:'learun-tree-view',
		props:{
			labelKey:{
				type:String,
				default:'label'
			},
			valueKey:{
				type:String,
				default:'value'
			},
			idKey:{
				type:String,
				default:'id'
			},
			pIdKey:{
				type:String,
				default:'pid'
			},
			options:{
				type:Array,
				default:()=>[]
			},
			isTreeData:Boolean,
			loadData:Function
		},
		data(){
			return {
			}
		},
		computed:{
			myOptions(){
				if(this.isTreeData){
					return this.options
				}
				else{
					return this.TOTREE(this.options,this.idKey,this.pIdKey,this.valueKey,this.labelKey)
				}
			}
		},
		methods:{
			handleSelect(item){
				this.$emit('change',item)
			}
		}
	}
</script>
<style lang="scss" scoped >
	.l-tree-view{
		padding: 8px 8px 0;
		background-color: #fff;
	}
</style>
