<template>
	<uni-data-checkbox v-model="myValue" :localdata="myOptions"></uni-data-checkbox>
</template>

<script>
	export default {
		name:'learun-radio',
		props:{
			value:{
				default:null
			},
			labelKey:{
				type:String,
				default:'label'
			},
			valueKey:{
				type:String,
				default:'value'
			},
			options:{
				type:Array,
				default:()=>[]
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		computed:{
			myValue:{
				get(){
					return this.value
				},
				set(val){
					this.$emit('input', val)
					
					const obj = this.myOptions.find(t=>t.value == val)
					this.$emit('change', obj)
				}
			},
			myOptions(){
				return this.options.map(t=>({value:t[this.valueKey],text:t[this.labelKey],disable:this.disabled}))
			}
		}
	}
</script>


