<script>
	export default {
		// 小程序：onLaunch 仅启动时调用一次
		// H5/App：onLaunch 打开网页/用户点刷新/代码热更新时均会调用；
		// 考虑到用户刷新网页时会丢失全局数据、页面栈、页面数据等，因此直接跳回首页即可

		async onLaunch(param) {
			try {
				// 处理URL中的code参数
				this.handleUrlCode();

				// PWA阻止默认弹出安装事件
				await this.handlePWAInstall();

				// 处理Web Worker和Service Worker
				await this.handleWorkerAndServiceWorker();

				// H5刷新时跳转处理
				this.handleH5Refresh(param);

				// 加载语言类型
				await this.FETCH_LANG_TYPE();
				await this.FETCH_LANG_DATA_NOT_RELAUNCH_TO();

				// 加载图标
				await this.FETCH_ICONS();


				// 小程序端处理更新
				this.handleMiniProgramUpdate();
			} catch (error) {
				console.error("onLaunch error:", error);
			}


		},

		methods: {
			handleUrlCode() {
				const href = window.location.href;
				if (href) {
					const codes = href.split("#code=");
					if (codes != null && codes.length > 1) {
						const code = codes[1];
						this.SET_GLOBAL("azureTokenCode", code);
					}
				}
			},
			handlePWAInstall() {
				return new Promise((resolve) => {
					window.addEventListener("beforeinstallprompt", (event) => {
						// 阻止默认行为
						// event.preventDefault()
						// 保存事件，稍后触发安装
						let deferredPrompt = event;
						this.SET_GLOBAL("PWAdeferredPrompt", deferredPrompt);
						this.SET_GLOBAL("showPWAInstall", true);
						// 获取当前页面栈
						let pages = getCurrentPages();
						// 获取数组中最后一个，即当前页面
						let currentPage = pages[pages.length - 1];
						// 调用页面的方法
						if (currentPage.onPWAInstall != null) {
							currentPage.onPWAInstall();
						}
						// 显示自定义安装按钮
						// document.getElementById('installBtn').style.display = 'block'
						resolve();
					});
				});
			},
			handleWorkerAndServiceWorker() {
				return new Promise(async (resolve, reject) => {
					if (Notification.permission !== "granted") {
						Notification.requestPermission();
					}

					let token = this.GET_GLOBAL("token");
					if (token == null) {
						token = localStorage.getItem("token");
					}
					let apiUrl = `${this.API}/message/msg/list/big/`;
					let swUrl = `/static/worker/sw.js?v=${new Date().toISOString()}`
					let currentUrl = window.location.href;
					
					if (currentUrl.includes("/page")) {
						swUrl = currentUrl.split("/page")[0] + swUrl
					} else {
						swUrl = "." + swUrl
					}
					
					if ("serviceWorker" in navigator) {
						try {
							const reg = await navigator.serviceWorker.register(swUrl);
							const sw = reg.installing || reg.waiting || reg.active;
							sw.postMessage({
								apiUrl: apiUrl,
								token: token,
								interval: 1800000 //3分钟
							});
							resolve();
						} catch (error) {
							console.error("Service Worker registration failed:", error);
							reject(error);
						}
					} else {
						resolve();
					}
				});
			},
			handleH5Refresh(param) {
				// #ifdef H5 || APP-VUE
				// H5 刷新时获取当前页面路径
				const pagePath = "/" + param.path;
				// 如果 H5 刷新后访问的不是首页/登录页/注册页，直接跳转回首页
				if (
					![
						"/pages/login",
						"/pages/home",
						"/pages/signup",
						"/pages/wxlogin",
						"/pages/workflow/releasetask/list",
						"/pages/workflow/releasetask/single",
						"/pages/navToPages",
						"/pages/appLogin",
						"/pages/wxloginTest",
					].includes(pagePath)
				) {
					this.$nextTick(() => {
						this.RELAUNCH_TO("/pages/home");
					});
				}
				// #endif
			},
			async loadLanguageData() {
				try {
					await this.FETCH_LANG_TYPE();
					await this.FETCH_LANG_DATA();
				} catch (error) {
					console.error("Failed to load language data:", error);
				}
			},
			handleMiniProgramUpdate() {
				// #ifdef MP-WEIXIN
				// 小程序端，处理更新
				// 目前只有微信小程序有更新管理器 getUpdateManager
				const updateManager = uni.getUpdateManager();
				updateManager.onUpdateReady(() => {
					this.HIDE_LOADING();
					this.CONFIRM(
						"更新提示",
						"小程序新版本已准备好，是否更新应用？",
						true
					).then((confirm) => {
						if (confirm) {
							updateManager.applyUpdate();
						}
					});
				});
				// #endif
			},
		},
	};
</script>
<style lang="scss">
	@import "@/uni_modules/uni-scss";
	@import "@/components/learun-mpui/styles/index.scss";
	@import "@/common/style/index.scss";
</style>