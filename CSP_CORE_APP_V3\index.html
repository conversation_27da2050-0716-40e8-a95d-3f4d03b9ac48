<!DOCTYPE html>
<html lang="zh-CN">
  <head>
  	<!-- 本页面是框架在 H5 和 App 端的模板 index.html （仅限 H5 和 App 端，小程序不使用本页） -->
  	<!-- 本页面已有的代码请勿修改，否则会引起 H5 端显示异常 -->
  
  	<meta charset="utf-8">
  	<meta http-equiv="X-UA-Compatible" content="IE=edge">
  	<!--<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">-->
  	<meta name="viewport"
  		content="width=device-width, height=device-height, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover">
  	<link rel="shortcut icon" type="image" href="<%= BASE_URL %>static/logo.png">
  	<link rel="manifest" href="<%= BASE_URL %>static/pwa-manifest.json" />
  	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  	<!-- <link rel="manifest" href="<%= BASE_URL %>static/pwaManifest.<%= VUE_APP_INDEX_CSS_HASH %>.json" data-hid="manifest"/> -->
  	<!-- <link data-n-head="ssr" rel="manifest" href="<%= BASE_URL %>static/pwa-manifest.json" data-hid="manifest"> -->
  	<title>
  		<%= htmlWebpackPlugin.options.title %>
  	</title>
  	<link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
  </head>
  <body>
  	<noscript>
  		<strong>您当前使用的浏览器已关闭或不支持 Javascript 功能，因此框架无法正常初始化，请检查设置或重新安装浏览器。</strong>
  	</noscript>
  	<div id="app"></div>
  	<!-- <div class="box" style="display: flex; flex-direction: column">
          <button id="installBtn" style="margin: 20px">一键安装应用</button>
          <div id="content" style="margin: 20px">判断环境</div>
      </div> -->
  </body>
  <script src="<%= BASE_URL %>static/signalr/signalr.min.js" charset="utf-8"></script>
  <script>
  	document.addEventListener('DOMContentLoaded', function() {
  		document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
  	})
	</script>
</html>
