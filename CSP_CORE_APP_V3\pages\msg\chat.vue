<template>
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-bottom':'48px'}">
		<!-- 聊天消息容器，nomore属性控制是否显示无更多消息提示 -->
		<learun-chat-wraper :nomore="false">
			<!-- 循环渲染聊天消息列表，每条消息根据发送者ID判断显示方向 -->
			<learun-chat-msg v-for="item of msgList" :key="item.f_MsgId" :content="item.f_Content"
				:type="item.f_SendUserId === chatUserId ? 'left' : 'right'" :date="dateDisplay(item.f_CreateDate)"
				:roundAvatar="roundAvatar" :imgAvatar="avatar(item.f_SendUserId)" iconAvatar="notice"
				iconStyle="background-color: #e4f2fd; color: #98c0da;" />
		</learun-chat-wraper>

		<!-- 消息输入框组件，支持输入和发送功能 -->
		<learun-chat-input v-model="msgInput" @sendMsg="sendMsg" :buttonDisabled="buttonDisabled"
			:placeholder="$t('输入要发送的消息内容')" />
	</view>
</template>

<script>
	import moment from "moment"
	export default {
		data() {
			return {
				chatUserId: null, // 当前聊天对象的用户ID
				icon: null, // 聊天对象的头像标识
				isSystem: false, // 是否为系统消息（未使用）

				msgList: [], // 聊天消息列表
				msgInput: '', // 输入框内容

				earliestTimePoint: '', // 最早消息的时间点（用于加载历史消息）
				nextTime: '', // 最新消息的时间点（用于增量加载）

				timer: null, // 自动获取消息的定时器
				ready: false, // 页面数据是否准备完成
				buttonDisabled: false // 发送按钮是否禁用
			}
		},

		async onLoad(query) {
			uni.setNavigationBarTitle({
				title: this.$t("对话列表")
			})
			// 页面加载时初始化数据，query包含路由参数（用户ID、名称、头像、是否系统消息）
			await this.init(query)
		},

		// 下拉刷新处理：立即拉取历史消息
		onPullDownRefresh() {
			this.fetchBefore().then(uni.stopPullDownRefresh)
		},

		// 页面显示时启动定时器，定期自动获取新消息
		onShow() {
			const intervalTime = this.LEARUN_CONFIG('pageConfig.chat.fetchMsg') // 获取配置的轮询间隔
			this.timer = setInterval(this.fetchMsg, intervalTime) // 启动定时器
		},

		// 页面隐藏时清除定时器，避免内存泄漏
		onHide() {
			clearInterval(this.timer)
		},

		// 页面卸载时清除定时器（确保资源释放）
		onUnload() {
			clearInterval(this.timer)
		},

		methods: {
			// 页面初始化方法，接收路由参数
			async init({
				id,
				name,
				icon,
				sys
			}) {
				this.LOADING(this.$t('加载消息…')) // 显示加载提示

				// 处理头像：如果头像标识存在且不含扩展名，则使用服务器路径
				if (icon && icon.indexOf('.') == -1) {
					this.icon = icon
				}

				this.chatUserId = id // 存储聊天对象ID
				this.isSystem = sys // 标记是否为系统消息（未使用）

				this.SET_TITLE(name) // 设置页面标题为聊天对象名称

				// 获取最新的聊天消息（last接口返回最近的消息）
				const message = await this.HTTP_GET({
					url: '/message/msg/list/last',
					params: {
						toId: id
					}
				})

				// 记录最新消息的时间点（用于后续增量加载）
				const nextTime = message[0] && message[0].f_CreateDate
				this.nextTime = moment(nextTime).format('YYYY-MM-DD HH:mm:ss')

				// 消息列表反转（接口返回倒序，转为正序显示）
				const msgList = message.reverse()

				// 记录最早消息的时间点（用于加载更早的历史消息）
				const earliest = msgList[0] && msgList[0].f_CreateDate
				this.earliestTimePoint = moment(earliest)
					.subtract(1, 'second')
					.format('YYYY-MM-DD HH:mm:ss')

				this.msgList = msgList // 初始化消息列表
				this.ready = true // 标记数据准备完成

				// 延迟滚动到页面底部，确保消息加载后显示最新内容
				setTimeout(() => {
					uni.pageScrollTo({
						scrollTop: 9999,
						duration: 100
					})
				}, 500)

				this.HIDE_LOADING() // 隐藏加载提示
			},

			// 拉取新消息（向上增量加载，获取比nextTime更新的消息）
			async fetchMsg() {
				if (!this.ready) return // 数据未准备完成时跳过

				// 调用接口获取新消息（big接口表示获取更大时间戳的消息）
				const message = await this.HTTP_GET({
					url: '/message/msg/list/big',
					params: {
						toId: this.chatUserId,
						time: this.nextTime
					} // 传入最新时间点
				})

				if (message && message.length > 0) {
					// 更新最新时间点
					const nextTime = message[0] && message[0].f_CreateDate
					this.nextTime = moment(nextTime).format('YYYY-MM-DD HH:mm:ss')

					// 过滤已存在的消息并合并到消息列表（反转保持顺序）
					this.msgList = this.msgList.concat(
						message.filter(t => this.msgList.findIndex(t2 => t2.f_MsgId == t.f_MsgId) === -1)
					).reverse()

					// 滚动到页面底部显示新消息
					setTimeout(() => {
						uni.pageScrollTo({
							scrollTop: 9999,
							duration: 100
						})
					}, 500)
				}
			},

			// 拉取历史消息（向下加载，获取比earliestTimePoint更早的消息）
			async fetchBefore() {
				// 调用接口获取历史消息（less接口表示获取更小时间戳的消息）
				const message = await this.HTTP_GET({
					url: '/message/msg/list/less',
					params: {
						toId: this.chatUserId,
						time: this.earliestTimePoint
					} // 传入最早时间点
				})

				const oldMsgList = message.reverse() // 反转消息列表为正序
				if (oldMsgList.length > 0) {
					// 更新最早时间点（用于下次加载更早的消息）
					this.earliestTimePoint = moment(oldMsgList[0].f_CreateDate)
						.subtract(1, 'second')
						.format('YYYY-MM-DD HH:mm:ss')

					// 将历史消息合并到消息列表顶部
					this.msgList = oldMsgList.concat(this.msgList)
				}
			},

			// 发送消息处理
			async sendMsg() {
				// 验证输入内容非空
				if (this.msgInput.trim().length <= 0) {
					this.TOAST(this.$t('发送的消息不能为空'))
					return
				}

				this.ready = false // 标记为处理中，防止重复发送
				this.buttonDisabled = true // 禁用发送按钮

				// 调用发送消息接口
				const result = await this.HTTP_POST({
					url: '/message/msg/send',
					data: {
						f_RecvUserId: this.chatUserId,
						f_Content: this.msgInput
					}, // 接收者ID和消息内容
					errorTips: this.$t('消息发送失败')
				})

				if (!result) return // 发送失败时返回

				// 构造发送的消息对象并添加到消息列表
				this.msgList.push({
					f_Content: this.msgInput,
					f_CreateDate: this.DATENOW(), // 当前时间
					f_IsSystem: null,
					f_MsgId: result, // 接口返回的消息ID
					f_RecvUserId: this.chatUserId, // 接收者ID
					f_SendUserId: this.GET_GLOBAL('loginUser').f_UserId // 发送者ID（当前用户）
				})

				this.msgInput = '' // 清空输入框

				// 立即滚动到页面底部显示新发送的消息
				setTimeout(() => {
					uni.pageScrollTo({
						scrollTop: 9999,
						duration: 10
					})
				}, 10)

				this.buttonDisabled = false // 启用发送按钮
				this.ready = true // 恢复可交互状态
			},

			// 格式化日期显示（使用TABLEITEM_DATEFORMAT方法）
			dateDisplay(date) {
				return this.TABLEITEM_DATEFORMAT(date).toString()
			},

			// 获取用户头像URL
			avatar(id) {
				const token = this.GET_GLOBAL('token') // 获取全局token
				// 聊天对象头像：优先使用传入的icon，否则加载当前用户头像或默认头像
				if (id == this.chatUserId && this.icon) {
					return `${this.API}/system/annexesfile/${this.icon}?token=${token}` // 服务器头像路径
				} else if (id !== this.chatUserId) {
					const user = this.GET_GLOBAL('loginUser') // 当前用户信息
					return user.f_HeadIcon ?
						`${this.API}/system/annexesfile/${user.f_HeadIcon}?token=${token}` // 当前用户头像
						:
						`/static/img-avatar/head.png` // 默认头像
				} else {
					return `/static/img-avatar/head.png` // 未知用户默认头像
				}
			},
		},

		computed: {
			// 计算头像是否为圆形（从配置中获取）
			roundAvatar() {
				return this.LEARUN_CONFIG('pageConfig.roundAvatar')
			}
		}
	}
</script>