<template>
	<!-- 页面根容器，设置最小高度为屏幕高度 -->
	<view class="page login-page" :style="{ 'min-height': screenHeight + 'px' }">
		<!-- 页面内容容器 -->
		<view class="content">
			<!-- 多语言设置按钮，当语言类型数量大于 1 时显示 -->
			<view @click="pickerLangType" class="learun-lang" v-if="langTypes().length > 1">
				<learun-icon type="menu-translation-m"></learun-icon>
			</view>
			<!-- 头部横幅容器 -->
			<view class="head-banner">
				<!-- 首页 Logo，使用背景图片显示 -->
				<view mode="aspectFit" class="logo" style="background-image: url('../static/logo.png');">
					<!-- Logo 图片 -->
				</view>

				<!-- 标题文字容器 -->
				<view class="title">
					<!-- 主标题 -->
					<text>Crystal Service Platform</text>
					<!-- 开发模式提示，仅在开发模式下显示 -->
					<text v-if="DEV" class="devTitle">(开发模式)</text>
					<!-- 副标题，显示应用版本 -->
					<text class="subTitle">{{ APP_VERSION }}</text>
				</view>

				<!-- 仅在生产环境显示，使用账号的原因说明，无此项微信审核通不过 -->
				<view v-if="!DEV">
					<view class="intro"><text></text></view>
					<view class="intro"><text></text></view>
					<view class="intro subIntro"><text></text></view>
				</view>
			</view>

			<!-- 账户输入框，当 ready 为 true 时显示 -->
			<view class="input-wrapper bg-white" v-if="ready">
				<!-- uni-easyinput 组件，用于输入账号 -->
				<uni-easyinput trim v-model="username" :placeholder="$t('账号')" prefixIcon="person" :inputBorder="false"
					:height="200"></uni-easyinput>
			</view>
			<!-- 密码输入框，当 ready 为 true 时显示 -->
			<view class="input-wrapper bg-white" v-if="ready">
				<!-- uni-easyinput 组件，用于输入密码 -->
				<uni-easyinput class="margin-top" trim v-model="password" :placeholder="$t('请输入密码')" prefixIcon="locked"
					type="password" :inputBorder="false"></uni-easyinput>
			</view>
			<!-- 仅在开发模式显示，用于输入 token 模拟登录 -->
			<view class="input-wrapper bg-white" v-if="DEV">
				<!-- uni-easyinput 组件，用于输入 token -->
				<uni-easyinput trim v-model="inputToken" placeholder="Token" prefixIcon="person" :inputBorder="false"
					:height="200" style="margin-top: 20px;" maxlength="-1"></uni-easyinput>
			</view>

			<!-- 登录按钮，点击调用 login 方法进行登录 -->
			<button class="button-wrapper margin-top" @click="login(null)" type="primary">{{ $t('登录') }}</button>
			<!-- 新用户注册按钮，当 enableSignUp 为 true 时显示，点击调用 signUp 方法 -->
			<button v-if="enableSignUp" class="button-wrapper margin-top" @click="signUp" type="primary" plain>
				{{ $t('新用户注册') }}
			</button>

			<!-- 仅在小程序环境显示，一键登录按钮 -->
			<!-- #ifdef MP -->
			<button v-if="MPLogin" class="button-wrapper margin-top" @click="login(PLATFORM)" type="success">
				{{ PLATFORM_TEXT }}一键登录
			</button>
			<!-- #endif -->

			<!-- 仅在开发模式且有多个后台地址时显示，用于切换后台地址 -->
			<view v-if="DEV && apiRootList.length > 1" class="margin-top" style="font-size: 16px;"
				@click="pickerApiRoot">
				<view><text>{{ $t('后台地址(点击切换') }}):</text></view>
				<view><text>{{ currentApiRoot }}</text></view>
			</view>

			<!-- 仅在开发模式且有多个后台地址时显示，选择 API 地址的弹层 -->
			<learun-picker-popup v-if="DEV && apiRootList.length > 1" ref="apiPicker" :options="apiRootList"
				@change="changeApiRoot">
			</learun-picker-popup>

			<!-- 语言选择弹层，当语言类型数量大于 1 时显示 -->
			<learun-picker-popup v-if="langTypes().length > 1" ref="langTypePicker" :options="langTypes()"
				@change="changeLangType">
			</learun-picker-popup>
		</view>
		<!-- 底部按钮容器 -->
		<view class="bottom-buttons">
			<!-- 安装应用按钮，点击调用 installApp 方法 -->
			<view class="" @click.stop="installApp">
				<!-- 安装图标 -->
				<learun-icon type="arrow-down-outlined" color="#2979ff" size="30"></learun-icon>
			</view>
			<!-- Azure 登录按钮，点击调用 AzureLogin 方法 -->
			<view class="" @click.stop="AzureLogin">
				<!-- Azure 登录图标 -->
				<learun-icon type="menu-file-cloud" color="#2979ff" size="30"></learun-icon>
			</view>
		</view>
		<!-- 页面底部版权文字 -->
		<view class="footer">{{ copyRightDisplay }}</view>
	</view>
</template>

<script>
	// 引入加密库
	import cryptoJS from '../common/crypto.js'

	export default {
		data() {
			return {
				// 用户名
				username: '',
				// 加密后的密码
				password: '',
				// 明文密码
				password2: '',
				// 页面是否准备好显示输入框
				ready: false,
				// 是否显示后台地址选择器
				showApiRootSelector: false,
				// 当前选择的后台地址
				currentApiRoot: '',
				// 后台地址列表
				apiRootList: [],
				// 开发模式下的测试账号列表
				devAccountList: [],
				// 输入的 token，用于开发模式模拟登录
				inputToken: "",
				// 屏幕高度
				screenHeight: 0,
				// 屏幕宽度
				screenWidth: 0,
				// 是否显示 PWA 安装提示
				showPWAInstall: false,
			}
		},

		// 页面加载时执行的钩子函数
		async onLoad() {
			this.getScreenInfo();
			// 初始化页面
			await this.FETCH_LANG_TYPE()
			await this.init()
		},
		// 屏幕尺寸变化时执行的钩子函数
		onResize() {
			// 更新屏幕高度和宽度
			this.getScreenInfo();
		},
		// 页面显示时执行的钩子函数
		async onShow() {
			// 加载图标
			await this.FETCH_ICONS();
			// 获取全局存储的 showPWAInstall 标志
			let showPWAInstall = this.GET_GLOBAL("showPWAInstall")
			if (showPWAInstall) {
				this.showPWAInstall = showPWAInstall
			}

		},
		methods: {
			// 获取屏幕高度和宽度
			async getScreenInfo() {
				uni.getSystemInfo({
					success: (res) => {
						// 更新屏幕高度
						this.screenHeight = res.windowHeight;
						// 更新屏幕宽度
						this.screenWidth = res.windowWidth;
					},
					fail: (err) => {
						// 获取屏幕信息失败时打印错误信息
						console.error('获取屏幕信息失败:', err);
					}
				});
			},
			// 页面初始化方法
			async init() {
				// 获取全局存储的 Azure token 代码
				const azureTokenCode = this.GET_GLOBAL("azureTokenCode")
				// 获取 Azure 配置信息
				const azureConfig = this.LEARUN_CONFIG('azure')
				if (azureTokenCode) {
					try {
						// 调用 Azure 登录接口
						const res = await this.retryApiCall(() => this.HTTP_POST({
							url: '/login/azure?code=' + azureTokenCode + "&redirectUri=" + azureConfig[
								"RedirectUri"],
							errorTips: this.$t('登录时发生错误')
						}))
						// 获取登录返回的 token
						const token = res.token;
						if (token) {
							// 存储 token 到全局和本地存储
							this.SET_GLOBAL('token', token)
							this.SET_STORAGE('token', token)
							// 创建服务工作者
							this.createSW(token)
							// 获取当前用户信息
							const success = await this.FETCH_CURRENT_USERINFO()
							if (success) {
								// 隐藏加载提示
								this.HIDE_LOADING()
								// 跳转到首页
								this.TAB_TO('/pages/home')
							} else {
								// 获取用户信息失败时显示提示信息
								this.TOAST(this.$t('获取用户/部门/公司信息时出现错误，登录失败'))
							}
						}
					} catch (error) {
						// Azure 登录失败时打印错误信息并显示提示信息
						console.error('Azure 登录失败:', error);
						this.TOAST(this.$t('Azure 登录失败，请稍后重试'));
					}
				} else {
					console.log('else: ');
					// 根据开发或生产环境获取后台地址索引
					const index = this.DEV ? this.LEARUN_CONFIG('devApiHostIndex') : this.LEARUN_CONFIG(
						'prodApiHostIndex')
					// 获取后台地址列表
					const apiRootList = this.LEARUN_CONFIG('apiHost')
					// 设置当前后台地址
					this.currentApiRoot = apiRootList[index]
					// 处理后台地址列表，转换为包含 label 和 value 的对象数组
					this.apiRootList = apiRootList.map((t) => ({
						label: t,
						value: t
					}))
					// 如果是开发模式，填入测试账号密码
					if (this.DEV) {
						// 获取开发模式下的测试账号列表
						this.devAccountList = this.LEARUN_CONFIG('devAccount')
						// 获取当前索引对应的测试账号信息
						const account = this.devAccountList[index] || {
							username: '',
							password: '',
							password2: ''
						}
						// 设置用户名
						this.username = account.username
						// 设置加密后的密码
						this.password = account.password
						// 设置明文密码
						this.password2 = account.password
					}
					// 获取语言类型配置完成，页面准备好显示输入框
					this.ready = true
				}
			},

			// 点击新用户注册按钮的处理方法
			signUp() {
				// 显示加载提示
				this.LOADING(this.$t('前往注册…'));
				setTimeout(() => {
					// 跳转到注册页面
					this.NAV_TO('/pages/signup');
				}, 1500);
			},

			// 打开后台地址选择器的方法
			pickerApiRoot() {
				this.$refs.apiPicker.open()
			},
			// 切换后台地址的方法
			changeApiRoot({
				index,
				value
			}) {
				// 更新当前后台地址
				this.currentApiRoot = value
				// 存储当前后台地址到全局
				this.SET_GLOBAL('apiRoot', value)

				// 如果是开发模式，填入测试账号密码
				if (this.DEV && this.devAccountList && this.devAccountList[index]) {
					// 获取当前索引对应的测试账号信息
					const account = this.devAccountList[index] || {
						username: '',
						password: '',
						password2: ''
					}
					// 设置用户名
					this.username = account.username
					// 设置加密后的密码
					this.password = account.password
					// 设置明文密码
					this.password2 = account.password
				}
			},

			// 发起登录的方法
			// type=null 时表示使用账号密码登录
			// type='weixin'/'alipay'/'dingtalk' 时表示使用小程序一键登录
			async login(type) {
				// 获取用户名、密码和验证函数
				const {
					username,
					password,
					check
				} = this

				// 账号密码登录时，验证输入，输入有误则返回
				if (!type && !check()) {
					return
				}
				// 显示加载提示
				this.LOADING(this.$t('登录中…'))

				// 根据不同的登录方式，调用 API
				let res = null

				// 不是小程序，则提交用户名、密码登录
				// 是小程序，则申请授权码登录
				if (!type) {
					let _username = username;
					const usernameList = username.split("@");
					let tenantNo = "";
					if (usernameList.length == 2) {
						_username = usernameList[1];
						tenantNo = usernameList[0];
					}

					// 登录功能登录时传给后台AD登录密码是明码，需改为加密密文，后台对密文解密后再验证 modify by kyle feng 26 Aug 2024
					let key = cryptoJS.enc.Utf8.parse('cspLogin000000000000000000000000'); // 32位
					let encrypted = cryptoJS.AES.encrypt(password, key, {
						iv: cryptoJS.enc.Utf8.parse("****************"), // 16位,
						mode: cryptoJS.mode.CBC,
						padding: cryptoJS.pad.Pkcs7,
					});
					let password2 = encrypted.toString()

					try {
						// 调用账号密码登录接口
						res = await this.retryApiCall(() => this.HTTP_POST_ALL({
							url: '/login',
							data: {
								account: _username,
								password: this.MD5(password),
								password2: password2
							},
							errorTips: this.$t('登录时发生错误'),
						}))
					} catch (error) {
						// 账号密码登录失败时打印错误信息，隐藏加载提示并显示提示信息
						console.error(this.$t('账号密码登录失败:'), error);
						this.HIDE_LOADING();
						this.TOAST(this.$t('账号密码登录失败，请稍后重试'));
						return;
					}
				} else {
					// 获取小程序登录授权码
					const [codeErr, {
						code
					} = {}] = await uni.login({
						provider: type
					})
					if (codeErr || !code) {
						// 获取授权码失败时隐藏加载提示，显示确认框
						this.HIDE_LOADING()
						this.CONFIRM(this.$t('登录失败'), this.$t('无法获取小程序登录授权码'))
						return
					}

					try {
						// 调用小程序一键登录接口
						res = await this.retryApiCall(() => this.HTTP_POST('/user/openid_login', {
							code,
							type
						}, '登录时发生错误'))
					} catch (error) {
						// 小程序一键登录失败时打印错误信息，隐藏加载提示并显示提示信息
						console.error(this.$t('小程序一键登录失败:'), error);
						this.HIDE_LOADING();
						this.TOAST(this.$t('小程序一键登录失败，请稍后重试'));
						return;
					}
				}

				// 登录结果数据为空时，隐藏加载提示，显示提示信息
				if (!res.data.data) {
					this.HIDE_LOADING();
					this.LOADING(this.$t(res.data.info));
					setTimeout(() => {
						this.HIDE_LOADING();
					}, 1500);
					return
				}

				// 账号密码不匹配时，显示提示信息
				if (res.data.info == '账号密码不匹配') {
					this.SHOWTOAST(this.$t('账号密码不匹配'), 'error', 2000);
					return
				}
				// 密码已过期时，显示提示信息并跳转到修改密码页面
				if (res.data.info == '密码已过期') {
					this.LOADING(this.$t('密码已过期，前往更改…'));
					setTimeout(() => {
						this.NAV_TO('/pages/changePassword');
					}, 1500);
					return
				}

				// 获取登录返回的 token
				let token = res.data.data.token
				if (this.DEV) {
					// 模拟登陆
					if (this.inputToken) {
						token = this.inputToken
					}
				}
				// 存储 token 到全局和本地存储
				this.SET_GLOBAL('token', token)
				this.SET_STORAGE('token', token)
				// 创建服务工作者
				this.createSW(token)

				// 获取当前用户信息
				const success = await this.FETCH_CURRENT_USERINFO()
				if (!success) {
					// 获取用户信息失败时显示提示信息
					this.TOAST(this.$t('获取用户/部门/公司信息时出现错误，登录失败'))
					return
				}
				// 登录成功后，加载多语言数据
				await this.FETCH_LANG_DATA();
				// 隐藏加载提示
				this.HIDE_LOADING()
				// 跳转到首页
				this.TAB_TO('/pages/home')
			},

			// 验证用户输入的方法
			check() {
				// 获取用户名和密码
				const {
					username,
					password
				} = this
				if (username.length <= 0 || password.length <= 0) {
					// 用户名或密码为空时，显示确认框并返回 false
					this.CONFIRM(this.$t('输入错误', '账号或密码不能为空，请重新输入。'))
					return false
				}
				return true
			},

			// 打开多语言选择器的方法
			pickerLangType() {
				this.$refs.langTypePicker.open(this.GET_STORAGE('learun_lang_type'))
			},
			// 切换语言的方法
			async changeLangType({
				index,
				value
			}) {
				// 存储选择的语言类型到本地存储
				this.SET_STORAGE('learun_lang_type', value)
				// 加载多语言数据
				await this.FETCH_LANG_DATA()
			},
			// 获取语言类型列表的方法
			langTypes() {
				return this.GET_LANG_TYPES();
			},
			// 显示 PWA 安装提示的方法
			onPWAInstall() {
				this.showPWAInstall = true
			},
			// 安装 PWA 应用的方法
			installApp() {
				// 获取全局存储的 PWA 安装提示事件
				let deferredPrompt = this.GET_GLOBAL("PWAdeferredPrompt")
				if (deferredPrompt) {
					// 触发 PWA 安装
					deferredPrompt.prompt()

					// 监听安装结果
					deferredPrompt.userChoice.then((choiceResult) => {
						if (choiceResult.outcome === 'accepted') {
							console.log('用户安装了 PWA')
						} else {
							console.log('用户拒绝安装 PWA')
						}

						// 重置事件
						deferredPrompt = null
						this.showPWAInstall = false
					})
				}
			},
			// 创建服务工作者的方法
			createSW(token) {
				// 检查当前环境是否支持 Notification 对象
				if (typeof window !== 'undefined' && 'Notification' in window) {
					if (Notification.permission !== 'granted') {
						// 请求通知权限
						Notification.requestPermission();
					}
				}
				// 构建消息列表 API 地址
				let apiUrl = `${this.API}/message/msg/list/cache/`;
				if ('serviceWorker' in navigator) {
					// 注册服务工作者
					navigator.serviceWorker?.register('../static/worker/sw.js?v=' + new Date().toISOString())
						.then(reg => {
							// 获取服务工作者实例
							const sw = reg.installing || reg.waiting || reg.active;
							// 向服务工作者发送消息，包含 API 地址和 token
							sw.postMessage({
								apiUrl: apiUrl,
								token: token,
								interval: 1800000, //3分钟
							});
						})
						.catch((e) => console.error('registration failed', e))
				}
			},
			// Azure 登录的方法
			AzureLogin() {
				// 获取 Azure 配置信息
				let azureConfig = this.LEARUN_CONFIG('azure')
				// 构建 Azure 认证 URL
				const authUrl = "https://login.microsoftonline.com/" + azureConfig["TenantId"] +
					"/oauth2/v2.0/authorize?response_type=code" +
					"&scope=user.read%20User.ReadBasic.All%20openid%20profile" +
					"&client_id=" + azureConfig["ClientId"] +
					"&redirect_uri=" + azureConfig["RedirectUri"] +
					"&state=86ec3f20-5dd5-4192-96f7-5789c4a38663&nonce=6c572677-46a3-48e6-969e-69208a23955a&client_info=1&x-client-SKU=MSAL.JS&x-client-Ver=1.1.3&client-request-id=b09b2205-1309-4621-ba33-584a97b8ae1b&response_mode=fragment"
				// 打开认证 URL 以启动 OAuth 流程
				window.location.href = authUrl;
			},
			// 重试 API 调用的函数
			async retryApiCall(apiCall, maxRetries = 3, retryDelay = 1000) {
				let retries = 0;
				while (retries < maxRetries) {
					try {
						// 调用 API
						return await apiCall();
					} catch (error) {
						retries++;
						if (retries < maxRetries) {
							// 重试前打印提示信息
							console.log(`API 调用失败，正在进行第 ${retries} 次重试...`);
							// 等待一段时间后重试
							await new Promise(resolve => setTimeout(resolve, retryDelay));
						} else {
							// 达到最大重试次数，抛出错误
							throw error;
						}
					}
				}
			}
		},

		computed: {
			// 页面底部公司名、版权信息
			copyRightDisplay() {
				// 获取当前年份
				const year = new Date().getFullYear()
				// 获取公司名称
				const company = this.LEARUN_CONFIG('company')
				// 返回版权信息字符串
				return `Copyright © ${year} ${company}`
			},

			// 是否显示小程序登录按键
			MPLogin() {
				// 根据配置判断是否显示小程序登录按钮
				return this.LEARUN_CONFIG(`miniProgramAccount.${this.PLATFORM}`).includes('login')
			},

			// 是否展示注册按钮
			enableSignUp() {
				// 根据配置判断是否显示注册按钮
				return this.LEARUN_CONFIG('enableSignUp')
			},

			// 是否为租户模式
			isTenant() {
				// 根据配置判断是否为租户模式
				return this.LEARUN_CONFIG('isTenant')
			},
		}
	}
</script>
<style lang="scss" scoped>
	// 页面根容器样式
	.page {
		// 顶部内边距
		padding-top: 100px;
	}

	// 登录页面样式
	.login-page {
		// 水平居中显示
		display: flex;
		justify-content: center;
		// 宽度占满屏幕
		width: 100%;
		/* 设置透视效果，用于 3D 变换 */
		perspective: 1000px;

		// 头部横幅样式
		.head-banner {
			// 底部外边距
			margin-bottom: 32px;

			// 标题样式
			.title {
				// 块级显示
				display: block;
				// 上下外边距
				margin: 8px 0;
				// 字体大小
				font-size: 20px;
				// 底部外边距
				margin-bottom: 16px;
				// 文字颜色
				color: $uni-main-color;
			}

			// 开发模式提示样式
			.devTitle {
				// 文字颜色
				color: $uni-error;
			}

			// 副标题样式
			.subTitle {
				// 字体大小
				font-size: 16px;
				// 文字颜色
				color: $uni-info;
			}

			// Logo 样式
			.logo {
				// 背景图片自适应
				background-size: contain;
				// 高度
				height: 86px;
				// 宽度
				width: 120px;
				// 文本居中
				text-align: center;
				// 行内块级显示
				display: inline-block;
				// 圆角
				border-radius: 2px;
			}

			// 登录项样式
			.login-item {
				// 上下内边距
				padding: 12rpx 0;
				// 底部边框
				border-bottom: 1px solid #eee;
				// 底部外边距
				margin-bottom: 20rpx;

				// 子元素 u-icon 样式
				/deep/.u-icon {
					// 宽度
					width: 80rpx;
				}
			}

			// 说明文字样式
			.intro {
				// 字体大小
				font-size: 12px;
				// 顶部外边距
				margin-top: 8px;
				// 文字颜色
				color: $uni-base-color;
			}

			// 副标题说明文字样式
			.subIntro {
				// 文字颜色
				color: $uni-extra-color;
			}
		}

		// 内容区域样式
		.content {
			// 文本居中
			text-align: center;
			// 宽度为屏幕宽度的 90%
			width: 90%;
			/* 最大宽度限制，避免在大屏上过于分散 */
			max-width: 600px;
			// 水平居中
			margin: 0 auto;
			// 左右内边距
			padding: 0 24px;
			// 盒模型包含内边距和边框
			box-sizing: border-box;
		}

		// 输入框容器样式
		.input-wrapper {
			// 宽度占满容器
			width: 100%;
			// 盒模型包含内边距和边框
			box-sizing: border-box;
			// 底部外边距
			margin-bottom: 15px;
			/* 保持子元素的 3D 变换 */
			transform-style: preserve-3d;
			// 添加过渡效果
			transition: transform 0.3s ease;

			// 输入框聚焦时的样式
			&:focus-within {
				/* 3D 向前移动 */
				transform: translateZ(10px);
			}
		}

		// 按钮容器样式
		.button-wrapper {
			// 宽度占满容器
			width: 100%;
			// 盒模型包含内边距和边框
			box-sizing: border-box;
			// 底部外边距
			margin-bottom: 15px;
			/* 保持子元素的 3D 变换 */
			transform-style: preserve-3d;
			// 添加过渡效果
			transition: transform 0.3s ease;

			// 鼠标悬停时的样式
			&:hover {
				/* 鼠标悬停时 3D 向前移动 */
				transform: translateZ(10px);
			}
		}

		// 页脚样式
		.footer {
			// 绝对定位
			position: absolute;
			// 左对齐
			left: 0;
			// 右对齐
			right: 0;
			// 底部外边距
			bottom: 8px;
			// 适配安全区域
			bottom: calc(8px + env(safe - area - inset - bottom));
			// 文本居中
			text-align: center;
			// 字体大小
			font-size: 12px;
			// 文字颜色
			color: $uni-info;
			// 底部外边距
			bottom: 8px;
		}

		// 多语言按钮样式
		.learun-lang {
			// 绝对定位
			position: absolute;
			// 顶部外边距
			top: 24px;
			// 右侧外边距
			right: 24px;
		}

		// 底部按钮容器样式
		.bottom-buttons {
			// 绝对定位
			position: absolute;
			// 底部外边距
			bottom: 40px;
			// 左对齐
			left: 0;
			// 右对齐
			right: 0;
			// 文本居中
			text-align: center;
			// 水平分布
			display: flex;
			justify-content: space-evenly;
			// 宽度占满屏幕
			width: 100%;
		}

		// 白色背景输入框和按钮样式
		/* 输入框和按钮自适应宽度 */
		.bg-white,
		button {
			// 宽度占满容器
			width: 100%;
			// 盒模型包含内边距和边框
			box-sizing: border-box;
		}
	}
</style>