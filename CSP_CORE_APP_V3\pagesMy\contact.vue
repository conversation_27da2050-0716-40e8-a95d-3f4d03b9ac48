<template>
	<view class="page">
		<uni-list>
			<uni-list-item :title="$t('手机')" :rightText="currentUser.f_Mobile"></uni-list-item>
			<uni-list-item :title="$t('电话')" :rightText="currentUser.f_Telephone"></uni-list-item>
			<uni-list-item :title="$t('邮箱')" :rightText="currentUser.f_Email"></uni-list-item>
			<uni-list-item :title="$t('微信')" :rightText="currentUser.f_WeChat"></uni-list-item>
			<uni-list-item :title="$t('QQ')" :rightText="currentUser.f_OICQ"></uni-list-item>
		</uni-list>
	</view>
</template>

<script>
	export default {
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("我的联系方式")
			})
			await this.PAGE_LAUNCH();
		},
		computed: {
			currentUser() {
				return this.GET_GLOBAL('loginUser') || {}
			}
		}
	}
</script>