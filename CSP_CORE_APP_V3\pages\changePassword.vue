<template>
	<view class="page">
		<!-- 返回按钮：点击返回上一页，使用uni-icons图标 -->
		<view class="back-button" @click="goBack">
			<uni-icons type="left" size="24" color="#333"></uni-icons>
		</view>
		<view class="content">
			<!-- 多语言设置按钮：当存在多种语言时显示，点击打开语言选择弹窗 -->
			<view @click="pickerLangType" class="learun-lang" v-if="langTypes().length > 1">
				<learun-icon type="menu-translation-m"></learun-icon>
			</view>
			<!-- 语言选择弹窗：与多语言按钮联动，通过ref引用操作弹窗，选项变化时触发语言切换 -->
			<learun-picker-popup v-if="langTypes().length > 1" ref="langTypePicker" :options="langTypes()"
				@change="changeLangType">
			</learun-picker-popup>
			<!-- 首页Logo：通过背景图显示，使用响应式布局保持比例 -->
			<view mode="aspectFit" class="logo"
				:style="{ backgroundImage: `url('/csp_core_app_321/static/logo.png')` }">
			</view>
			<!-- 公司选择器：使用uni-picker组件，绑定公司索引和列表，支持下拉选择公司 -->
			<picker @change="bindPickerChange" :value="companyCode" :range="companyArray" range-key="name"
				class="margin-top">
				<view class="company-view">
					<uni-icons type="home" size="16" color="rgb(192, 196, 204)" class="company-icons"></uni-icons>
					<!-- 公司显示区域：根据选择状态显示选中公司或提示信息 -->
					<view :class="'uni-input ' + (companyIndex >= 0 ? '' : 'placeholder-view')">
						{{ companyIndex >= 0 ? companyArray[companyIndex] : $t("请选择公司") }}
					</view>
				</view>
			</picker>

			<!-- 工号输入框：使用uni-easyinput组件，绑定工号数据，带用户图标和无边框样式 -->
			<uni-easyinput class="margin-top input-div" trim v-model="empNo" :placeholder="$t('工号')" prefixIcon="person"
				:inputBorder="false" :height="inputHeight"></uni-easyinput>

			<!-- 用户输入旧密码框：密码输入框，带锁定图标，隐藏边框 -->
			<uni-easyinput class="margin-top input-div" trim v-model="oldPassword" :placeholder="$t('请输入旧密码')"
				prefixIcon="locked" type="password" :inputBorder="false" :height="inputHeight"></uni-easyinput>
			<!-- 用户输入密码框：新密码输入框，相同样式结构 -->
			<uni-easyinput class="margin-top input-div" trim v-model="password" :placeholder="$t('请输入新密码')"
				prefixIcon="locked" type="password" :inputBorder="false" :height="inputHeight"></uni-easyinput>

			<!-- 用户二次输入密码框：验证新密码一致性，相同样式 -->
			<uni-easyinput class="margin-top input-div" trim v-model="verifyPassword" :placeholder="$t('请再次输入新密码')"
				prefixIcon="locked" type="password" :inputBorder="false" :height="inputHeight"></uni-easyinput>

			<!-- 用户手机号输入框：包含区号选择和手机号输入的组合布局 -->
			<view class="phone-view margin-top input-div">
				<!-- 区号选择器：使用uni-picker选择电话区号，绑定区号数据 -->
				<uni-icons type="phone" size="16" color="rgb(192, 196, 204)" class="phone-icon"></uni-icons>
				<picker @change="bindPickerChangeCode" :value="areaCode" :range="companyCodeArray"
					range-key="codePhone">
					<view>{{areaCode}}</view>
				</picker>
				<!-- 手机号输入框：无边框样式，绑定手机号数据 -->
				<uni-easyinput class="phone-input" trim v-model="phone" :placeholder="$t('手机号码')" :inputBorder="false"
					:height="inputHeight"></uni-easyinput>
			</view>
			<!-- 验证码输入区域：包含验证码输入框和获取验证码按钮 -->
			<view class="code-view">
				<uni-easyinput class="margin-top input-div code-input" trim v-model="code" :placeholder="$t('请输入验证码')"
					prefixIcon="mail-open-filled" :inputBorder="false" :height="inputHeight"></uni-easyinput>
				<!-- 获取验证码按钮：根据发送状态显示倒计时或可点击状态 -->
				<button @click="getCode" :disabled="!sendOk" size="lg" line="blue" class="margin-top block" block>
					{{ sendOk ? $t('获取验证码') : $t(`请`) + ` ` + $t(`${waitSec}`) + `S` + ` ` + $t(`后`) + ` ` + $t(`重发`) }}
				</button>
			</view>
			<!-- 修改密码按钮：主操作按钮，点击触发密码修改逻辑 -->
			<button class="margin-top" @click="changeUp" type="primary">{{ $t('确认修改密码') }}</button>
		</view>
	</view>
</template>

<script>
	import moment from "moment"; // 时间处理库
	import cryptoJS from '../common/crypto.js'; // 加密工具库

	export default {
		data() {
			return {
				// 公司代码：当前选中公司的代码
				companyCode: "",
				// 公司索引：picker组件选中的索引值（-1表示未选择）
				companyIndex: -1,
				// 工号：用户输入的工号信息
				empNo: "",
				// 手机号：用户输入的手机号码
				phone: "",
				// 验证码：用户输入的短信验证码
				code: "",
				// 手机号区号：默认中国大陆区号
				areaCode: "+86",
				// 旧密码：用户输入的原密码
				oldPassword: '',
				// 首次输入的密码：用户输入的新密码
				password: "",
				// 再次输入的密码：用于验证新密码一致性
				verifyPassword: "",
				// 是否发送了验证码：标记验证码是否已发送
				sendCode: false,
				// 是否可以发送验证码：控制按钮可点击状态
				sendOk: true,
				// 定时器：用于验证码倒计时
				timer: null,
				// 验证码重发等待秒数：倒计时初始值（90秒）
				waitSec: 90,
				// 公司列表：展示的公司名称数组
				companyArray: [],
				companyList: [], // 原始公司数据列表（包含更多字段）
				companyCodeArray: [], // 电话区号列表
				// 屏幕高度和宽度：用于响应式布局计算
				screenHeight: 0,
				screenWidth: 0,
				// 输入框高度：根据屏幕宽度动态计算
				inputHeight: 50,
				// 系统国家：存储系统配置的国家信息
				systemState: [],
			};
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("修改密码")
			})
			this.getScreenInfo(); // 初始化屏幕信息
			await this.fetchCompanyList(); // 获取公司列表数据
			await this.fetchSystemNationAndAreaCode(); // 获取系统国家和区号数据
			// 获取电话区号列表并处理
			const resPhone = await this.HTTP_GET({
				url: '/register/phoneAreaCode'
			})
			JSON.parse(JSON.stringify(resPhone)).rows.forEach(resCode => {
				this.companyCodeArray.push(resCode.sys_value2)
			})
			this.companyCodeArray = JSON.parse(JSON.stringify(this.companyCodeArray))
		},
		onResize() {
			this.getScreenInfo(); // 屏幕尺寸变化时更新布局信息
		},
		methods: {
			// 多语言切换：打开语言选择弹窗，传入当前语言类型
			pickerLangType() {
				this.$refs.langTypePicker.open(this.GET_STORAGE('learun_lang_type'));
			},
			// 语言类型列表：获取可用的多语言类型
			langTypes() {
				return this.GET_LANG_TYPES();
			},
			// 语言切换处理：更新存储并重新获取语言数据
			async changeLangType({
				index,
				value
			}) {
				this.SET_STORAGE('learun_lang_type', value); // 存储选择的语言类型
				await this.FETCH_LANG_DATA(); // 重新获取语言翻译数据
			},
			// 获取屏幕信息：通过uni-app API获取设备屏幕尺寸
			getScreenInfo() {
				uni.getSystemInfo({
					success: (res) => {
						// 更新屏幕尺寸和输入框高度（根据屏幕宽度动态计算，50-200px之间）
						this.screenHeight = res.windowHeight;
						this.screenWidth = res.windowWidth;
						this.inputHeight = Math.min(200, Math.max(50, this.screenWidth * 0.1));
					},
					fail: (err) => {
						console.error(this.$t('获取屏幕信息失败:'), err); // 错误处理
					},
				});
			},
			// 获取公司列表：通过API请求获取可选公司数据
			async fetchCompanyList() {
				// 发送HTTP GET请求获取公司列表
				const res = await this.HTTP_GET({
						url: '/register/companyList'
					})
					.then(companiesList => {
						// 处理响应数据，提取公司名称和原始数据
						companiesList.rows.forEach(res => {
							this.companyArray.push(res.sys_value2)
							this.companyList.push(JSON.parse(JSON.stringify(res)))
						})
					})
				// 额外获取系统国家信息（可能用于后续逻辑）
				this.systemState = await this.HTTP_GET({
					url: '/register/systemNation'
				})
			},
			// 获取系统国家和区号：关联国家信息和对应的电话区号
			async fetchSystemNationAndAreaCode() {
				// 获取系统国家信息
				const systemNation = await this.HTTP_GET({
					url: '/register/systemNation'
				});
				// 获取电话区号列表
				const phoneAreaCodeResponse = await this.HTTP_GET({
					url: '/register/phoneAreaCode'
				});
				const phoneAreaCodeList = phoneAreaCodeResponse.rows;
				// 根据系统国家查找匹配的区号
				const matchingAreaCode = phoneAreaCodeList.find(
					(phone) => phone.sys_value1 === systemNation // sys_value1可能存储国家标识
				);
				if (matchingAreaCode) {
					this.areaCode = matchingAreaCode.sys_value2; // 设置默认区号
				}
			},
			// 获取验证码：发送验证码前的校验和网络请求
			async getCode() {
				// 验证输入合法性和发送状态
				if (!this.validateBeforeSendCode() || !this.sendOk) {
					return;
				}
				this.sendOk = false; // 禁用按钮
				// 计算下次可发送时间（90秒后）
				const next = moment().add(90, 'seconds');
				this.SET_STORAGE('nextTime', next.format('YYYY-MM-DD HH:mm:ss')); // 存储倒计时时间
				this.updateTimer(next); // 启动倒计时定时器
				this.showToast(this.$t('验证码发送成功'), 'success'); // 显示成功提示
				this.sendCode = true; // 标记验证码已发送
			},
			// 验证发送验证码前的输入：主要校验手机号
			validateBeforeSendCode() {
				return this.validatePhone(); // 调用手机号验证方法
			},
			// 返回上一页：导航回登录页面，带加载提示
			goBack() {
				this.showLoading(this.$t('返回中...')); // 显示加载提示
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login'
					}); // 延迟导航（模拟加载过程）
				}, 1000);
			},
			// 点击修改密码按钮：触发密码修改逻辑
			async changeUp() {
				// 验证密码和手机号合法性
				if (!this.validatePasswords()) return;
				if (!this.validatePhone()) return;
				this.SHOWTOAST(this.$t('注册中'), 'loading', 1000); // 显示加载状态
				// 发送密码修改请求
				await this.HTTP_PUT_ALL({
					url: '/user/password',
					data: {
						// 提交表单数据，包含加密后的密码
						company_Code: this.companyCode,
						emp_No: this.empNo,
						phone_Area_Code: this.areaCode,
						phone_Number: this.phone,
						code: this.code,
						password: this.MD5(this.oldPassword), // 旧密码加密
						new_Password: this.MD5(this.verifyPassword), // 新密码加密
					},
					errorTips: this.$t('修改密码失败'), // 错误提示文案
				}).then(response => {
					console.log('response: ', response);
					// 处理响应结果
					if (!response.data.data || response.data.info == this.$t('新旧密码不能相同！')) {
						this.SHOWTOAST(response.data.info, 'error', 1500); // 显示错误提示
					}
					if (response.data.data) {
						// 成功时提示并导航回登录页
						this.LOADING(this.$t('修改密码成功，前往登录…'));
						setTimeout(() => {
							this.NAV_TO('/pages/login');
						}, 1500);
					}
				})
			},
			// 选择区号：处理区号选择器的变化事件
			bindPickerChangeCode(e) {
				// 根据选中索引获取对应的区号
				this.areaCode = this.companyCodeArray[e.detail.value]
			},
			// 验证密码和确认密码：检查密码强度和一致性
			validatePasswords() {
				// 密码为空校验
				if (!this.password) {
					this.showPrompt(this.$t('请输入密码'));
					return false;
				}
				// 密码强度正则：至少8位，包含字母、数字、特殊字符
				const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&+-_])[A-Za-z\d@$!%*#?&+-_]{8,}$/;
				if (!passwordRegex.test(this.password)) {
					this.showPrompt(this.$t('密码长度至少为 8，至少含有一个字母、一个数字和一个特殊字符'));
					return false;
				}
				// 确认密码为空校验
				if (!this.verifyPassword) {
					this.showPrompt(this.$t('请再次输入密码'));
					return false;
				}
				// 密码一致性校验
				if (this.password !== this.verifyPassword) {
					this.showPrompt(this.$t('两次输入的密码不一致'));
					return false;
				}
				// 验证码为空校验
				if (!this.code) {
					this.showPrompt(this.$t('请输入验证码'));
					return false;
				}
				return true;
			},
			// 验证手机号是否合法：支持多国家/地区的号码格式校验
			validatePhone() {
				if (!this.phone) {
					this.showPrompt(this.$t('手机号不能为空'));
					return false;
				}
				// 组合完整手机号（区号+号码）
				const fullPhoneNumber = this.areaCode + this.phone;
				// 多国家/地区的手机号正则表达式
				const regexPatterns = [
					/^\+?86?1[3-9]\d{9}$/, // 中国大陆
					/^(\+?852)?[569]\d{7}$/, // 中国香港
					/^(\+?65)?[89]\d{7}$/, // 新加坡
					/^\+84(3[2-9]|5[2689]|7[0|6-9]|8[1-9]|9[0-4|6-9])\d{7}$/, // 越南
					/^(\+?855|0)(1|6|7|8|9)\d{8}$/, // 柬埔寨
					/^(\+?880)?1[3-9]\d{8}$/ // 孟加拉国
				];
				// 遍历正则匹配
				for (const pattern of regexPatterns) {
					const isMatch = pattern.test(fullPhoneNumber);
					if (isMatch) {
						return true;
					}
				}
				// 格式不匹配时提示
				this.showPrompt(this.$t('手机号格式不正确'));
				return false;
			},
			// 选择公司的事件处理函数：根据选中公司更新相关信息
			async bindPickerChange(e) {
				this.companyIndex = e.detail.value; // 更新选中索引
				// 获取电话区号列表并关联公司信息
				const resPhone = await this.HTTP_GET({
					url: '/register/phoneAreaCode'
				})
				JSON.parse(JSON.stringify(this.companyList)).forEach((res) => {
					// 匹配选中公司的代码
					if (res.sys_value2 === this.companyArray[this.companyIndex]) {
						this.companyCode = res.sys_value1; // 设置公司代码
					}
					// 根据公司代码查找对应的区号
					if (res.sys_value1 === this.companyCode) {
						resPhone.rows.forEach(phone => {
							if (phone.sys_value1 == res.sys_type) { // sys_type可能为国家标识
								this.areaCode = phone.sys_value2; // 修正区号赋值
							}
						})
					}
				})
			},
			// 显示提示信息：通用提示方法，带非模态弹窗
			showPrompt(message) {
				uni.showToast({
					title: message,
					icon: 'none', // 无图标
					duration: 2000, // 显示2秒
				});
			},
			// 显示加载提示：通用加载状态方法
			showLoading(message) {
				uni.showLoading({
					title: message,
				});
			},
			// 隐藏加载提示：关闭加载状态
			hideLoading() {
				uni.hideLoading();
			},
			// 显示普通提示：默认成功提示
			showToast(message) {
				uni.showToast({
					title: message,
					icon: 'success', // 成功图标
				});
			},
			// 更新验证码重发定时器：处理倒计时逻辑
			updateTimer(nextTime) {
				// 定时器回调函数：检查是否到达可重发时间
				const setTime = () => {
					this.sendOk = moment(nextTime).isSameOrBefore(); // 判断是否超时
					if (!this.sendOk) {
						// 计算剩余时间（四舍五入取整）
						this.waitSec = moment.duration(moment(nextTime).diff()).asSeconds().toFixed(0);
					} else {
						clearInterval(this.timer); // 清除定时器
					}
				};
				this.timer = setInterval(setTime, 1000); // 每秒更新一次
				setTime(); // 立即执行一次初始化显示
			},
		},
	};
</script>

<style lang="less">
	page {
		height: 100%;
	}

	/* #ifdef MP-ALIPAY */
	.page {
		height: 100%;
		position: absolute;
	}

	/* #endif */
</style>

<style lang="less" scoped>
	.page {
		height: 100%;
		width: 100%;
		background-color: #ffffff;

		.back-button {
			position: absolute;
			top: 20px;
			left: 20px;
			z-index: 10; // 确保按钮在顶层
		}

		.content {
			text-align: center;
			width: 90%;
			max-width: 600px; // 限制最大宽度，适应大屏幕
			margin: 0 auto; // 水平居中
			padding: 0 38rpx;
			border-radius: 20px 20px 0 0;
			position: relative;
			top: 10%; // 垂直居中偏移
			background-color: #ffffff;

			.learun-lang {
				position: absolute;
				top: -20px; // 定位在内容区域上方
				right: 20px;
			}

			.logo {
				background-size: contain; // 背景图保持原始比例
				height: 110px;
				width: 150px;
				text-align: center;
				display: inline-block;
				border-radius: 2px;
			}

			.company-view {
				.company-icons {
					padding: 0 5px; // 图标内边距
				}

				border: 1px solid #E5E5E5; // 浅灰色边框
				border-radius: 4px; // 圆角
				min-height: 36px;
				display: flex; // 弹性布局
				flex-direction: row;
				align-items: center; // 垂直居中
			}

			.input-div {
				border: 1px solid #E5E5E5; // 统一输入框边框样式
				border-radius: 4px;
			}

			.phone-view {
				display: flex; // 弹性布局排列区号和手机号输入
				flex-direction: row;
				align-items: center;

				.phone-icon {
					padding: 0 5px; // 图标内边距
				}
			}

			.code-view {
				display: flex; // 弹性布局排列验证码输入框和按钮

				.phone-input {
					border-radius: 4px 0px 0px 4px; // 左半部分圆角
				}

				uni-button {
					border-radius: 0px 4px 4px 0px; // 右半部分圆角
					border: 1px solid #E5E5E5; // 统一边框
					height: 38px; // 固定高度
				}

				uni-button::after {
					border-radius: 0px 4px 4px 0px; // 去除默认按钮边框
					border: none;
				}
			}

			.placeholder-view {
				color: #999; // 浅灰色提示文字
				font-size: 12px;
				font-weight: 200; // 细字体
			}
		}
	}

	/* 媒体查询，针对小屏幕设备进一步调整样式：屏幕宽度小于480px时应用 */
	@media (max-width: 480px) {
		.page .content {
			top: 5%; // 减小顶部偏移
			padding: 0 20px; // 减小内边距
		}

		.page .content .logo {
			height: 100px; // 缩小Logo尺寸
			width: 140px;
		}
	}
</style>