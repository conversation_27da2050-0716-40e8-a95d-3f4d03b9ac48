<template>
	<!-- 页面容器，设置顶部内边距为 56px -->
	<view class="page" style="padding-top: 56px;">
		<!-- 固定在顶部的白色背景容器 -->
		<view class="bg-white fixed">
			<!-- 搜索框组件，设置占位符，隐藏取消按钮，绑定搜索文本，监听确认和清空事件 -->
			<uni-search-bar :placeholder="$t('搜索关键字')" cancelButton="none" @confirm="searchChange(false)"
				@clear="searchChange(true)" v-model="searchText"></uni-search-bar>
		</view>
		<!-- 列表组件，当数据准备好时显示 -->
		<uni-list v-if="ready">
			<!-- 列表项，可点击，遍历显示选项列表 -->
			<uni-list-item clickable @click="itemClick(item)" :key="index" v-for="(item,index) in showOptions"
				direction="column">
				<!-- 列表项主体内容插槽 -->
				<template v-slot:body>
					<view>
						<!-- 遍历列配置，显示每列的标签和对应的值 -->
						<view v-for="(col,index2) in columns" :key="index2"
							class="learun-list-item__content learun-flex">
							<text>{{col.label}}</text>
							<text>{{item[col.prop]}}</text>
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 是否以弹窗层形式显示
				isLayer: true,
				// 所有选项数据
				options: [],
				// 当前显示的选项数据
				showOptions: [],
				// 列配置信息
				columns: [],
				// 搜索框中的文本
				searchText: '',
				// 当前页码
				page: 1,
				// 总记录数
				total: 1,
				// 数据是否准备好
				ready: false
			}
		},

		// 页面返回时，移除事件监听
		onBackPress() {
			this.OFF('learun-layer-picker')
		},
		// 页面卸载时，移除事件监听
		onUnload() {
			uni.setNavigationBarTitle({
				title: this.$t("选择一个选项")
			})
			this.OFF('learun-layer-picker')
		},

		async onLoad() {
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面
				this.init()
			}
		},

		methods: {
			// 页面初始化方法
			init() {
				// 获取页面传递的列配置和选项数据
				const {
					columns,
					options
				} = this.GET_PARAM()
				this.options = options
				this.showOptions = []
				this.columns = columns
				// 获取列表数据
				this.fetchList()
				this.ready = true
			},

			// 列表项点击事件处理方法
			itemClick(item) {
				// 触发自定义事件，传递选中的列表项数据
				this.EMIT('learun-layer-picker', item)
				// 返回上一页
				this.NAV_BACK()
			},

			// 搜索框变化事件处理方法
			searchChange(isClear) {
				if (isClear) {
					// 清空搜索框文本
					this.searchText = ''
					// 显示所有选项数据
					this.showOptions = this.options
				}
				// 重置页码和总记录数
				this.page = 1
				this.total = 1
				this.showOptions = []
				// 重新获取列表数据
				this.fetchList()
			},

			// 获取列表数据方法
			fetchList() {
				// 如果当前页码大于总记录数，不再获取数据
				if (this.page > this.total) {
					return
				}
				let options = []
				if (this.searchText) {
					// 根据搜索文本过滤选项数据
					options = this.options.filter(t => {
						let res = false
						for (const col of this.columns) {
							const item = (t[col.prop] || '') + ''
							if (item.indexOf(this.searchText) != -1) {
								res = true
								break
							}
						}
						return res
					})
				} else {
					// 没有搜索文本，显示所有选项数据
					options = this.options
				}
				// 更新总记录数
				this.total = options.length
				// 对选项数据进行分页处理
				const list = this.PAGINATION(this.page, 20, options)
				// 将分页后的数据追加到显示列表中
				this.showOptions = this.showOptions.concat(list)
				// 页码加 1
				this.page++
			}
		},
		computed: {
			// 这里没有定义计算属性
		},
		/**
		 * 上拉加载回调函数
		 */
		onReachBottom() {
			// 上拉加载时，继续获取列表数据
			this.fetchList()
		}
	}
</script>