<template>
	<view>
		<view @click.stop="handleClick" class="learun-input learun-input-border"
			:class="{'learun-input-placeholder':VALIDATENULL(value)}">
			<view class="learun-input__content"><text>{{label()}}</text></view>
			<view v-if="VALIDATENULL(value)" class="learun-input-icon-right learun-input-icon-right-bottom">
				<uni-icons type="bottom" size="14" color="#c0c4cc"></uni-icons>
			</view>
			<view v-else-if="!disabled" class="learun-input-icon-right">
				<view @tap.stop="handleClear">
					<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'learun-custom-layer-picker',
		props: {
			value: {
				default: null
			},
			placeholder: {
				type: String,
				default: '请选择'
			},
			disabled: {
				type: Boolean,
				default: false
			},
			clearable: {
				type: Boolean,
				default: true
			},
			params: {
				type: Object,
				default: () => {
					return {}
				}
			},
			title: String,
			nav: String,
			validate: Function,
			getLabel: Function

		},

		methods: {
			handleClick() {
				if (this.disabled) {
					return
				}

				if (!this.myValidate()) {
					return
				}

				this.ONCE('learun-custom-layer-picker', ({
					value
				}) => {

					this.$emit('input', value)
					this.$emit('change', value)
				})

				if (this.nav) {
					this.NAV_TO(this.nav, {
						value: this.value,
						params: this.params
					}, true)
				}
			},
			handleClear() {
				this.$emit('input', undefined)
				this.$emit('change', undefined)
			},
			label() {
				if (this.VALIDATENULL(this.value)) {
					return this.$t(this.placeholder)
				} else if (this.getLabel) {
					return this.$t(this.getLabel(this.value))
				} else {
					return this.$t(this.value)
				}
			},
			myValidate() {
				if (this.validate) {
					return this.validate(this.params);
				} else {
					return true
				}
			}
		}
	}
</script>