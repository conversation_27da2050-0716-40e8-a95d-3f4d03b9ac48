<template>
  <view class="learun-customform-tab">
    <view v-if="title" class="learun-customform-tab__title">
      <text>{{ $t(title) }}</text>
    </view>
    <scroll-view
      class="scroll-tab"
      scroll-x="true"
      scroll-left="10"
      :scroll-with-animation="true"
    >
      <block v-for="(item, index) in showTabList" :key="index">
        <view
          class="scroll-tab-item"
          :class="{ active: tabIndex == index }"
          v-if="item.display != false"
          @tap="toggleTab(index)"
        >
          {{ item.label }}
          <view class="scroll-tab-line"></view>
        </view>
      </block>
    </scroll-view>

    <view v-for="(row, index) in showTabList" :key="index">
      <learun-customform-tab-item
        v-if="row.display != false"
        :rowData="row"
        :isOpen="index == tabIndex"
        :getDataSource="getDataSource"
        :formId="formId"
        :tableId="tableId"
        :editMode="editMode"
        @addRow="addTableRow"
        @deleteRow="deleteTableRow"
        @rowChange="rowChange"
        @input="setValue"
        @change="handleChange"
        @btnClick="handleBtnClick"
        ref="gridtable"
      />
    </view>
  </view>
</template>

<script>
import get from "lodash/get";
export default {
  name: "learun-customform-tab",
  props: {
    title: String,
    editMode: {
      type: Boolean,
      default: true,
    },
    value: {
      type: Array,
      default: () => [],
    },

    component: {},
    getDataSource: Function,
    // formData:{},
    tableId: String,
    formId: String,
    required: Boolean,
    formId: String,
  },
  data() {
    return {
      myData: {},
      isOpen: true,
      tabIndex: 0,
    };
  },
  computed: {
    tabList() {
      return this.component.children;
    },
    tabContent() {
      return this.tabList[this.tabIndex].children;
    },
    formData() {
      return this.GET_DATA(`learun_form_data_${this.formId}`);
    },
    showTabList() {
      let res = this.tabList;
      if (
        this.VALIDATENULL(this.component.config.tabKey) ||
        this.VALIDATENULL(this.component.config.options)
      ) {
        return res;
      } else {
        this.component.config.options.forEach((item) => {
          if (
            !this.VALIDATENULL(item.label) &&
            !this.VALIDATENULL(item.value)
          ) {
            if (this.formData[this.component.config.tabKey] == item.value) {
              res.find((t) => t.i == item.label).display = !!item.display;
            } else {
              res.find((t) => t.i == item.label).display = !item.display;
            }
          }
        });
      }
      return res;
    },
  },
  watch: {
    showTabList: {
      handler(tabList) {
        // 如果隐藏的是当前的展示页，找到第一个显示的tab
        if (tabList[this.tabIndex].display == false) {
          for (let i = 0; i < tabList.length; i++) {
            if (tabList[i].display != false) {
              this.tabIndex = i;
              break;
            }
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },

  created() {},

  methods: {
    //切换选项卡
    toggleTab(index) {
      this.tabIndex = index;
    },
    handleClickTitle() {
      this.isOpen = !this.isOpen;
    },

    handleBtnClick(event) {
      this.$emit("btnClick", event);
    },
    handleChange(event) {
      this.$emit("change", event);
    },

    setValue(event) {
      this.$emit("input", event);
    },
    getValue(path) {
      return get(this.GET_DATA(`learun_form_data_${this.formId}`), path);
    },
    rowChange({ event, col }) {
      this.$emit("rowChange", {
        event,
        col,
      });
    },
    addTableRow(event) {
      this.$emit("addRow", event);
    },
    deleteTableRow({ event, col }) {
      this.$emit("deleteRow", {
        event,
        col,
      });
    },
    async validate() {
      if (this.$refs.gridtable) {
        for (let i = 0, len = this.$refs.gridtable.length; i < len; i++) {
          const res = await this.$refs.gridtable[i].validate();
          if (!res) {
            return false;
          }
        }
      }

      return true;
    },
  },
};
</script>
<style lang="scss" scoped>
.learun-customform-tab {
  &__title {
    font-size: 13px;
    color: #666666;
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
  }

  &__item {
    display: flex;
    align-items: center;

    .tab-title {
      padding: 10px;
    }

    .tab-title_active {
      color: #007aff;
      font-weight: bold;
    }
  }

  margin-bottom: 16px;
}

.scroll-tab {
  white-space: nowrap;
  margin-bottom: 10px;
  text-align: center;
}

.scroll-tab-item {
  display: inline-block;
  margin: 10px 15px 0 15px;
}

.active {
  color: #007aff;
}

.active .scroll-tab-line {
  border-bottom: 2px solid #007aff;
  height: 0;
}
</style>
