<template>
	<view class="page login-page" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<view class="content">
			<view class="head-banner">

				<view mode="aspectFit" class="logo" style="background-image: url('/csp_core_app/static/logo.png')">
				</view>
				<view class="title">
					<text>Loading...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				username: '',   
				password: '',
				password2: '', //密码明码
				ready: false,
				showApiRootSelector: false,
				currentApiRoot: '',
				apiRootList: [],
				devAccountList: [],
				qywxID: '',
				searchData: {},
				opencode: '',
			}
		},
		async onLoad(options) { //option为object类型，会序列化上个页面传递的参数
			// const index = this.DEV ? this.LEARUN_CONFIG('devApiHostIndex') : this.LEARUN_CONFIG('prodApiHostIndex')
			// const apiRootList =  this.LEARUN_CONFIG('apiHost')
			// this.currentApiRoot = apiRootList[index]
			// this.apiRootList = apiRootList.map((t)=>({label:t,value:t}))

			let res = null
			this.searchData.openid = options.code
			this.opencode = options.code

			console.log(42, options)
			//--------快捷表单跳转参数--------------
			if (options && options.state) {
				const instate = options.state
				if (instate.indexOf("create") != -1) {
					let inparam = {};
					inparam.optype = 'create'
					inparam.toformid = instate.replace("create", "")

					// if (inparam.toformid.indexOf("_") >= 0) {
					// 	inparam.toformid.subString(1, inparam.toformid.length)
					// }

					this.SET_GLOBAL('createformparam', inparam)
					//console.log(inparam,"inparam")
				}
				//跳转至明细页面，
				if (instate.indexOf("workflowDetail") != -1) {
					var NavToPagesUrl = "/pages/navToPages?page='" + instate + "'";
					this.SET_GLOBAL("NavToPagesUrl", NavToPagesUrl);
				}
				if (instate.indexOf("workflowList") != -1) {
					var NavToPagesUrl = "/pages/navToPages?page='/pages/workflow/mytask/list'";
					this.SET_GLOBAL("NavToPagesUrl", NavToPagesUrl);
				}
				//options.optype && options.toformid	

			}
			//---------------------------------------

			this.HIDE_LOADING()
			this.TAB_TO('/pages/home')
		},

		methods: {
			// 页面初始化
			async init() {
				this.ready = true
			},
		}
	}
</script>

<style lang="scss" scoped>
	.login-page {
		display: flex;
		justify-content: center;
		flex-direction: column;

		.head-banner {
			margin-bottom: 32px;

			.title {
				display: block;
				margin: 8px 0;
				font-size: 10px;
				margin-bottom: 16px;
				color: $uni-main-color;
			}

			.devTitle {
				color: $uni-error;
			}

			.subTitle {
				font-size: 16px;
				color: $uni-info;
			}

			.logo {
				background-size: contain;
				height: 36px;
				width: 50px;
				text-align: center;
				display: inline-block;
				border-radius: 2px;
			}

			.login-item {
				padding: 12rpx 0;
				border-bottom: 1px solid #eee;
				margin-bottom: 20rpx;

				/deep/ .u-icon {
					width: 80rpx;
				}
			}

			.intro {
				font-size: 12px;
				margin-top: 8px;
				color: $uni-base-color;
			}

			.subIntro {
				color: $uni-extra-color;
			}
		}

		.content {
			text-align: center;
			width: 100%;
			padding: 0 24px;
			box-sizing: border-box;
		}

		.footer {
			position: absolute;
			left: 0;
			right: 0;
			bottom: 8px;
			bottom: calc(8px + env(safe-area-inset-bottom));
			text-align: center;
			font-size: 12px;
			color: $uni-info;

			/* #ifdef MP-ALIPAY */
			bottom: 8px;
			/* #endif */
		}
	}
</style>