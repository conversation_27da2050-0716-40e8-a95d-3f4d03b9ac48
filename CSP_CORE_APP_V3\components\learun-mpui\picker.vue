<template>
	<view>
		<view 
			@click.stop="handleClick" 
			:class="['learun-input','learun-input-border',{'learun-input-placeholder':myValue == -1}]"
			>
			<view class="learun-input__content" ><text>{{$t(label)}}</text></view>
			
			
			<view v-if="myValue == -1" class="learun-input-icon-right learun-input-icon-right-bottom">
				<uni-icons  type="bottom" size="14" color="#c0c4cc"></uni-icons>
			</view>
			<view  v-else-if="!disabled" class="learun-input-icon-right">
				<view @tap.stop="handleClear">
					<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
				</view>
			</view>
			
			
			
		</view>
		<learun-picker-popup
			:options="myOptions"
			@change="change"
			ref="myPopup"
		>
		</learun-picker-popup>
	</view>
</template>

<script>
	export default {
		name:'learun-picker',
		emits: ['change', 'input'],
		props:{
			value:[String,Number],
			placeholder:{
				type:String,
				default:'请选择'
			},
			labelKey:{
				type:String,
				default:'label'
			},
			valueKey:{
				type:String,
				default:'value'
			},
			options:{
				type:Array,
				default:()=>[]
			},
			disabled:Boolean
		},
		data(){
			return {
			}
		},
		computed:{
			myValue:{
				get(){					
					if(this.VALIDATENULL(this.value)){
						return -1
					}
					return this.myOptions.findIndex(t=>t.value ==  this.value)
				},
				set(val){
					if(val == -1){
						this.$emit('input', '')
						this.$emit('change', undefined)
					}
					else{
						this.$emit('input', this.myOptions[val].value)
						this.$emit('change', this.options[val])
					}
				}
			},
			myOptions(){
				return this.options.map(t=>({value:t[this.valueKey],label:t[this.labelKey] || ''}))
			},
			label(){
				if(this.myValue == -1){
					return this.$t(this.placeholder) || ''
				}
				else{
					return this.myOptions[this.myValue].label || ''
				}
			}
		},
		methods:{
			change({index}){
				this.myValue = index
			},
			handleClear(){
				if(this.disabled){
					return
				}
				this.myValue = -1
			},
			handleClick(){
				if(this.disabled){
					return
				}
				this.$refs.myPopup.open(this.myValue != -1 ? this.myOptions[this.myValue].value : undefined)
			}
		}
	}
</script>


