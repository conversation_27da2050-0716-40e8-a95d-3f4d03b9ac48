<!-- 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
* Purpose      : 新闻控件
* Date         : 12 dec 2023 
* Author       : <PERSON> (ISD/CSC)
* Note         : 
* -------------------------------------------------
* Date				Auther				Descript
* 12 dec 2023	<PERSON>			the first version
* 
* the latest update: 12 dec 2023 16点19分
*~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 -->

<template>
	<view style="margin-bottom: 10rpx;">
		<template>
			<uni-segmented-control
				:current="tab" 
				:values="tabList"
				@clickItem="changeTab" 
				styleType="text" 
				class="category-title"
			>
			</uni-segmented-control>
			
			<view :key="item.id" v-for="item in filterList" class="l-min-databoard" @click.stop="handleRowClick(item)">
			  <view class="image">
					<image :src="item[dataImageKey] ? item[dataImageKey] : defaultImage"></image>
				</view>
			  <view class="item-right">
			    <view class="title">{{ item[dataTitleKey] }}</view>
					<view class="time">
						<uni-dateformat :date="item[dataTimeKey]" :threshold="[0,0]" format="yyyy-MM-dd"></uni-dateformat>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	let context = null
	let touchs = []

	export default {
		name: 'crystal-news',
		props: {
			dataCode: {
				type: String,
				default: 'news'
			},
			dataImageKey: {
				type: String,
				default: 'icon'
			},
			dataTimeKey: {
				type: String,
				default: 'time'
			},
			dataTitleKey: {
				type: String,
			},
			dataCategoryKey: {
				type: String,
			}
		},
		data() {
			return {
				list: [],
				tab: 0,
				tabList: [this.$t('最新资讯'),this.$t('公告')],
				defaultImage: "https://webapp.crystal-csc.cn/weixin/View/QY_News/image.aspx?FilePath=/ANNOUNCEMENT/272/icon.png"
			}
		},

		async created() {
			if(this.dataCode) {
			  this.list = await this.FETCH_DATASOURCE(this.dataCode);
			}
		},
		
		computed: {
			filterList() {
				return this.list.filter(t => {
					return t[this.dataCategoryKey] == this.tab
				})
			}
		},

		async onLoad() {
		},

		methods: {
			// 点击 Tab 页切换；储存当前滚动位置；有搜索条件时切页必重刷列表；空列表时切页尝试拉取一次
			async changeTab({currentIndex}) {
				this.tab = currentIndex
			},
			handleRowClick(row) {
				let param = {
					"html": row.content.replaceAll("PDFShow","https://webapp.crystal-csc.cn/weixin/View/QY_News/PDFShow"),
					"title": row[this.dataTitleKey],
					"time": row[this.dataTimeKey],
				}
				this.SET_PARAM(param);
				this.NAV_TO("/pages/loadWeb", param, true)
			}
		}
	}
</script>

<style lang="less" scoped>
	.l-min-databoard{
		// text-align: center;
		display: flex;
		margin: 0 10rpx;
		border-bottom: 1px solid #DCDCDC;
		padding: 8rpx 0;
	
		.item-right {
			width: 80%;
		}
		.title {
			overflow: hidden;
			-webkit-line-clamp: 1;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
		}
		.image image {
			width: 140rpx;
			height: 100rpx;
			margin-right: 20rpx;
			font-size: 60rpx;
			text-align: center;
		}
		.time {
			text-align: right;
			color: #999999;
			margin-top: 20rpx;
		}	
	}
	.category-title {
		border-bottom: 1px solid #DCDCDC;
		margin-top: 10px;
		padding-bottom: 10px;
	}
</style>