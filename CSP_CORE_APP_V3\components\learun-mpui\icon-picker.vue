<template>
	<view class="learun-input learun-input-border" @click.stop="handleClick"  :class="{'learun-input-placeholder':VALIDATENULL(value)}" >
		<view class="learun-input__content" ><text>{{$t(text)}}</text></view>
		<view class="learun-input-icon-right">
			<uni-icons v-if="VALIDATENULL(value)"  type="bottom" size="14" color="#c0c4cc" ></uni-icons>
			<view   v-else-if="!disabled"  @tap.stop="handleClear" >
				<uni-icons type="clear" size="14" color="#c0c4cc" ></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'learun-icon-picker',
		props:{
			value: { default: null },
			placeholder:{
				type:String,
				default:'请选择'
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		computed:{
			text(){
				if(this.VALIDATENULL(this.value)){
					return $t(this.placeholder) || ''
				}
				return this.value
			}
		},
		methods:{
			handleClick(){
				if (this.disabled) {
				  return
				}
				
				this.ONCE('select-learun-icon', data => {
				  this.$emit('input', data)
				  this.$emit('change', data)
				})
				
				this.NAV_TO_LAYER('/pages/common/learun-icon-picker')
			},
			handleClear(){
				if (this.disabled) {
				  return
				}
				
				this.$emit('input', '')
				this.$emit('change', undefined)
			}
		}
	}
</script>
