<template>
	<view>
		<view style="padding: 8px;">
			<!-- 页面标题，使用多语言翻译，加粗居中显示 -->
			<view
				style="margin-bottom: 15px; font-size: 30px; color: rgb(0, 144, 255); text-align: center; font-weight: 600;">
				{{ $t(title) }}
			</view>
			<!-- 表单容器，使用uni-forms进行表单验证，绑定数据和验证规则 -->
			<uni-forms :modelValue="formData" :rules="rules" :label-width="100" ref="myForm">
				<!-- 身份证号码表单项，必填项 -->
				<uni-forms-item :label="$t('身份证号码')" name="idCardCode" required>
					<uni-easyinput v-model="formData.idCardCode" :placeholder="$t('请输入身份证号码')" :inputBorder="false"
						class="form-item learun-flex-box" />
				</uni-forms-item>
				<!-- 公司选择表单项，必填项，使用自定义选择器组件 -->
				<uni-forms-item :label="$t('公司')" name="company_Code" required>
					<learun-picker v-model="formData.company_Code" :placeholder="$t('请选择公司')" :options="companyList" />
				</uni-forms-item>
				<!-- 工号表单项，必填项 -->
				<uni-forms-item :label="$t('工号')" name="emp_No" required>
					<uni-easyinput v-model="formData.emp_No" :placeholder="$t('请输入完整工号')" :inputBorder="false"
						class="form-item learun-flex-box" />
				</uni-forms-item>
			</uni-forms>
			<!-- 提示信息，显示红色警告文本 -->
			<view style="font-size: 15px; color: red;">{{ $t(fristMessage) }}</view>
		</view>

		<view class="padding">
			<!-- 提交按钮，点击触发密码重置流程 -->
			<button @click="submit" type="primary">{{$t('设置密码')}}</button>
		</view>
	</view>
</template>

<script>
	import {
		forEach
	} from 'lodash';
	export default {
		data() {
			return {
				formData: {
					idCardCode: '', // 身份证号码输入值
					company_Code: '', // 公司代码选择值
					emp_No: '', // 工号输入值
				},
				rules: {
					// 身份证号码验证规则：必填
					'idCardCode': {
						rules: [{
							required: true,
							errorMessage: this.$t('请输入身份证号码'),
						}]
					},
					// 公司选择验证规则：必填
					'company_Code': {
						rules: [{
							required: true,
							errorMessage: this.$t('请选择公司'),
						}]
					},
					// 工号验证规则：必填
					'emp_No': {
						rules: [{
							required: true,
							errorMessage: this.$t('请输入工号'),
						}]
					},
				},
				companyList: [], // 公司列表选项
				title: "", // 页面标题
				showTip: "", // 提示信息（来自路由参数）
				fristMessage: "" // 首次提示信息（来自路由参数）
			}
		},

		async onLoad() {
			this.LOADING({
				title: this.$t('加载中')
			}); // 显示加载提示
			this.title = this.$t("重置查询密码"); // 设置默认标题
			const param = this.GET_PARAM(); // 获取路由参数
			// 解析路由参数并更新页面显示
			if (param) {
				param.title && (this.title = param.title);
				param.showTip && (this.showTip = param.showTip);
				param.fristMessage && (this.fristMessage = param.fristMessage);
			}
			this.SET_TITLE(this.title); // 设置页面标题
			// 加载公司列表数据（从数据源获取）
			const company = await this.FETCH_DATASOURCE("Payroll_Company_Code");
			// 转换数据格式为选择器所需的{value, label}结构
			forEach(company, v => {
				this.companyList.push({
					value: v.company_code,
					label: v.company_code
				});
			})
			this.HIDE_LOADING(); // 隐藏加载提示
			// 显示路由传递的提示信息
			if (this.showTip) {
				this.TOAST(this.showTip);
			}
		},

		methods: {
			// 表单提交处理方法
			async submit() {
				// 验证表单有效性
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm);
				if (err) return; // 验证失败则返回

				const {
					idCardCode,
					company_Code,
					emp_No
				} = this.formData;
				// 获取预存的身份证号码前15位（假设数据源中存储脱敏后的身份证）
				const id_noList = await this.FETCH_DATASOURCE("ID_NO");
				const id_no = id_noList.length > 0 ? id_noList[0]["id_no"] : "";

				// 身份证号码脱敏处理（保留前6位和中间8-17位）
				const inputId_No = idCardCode.length === 18 ?
					idCardCode.substring(0, 6) + idCardCode.substring(8, 17) :
					idCardCode;
				// 验证输入的身份证是否与预存一致（防止修改他人密码）
				if (inputId_No !== id_no) {
					this.TOAST(this.$t('请输入本人的身份证号码！！'));
					return;
				}

				this.LOADING(this.$t('验证…')); // 显示验证加载提示
				// 发送员工信息验证请求
				const success = await this.HTTP_POST({
					url: '/hrattf008/checkEmployee',
					data: {
						ID_Card_No: idCardCode,
						Company_Code: company_Code,
						Emp_No: emp_No
					},
					errorTips: this.$t('验证失败')
				});
				this.HIDE_LOADING(); // 隐藏加载提示
				if (!success) return; // 验证失败则返回

				// 构造跳转参数，携带验证通过的员工信息
				const param = {
					Company_Code: company_Code,
					Emp_No: emp_No,
					idCardCode: idCardCode,
					needOldPw: false // 标识无需旧密码（首次设置/重置场景）
				};
				// 跳转到密码设置页面，并传递参数
				this.JUMP_TO('/pages/password/set', param, true);
				// this.TOAST('密码修改成功') // 预留的成功提示（当前代码未启用）
			},
		}
	}
</script>

<style lang="less" scoped>
	.form-item {
		/* 表单项公共样式：边框和圆角 */
		border: 1px solid #F0F0F0;
		border-radius: 4px;
	}
</style>