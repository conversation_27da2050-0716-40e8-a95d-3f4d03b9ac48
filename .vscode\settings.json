{"json.schemas": [{"fileMatch": ["app-config.json"], "schema": {"type": "object", "properties": {"template": {"description": "<PERSON><PERSON><PERSON> hình riêng của template ZaUI Coffee", "type": "object", "properties": {"name": {"type": "string", "enum": ["CSP of CETVN"], "description": "Tên template. <PERSON><PERSON><PERSON> b<PERSON><PERSON> phải mang giá trị CSP of CETVNe"}, "headerLogo": {"type": "string", "description": "Đường dẫn tuyệt đối, bắt buộc phải có \"https://\". Nếu bạn muốn sử dụng logo của Mini App, bạn có thể lấy đường dẫn bằng cách truy cập trang quản lý ứng dụng trên mini.zalo.me, rồ<PERSON> nhấn chuột phải vào logo và chọn \"Copy Image Address\"."}, "primaryColor": {"type": "string", "description": "<PERSON><PERSON><PERSON> chủ đạo (<PERSON><PERSON> dụ<PERSON> cho n<PERSON>, li<PERSON><PERSON> kế<PERSON>, bi<PERSON><PERSON> tượ<PERSON>,...)", "pattern": "^#([A-Fa-f0-9]{6})$"}, "currencySymbol": {"type": "string", "description": "Đơn vị tiền tệ.", "default": "đ"}, "prefixCurrencySymbol": {"type": "boolean", "description": "Bật lên đối với các đơn vị tiền tệ mà ký hiệu cần phải được đặt trước số tiền (ví dụ: $5). Hoặc giữ nguyên nếu ký hiệu cần đặt sau số tiền (ví dụ: 500đ).", "default": false}}, "required": ["name"]}}}}]}