<template>
	<view class="learun-file-picker">
		<upload-image
			v-if="showType === 'grid'" 
			:readonly="disabled"
			:image-styles="imageStyles" 
			:files-list="fileList" 
			:limit="limitLength" 
			:disablePreview="disablePreview"
			:delIcon="delIcon" 
			@choose="choose" 
			@delFile="delFile">
			<slot>
				<view class="is-add">
					<view class="icon-add"></view>
					<view class="icon-add rotate"></view>
				</view>
			</slot>
		</upload-image>
		<upload-file
			v-if="showType !== 'grid'" 
			:readonly="disabled"
			:list-styles="listStyles" 
			:files-list="fileList" 
			:showType="showType" 
			:delIcon="delIcon"
			
			@choose="choose" 
			@delFile="delFile"
			>
			<slot>
				<button type="primary" size="mini">{{$t('选择文件')}}</button>
			</slot>
		</upload-file>
	</view>
</template>

<script>
import uploadImage from './file-picker/upload-image.vue'
import uploadFile from './file-picker/upload-file.vue'

import {
	chooseAndUploadFile
} from './file-picker/choose-and-upload-file.js'

import {
	get_extname,
	get_files_and_is_max,
	get_file_data
} from './file-picker/utils.js'	

export default {
  name: 'learun-upload-file',
	components: {
		uploadImage,
		uploadFile
	},
  props: {
		value:{},
		
		listType: String,
		
		disabled:Boolean,
		accept:{
			type:String,
			default:''
		},
		
		disablePreview: {
			type: Boolean,
			default: false
		},
		delIcon: {
			type: Boolean,
			default: true
		},
		// 最大选择个数 ，h5只能限制单选或是多选
		limit: {
			type: [Number, String],
			default: 9     // 文件限制数量
		},
		sizeType:String, // 文件大小单位
		maxSize:[String, Number],  // 单个文件最大大小
		isTip:Boolean,   // 是否显示提示语
		
		listStyles: {
			type: Object,
			default () {
				return {
					// 是否显示边框
					border: true,
					// 是否显示分隔线
					dividline: true,
					// 线条样式
					borderStyle: {}
				}
			}
		},
		imageStyles: {
			type: Object,
			default () {
				return {
					width: 'auto',
					height: 'auto'
				}
			}
		},
		
		isOne:Boolean
		
	},
	watch:{
		value:{
			handler(val){
				if (this.VALIDATENULL(val)) this.localValue = []
				else this.localValue = val
			},
			immediate: true
		}
	},
	
	data() {
		return {
			files: [],
			localValue: []
		}
	},
	computed:{
		fileList(){
			return this.localValue.filter(t=>t.uploadType != 'remove')
		},
		fileMediatype(){
			if(this.listType == 'picture-card'){
				return 'image'
			}
			else{
				return 'all'
			}
		},
		showType() {
			// console.log('this.showType: ',this.showType);
			if (this.fileMediatype === 'image') {
				return 'grid'
			}
			return 'list'
		},
		limitLength() {
			if (!this.limit) {
				return 1
			}
			if (this.limit >= 9) {
				return 9
			}
			return this.limit
		}
	},
	methods:{		
		/**
		 * 选择文件
		 */
		choose() {
			if (this.disabled) return
			if (this.files.length >= Number(this.limitLength)) {
				uni.showToast({
					title: `您最多选择 ${this.limitLength} 个文件`,
					icon: 'none'
				})
				return
			}
			this.chooseFiles()
		},
		
		/**
		 * 删除文件
		 * @param {Object} index
		 */
		delFile(id) {
			const fileItem = this.localValue.find(t=>t.fileID == id)
			if(fileItem){
				if(fileItem.uploadType == 'old'){
					fileItem.uploadType = 'remove'
				}
				else{
					const index = this.localValue.findIndex(t=>t.fileID == id)
					if(index != -1){
						this.localValue.splice(index,1)
					}

					const index2 = this.files.findIndex(t=>t.uuid == fileItem.uuid)
					if(index2 != -1){
						this.files.splice(index2,1)
					}
					
				}
			}
			
			//console.log(this.localValue)
			this.$emit('change', this.localValue)
			this.$emit('input', this.localValue)
		},
		
		/**
		 * 处理返回事件
		 */
		setEmit() {
			const data = this.backObject(this.files)
			if (!this.localValue) {
				this.localValue = []
			}
			this.localValue = this.localValue.filter(t=>t.uploadType != 'add')
			this.localValue.push(...data)			
			
			//console.log(this.localValue,this.files,'setEmit')
			
			
			this.$emit('change', this.localValue)
			this.$emit('input', this.localValue)
		},
		
		/**
		 * 处理返回参数
		 * @param {Object} files
		 */
		backObject(files) {
			let newFilesData = []
			files.forEach(v => {
				newFilesData.push({
					extname: v.extname,
					fileType: v.fileType,
					image: v.image,
					name: v.name,
					path: v.path,
					size: v.size,
					fileID:this.GUID(),
					uuid:v.uuid,
					url: v.url,
					uploadType:'add'
				})
			})
			
			return newFilesData
		},
		
		/**
		 * 选择文件并上传
		 */
		chooseFiles() {
			const _extname = get_extname(this.accept)//
			// 获取后缀
			chooseAndUploadFile({
				type: this.fileMediatype,
				compressed: false,
				sizeType: ['original', 'compressed'],
				// TODO 如果为空，video 有问题
				extension: _extname.length > 0 ? _extname : undefined,
				count: this.limitLength - this.files.length, //默认9
				onChooseFile: this.chooseFileCallback,
				onUploadProgress: progressEvent => {
					//console.log(progressEvent,'progressEvent')
					//this.setProgress(progressEvent, progressEvent.index)
				}
			})
			.then(result => {
				this.setEmit()
				//this.setSuccessAndError(result.tempFiles)
			})
			.catch(err => {
				console.log('选择失败', err)
			})
		},
		
		/**
		 * 选择文件回调
		 * @param {Object} res
		 */
		async chooseFileCallback(res) {
			const _extname = get_extname(this.accept)
			// 如果这有一个文件 ，需要清空本地缓存数据
			if (this.isOne) {
				this.files = []
			}
		
			let {
				filePaths,
				files
			} = get_files_and_is_max(res, _extname)
			
			
			if (!(_extname && _extname.length > 0)) {
				filePaths = res.tempFilePaths
				files = res.tempFiles
			}
		
			let currentData = []
			let errorNum = 0
			for (let i = 0; i < files.length; i++) {
				if (this.limitLength - this.files.length <= 0) break
				
				files[i].uuid = Date.now()
				let filedata = await get_file_data(files[i], this.fileMediatype)
				filedata.progress = 100
				filedata.status = 'ready'
				
				if(this.compareSize(filedata.size)){
					this.files.push(filedata)
					currentData.push({
						...filedata,
						file: files[i]
					})
				}
				else{
					errorNum++
				}
			}
			res.tempFiles = files
			
			if(errorNum > 0){
				uni.showToast({
					title: `${errorNum} 个文件大小超出${this.maxSize}${this.sizeType}`,
					icon: 'none',
					duration: 5000
				})
			}
		},
		compareSize(fileSize){
			if(this.maxSize && this.sizeType){
				let size = fileSize;
				switch(this.sizeType){
						case 'GB':
								size = 1024 * 1024 * 1024 * Number(this.maxSize);
								break;
						case 'MB':
								size = 1024 * 1024 * Number(this.maxSize);
								break;
						case 'KB':
								size = 1024 * Number(this.maxSize);
								break;
				}
				if(size <  fileSize){
						return false;
				}
			}
			return true
		}
	}
}
</script>


<style lang="scss" scoped>
	.learun-file-picker {
		box-sizing: border-box;
		overflow: hidden;		
		
		uni-button[size=mini], button[size=mini]{
			height: 36px !important;
			width: 120px;
			margin-left:0;
		}
	}

	.is-add {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
		justify-content: center;
	}

	.icon-add {
		width: 50px;
		height: 5px;
		background-color: #f1f1f1;
		border-radius: 2px;
	}

	.rotate {
		position: absolute;
		transform: rotate(90deg);
	}
</style>
