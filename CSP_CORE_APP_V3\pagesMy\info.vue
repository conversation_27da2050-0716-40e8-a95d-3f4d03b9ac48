<template>
	<view class="page">
		<uni-list>
			<uni-list-item :title="$t('账号')" :rightText="currentUser.f_Account"></uni-list-item>
			<uni-list-item :title="$t('姓名')" :rightText="currentUser.f_RealName"></uni-list-item>
			<uni-list-item :title="$t('性别')"
				:rightText="Number(currentUser.f_Gender) === 1 ? $t('男') : $t('女')"></uni-list-item>

			<uni-list-item :title="$t('公司')" :rightText="userInfo.company"></uni-list-item>
			<uni-list-item :title="$t('部门')" :rightText="userInfo.department"></uni-list-item>
			<uni-list-item :title="$t('岗位')" :rightText="userInfo.post"></uni-list-item>
			<uni-list-item :title="$t('角色')" :rightText="userInfo.role"></uni-list-item>
		</uni-list>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {}
			}
		},

		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("我的信息")
			})
			if (await this.PAGE_LAUNCH()) {
				await this.init()
			}
		},

		methods: {
			// 初始化页面
			async init() {
				if (this.GET_GLOBAL('loginUser') == null) {
					return;
				}
				//const { F_CompanyId: companyId, F_DepartmentId: departmentId } = this.currentUser



				const info = {
					company: (await this.GET_CURRENT_COMPANY()).f_FullName || this.$t('总集团公司'),
					department: (await this.GET_CURRENT_DEPARTMENT()).f_FullName || '(无)',
					post: '(无)',
					role: '(无)'
				}

				if (this.currentUser.role && this.currentUser.role[0]) {
					info.role = this.currentUser.role.map(t => t.f_FullName).join(' · ')
				}

				if (this.currentUser.post && this.currentUser.post[0]) {
					info.post = this.currentUser.post.map(t => t.f_Name).join(' · ')
				}

				this.userInfo = info
			}
		},

		computed: {
			// 当前用户对象
			currentUser() {
				return this.GET_GLOBAL('loginUser') || {}
			}
		}
	}
</script>