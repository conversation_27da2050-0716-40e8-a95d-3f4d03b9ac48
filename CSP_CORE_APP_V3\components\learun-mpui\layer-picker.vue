<template>
	<view>
		<view @click.stop="handleClick" class="learun-input learun-input-border" :class="{'learun-input-placeholder':VALIDATENULL(value)}" v-if="!onlyButton">
			<view class="learun-input__content" ><text>{{label()}}</text></view>
			<view class="learun-input-icon-right">
				<uni-icons v-if="VALIDATENULL(value)"  type="bottom" size="14" color="#c0c4cc" ></uni-icons>
				<view  v-else-if="!disabled"  @tap.stop="handleClear" >
					<uni-icons type="clear" size="14" color="#c0c4cc" ></uni-icons>
				</view>
			</view>
		</view>
		<view v-else>
			<button :disabled="disabled" :type="danger ? 'warn' : type" :size="size" @click.stop="handleClick">
				<view style="display: flex;align-items: center;">
					<learun-icon :type="iconName" :size="16" :isBlock="false" style="margin-right: 2px" v-if="iconName" :color="type=='default'? '': '#fff'"/>
					<text>{{$t(content)}}</text>
				</view>
			</button>
		</view>
	</view>
</template>

<script>

export default {
  name: 'learun-layer-picker',
  props: {
    value: { default: null },
		placeholder:{
			type:String,
			default:'请选择'
		},
		disabled:{
			type:Boolean,
			default:false
		},
		clearable:{
			type:Boolean,
			default:true
		},
		columns:{
			type:Array,
			default:() => []
		},
		options:{
			type:Array,
			default:()=>[]
		},
		labelKey:{
				type:String,
				default:'label'
		},
		valueKey:{
				type:String,
				default:'value'
		},
    	title:String,
		onlyButton: {
			type:Boolean,
			default: false
		},
		content: String,
		type: {
			type:String,
			default: 'primary'
		},
		size: {
			type:String,
			default: 'mini'
		},
		danger: Boolean,
		iconName: String,

  },

  methods: {
		handleClick(){
			if (this.disabled) {
			  return
			}
			
			this.ONCE('learun-layer-picker', data => {
				// console.log(this.valueKey,data,'learun-layer-picker');
			  this.$emit('input', data[this.valueKey])
			  this.$emit('change', data)
				
				// console.log(this.value);
			})
			
			
			
			this.NAV_TO_LAYER('/pages/common/learun-layer-picker', {columns:this.myColumns,options:this.options})
		},
		handleClear(){
			this.$emit('input', undefined)
			this.$emit('change', undefined)
		},
		label(){
			if(this.VALIDATENULL(this.value)){
				return this.$t(this.placeholder)
			}
			else{
				const res = this.options.find(t=>t[this.valueKey] == this.value)
				// console.log(res,'测试弹窗值')
				if(res){
					return res[this.labelKey]
				}
				else{
					return this.value
				}
			}
		},
  },

  computed: {
		
		
    displayPlaceholder() {
      if (this.disabled) {
        return ''
      }

      if (this.placeholder) {
        return this.$t(this.placeholder)
      }

      return this.title ? `${this.$t('请选择')}${this.$t(this.title)}` : this.$t('请选择…')
    },
		myColumns(){
			return this.columns.filter(t=>!t.hidden)
		}
  }
}
</script>
