<template>
	<view class="page desktop">
		<!-- 桌面配置渲染区域，homeSettingReady为true时显示 -->
		<view v-if="homeSettingReady">
			<!-- 数据可视化组件，传入桌面配置和数据获取方法 -->
			<learun-bi :config="deskScheme" :getData="getDesktopData" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 桌面配置加载完成标志
				homeSettingReady: false,
				// 是否需要加载任务统计数据（待办/已办/委托任务数量）
				isLoadTaskNum: false,
				// 是否需要加载任务列表数据（代办任务列表）
				isLoadTaskList: false,

				desktopId: '', // 当前桌面ID（从路由参数获取）
				deskScheme: {}, // 桌面配置方案（包含各组件布局和数据源配置）
				datasource: {}, // 存储各数据源数据（任务数据、图表数据等）
			}
		},

		async onLoad({
			id,
			title
		}) {
			uni.setNavigationBarTitle({
				title: this.$t("桌面设计")
			})
			// 页面启动验证及初始化
			if (await this.PAGE_LAUNCH()) {
				this.desktopId = id // 提取路由参数中的桌面ID
				this.SET_TITLE(title); // 设置页面标题
				await this.refresh() // 刷新页面数据
			}
		},

		// 下拉刷新处理
		onPullDownRefresh() {
			this.refresh().then(() => {
				this.TOAST('已更新数据') // 提示数据更新成功
				uni.stopPullDownRefresh() // 停止下拉刷新动画
			})
		},

		methods: {
			// 刷新页面数据（核心数据加载方法）
			async refresh() {
				this.LOADING('加载数据') // 显示加载进度提示
				// 重置数据加载状态
				this.isLoadTaskNum = false
				this.isLoadTaskList = false
				this.homeSettingReady = false

				await this.initSetting() // 初始化桌面配置和数据源

				this.SET_ALL_DATA(this.datasource) // 将数据源数据全局存储
				this.homeSettingReady = true // 标记配置准备完成

				this.HIDE_LOADING() // 隐藏加载进度提示
			},

			// 初始化桌面配置信息（核心配置解析方法）
			async initSetting() {
				const datasourceMap = {} // 存储已处理的数据源Code（去重）
				const datasourceList = [] // 收集需要加载的数据源Code

				// 获取桌面配置数据（从后端获取已发布的桌面配置）
				const deskSetting = await this.HTTP_GET({
					url: `/desktop/setting/release/${this.desktopId}`
				})
				const deskScheme = JSON.parse(deskSetting.lr_desktop_schemeEntity.f_Content) // 解析配置内容

				// 遍历配置项，确定需要加载的数据类型和数据源
				for (let schemeItem of deskScheme) {
					// 任务列表组件（需要加载代办任务列表）
					if (['app-mytasklist'].includes(schemeItem.type)) {
						this.isLoadTaskList = true
					}
					// 任务统计组件（需要加载任务数量统计）
					else if (['app-mytask'].includes(schemeItem.type)) {
						this.isLoadTaskNum = true
					}
					// 数据看板/图表组件（需要加载对应数据源）
					else if (['app-databoard'].includes(schemeItem.type)) {
						for (const databoardItem of schemeItem.config.list) {
							if (!datasourceMap[databoardItem.dataCode]) {
								datasourceMap[databoardItem.dataCode] = true
								datasourceList.push(databoardItem.dataCode)
							}
						}
					} else if (['app-chartbar', 'app-chartline', 'app-chartpie', 'app-datalist'].includes(schemeItem
							.type)) {
						if (!datasourceMap[schemeItem.config.dataCode]) {
							datasourceMap[schemeItem.config.dataCode] = true
							datasourceList.push(schemeItem.config.dataCode)
						}
					} else if (['app-chartlinebar'].includes(schemeItem.type)) {
						// 处理混合图表组件的多个数据源
						schemeItem.config.dataList.map((item) => {
							const {
								dataCode
							} = item;
							if (!datasourceMap[dataCode]) {
								datasourceMap[dataCode] = true
								datasourceList.push(dataCode)
							}
						})
					}
				}

				// 按配置顺序排序桌面组件（y轴坐标决定顺序）
				this.deskScheme = deskScheme.sort((a, b) => a.y - b.y)

				// 加载任务统计数据（待办/已办/委托任务数量）
				if (this.isLoadTaskNum) {
					await this.initWFTask()
				}

				// 加载任务列表数据（代办任务列表）
				if (this.isLoadTaskList) {
					await this.initWFList()
				}

				// 批量加载数据源数据（图表、数据看板等所需数据）
				for (const dataCode of datasourceList) {
					const res = await this.FETCH_DATASOURCE(dataCode)
					this.datasource[dataCode] = res || [] // 存储数据源结果
				}

				// 确保DOM更新后再标记配置完成（解决异步渲染问题）
				this.homeSettingReady = false
				this.$nextTick(() => {
					this.homeSettingReady = true
				})
			},

			// 更新流程任务统计数据（待办/已办/委托任务数量）
			async initWFTask() {
				const queryData = {
					rows: 1,
					page: 1,
					sidx: 'F_CreateDate DESC'
				} // 单页获取最新数据
				const queryData2 = {
					...queryData,
					sidx: 't1.F_CreateDate DESC'
				} // 调整排序字段

				// 并行请求三类任务数据
				const [unCompletedData, completedData, delegateData] = await Promise.all([
					this.HTTP_POST({
						url: '/workflow/process/uncompleted/mypage',
						params: queryData2,
						data: {}
					}), // 待办任务
					this.HTTP_POST({
						url: '/workflow/process/completed/mypage',
						params: queryData2,
						data: {}
					}), // 已办任务
					this.HTTP_GET({
						url: '/workflow/process/delegate/mypage',
						params: queryData
					}), // 委托任务
				])

				// 将任务数量存入数据源（供组件调用）
				this.datasource['nCompletedNum'] = unCompletedData.records
				this.datasource['completedNum'] = completedData.records
				this.datasource['delegateNum'] = delegateData.records
			},

			// 加载代办任务列表数据（前5条最新任务）
			async initWFList() {
				const queryData = {
					rows: 5,
					page: 1,
					sidx: 't1.F_CreateDate DESC'
				} // 获取前5条数据，按时间倒序
				const unCompletedData = await this.HTTP_POST({
					url: '/workflow/process/uncompleted/mypage',
					params: queryData,
					data: {}
				})
				this.datasource['taskList'] = unCompletedData.rows || [] // 存储任务列表
			},

			// 提供给learun-bi组件的数据获取方法
			getDesktopData({
				type,
				code
			}) {
				// 根据数据类型返回对应数据源
				switch (type) {
					// 任务数量统计（默认值处理：避免undefined）
					case 'nCompletedNum':
					case 'delegateNum':
					case 'completedNum':
						return this.GET_DATA(type) || 0
						// 任务列表数据
					case 'taskList':
						return this.GET_DATA(type) || []
						// 通用数据源（按code获取）
					case 'datasource':
						return this.GET_DATA(code) || []
				}
				return null // 未知类型返回null
			},

			// 功能按钮点击处理（代码中未完全实现，保留原有逻辑）
			funcListClick(e) {
				if (e.detail.index === 100) {
					this.moreClick(0)
					return
				}

				const item = this.funcListDisplay[e.detail.index]
				// 系统类型为2时跳转到自定义表单页面
				if (item.f_IsSystem === 2) {
					this.NAV_TO(`/pages/customapp/list?formId=${item.f_FormVerison}`, item, true)
					return
				}
				// 其他情况跳转到模块列表页
				this.NAV_TO(`/pages/${item.f_Url}/list`)
			},
		},

		computed: {
			// 计算属性（预留扩展，当前未使用）
		}
	}
</script>