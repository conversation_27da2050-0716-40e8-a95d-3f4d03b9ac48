/**
 * 系统表单流程通用的方法
 * 保存草稿，提交流程，审核流程。。。
 */
export default {
	data(){
		return {
			wfTitle:'',
			wfCode:'',
			wfProcessId:'',
			wfTaskId:'',
			wfData:[],
			wfCurrentNode:{},
			wfLogs:[],
			auditLines: [],
			auditLogList: [],
			
			wfIsDraft:false,
			needWorkflowInfo:false,
			wfIsCancel:false,
			wfIsRead:false,
			
			wfTab: 0,
			wfTabList: [this.$t('表单信息'),this.$t('审批日志'),this.$t('流程信息'),  /* this.$t('审核路线') */],
			wfButtons:[],
			
			wfFormData:null,
			wfCodeCopy: '',
			auditLogColumns: [
				{ label: this.$t('节点名称'), rowid: 'name' },
				{ label: '处理人', rowid: 'userNames' },
				{ label: '处理动作', rowid: 'content' },
				{ label: '处理时间', rowid: 'time' },
				{ label: '审批意见', rowid: 'des' },
			],
			wfTask: {},
		}
	},
	methods: {
		// 切换
		wfChangeTab({currentIndex}){
			this.wfTab = currentIndex
		},
		// 是否是流程模式
		isWorkflow() {
			return this.mode.indexOf('wf_') > -1
		},
		
		// 流程初始化
		async wf_init(param){
			const params = this.GET_PARAM() || param
			this.wfCode = params.wfCode
			this.wfProcessId = params.wfProcessId
			this.wfData =  params.wfData
			this.wfCodeCopy = params.wfCode; // *获取数据使用
			this.wfCurrentNode = params.wfCurrentNode
			this.wfTitle =  params.wfTitle
			
			this.wfTaskId = params.wfTaskId
			this.wfLogs = params.wfLogs
			this.auditLines = params.auditLines
			this.auditLogList = params.auditLogList
			this.wfButtons = params.wfButtons
			this.wfFormData = params.wfFormData
			
			let wfFormAuthFieldsMap = params.wfFormAuthFieldsMap
			
			this.wfTask = params.wfTask
			
			
			if(this.wfTitle){
				this.SET_TITLE(this.wfTitle)
			}
			
			switch(this.mode){
				case 'wf_draft':
					this.isUpdate = true
					this.formData = await this.loadFormData(this.wfProcessId)
					this.needWorkflowInfo = true
					break
				case 'wf_again':
				case 'wf_lookmy':
				case 'wf_look':
					this.isUpdate = true
					this.formData = await this.loadFormData(this.wfProcessId)
					break
				case 'wf_audit':
					this.formData = await this.loadFormData(this.wfProcessId)
					if(this.formData){
						this.isUpdate = true
					}
					this.needWorkflowInfo = true
					break
				case 'wf_create':
					break
			}
		
			// 表单字段设置必填项
			if(this.formScheme && wfFormAuthFieldsMap){
				let formScheme = this.formScheme
				const components =  formScheme.components
					for(let j = 0,jlen = components.length;j < jlen; j++ ){
						const component = components[j]
						if(!['gridtable'].includes(component.type)){
								if(component.config.display){
									let formId = component.id.toLowerCase() // .replace('entity','')
									for(let _key in wfFormAuthFieldsMap){
										if(_key.toLowerCase() == formId){
											component.config.required = wfFormAuthFieldsMap[_key].required
										}
									}
								}
						}
						else{
							component.children.forEach((cell)=>{
								let formId = cell.id.toLowerCase()
								for(let _key in wfFormAuthFieldsMap){
									if(_key.toLowerCase() == formId){
										cell.config.required = wfFormAuthFieldsMap[_key].required
									}
								}
							})
						}
					}
			}
			
		},
		
		// 流程表单校验
		async wf_formValidate(){
			let verifyResult = true
			if(this.$refs.form){
				verifyResult = await this.$refs.form.validate()
			}
			return verifyResult
		},
		
		// 保存草稿
		async wf_draft(){
			this.LOADING('正在保存…')
			const loginInfo = this.GET_GLOBAL('loginUser')
			if(await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})){
				const wfData = {
						processId:this.wfProcessId,
						schemeCode:this.wfCode,
						userId:loginInfo.f_UserId,
						title:''
				}
				
				const res = await this.HTTP_POST({
					url:'/workflow/process/draft',
					data:wfData
				})
				// this.wfCode = ''
				this.wfIsDraft = true
				this.HIDE_LOADING()
				if(res){
					console.log(await this.getMessage()) // *获取数据使用
					this.TOAST('保存成功', 'success')
				}
			}
			else{
				this.HIDE_LOADING()
			}
		},
		
		// 流程提交
		async wf_submit() {
			this.LOADING('正在提交…')			
			if(await this.wf_formValidate()){
				if(await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})){
					if(this.mode == 'wf_again') {
						await this.wf_again();
						return;
					}
					const loginInfo = this.GET_GLOBAL('loginUser')
					const wfData = {
							processId:this.wfProcessId,
							schemeCode:this.wfCode,
							userId:loginInfo.f_UserId,
							title:''
					}
					this.wfCodeCopy = this.wfCode; // *获取数据使用
					
					if(!this.wfIsDraft){
						await this.HTTP_POST({
							url:'/workflow/process/draft',
							data:wfData
						})
						this.wfCode = ''
						wfData.schemeCode = ''
						this.wfIsDraft = true
					}
					console.log(await this.getMessage()); // *获取数据使用
					const delegateUserList = await this.HTTP_GET({
						url:`/workflow/delegate/users/${this.wfCodeCopy}`,
					})
					// 获取接下来节点审核人
							this.ONCE('learun-wfsubmit-info',data => {
								const wfData = {}
								wfData.title = data.title
								wfData.nextUsers = data.nextUsers
								wfData.des = data.des
								wfData.fileId = data.fileId
								wfData.level = data.level
								wfData.processId =  this.wfProcessId,
								wfData.userId = data.userId || loginInfo.f_UserId,
								setTimeout(async () => {
									this.LOADING('正在提交…')
									const res = await this.HTTP_POST({
										url:'/workflow/process/create',
										data:wfData
									})
									this.EMIT(`learun-workflow-list-change`)
									// this.NAV_BACK()
									this.TOAST(`流程提交成功`, 'success')
									if (this.isToken) {
										this.RELAUNCH_TO('/pages/home')
									} else {
										this.NAV_BACK(2)
									}
								},10)
							})
							this.HIDE_LOADING()
							this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
								processId: this.wfProcessId,
								nodeId:this.wfCurrentNode.id,
								operationCode: 'create',
								wfData: this.wfData,
								isNextAuditor: this.wfCurrentNode.isNextAuditor,

								isCustmerTitle:this.wfCurrentNode.isCustmerTitle,
								isDes: this.wfCurrentNode.isDes, 
								isUploadFile: this.wfCurrentNode.isUploadFile,
								isLevel: this.wfCurrentNode.isLevel,
								delegateUserList
							})
							return
						
				}
				else{
					this.HIDE_LOADING()
				}
			}
			else{
				this.HIDE_LOADING()
			}
		},
		
		// 流程重新提交
		async wf_again(){

			this.LOADING('正在提交…')
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfPostData = {
					processId: this.wfProcessId,
					des: paramData.des,
					fileId: paramData.fileId,
					nextUsers: paramData.nextUsers,
				  };
				setTimeout(async () => {
					this.LOADING('正在提交…')
					const success = await this.HTTP_POST(
						{
							url:`/workflow/process/CreateAgain`,
							data:{
								processId:this.wfProcessId,           
								des:`重新提交` 
							},
							errorTips:'已阅提交发生错误'
						}
					)
					this.HIDE_LOADING()
					if(success){
						this.TOAST('提交成功！')
						this.EMIT(`learun-workflow-list-change`)
						this.NAV_TO(`/pages/workflow/mytask/list`)
					}
					
				},10)
			})
			this.HIDE_LOADING()
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				processId: this.wfProcessId,
				nodeId:this.wfCurrentNode.id,
				operationCode: 'createAgain',
				wfData: this.wfData,
				isNextAuditor: this.wfCurrentNode.isNextAuditor,

				isDes: true, 
				isUploadFile: this.wfCurrentNode.isUploadFile,
				isRejectBackOld: this.wfTask.f_IsRejectBackOld
			})
			
		},
		
		
		// 作废流程
		async wf_Delete(){
			if (!(await this.CONFIRM('作废流程', `确定要作废吗？`, true))) {
			  return
			}
			this.LOADING('正在作废…')
			const success = await this.HTTP_DELETE(
				{
					url:`/workflow/process/${this.wfProcessId}`,
					errorTips:'作废时发生错误'
				}
			)
			this.HIDE_LOADING()
			if(success){
				this.TOAST('作废成功！')
				this.EMIT(`learun-workflow-list-change`)
				this.NAV_BACK()
			}
		},
		
		
		// 流程撤销
		async wf_revokeAudit(){
			if (!(await this.CONFIRM('撤销审核', `确定要撤销吗？`, true))) {
			  return
			}
			this.LOADING('正在撤销…')
			const success = await this.HTTP_PUT(
				{
					url:`/workflow/process/audit/revoke/${this.wfProcessId}?taskId=${this.wfTaskId}`,
					errorTips:'撤销审核时发生错误'
				}
			)
			this.HIDE_LOADING()
			if(success){
				this.TOAST('撤销成功！')
				this.EMIT(`learun-workflow-list-change`)
				this.NAV_BACK()
			}
		},
		// 流程确认阅读
		async wf_read(){
			this.LOADING('正在提交…')
			const success = await this.HTTP_PUT(
				{
					url:`/workflow/process/read/${this.wfTaskId}`,
					errorTips:'已阅提交发生错误'
				}
			)
			this.HIDE_LOADING()
			if(success){
				this.TOAST('已阅成功！')
				this.EMIT(`learun-workflow-list-change`)
				this.NAV_BACK()
			}
		},
		async wf_action(btn, isToken) {			
			switch(btn.prop){
				case 'learun_create':
					this.LOADING('表单保存…')
					if (!(await this.wf_formValidate())) {
						this.HIDE_LOADING()
						return;
					}
					if(await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})){
						this.HIDE_LOADING()
						await this.wf_again()
					}
					else{
						this.HIDE_LOADING()
					}
					break
				case 'learun_detete':
					await this.wf_Delete()
					break
				case 'learun_sign':
					if (!this.wfCurrentNode?.isNotSavaForm) {
						if (!(await this.wf_formValidate())) {
							this.HIDE_LOADING()
							return;
						}
						if(!await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})) {
							this.HIDE_LOADING()
							return;
						}
					}
					await this.addSignTask()
					break;
					case 'learun_transfer':
						if (!this.wfCurrentNode?.isNotSavaForm) {
							if (!(await this.wf_formValidate())) {
								this.HIDE_LOADING()
								return;
							}
							if(!await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})) {
								this.HIDE_LOADING()
								return;
							}
						}
						await this.wfTransfer()
						break;
					case 'learun_connect':
						if (!this.wfCurrentNode?.isNotSavaForm) {
							if (!(await this.wf_formValidate())) {
								this.HIDE_LOADING()
								return;
							}
							if(!await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})) {
								this.HIDE_LOADING()
								return;
							}
						}
						await this.wfConnect()
						break;
					case 'learun_reply_connect':
						if (!this.wfCurrentNode?.isNotSavaForm) {
							if (!(await this.wf_formValidate())) {
								this.HIDE_LOADING()
								return;
							}
							if(!await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})) {
								this.HIDE_LOADING()
								return;
							}
						}
						await this.wfReplyConnect(btn)
						break;
					case 'learun_cancelConnect':
						await this.cancelConnectAudit();
						break;
					case 'navTo':
						this.navTo(btn);
						break
				default:
					this.LOADING('表单保存…')
					if (!this.wfCurrentNode?.isNotSavaForm) {
						if(btn.prop != "disagree" || this.wfCurrentNode.rejectNeedForm){// 流程驳回表单不做校验
							if (!(await this.wf_formValidate())) {
								this.HIDE_LOADING()
								return;
							}
						}
						if(!await this.handleSave({keyValue:this.wfProcessId,isEdit:this.isUpdate})) {
							this.HIDE_LOADING()
							return;
						}
					}
					if(this.wfTask && this.wfTask.f_Type == 6){ // 加签审批
						await this.auditSignTask(btn);
						return;
					}
					if (btn.prop === 'agree' && this.getMessage) { // *获取数据使用
						console.log(await this.getMessage())
					}
					this.HIDE_LOADING()
					await this.auditTask(btn);
					break
			}
			
			
			
		},
		// 审核
		async auditTask(btn) {
			let rejectNodeList = [];
            if (this.wfCurrentNode?.rejectType == '2' && btn.prop == 'disagree' && this.wfTask && this.wfTask.f_Type != 5) {
              const nodes = this.COPY(this.wfData);
              rejectNodeList = nodes
                .filter((t) => t.hasFinish && t.id != this.wfCurrentNode?.id && t.type !== 'scriptTask')
                .map((t) => {
                  if (t.type === 'startEvent' && !t.name) {
                    t.name = '开始节点';
                  }
                  return {...t, label: t.name, value: t.id};
                });
            }
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfData = {
					code: btn.prop,
					name: btn.label,
					tag: paramData.auditTags,
					des: paramData.des,
					fileId: paramData.fileId,
					nextUsers: btn.prop == 'disagree' ? undefined : paramData.nextUsers,
					nextId: btn.prop == 'disagree' && paramData ? paramData.nodeId : undefined,
					isRejectBackOld: paramData.isRejectBackOld,
				  };
				setTimeout(async () => {
					this.LOADING('正在提交…')
					const res = await this.HTTP_PUT({
						url: `/workflow/process/audit/${this.wfTaskId}`,
						data:wfData
					})
					this.EMIT(`learun-workflow-list-change`)
					this.NAV_BACK()
					this.TOAST(`流程提交成功`, 'success')
				},10)
			})
			this.HIDE_LOADING()
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				processId: this.wfProcessId,
				nodeId:this.wfCurrentNode.id,
				operationCode: btn.prop,
				wfData: this.wfData,
				isNextAuditor: this.wfCurrentNode.isNextAuditor,

				isDes: true,
				rejectNodeList,
				auditTags: this.wfCurrentNode.Configure || [],
				isUploadFile: this.wfCurrentNode.isUploadFile,
				isDesReq: this.wfCurrentNode?.isDesReq,
                taskType: this.wfTask?.f_Type,
				isRejectBackOld: this.wfTask.f_IsRejectBackOld
			})
		},
		// 加签审批
		async auditSignTask(btn) {
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfData = {
					code: btn.prop,
					name: btn.label,
					tag: paramData.auditTags,
					des: paramData.des,
					fileId: paramData.fileId,
				  };
				setTimeout(async () => {
					this.LOADING('正在提交…')
					const res = await this.HTTP_PUT({
						url:`/workflow/process/signaudit/${this.wfTaskId}`,
						data:wfData
					})
					this.EMIT(`learun-workflow-list-change`)
					this.NAV_BACK()
					this.TOAST(`流程提交成功`, 'success')
				},10)
			})
			this.HIDE_LOADING()
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				isDes: true,
				isDesReq: this.wfCurrentNode.isDesReq,
                auditTags: this.wfCurrentNode.Configure || [],
				isUploadFile: this.wfCurrentNode.isUploadFile,
			})
		},
		// 添加加签任务
		async addSignTask() {
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfData = {
					toUserId: paramData.signUser,
					tag: paramData.auditTags,
					des: `${paramData.signUserName}${paramData.des ? '-' + paramData.des : ''}`,
					fileId: paramData.fileId,
				  };
				setTimeout(async () => {
					this.LOADING('正在提交…')
					const res = await this.HTTP_PUT({
						url:`/workflow/process/sign/${this.wfTaskId}`,
						data:wfData
					})
					this.EMIT(`learun-workflow-list-change`)
					this.NAV_BACK()
					this.TOAST(`流程提交成功`, 'success')
				},10)
			})
			this.HIDE_LOADING()
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				operationCode: 'learun_sign',
				isDes: true,
				isDesReq: true,
                auditTags: this.wfCurrentNode.Configure || [],
				isUploadFile: this.wfCurrentNode.isUploadFile,
			})
		},
		// 转办任务
		async wfTransfer() {
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfData = {
					toUserId: paramData.transferUser,
					tag: paramData.auditTags,
					des: `${paramData.transferUserName}${paramData.des ? '-' + paramData.des : ''}`,
					fileId: paramData.fileId,
				  };
				setTimeout(async () => {
					this.LOADING('正在提交…')
					const res = await this.HTTP_PUT({
						url:`/workflow/process/transfer/${this.wfTaskId}`,
						data:wfData
					})
					this.EMIT(`learun-workflow-list-change`)
					this.NAV_BACK()
					this.TOAST(`流程提交成功`, 'success')
				},10)
			})
			this.HIDE_LOADING()
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				operationCode: 'learun_transfer',
				isDes: true,
				isDesReq: true,
                auditTags: this.wfCurrentNode.Configure || [],
				isUploadFile: this.wfCurrentNode.isUploadFile,
			})
		},
		// 沟通
		async wfConnect() {
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfData = {
					toUserId: paramData.connectUser,
					tag: paramData.auditTags,
					des: `${paramData.connectUserName}${paramData.des ? '-' + paramData.des : ''}`,
					fileId: paramData.fileId,
					connectType: paramData.connectType
				  };
				setTimeout(async () => {
					this.LOADING('正在提交…')
					const res = await this.HTTP_PUT({
						url:`/workflow/process/connect/${this.wfTaskId}`,
						data:wfData
					})
					this.EMIT(`learun-workflow-list-change`)
					this.NAV_BACK()
					this.TOAST(`流程发起沟通成功！`, 'success')
				},10)
			})
			this.HIDE_LOADING()
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				operationCode: 'learun_connect',
				isDes: true,
				isDesReq: true,
                auditTags: this.wfCurrentNode.Configure || [],
				isUploadFile: this.wfCurrentNode.isUploadFile,
			})
		},
		// 取消沟通
		async cancelConnectAudit() {
			this.LOADING(this.$t('正在提交…'))
			const res = await this.HTTP_POST({
				url:`/workflow/process/cancelConnect/${this.wfTaskId}`,
			})
			if (res) {
				this.TOAST(this.$t(`流程取消沟通成功！`), 'success')
				this.EMIT(`learun-workflow-list-change`)
				this.HIDE_LOADING()
				this.NAV_BACK()
			}
			this.HIDE_LOADING()
		},
		// 回复沟通
		async wfReplyConnect(btn) {
			this.ONCE('learun-wfsubmit-info',paramData => {
				const wfData = {
					code: btn.prop,
					name: btn.label,
					tag: paramData.auditTags,
					des: paramData.des,
					fileId: paramData.fileId,
				  };
				setTimeout(async () => {
					this.LOADING(this.$t('正在提交…'))
					const res = await this.HTTP_PUT({
						url:`/workflow/process/connectaudit/${this.wfTaskId}`,
						data:wfData
					})
					this.EMIT(`learun-workflow-list-change`)
					this.HIDE_LOADING()
					this.NAV_BACK()
					this.TOAST(this.$t(`流程回复沟通成功！`), 'success')
				},10)
			})
			
			this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info',{
				isDes: true,
				isDesReq: this.wfCurrentNode.isDesReq,
                auditTags: this.wfCurrentNode.Configure || [],
				isUploadFile: this.wfCurrentNode.isUploadFile,
			})
		},
		// 跳转
		navTo(btn){
			if (btn.appUrl) {
				this.NAV_TO(btn.appUrl)
			}
		},
		wf_open_action(){
			this.$refs.wfpopup.open(this.$t(`流程处理`),this.wfButtons)
		},

		// *获取数据使用
		async getMessage() {
			let formData = {}
			if (this.wfCurrentNode.formType === '1' && this.wfCurrentNode.formVerison) {
				formData = await this.HTTP_GET({
					url: `/custmerform/data/${this.wfCurrentNode.formVerison}`,
					params: { key: this.wfCurrentNode.formRelationId, keyValue: this.wfProcessId }
				})
			} else if (this.wfCurrentNode.formAppUrl){
				formData = await this.loadFormData(this.wfProcessId)
			}
			const postData = {
				wfProcessId: this.wfProcessId,
				wfSchemeCode: this.wfCodeCopy || this.GET_GLOBAL('learun_wf_code'),
				wfNodeCode: this.wfCurrentNode.nodeCode,
				formSchemeId: this.wfCurrentNode.formType === '1' ? this.wfCurrentNode.formVerison : '',
				formPKey: this.wfCurrentNode.formType === '1' ? this.wfCurrentNode.formRelationId : 'f_Id',
				formPKeyValue: this.wfCurrentNode.formType === '1' ? this.wfProcessId : '',
				wfFormData: JSON.stringify(formData),
				methodName: this.wfCurrentNode.methodName || '',
			}
			console.log(postData, 'postData')
			this.SET_GLOBAL('learun_wf_code', '')
		}
	}
}