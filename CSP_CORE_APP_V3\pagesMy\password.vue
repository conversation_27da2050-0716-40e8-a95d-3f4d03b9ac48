<template>
	<view class="page" style="padding-top: 8px;">
		<view class="bg-white learun-list-form">
			<uni-forms :modelValue="formData" :rules="rules" ref="myForm">
				<uni-forms-item :label="$t('旧密码')" name="oldPwd" required>
					<uni-easyinput v-model="formData.oldPwd" :placeholder="$t('请输入')" :inputBorder="false"
						type="password">
					</uni-easyinput>
				</uni-forms-item>
				<uni-forms-item :label="$t('新的密码')" name="newPwd" required>
					<uni-easyinput v-model="formData.newPwd" :placeholder="$t('请输入')" :inputBorder="false"
						type="password">
					</uni-easyinput>
				</uni-forms-item>
				<uni-forms-item :label="$t('确认输入')" name="confirmPwd" required>
					<uni-easyinput v-model="formData.confirmPwd" :placeholder="$t('请输入')" :inputBorder="false"
						type="password">
					</uni-easyinput>
				</uni-forms-item>
			</uni-forms>


		</view>

		<view class="padding">
			<button @click="submit" type="primary">{{$t('确认修改')}}</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					oldPwd: '',
					newPwd: '',
					confirmPwd: ''
				},
				rules: {
					'oldPwd': {
						rules: [{
							required: true,
							errorMessage: this.$t('请填写旧密码'),
						}]
					},
					'newPwd': {
						rules: [{
							required: true,
							errorMessage: this.$t('请填写新密码'),
						}]
					},
					'confirmPwd': {
						rules: [{
							required: true,
							errorMessage: this.$t('请再次输入新密码'),
						}]
					},
				},


			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("更改密码")
			})
			await this.PAGE_LAUNCH()
		},

		methods: {
			// 提交修改
			async submit() {
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm)

				if (err) {
					return
				}

				const {
					oldPwd,
					newPwd,
					confirmPwd
				} = this.formData

				if (newPwd !== confirmPwd) {
					this.CONFIRM(this.$t('操作失败', '新密码和确认密码输入不一致，请修改。'))
					return
				}

				this.LOADING(this.$t('提交…'))


				const success = await this.HTTP_PUT({
					url: '/organization/user/password',
					data: {
						password: this.MD5(newPwd),
						oldPassword: this.MD5(oldPwd)
					},
					errorTips: this.$t('未能成功修改密码')
				})

				this.HIDE_LOADING()
				if (!success) {
					return
				}

				this.NAV_BACK()
				this.TOAST(this.$t('密码修改成功'))
			}
		}
	}
</script>