<template>
	<!-- 页面视图，设置最小高度、顶部和底部内边距 -->
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-top':'56px','padding-bottom':'40px'}">
		<!-- 固定在顶部的搜索栏容器 -->
		<view class="bg-white fixed">
			<!-- 搜索栏组件，绑定搜索关键词，监听确认、取消和清空事件 -->
			<uni-search-bar v-model="keyword" @confirm="searchData" @cancel="cancelSearch" @clear="cancelSearch" />
		</view>
		<!-- 列表展示区域 -->
		<view>
			<!-- uni-list 组件用于展示列表项 -->
			<uni-list>
				<!-- 循环渲染列表项，每个列表项可点击，绑定点击事件 -->
				<uni-list-item clickable @click="itemClick(item)" :key="item.f_Id" v-for="item in list"
					direction="column">
					<!-- 列表项头部内容 -->
					<template v-slot:header>
						<view class="learun-flex">
							<!-- 显示列表项的名称 -->
							<text class="learun-list-item__title">
								{{$t(item.f_Name)}}
							</text>
							<!-- 删除按钮，点击触发删除操作 -->
							<button @click.stop="handleDelete(item)" style="margin-left:16px;" type="warn"
								class="learun-btn">{{$t('删除')}}</button>
						</view>
					</template>
					<!-- 列表项主体内容 -->
					<template v-slot:body>
						<view class="learun-list-item__content">
							<!-- 显示成员信息 -->
							<view><text>成员：{{item.person}} </text></view>
							<!-- 显示负责人信息 -->
							<view><text>负责人：{{item.f_CreateUserName}}</text></view>
							<!-- 显示打卡日期信息 -->
							<view><text>打卡日期：{{item.date}}</text></view>
							<!-- 显示打卡时间信息，替换分隔符 -->
							<view><text>打卡时间：{{item.f_Time.replace(/\|/g,' ')}}</text></view>
							<!-- 显示打卡地点信息 -->
							<view><text>打卡地点：{{item.f_Scope.split(',')[0]}}</text></view>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<!-- 加载更多组件，根据加载状态显示不同提示 -->
			<uni-load-more v-if="loading || status === 'noMore' " :status="status" />
		</view>
		<!-- 底部操作按钮区域 -->
		<view class="learun-bottom-btns">
			<!-- 新增考勤组按钮，点击触发新增操作 -->
			<button @click.stop="handleAdd" type="primary">{{$t('新增考勤组')}}</button>
		</view>
	</view>
</template>

<script>
	// 导入自定义表单混入
	import customFormMixins from '@/common/customform.js'
	export default {
		// 使用混入
		mixins: [customFormMixins],
		data() {
			return {
				// 是否显示搜索框
				showSearch: false,
				// 是否正在加载数据
				loading: true,
				// 加载状态，'loading' 或 'noMore'
				status: 'loading',
				// 每页显示的记录数
				rows: 20,
				// 当前页码
				page: 1,
				// 总记录数
				total: 1,
				// 列表数据
				list: [],
				// 搜索关键词
				keyword: '',
				// 模块 ID
				moduleId: '',
				// 星期选项
				dayOptions: [{
					label: this.$t('星期一'),
					value: '1'
				}, {
					label: this.$t('星期二'),
					value: '2'
				}, {
					label: this.$t('星期三'),
					value: '3'
				}, {
					label: this.$t('星期四'),
					value: '4'
				}, {
					label: this.$t('星期五'),
					value: '5'
				}, {
					label: this.$t('星期六'),
					value: '6'
				}, {
					label: this.$t('星期天'),
					value: '7'
				}],
				// 班次选项
				options: [{
					value: '1',
					label: this.$t('一天一班')
				}, {
					value: '2',
					label: this.$t('一天两班')
				}, {
					value: '3',
					label: this.$t('一天三班')
				}],
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("考勤设置")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面
				await this.init();
			}
		},
		methods: {
			// 关闭页面，返回上一页
			closePage() {
				this.NAV_BACK()
			},
			async init() {
				// 获取模块 ID
				this.moduleId = this.GET_MODULEID()
				// 刷新列表数据
				await this.refreshList()
				// 监听列表数据变化事件，触发刷新列表操作
				this.ON(`custom-list-change-${this.moduleId}`, this.refreshList)
			},
			async fetchList() {
				// 如果当前页码大于总页数，不再加载数据
				if (this.page > this.total) {
					return
				}
				// 标记正在加载数据
				this.loading = true
				// 发送 HTTP 请求获取列表数据
				const result = await this.HTTP_GET({
					url: '/demoApp/apprule/page',
					params: {
						page: this.page,
						rows: this.rows,
						sidx: "f_CreateDate DESC",
						f_Name: this.keyword
					}
				})
				// 如果请求结果为空，返回
				if (!result) {
					return
				}
				// 过滤出新的数据
				const newList = result.rows.filter(t => !this.list.some(t2 => t2.f_Id === t.f_Id))
				// 拉取新数据中的组织结构信息
				await this.fetchOrganizeInfo(newList);
				// 处理新数据，转化为需要的格式
				for (const item of newList) {
					item.person = '';
					const users = item.f_Person.split(',')
					for (const userid of users) {
						const user = this.GET_DATA('learun_users_map')[userid]
						if (user) {
							if (item.person) {
								item.person += ","
							}
							item.person += user.label || '';
						}
					}
					const dateList = []
					const dates = item.f_Date.split(',')
					for (const dateid of dates) {
						const dataItem = this.dayOptions.find(t => t.value == dateid)
						if (dataItem) {
							dateList.push(dataItem.label)
						}
					}
					item.date = String(dateList);
				}
				// 更新总记录数
				this.total = result.total
				// 合并新数据到列表中
				this.list = this.list.concat(newList)
				// 页码加 1
				this.page++
				// 标记加载完成
				this.loading = false
				// 如果当前页码大于总页数，设置加载状态为 'noMore'
				if (this.page > this.total) {
					this.status = "noMore"
				}
				console.log(result, 'result');
			},
			// 拉取结果中的组织结构信息
			async fetchOrganizeInfo(list) {
				const userIdList = []
				// 收集所有用户 ID
				for (const row of list) {
					const users = row.f_Person.split(',')
					for (const userid of users) {
						if (userIdList.findIndex(t => t == userid) == -1) {
							userIdList.push(userid)
						}
					}
				}
				// 调用方法拉取组织结构信息
				await this.learun_form_fetchOrganizeInfo(userIdList, [])
			},
			// 刷新列表（清空并重新拉取）
			async refreshList() {
				// 标记正在加载数据
				this.loading = true;
				// 设置加载状态为 'loading'
				this.status = 'loading';
				// 清空列表数据
				this.list = [];
				// 重置页码为 1
				this.page = 1;
				// 重置总记录数为 1
				this.total = 1;
				// 重新拉取列表数据
				await this.fetchList()
			},
			// 搜索数据，触发刷新列表操作
			async searchData() {
				await this.refreshList()
			},
			// 取消搜索，清空关键词并刷新列表
			async cancelSearch() {
				this.keyword = ''
				await this.refreshList()
			},
			// 列表项点击事件，跳转到编辑页面
			itemClick(row) {
				this.NAV_TO_LAYER('/pagesDemo/clock/addSetting?type=edit', {
					keyValue: row.f_Id,
					moduleId: this.moduleId,
					title: row.f_Name,
				}, true)
			},
			// 新增考勤组，跳转到新增页面
			handleAdd() {
				this.NAV_TO_LAYER('/pagesDemo/clock/addSetting?type=create', {
					moduleId: this.moduleId,
					title: '添加考勤组',
				}, true)
			},
			async handleDelete(row) {
				// 确认是否删除
				if (!(await this.CONFIRM(this.$t('删除项目'), this.$t('确定要删除该项吗？'), true))) {
					return
				}
				// 显示加载提示
				this.LOADING('提交删除中…')
				// 发送 HTTP 请求删除数据
				const success = await this.HTTP_DELETE({
					url: "/demoApp/apprule/" + row.f_Id,
					errorTips: '删除失败'
				})
				// 隐藏加载提示
				this.HIDE_LOADING()
				if (success) {
					// 显示删除成功提示
					this.TOAST('删除成功', 'success')
					// 从列表中移除被删除的项
					const list = this.list
					const index = list.findIndex(item => item.f_Id === row.f_Id)
					list.splice(index, 1)
				}
			},
			// 切换搜索框显示状态
			toggleSearch() {
				this.showSearch = !this.showSearch
			}
		},
		/**
		 * 下拉刷新回调函数
		 */
		async onPullDownRefresh() {
			// 清空搜索关键词
			this.keyword = ''
			// 刷新列表数据
			await this.refreshList()
			// 停止下拉刷新动画
			uni.stopPullDownRefresh()
		},
		/**
		 * 上拉加载回调函数
		 */
		onReachBottom() {
			// 加载更多数据
			this.fetchList()
		}
	}
</script>