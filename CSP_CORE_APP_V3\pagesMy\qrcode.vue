<template>
	<view class="page">
		<view class="bg">
			<view class="info">
				<image :src="avatar" class="avatar" @error="imageError" />
				<view class="infotext">
					<view class="text-xl">{{ currentUser.name }}</view>
					<uni-tag line="green" size="small" :text="userTag"></uni-tag>
				</view>
			</view>

			<view class="qrcode"><tki-qrcode :val="qrCodeValue" :size="550" loadMake /></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				qrCodeValue: 'https://www.learun.cn',
				defaultSrc: null,
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("我的二维码")
			})
			await this.PAGE_LAUNCH()
		},
		computed: {
			// 当前用户对象
			currentUser() {
				return this.GET_GLOBAL('loginUser') || {}
			},

			// 头像图片 url
			avatar() {
				if (this.defaultSrc) {
					return this.defaultSrc
				}
				const token = this.GET_GLOBAL('token')
				return this.currentUser.f_HeadIcon ?
					`${this.API}/system/annexesfile/${this.currentUser.f_HeadIcon}?token=${token}` :
					`/static/img-avatar/head.png`
			},

			// 用户公司部门 tag
			userTag() {
				const {
					companyId,
					departmentId
				} = this.currentUser
				if (!companyId) {
					return this.$t(`总集团公司`)
				}

				const company = this.GET_GLOBAL('company')
				const dep = this.GET_GLOBAL('department')
				const companyName = company[companyId].name
				if (!dep) {
					return companyName
				}

				return `${companyName} / ${dep[departmentId].name}`
			}
		},
		methods: {
			imageError() {
				this.defaultSrc = `/static/img-avatar/head.png`
			}
		},
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #2f2d2d;
		position: absolute;
		display: flex;
		justify-content: center;
		align-items: center;
		bottom: 0;
		top: 0;
		right: 0;
		left: 0;

		.bg {
			background: #ffffff;
			border-radius: 5px;
			padding: 20rpx;
			display: inline-block;

			.info {
				display: flex;
				align-items: center;
				margin-bottom: 10px;

				.avatar {
					width: 120rpx;
					height: 120rpx;
					margin-right: 15px;
					border-radius: 2px;
				}

				.infotext>view {
					margin-bottom: 10px;
				}
			}
		}
	}
</style>