<template>
	<view class="learun-table" >
		<scroll-view
			scroll-x
			style="width: 100%;height: 100%;"
			:show-scrollbar="false"
			:scroll-with-animation="false"
		>
		<view class="learun-table__wraper">
			<view class="learun-table__header" :style="{width: myWidth + 'px'}" v-if="showHeader" >
					<view class="learun-table__header__wraper"  :style="{width: myWidth + 'px'}" >
						<view 
							:style="{width: (col.width || 120) + 'px',flex:col.isMinWidth?1:'', 'justify-content': col.align || 'left'}" 
							class="learun-table__header__col"   
							v-for="(col,index) in columns" 
							:key="index"  >
							<text>{{$t(col.label)}}</text>
						</view>
					</view>
			</view>
			<view class="learun-table__content" :style="{width: myWidth + 'px'}"  >
				<scroll-view
					scroll-y
					style="width: 100%;height: 100%;"
					:show-scrollbar="false"
					:scroll-top="scrollTop"
					:scroll-with-animation="false"
				>
					<view
						v-for="(row,index) in dataSource"
						:key="index"
						:class="['learun-table__content__row',(index+1)%2==0?'learun-table__content__row_stripe':'']"  
						:style="{width: myWidth + 'px'}" 
						
						@click.stop="rowClick(row,index)"
						>
						
						<view
							:class="['learun-table__content__td',{'isEllipsis':col.isEllipsis}]"
							v-for="(col,index2) in columns" 
							:key="index2"
							:style="{width: (col.width || 120) + 'px',flex:col.isMinWidth?1:''}" 
							>
							<learun-table-item  v-if="col.myScheme" :component="col.myScheme" :getDataSource="getDataSource" :value="row[col.rowid]" :rowData="row" @input="setValue($event.path,$event.value,row)" >
							</learun-table-item>
							<template v-else >
								{{myDisplayText(row[col.rowid],col.scheme)}}
							</template>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		</scroll-view>
		<view class="learun-table__floor" v-if="isPage">
			<uni-pagination showIcon :pageSize="pageSize" :current="page" :total="total" @change="pageChange"></uni-pagination>
		</view>
		
	</view>
</template>

<script>
	import debounce from "lodash/debounce"
	import set from "lodash/set"
	export default {
		name:'learun-table',
		props: {
			page:{ default: 1 },
			pageSize:{ default: 20 },
			total:{ default: 0 },
			columns:{ default: () => [] },
			dataSource:{ default: () => [] },
			showHeader: { default: true },
			isPage: { default: true },
			displayText:Function,
			getDataSource:Function,
		},
		data(){
			return {
				scrollTop:0
			}
		},
		
		computed:{
			myWidth(){
				let width = 0
				this.columns.forEach(col=>{
					width += col.width || 120
				})
				
				return width
			}
		},
		
		methods:{
			myDisplayText(value,scheme){
				// console.log(value,scheme,'myDisplayText')
				if(this.displayText && scheme){
					return this.displayText(value,scheme)
				}
				return value
			},
			rowClick(row,index){
				this.$emit('rowClick',{row,index})
			},
			pageChange(e){
				this.scrollTop = 1000
				this.$nextTick(()=>{
					this.scrollTop = 0
				})
				this.$emit('pageChange',e)
			},
			
			setValue(prop,value,data){
				set(data,prop,value)
			}
		}
	}
</script>
