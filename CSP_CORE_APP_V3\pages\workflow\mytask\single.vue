<template>
	<!-- 当页面准备好时显示主容器 -->
	<view v-if="ready" class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-top': '52px',
    'padding-bottom':(isCancel || isRead || type == 'audit')? '40px':0}">
		<!-- 根据是否加载表单显示不同的分段控件 -->
		<view v-if="isLoadForm" class="bg-white fixed" style="padding: 8px;">
			<uni-segmented-control :current="tab" :values="tabList" @clickItem="changeTab">
			</uni-segmented-control>
		</view>
		<view v-else class="bg-white fixed" style="padding: 8px;">
			<uni-segmented-control :current="tab" :values="tabList2" @clickItem="changeTab">
			</uni-segmented-control>
		</view>
		<!-- 渲染表单 -->
		<learun-customform-wraper v-if="isLoadForm" v-show="tab == 0" :top="!isLoadForm? 0 : 52" :formId="formId"
			:editMode="editMode" :scheme="formScheme" :isUpdate="isFormUpdate" :loadParams="loadFormParams"
			@ready="handleFormReady" ref="form" />
		<!-- 显示审批日志表格 -->
		<learun-table v-show="(isLoadForm && tab == 1) || (!isLoadForm && tab == 0)" :isPage="false"
			:columns="auditLogColumns" :dataSource="auditLogList" />
		<!-- 显示工作流时间线 -->
		<learun-workflow-timeline v-show="(isLoadForm && tab == 2) || (!isLoadForm && tab == 1)" :wfLogs="wfLogs">
		</learun-workflow-timeline>

		<learun-workflow-auditline v-show="(isLoadForm && tab == 3) || (!isLoadForm && tab == 2)"
			:list="auditLines"></learun-workflow-auditline>
		<!-- 操作区按钮 -->
		<view v-if="ready" class="learun-bottom-btns">
			<!-- 撤销操作按钮 -->
			<button v-if="isCancel" @click.stop="wf_revokeAudit" type="warn">{{$t('撤销')}}</button>
			<!-- 流程处理按钮 -->
			<button v-else-if="type == 'audit' || type == 'again'" @click.stop="action"
				type="primary">{{$t('流程处理')}}</button>
			<!-- 确认阅读按钮 -->
			<button v-else-if="isRead" @click.stop="wf_read" type="primary">{{$t('确认阅读')}}</button>
		</view>
		<!-- 弹出按钮组件 -->
		<learun-popup-buttons ref="popup" :buttons="wfButtons" @click="wfAction">
		</learun-popup-buttons>
	</view>
</template>

<script>
	// 引入 lodash 的 pick 方法
	import pick from "lodash/pick"
	// 引入工作流混入模块
	import workflowMixins from '@/common/workflow.js'
	export default {
		// 使用工作流混入模块
		mixins: [workflowMixins],
		data() {
			return {
				// 当前选中的分段控件索引
				tab: 0,
				// 加载表单时的分段控件选项列表
				tabList: [this.$t('表单信息'), this.$t('审批日志'), this.$t('流程信息'), /* '审批路线' */ ],
				// 未加载表单时的分段控件选项列表
				tabList2: [this.$t('审批日志'), this.$t('流程信息'), /* '审批路线' */ ],
				// 表单编辑模式
				editMode: false,
				// 页面类型
				type: 'audit',
				// 页面是否准备好
				ready: false,
				// 是否显示撤销操作按钮
				isCancel: false,
				// 是否显示确认阅读按钮
				isRead: false,
				// 是否通过 token 初始化
				isToken: false,
				// 审批日志表格列配置
				auditLogColumns: [{
						label: '节点名称',
						rowid: 'name'
					},
					{
						label: '处理人',
						rowid: 'userNames'
					},
					{
						label: '处理动作',
						rowid: 'content'
					},
					{
						label: '处理时间',
						rowid: 'time'
					},
					{
						label: '审批意见',
						rowid: 'des'
					},
				]
			}
		},
		async onLoad(param) {
			uni.setNavigationBarTitle({
				title: this.$t("任务详情")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 处理分享消息跳转来的情况
				this.isToken = false;
				if (param.token) {
					this.isToken = true;
					// 通过 token 初始化页面
					await this.initToken(param.token)
				} else {
					// 正常初始化页面
					await this.init(param)
				}
			}
		},

		// 小程序专有，分享任务到聊天
		// #ifdef MP
		onShareAppMessage() {
			// 选择需要分享的任务信息属性
			const props = ['F_Id', 'F_Title', 'F_TaskId', 'F_ProcessId', 'F_IsStart', 'F_IsFinished', 'F_EnabledMark']
			const taskInfo = {
				...pick(this.currentTask, props),
				mark: 'pre'
			}

			// 获取页面参数
			const queryString = this.MP_SHARE_ENCODE(taskInfo, {
				type: this.type
			})
			return {
				// 分享标题
				title: `${this.GET_GLOBAL('loginUser').realName}发起的${this.currentTask.F_Title}工作流程`,
				// 分享描述
				desc: this.$t('我发起了一个工作流程，快来看看吧'),
				// 分享路径
				path: `pages/home?${queryString}`
			}
		},
		// #endif

		methods: {
			// 页面初始化
			async init(param) {
				// 显示加载提示
				this.LOADING(this.$t('加载数据中…'))
				this.ready = false;
				// 获取页面类型
				this.type = param.type
				// 获取工作流标题
				this.wfTitle = param.f_ProcessTitle
				// 设置页面标题
				this.SET_TITLE(param.f_ProcessTitle)

				// 根据页面类型进行不同的初始化操作
				switch (param.type) {
					case 'audit': // 审核流程
					case 'again': // 流程重新提交
						this.editMode = true
						// 初始化工作流
						await this.wfInit({
							taskId: param.f_Id,
							type: param.type
						})
						break
					case 'look': // 查看已办
						this.isCancel = (param.f_IsCancel == 1 && param.f_IsFinished != 1 && param.isDelete != 3) ?
							true : false
						// 初始化工作流
						await this.wfInit({
							taskId: param.f_Id,
							type: param.type
						})
						break
					case 'read': // 查阅流程
						this.isRead = param.f_State == 1 ? true : false
						// 初始化工作流
						await this.wfInit({
							taskId: param.f_Id,
							type: param.type
						})
						break
					case 'lookmy':
						// 初始化工作流
						await this.wfInit({
							processId: param.f_Id,
							type: param.type
						})
						break
				}

				if (!this.isLoadForm) {
					// 隐藏加载提示
					this.HIDE_LOADING()
				}
				this.ready = true
			},
			// 通过 token 初始化页面
			async initToken(token) {
				this.ready = false;
				// 初始化工作流
				await this.wfInit({
					token
				})
				console.log(this.type, 'this.type')
				if (['audit', 'again'].includes(this.type)) {
					this.editMode = true
				}
				// 设置页面标题
				this.SET_TITLE(this.wfTitle)
				if (!this.isLoadForm) {
					// 隐藏加载提示
					this.HIDE_LOADING()
				}
				this.ready = true
			},

			// 表单准备好时的处理函数
			handleFormReady() {
				// 隐藏加载提示
				this.HIDE_LOADING()
			},

			// 保存表单
			async handleSave() {
				if (!this.$refs.form) {
					return true
				}
				// 调用表单的保存方法
				const res = await this.$refs.form.saveForm(this.formId, this.formRelationId, this.wfProcessId, true)
				if (res) {
					this.isFormUpdate = true
				}
				return res
			},

			// 流程处理
			async action(actionType) {
				// 打开弹出按钮组件
				this.$refs.popup.open(this.$t(`流程处理`), this.wfButtons)
			},
			// 弹出按钮点击处理函数
			async wfAction(btn) {
				// 执行工作流操作
				await this.wf_action(btn, this.isToken)
				// 关闭弹出按钮组件
				this.$refs.popup.close()
			},

			// 切换分段控件
			changeTab({
				currentIndex
			}) {
				// 更新当前选中的分段控件索引
				this.tab = currentIndex
			}
		}
	}
</script>