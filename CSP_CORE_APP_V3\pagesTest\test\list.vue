﻿<template>
	<view v-if="ready" class="page"
		:style="{'height':SCREENHEIGHT()+'px','padding-top':'40px','padding-bottom':isAdd() ?'40px':''}">
		<view class="learun-top-bar fixed" style="padding-right: 8px;">
			<view class="learun-total-bar">
				<text>{{ $t('共') }}{{total}}{{$t('条数据')}}</text>
			</view>
			<view class="learun-search-btn" @click.stop="searchClick">
				<text>{{$t('筛选')}}</text>
				<view class="learun-search-btn__flag" />
			</view>
		</view>
		<learun-table :page="page" :pageSize="rows" :total="total" :columns="showColumns()" :dataSource="list"
			:displayText="learun_form_displayText" @rowClick="handleRowClick" @pageChange="handlePageChange" />
		<!--新增按钮-->
		<view v-if="isAdd()" class="learun-bottom-btns">
			<button @click.stop="handleAdd" type="primary">{{$t('新增')}}</button>
		</view>
		<learun-popup-buttons ref="popup" :buttons="rowBtns" @click="BUTTONS_CLICK" />
	</view>
</template>

<script>
	import customFormMixins from '@/common/customform.js'
	export default {
		mixins: [customFormMixins],
		data() {
			return {
				moduleId: '',
				rowBtns: [{
					prop: 'Edit',
					label: '编辑',
					type: 'primary'
				}, {
					prop: 'Delete',
					label: '删除',
					type: 'warn'
				}, ],
				columns: [{
					label: '输入框',
					rowid: 'f_text',
					width: 120,
					align: 'left'
				}, {
					label: '多行文本',
					rowid: 'f_textarea',
					width: 120,
					align: 'left'
				}, ],
				// 数据
				rows: 20,
				page: 1,
				total: 0,
				list: [],
				queryParams: {},
				// 主键
				primaryKey: '',
				// 编辑状态
				editRow: null,
				// 页面相关参数
				ready: false
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("测试流程")
			})
			if (await this.PAGE_LAUNCH()) {
				await this.init()
			}
		},
		onUnload() {
			this.OFF(`custom-list-change-${this.moduleId}`)
		},
		methods: {
			// 初始化页面
			async init(formId) {
				this.LOADING('加载中...')



				this.moduleId = this.GET_MODULEID()
				// 获取页面权限信息
				await this.FETCH_AUTH(this.moduleId)
				this.ON(`custom-list-change-${this.moduleId}`, this.fetchList)
				// 加载数据源数据
				await this.fetchDataSource(this.columns)
				await this.fetchList()
				this.HIDE_LOADING()
				this.ready = true
			},
			// 拉取列表
			async fetchList() {
				this.queryParams.rows = this.rows
				this.queryParams.page = this.page
				this.queryParams.sidx = "F_Id"
				const result = await this.HTTP_GET({
					url: `/test/test/page`,
					params: this.queryParams,
					errorTips: '加载数据时出错'
				})
				if (!result) {
					return
				}
				this.total = result.records
				this.page = result.page
				this.list = result.rows
				await this.fetchOrganizeInfo(result.rows)
			},
			async handlePageChange({
				current
			}) {
				this.LOADING('加载数据中...')
				this.page = current
				await this.fetchList()
				this.HIDE_LOADING()
			},
			handleRowClick({
				row,
				index
			}) {
				this.editRow = row
				this.$refs.popup.open(`操作第${index + 1}行`, this.myBtns())
			},
			handleAdd() {
				this.NAV_TO('./single?type=create', {
					title: 'test【新增】',
					moduleId: this.moduleId
				})
			},
			handleEdit() {
				this.$refs.popup.close()
				this.NAV_TO('./single?type=edit', {
					title: 'test【编辑】',
					keyValue: this.editRow.f_Id,
					moduleId: this.moduleId
				})
			},
			async handleDelete() {
				if (!(await this.CONFIRM('删除项目', '确定要删除该项吗？', true))) {
					return
				}
				this.$refs.popup.close()
				this.LOADING('提交删除中…')
				const success = await this.HTTP_DELETE({
					url: "/test/test/" + this.editRow.f_Id,
					errorTips: '删除失败'
				})
				this.HIDE_LOADING()
				if (success) {
					this.TOAST('删除成功', 'success')
					this.fetchList()
				}
			},
			// 拉取自定义应用需要数据源的字段的数据源
			async fetchDataSource(columns) {
				for (let i = 0, len = columns.length; i < len; i++) {
					if (columns[i].scheme) {
						await this.learun_form_fetchDataSource(columns[i].scheme)
					}
				}
			},
			// 拉取结果中的组织结构信息
			async fetchOrganizeInfo(list) {
				const departmentIdList = []
				const userIdList = []
				const areaList = []
				for (const row of list) {
					for (const key in row) {
						if (!row[key]) {
							continue
						}
						const column = this.columns.find(t => t.rowid == key)
						if (!column || !column.scheme) {
							continue
						}
						switch (column.scheme.type) {
							case 'userSelect':
							case 'createuser':
							case 'modifyuser':
								if (userIdList.findIndex(t => t == row[key]) == -1) {
									userIdList.push(row[key])
								}
								break
							case 'departmentSelect':
							case 'department':
								if (departmentIdList.findIndex(t => t == row[key]) == -1) {
									departmentIdList.push(row[key])
								}
								break
							case 'areaselect':
								if (row[key]) {
									areaList.push(row[key])
								}
								break
						}
					}
				}
				await this.learun_form_fetchOrganizeInfo(userIdList, departmentIdList)
				await this.learun_form_fetchAreaInfo(areaList)
			},
			searchClick() {
				this.ONCE('learun-customapp-query', (data) => {
					this.queryParams = data
					setTimeout(async () => {
						this.LOADING('加载中...')
						this.page = 1
						this.total = 2
						this.list = []
						await this.fetchList()
						this.HIDE_LOADING()
					})
				})
				this.NAV_TO(`./query`, {
					formScheme: this.formScheme,
					pageScheme: this.pageScheme,
					formData: this.queryParams
				})
			},
			myBtns() {
				return this.rowBtns.filter(t => this.GET_BUTTON_AUTH(t.prop, this.moduleId))
			},
			isAdd() {
				return this.GET_BUTTON_AUTH('Add', this.moduleId)
			},
			showColumns() {
				return this.columns.filter(t => this.GET_COLUMN_AUTH(t.rowid, this.moduleId))
			}
		}
	}
</script>