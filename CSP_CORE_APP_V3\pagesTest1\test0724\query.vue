﻿<template>
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px','background-color':'#fff','padding-bottom':'40px'}">
		<uni-nav-bar fixed statusBar leftIcon="closeempty" @clickLeft="closePage" title="筛选条件"></uni-nav-bar>
		<!-- 渲染表单 查询条件 -->
		<uni-forms v-if="ready" :modelValue="formData" label-position="top" :label-width="320" ref="myForm">
			<view style="padding:8px;">
				<view v-for="component in components" :key="component.key">
					<learun-customform-item :component="component" :getDataSource="learun_form_getDataSource"
						:formId="formId" :value="getValue(component.id)" @input="setValue" @change="handleChange"
						:isEdit="true">
					</learun-customform-item>
				</view>
			</view>
		</uni-forms>
		<view class="learun-bottom-btns" v-if="ready">
			<button @click.stop="handleClear" style="flex:1;">清除</button>
			<button @click.stop="handleOk" style="flex:2;" type="primary">确定</button>
		</view>
	</view>
</template>

<script>
	import get from 'lodash/get'
	import set from 'lodash/set'
	import customFormMixins from '@/common/customform.js'
	export default {
		mixins: [customFormMixins],
		data() {
			return {
				isLayer: true,
				ready: false,
				formId: this.GUID(),
				formData: {},
				components: [{
					"id": "f_text",
					"type": "input",
					"config": {
						"label": "文本1",
						"placeholder": this.$t("请输入"),
						"preIcon": {},
						"sufIcon": {},
						"id": "631254331721808786719"
					}
				}, {
					"id": "f_textarea",
					"type": "input",
					"config": {
						"label": "文本2",
						"placeholder": this.$t("请输入"),
						"preIcon": {},
						"sufIcon": {},
						"id": "531440291721808788068"
					}
				}, ],
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("查询条件")
			})
			if (await this.PAGE_LAUNCH()) {
				await this.init();
			}
		},
		onBackPress() {
			this.OFF('learun-customapp-query')
		},
		onUnload() {
			this.OFF('learun-customapp-query')
		},
		methods: {
			async init() {
				this.LOADING(this.$t('加载数据中…'))
				const {
					formData
				} = this.GET_PARAM()
				this.SET_DATA(`learun_form_data_${this.formId}`, this.formData)
				for (const component of this.components) {
					// 加载选择数据
					await this.learun_form_fetchDataSource(component, formData)
					this.$set(this.formData, component.id, formData[component.id])
				}
				this.HIDE_LOADING()
				this.ready = true
			},
			// 组件数据值改变
			async handleChange({
				data,
				component
			}) {
				if (["checkbox", "radio", "select", "selectMultiple", "treeSelect", "inputLayer", 'companySelect',
						'departmentSelect', 'userSelect'
					].includes(component.type)) {
					await this.clearSubValue(component.id)
				}
			},
			async clearSubValue(upProp) {
				for (const component of this.components) {
					if (component.config.upCtrl == upProp) {
						// 获取数据值
						await this.learun_form_fetchDataSource(component, this.formData)
						component.key = this.GUID()
						this.setValue({
							path: component.id,
							value: undefined
						})
						await this.clearSubValue(component.id)
					}
				}
			},
			// 设置表单数据的方法
			setValue({
				path,
				value
			}) {
				set(this.formData, path, value)
			},
			// 获取表单数据的方法
			getValue(path) {
				return get(this.formData, path)
			},
			closePage() {
				uni.navigateBack()
			},
			handleClear() {
				this.formData = {}
				this.SET_DATA(`learun_form_data_${this.formId}`, this.formData)
			},
			handleOk() {
				const formData = this.COPY(this.formData)
				this.EMIT('learun-customapp-query', formData)
				uni.navigateBack()
			}
		}
	}
</script>