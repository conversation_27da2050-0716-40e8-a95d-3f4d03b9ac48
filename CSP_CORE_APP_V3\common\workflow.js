import customForm from '@/common/customform.js'
import workflowSysMixins from '@/common/workflow_sys.js'
/**
 * 工作流表单数据处理相关方法
 * （用于工作流表单页面，包含拉取、提交等函数，注意不可用于自定义应用和代码生成器页面）
 * 
 * 用户新建表单的流程：
 *  1、拉取流程信息
 *      调用 fetchProcessInfo、 getCurrentNode
 *  2、根据流程信息里的表单相关定义来拉取表单 scheme
 *      调用 fetchSchemeData
 *     （如果是使用移动端本地表单，则无需拉取表单，且略去后续所有步骤）
 *  3、加载选单数据 （例如 checkbox、select 这些表单项有选单数据）
 *  4、赋予默认值 （例如 currentInfo 类表单项，自动把当前用户的信息填入）
 *  5、生成表单
 *      调用 getCustomForm 即可完成步骤 3~4
 * 
 * 用户打开已经填写的表单/草稿的流程：
 *  1、拉取流程信息
 *       调用 fetchProcessInfo、 getCurrentNode
 *  2、拉取表单 scheme；如果是使用移动端本地表单则无需此步骤
 *       调用 fetchSchemeData
 *  3、加载选单数据
 *       调用 fetchFormData
 *  4、拉取表单中已填入的值
 *  5、如果打开的是草稿，可能要把部分未填的项赋默认值
 *  6、生成表单
 *       调用 getCustomForm 即可完成步骤 4~5
 * 
 * 用户提交表单：
 *   1、提交草稿时无需验证字段，正式发布时需要
 *   2、获取提交的数据
 *       调用 getPostData 
 * 
 * （以上只是简单介绍；实际使用中，如果打开子流程，需要拉取父/子两个流程信息）
 */
export default {
  mixins: [customForm,workflowSysMixins],
	data(){
		return{
			wfTitle:'',
			wfCode:'',
			wfProcessId:'',
			wfProcess:{},
			wfTask:{},
			isConnect: false,
			wfScheme:{},
			wfData:[],
			wfCurrentNode:{},
			wfLogs:[],
			wfButtons:[],
			wfTaskId:'',
			wfSchemeName:'',
			formReadOnly: false,
			
			
			formId:'',
			formScheme:{},
			formRelationId:'',
			isLoadForm:false,
			isFormUpdate:false,
			loadFormParams:{},

			auditLines: [],
			auditLogList: [],
		}
	},
  methods: {
		/**
		 * 初始化流程
		 */
		async wfInit({taskId,processId,type,token}){
			let data = null
			if(taskId){
				this.wfTaskId = taskId
				data = await this.HTTP_GET({
					url:`/workflow/process/task/${taskId}`
				})
				
				this.wfProcess = data.process
				this.wfCodeCopy = data.process.f_SchemeCode;
				this.SET_GLOBAL('learun_wf_code', data.process.f_SchemeCode)
				this.wfProcessId = this.wfProcess.f_Id
				this.wfTask = data.task
				if (data.task.f_Sort != 1 && data.task.f_Type == 7) {
					this.isConnect = false;
				} else {
					this.isConnect = true;
				}
			}
			else if(processId){
				
				data = await this.HTTP_GET({
					url:`/workflow/process/${processId}`
				})
				
				this.wfProcessId = processId
				this.wfProcess = data.process
			} else if (token) {
				data = await this.HTTP_GET({
					url:`/workflow/process/task/token/${token}`
				})
				if (!data) {
					await this.wfInit({ processId: token, type: 'look'});
					this.type = 'look'
					return false;
				  }
				this.wfTaskId = data.task.f_Id
				this.wfProcess = data.process
				this.wfProcessId = this.wfProcess.f_Id
				this.wfTask = data.task
				this. wfTitle = data.task.f_ProcessTitle
				if (data.task.f_Sort != 1 && data.task.f_Type == 7) {
					this.isConnect = false;
				} else {
					this.isConnect = true;
				}
			}
			
		
			const {scheme} = data
			this.wfScheme = JSON.parse(scheme.f_Content)
			const {wfData}= this.wfScheme
			this.wfData = wfData
			
			const startNode = this.wfData.find(t=>t.type == 'startEvent')
			if(!startNode.name){
				startNode.name = this.$t('开始节点')
			}
			
			
			
			// 设置当前节点
			if(this.wfTask && this.wfTask.f_UnitId){
				this.wfCurrentNode = this.wfData.find(t=>t.id == this.wfTask.f_UnitId)
				
				if(this.wfCurrentNode.isInherit){
					// 如果是继承表单就开始节点的表单信息
					this.wfCurrentNode.formType = startNode.formType
					this.wfCurrentNode.formCode = startNode.formCode
					this.wfCurrentNode.formVerison = startNode.formVerison
					this.wfCurrentNode.formRelationId = startNode.formRelationId
					this.wfCurrentNode.formAppUrl = startNode.formAppUrl
				
					this.wfCurrentNode.authFields = startNode.authFields
					this.wfCurrentNode.authAppFields = startNode.authAppFields
				}
			}
			else {
				this.wfCurrentNode = startNode
			}
			this.formReadOnly = this.wfCurrentNode.isReadonlyForm ? true : false
			
			if (token) {
				const myTaskType = data.task.f_Type;
				const stateType = data.task.f_State == 1? 'audit' : 'look'
				if (stateType == 1 && this.wfCurrentNode.type == 'startEvent' && myTaskType == 4) {
					stateType = 'again'
				}
				this.type = type || stateType;
				type = stateType
			}
			
			// 审核路线
			this.getAuditLines();
			this.setWFLogsAndTasks(data.logs,data.tasks)
			
			// 表单处理
			const {formType,formVerison,formAppUrl,formRelationId,authFields,authAppFields} = this.wfCurrentNode
			this.isFormUpdate = true
			this.formRelationId = formRelationId

			// 取消沟通
			if (this.wfTask?.f_State == 9) {
				// 沟通中
				const btns = []
				btns.push({
				  label: this.$t('取消沟通'),
				  prop: 'learun_cancelConnect',
				  type:'warn'
				});
				this.wfButtons = btns
			}
			
			else if(!['look','read','lookmy','again'].includes(type) && this.wfCurrentNode.type !== "startEvent"){
				// 设置按钮
				const btns = []
				if (this.wfTask.f_Type == 7) {
					btns.push({
						prop:'learun_reply_connect',
						label:this.$t('回复沟通')
					})
				}
				if (this.wfTask?.f_Type !== 7) {
					for(let btn of this.wfCurrentNode.btnlist){
						if(btn.code == 'agree'){
							btn.type = 'primary'
						}
						else if(btn.code == 'disagree'){
							btn.type = 'warn'
						}
						btn.label = this.$t(btn.name)
						btn.prop = btn.code
						
						btns.push(btn)
					}
				}
				if(this.wfCurrentNode.isAddSign && this.wfTask.f_Type != 6 && this.wfTask.f_Type != 7 && !this.wfCurrentNode.isCountersign){
					btns.push({
						prop:'learun_sign',
						label:this.$t('加签')
					})
				}
				if(this.wfCurrentNode.isAddConnect && this.wfTask.f_Type != 6 && this.isConnect){
					btns.push({
						prop:'learun_connect',
						label:this.$t('沟通')
					})
				}
				
				if(this.wfCurrentNode.isTransfer && this.wfTask.f_Type != 6 && this.wfTask.f_Type != 7){
					btns.push({
						prop:'learun_transfer',
						label:this.$t('转办')
					})
				}
				this.wfButtons = btns
			}
			else if(['again'].includes(type) || this.wfCurrentNode.type == "startEvent"){
				const btns = []
				btns.push({
					prop:'learun_create',
					label:this.$t('提交')
				})
				if(this.wfCurrentNode.isDelete){
					btns.push({
							prop:'learun_detete',
							label:this.$t('作废'),
							type:'warn'
					})
				}
				this.wfButtons = btns
			}
			if (this.wfCurrentNode.layerBtnlist && this.wfCurrentNode.layerBtnlist.length > 0) {
				for(let btn of this.wfCurrentNode.layerBtnlist){
					btn.label = this.$t(btn.name)
					btn.appUrl = btn.appUrl
					btn.prop = 'navTo'
					
					this.wfButtons.push(btn)
				}
			}
			
			await this.loadForm(formType,formVerison,formAppUrl,authFields,authAppFields)
		},
		setWFLogsAndTasks(logs,tasks){
			const res = []
			const taskMap = {}
			
			tasks.forEach(task=>{				 
				if(task.f_Type == 2){
					taskMap[task.f_UnitId+task.f_Type] = taskMap[task.f_UnitId+task.f_Type] || {
						unitId:task.f_UnitId,
						name:task.f_UnitName,
						userNames:[],
						des:this.$t('正在查阅'),
						time:task.f_CreateDate,
						type:'1' // 当前处理节点
					}
					taskMap[task.f_UnitId+task.f_Type].userNames.push(task.f_UserName)
				}
				else{
					taskMap[task.f_UnitId] = taskMap[task.f_UnitId] || {
						unitId:task.f_UnitId,
						name:task.f_UnitName,
						userNames:[],
						des:this.$t('正在审核'),
						time:task.f_CreateDate,
						type:'1'
					}
					taskMap[task.f_UnitId].userNames.push(task.f_UserName)
				}
			})
			
			for(let key in taskMap){
				res.push(taskMap[key])
				this.auditLogList.push({ ...taskMap[key], content:taskMap[key].des, des: '', userNames: taskMap[key].userNames.join(',')})
			}
			
			logs.forEach(log=>{
				const node = this.wfData.find((t) => t.id == log.f_UnitId);
				// 审批要点
				let tag = '';
				if (node && log.f_AuditTag) {
				  // node.Configure
				  const auditTags = log.f_AuditTag.split(',');
				  const labels = node.Configure.filter((t) => auditTags.includes(t.value)).map(
					(t) => t.label,
				  );
				  tag = String(labels);
				}
				if (node) {
					node.hasFinish = true;
					if (this.wfCurrentNode.id === node.id) {
						this.wfData.forEach((t) => (t.hasFinish = false));
					  }
				  }
				res.push({
					unitId:log.f_UnitId,
					name:log.f_UnitName,
					userNames:[log.f_UserName],
					des:log.f_Des?log.f_Des:log.f_OperationName,
					time:log.f_CreateDate,
					type:'0', // 已经处理节点
					fileId: log.f_FileId,
					tag,
				})
				this.auditLogList.push({
					id: log.f_Id,
					userNames: log.f_UserName,
					name: log.f_UnitName,
					content: log.f_OperationName,
					time: log.f_CreateDate,
					des: log.f_Des,
				})
			})
			this.auditLogList = this.auditLogList.reverse()
			this.wfLogs = res
		},
		async getAuditLines() {
			// const data = await ...// 这里通过接口获取审核路线数据
			const data = [
				{ nodeName: '1.开始', nodeUser: '' },
				{ nodeName: '2.提报', nodeUser: '张三(acount2222)' },
				{ nodeName: '3.确认', nodeUser: '李四(acount22221111)', current: true },
				{ nodeName: '4.会签', nodeUser: '王五(acount22223333)' },
				{ nodeName: '5.合稿', nodeUser: '张三(acount22224444)' },
			];
			this.auditLines = data
		},
		
		/**
		 * 流程创建初始化
		 */
		async wfInitCreate(code,formData,processId,isLoadFormData){
			this.wfCode = code
			this.wfCodeCopy = code; // *获取数据使用
			const data = await this.HTTP_GET({
				url:`/workflow/scheme/${code}`
			})
			const {scheme,schemeinfo} = data
			this.wfSchemeName = schemeinfo.f_Name
			
			this.wfscheme = JSON.parse(scheme.f_Content)
			const {wfData}= this.wfscheme
			const startNode = wfData.find(t=>t.type == 'startEvent')
			if(!startNode.name){
				startNode.name = this.$t('开始节点')
			}
			this.wfProcessId = processId || this.GUID()
			this.wfData = wfData
			this.wfCurrentNode = startNode		
			const {formType,formVerison,formAppUrl,formRelationId,authFields,authAppFields} = this.wfCurrentNode
			
			this.isFormUpdate = isLoadFormData
			this.formRelationId = formRelationId
			
			await this.loadForm(formType,formVerison,formAppUrl,authFields,authAppFields,formData)
		},
		
		/**
		 * 流程草稿初始化
		 */
		async wfInitDraft(processId, code){
			this.wfCodeCopy = code; // *获取数据使用
			this.wfProcessId = processId
			const data = await this.HTTP_GET({
				url:`/workflow/process/draft/${processId}`
			})
			
			this.wfProcess = data.process
			const {scheme} = data
			this.wfscheme = JSON.parse(scheme.f_Content)
			const {wfData}= this.wfscheme
			const startNode = wfData.find(t=>t.type == 'startEvent')
			if(!startNode.name){
				startNode.name = this.$t('开始节点')
			}
			this.wfData = wfData
			this.wfCurrentNode = startNode		
			const {formType,formVerison,formAppUrl,formRelationId,authFields,authAppFields} = this.wfCurrentNode
			this.isFormUpdate = true
			this.formRelationId = formRelationId
			
			await this.loadForm(formType,formVerison,formAppUrl,authFields,authAppFields)
		},
		
		/**
		 * 加载表单
		 */
		async loadForm(formType,formId,formUrl,authFields,authAppFields,formData){
			// 设置表单权限
			const authForms = {}
			const formAuthFieldsMap = {}
			

			
			/*加载表单数据*/
			if(formType == '1'){
				for(let i = 0,len = authFields.length;i<len;i++){
					const authItem = authFields[i]
					formAuthFieldsMap[authItem.prop] = authItem;
					let code = '3'
					if(authItem.isLook || authItem.isEdit){
						code = authItem.isEdit?'2':'1'
					}
					if (this.formReadOnly) {
						code = '1'
					}
					if(authItem.gridprop){
						authForms[authItem.gridprop] = authForms[authItem.gridprop] || {}
						authForms[authItem.gridprop][authItem.prop] = code
					}
					else{
						authForms[authItem.prop] = code
					}
				}

				
				this.formId = formId
				if(!this.VALIDATENULL(this.formId)){
					this.isLoadForm = true
					// 自定义表单的加载
					const formSchemeData = await this.HTTP_GET({url:`/custmerform/scheme/history/${formId}`})
					const formScheme = JSON.parse(formSchemeData.f_Scheme)
					// 转化表单 formInfo
					this.formScheme = formScheme
					formScheme.formInfo.components = this.getFormSchemeComponents(formScheme.formInfo.components)
					
					let key = ''
					const components =  formScheme.formInfo.components
						for(let j = 0,jlen = components.length;j < jlen; j++ ){
							const component = components[j]
							if(!['gridtable'].includes(component.type)){
									if(component.config.display){
											if(formAuthFieldsMap[component.id]){
													component.config.required = formAuthFieldsMap[component.id].required
											}
									}
							}
							else{
									component.children.forEach((cell)=>{
											if(formAuthFieldsMap[cell.id]){
													cell.config.required = formAuthFieldsMap[cell.id].required
											}
									})
							}

							
							if(component.id == this.formRelationId){
								key = component.config.field
							}
						}
					
					this.loadFormParams ={key,keyValue:this.wfProcessId}
					this.SET_DATA(`learun_auth_wfform`,{forms:authForms,isAuth:true})
				}
			}
			else if(!this.VALIDATENULL(formUrl)){
				for(let i = 0,len = authAppFields.length;i<len;i++){
					const authItem = authAppFields[i]
					
					let code = '3'
					if(authItem.isLook || authItem.isEdit){
						code = authItem.isEdit?'2':'1'
					}
					let authProps = authItem.field.split('|')
					
					if(authProps.length > 1){
						authForms[authProps[0].toLowerCase()] = authForms[authProps[0].toLowerCase()] || {}
						authForms[authProps[0].toLowerCase()][authProps[1].toLowerCase()] = code
						
						formAuthFieldsMap[authProps[1]] = authItem;
					}
					else{
						formAuthFieldsMap[authProps[0]] = authItem;
						
						authForms[authProps[0].toLowerCase()] = code
					}
				}
				
				this.isLoadForm = true
				
				this.SET_DATA(`learun_auth_wfform`,{forms:authForms,isAuth:true})
				const pages = getCurrentPages()
				const routeOptions = pages ? pages.slice(-1)[0].options : {}
				// 系统表单加载
				this.JUMP_TO_Param(
				  `${formUrl}`,
				  {
					type: `wf_${this.type}`,
					wfProcessId:this.wfProcessId,
					wfCode:this.wfCode,
					wfData:this.wfData,
					wfCurrentNode:this.wfCurrentNode,
					wfTitle:this.wfTitle,
					wfTaskId:this.wfTaskId,
					wfLogs:this.wfLogs,
					auditLines: this.auditLines,
					auditLogList: this.auditLogList,
					wfButtons:this.wfButtons,
					
					wfFormData:formData,
					
					wfFormAuthFieldsMap:formAuthFieldsMap,
					wfTask: this.wfTask,
					formReadOnly: this.formReadOnly,
				  }, true, {
					routeFrom: pages ? '/' + pages.slice(-1)[0].route : '',
					...routeOptions
				  }
				)
			}
		},

		// 转化表单 formInfo
		getFormSchemeComponents(components) {
			const childrenGroup = {};
			const componentMap = [];
			components.forEach(component=> {
				if (component.config.isSubTable) {
					childrenGroup[component.containerId] = childrenGroup[component.containerId] || []
					childrenGroup[component.containerId].push(component)
				} else {
					if (component.type === 'gridtable') {
						childrenGroup[component.id] = childrenGroup[component.id] || []
						component.children = childrenGroup[component.id]
					}
					componentMap.push(component)
				}
			})
			return componentMap;
		}
  }
}
