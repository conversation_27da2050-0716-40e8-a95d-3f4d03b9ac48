<template>
	<!-- 当页面准备好时，渲染页面内容 -->
	<view :key="pageKey" v-if="ready" class="page bg-white" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<!-- 自定义时间轴包装器组件 -->
		<learun-timeline-wraper>
			<!-- 循环渲染时间轴项 -->
			<learun-timeline-item v-for="(timeItem,index) in nowRule.times" :key="index" :isFirst="index == 0"
				:isLast="index == nowRule.times.length -1" :color="timeItem.need?'#00CACF':''">
				<!-- 显示时间标签和时间 -->
				<view style="font-size: 12px;">
					<text>{{timeItem.label}}:</text><text>{{timeItem.time}}</text>
					<!-- 如果不需要打卡、可打卡但未打卡，显示【未打卡】 -->
					<text v-if="!timeItem.need && timeItem.clockIn && !timeItem.clockInTime">【未打卡】</text>
				</view>
				<!-- 如果不需要打卡、可打卡且已打卡，显示打卡信息 -->
				<view v-if="!timeItem.need && timeItem.clockIn && timeItem.clockInTime">
					<view style="font-size: 17px;color: #333;margin-top: 8px;">
						打卡时间:{{timeItem.clockInTime}}
						<!-- 如果迟到，显示【迟到】 -->
						<text v-if="timeItem.clockInLater">【迟到】</text>
						<!-- 如果早退，显示【早退】 -->
						<text v-if="timeItem.clockInEarlier">【早退】</text>
					</view>
					<!-- 显示打卡地址 -->
					<view style="font-size: 12px;color: #6a6a6a;margin-top: 8px;"> <uni-icons type="location-filled"
							size="16" color="#0FC979" />{{nowRule.address}}</view>
				</view>
				<!-- 如果需要打卡且距离考勤范围小于300米，显示可打卡按钮 -->
				<view class="clockContent" v-if="timeItem.need && nowRule.distance < 300">
					<view @click.stop="clockIn(timeItem)" class="clockBtn active">
						<view style="font-size: 16px;">{{timeItem.btnName}}</view>
						<view style="font-size: 12px;margin-top: 4px;"><learun-time-now /></view>
					</view>
					<!-- 显示打卡地址 -->
					<view style="font-size: 12px;color: #6a6a6a;margin-top: 16px;"> <uni-icons type="location-filled"
							size="16" color="#0FC979" />{{nowRule.address}}</view>
				</view>
				<!-- 如果需要打卡但距离考勤范围大于等于300米，显示不可打卡按钮和提示 -->
				<view class="clockContent" v-else-if="timeItem.need">
					<view class="clockBtn">
						<view style="font-size: 16px;">{{timeItem.btnName}}</view>
						<view style="font-size: 12px;margin-top: 4px;"><learun-time-now /></view>
					</view>
					<view style="font-size: 12px;color: #6a6a6a;margin-top: 16px;"> <uni-icons type="info-filled"
							size="16" color="#FFC31E" />当前不在考勤范围内<text style="color: #00CACF;padding-left: 4px;"
							@click.stop="lookMap(timeItem)">查看考勤范围</text></view>
				</view>
			</learun-timeline-item>
		</learun-timeline-wraper>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 登录用户信息
				loginUser: null,
				// 考勤规则列表
				rules: [],
				// 打卡记录列表
				clockInList: [],
				// 当前使用的考勤规则
				nowRule: null,
				// 当前时间
				time: '',
				// 用户所在纬度
				lat: 0,
				// 用户所在经度
				lng: 0,
				// 获取位置的时间间隔控制变量
				getLoactionTime: 1,
				// 页面是否准备好的标志
				ready: false,
				// 定时器
				timer: null,
				// 页面的唯一标识，用于强制刷新页面
				pageKey: 1
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("考勤打卡")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面
				await this.init();
			}
		},
		methods: {
			async init() {
				// 显示加载提示
				// 标记页面未准备好
				this.ready = false;
				// 获取全局登录用户信息
				this.loginUser = this.GET_GLOBAL('loginUser');
				// 获取考勤规则列表
				this.rules = await this.getClockRules();
				// 如果没有考勤规则，隐藏加载提示并返回
				if (this.rules.length == 0) {
					this.HIDE_LOADING();
					return
				}
				// 获取打卡记录列表
				this.clockInList = await this.getClockInList();
				// 设置当前使用的考勤规则
				this.setNowRule();
				// 标记页面准备好
				this.ready = true;
				// 隐藏加载提示
				this.HIDE_LOADING();
				// 设置用户位置信息
				this.setLocation();
			},
			async getClockRules() {
				// 发送请求获取考勤规则列表
				let list = await this.HTTP_GET({
					url: '/demoApp/apprules',
					params: {
						f_Person: this.loginUser.f_UserId,
						page: 1,
						rows: 1,
					}
				})
				// 获取当前星期
				let date = new Date()
				const week = date.getDay()
				// 过滤出符合当前星期的考勤规则
				list = list.filter(t => t.f_Date.indexOf(week) > -1)
				return list;
			},
			setNowRule() {
				// 遍历考勤规则列表
				for (let item of this.rules) {
					// 解析考勤范围信息
					const scopeList = item.f_Scope.split(',')
					// 解析考勤时间信息
					const timeList = item.f_Time.split(',')
					// 构建当前考勤规则对象
					const ruleItem = {
						id: item.f_Id,
						name: item.f_Name,
						address: scopeList[0],
						lng: scopeList[1],
						lat: scopeList[2],
						times: [],
					}
					// 遍历考勤时间列表
					for (let time of timeList) {
						// 解析每个时间项
						let timeItems = time.split('|')
						let timeItems2 = timeItems[1].split(' - ')
						// 构建上班节点信息
						const startNode = {
							label: timeList.length > 1 ? `${timeItems[0]}${this.$t('上班时间')}` : this.$t('上班时间'),
							btnName: this.$t('上班打卡'),
							time: timeItems2[0],
							need: false,
							type: 1, // 上班
							timeDiff: this.DATENOW_DIFF(`${this.DATENOW('YYYY-MM-DD')} ${timeItems2[0]}`)
						}
						// 确保时间差为正数
						if (startNode.timeDiff < 0) {
							startNode.timeDiff = -startNode.timeDiff
						}
						// 查找上班打卡记录
						let clockInLog = this.clockInList.find(t => t.f_RuleId == ruleItem.id && t.f_OfficeTime ==
							timeItems2[0])
						if (clockInLog) {
							// 记录打卡ID
							startNode.clockId = clockInLog.f_Id;
							// 标记已打卡
							startNode.clockIn = true;
							// 格式化打卡时间
							startNode.clockInTime = this.TABLEITEM_DATEFORMAT(clockInLog.f_OfficeTimePunch,
								'YYYY-MM-DD HH:mm:ss').toString();
							// 判断是否迟到
							startNode.clockInLater = this.DATE_DIFF(clockInLog.f_OfficeTimePunch,
								`${this.DATENOW('YYYY-MM-DD')} ${timeItems2[0]}`) > 60000
						}
						// 将上班节点添加到当前考勤规则的时间列表中
						ruleItem.times.push(startNode)
						// 构建下班节点信息
						const endNode = {
							label: timeList.length > 1 ? `${timeItems[0]}${this.$t('下班时间')}` : this.$t('下班时间'),
							btnName: this.$t('下班打卡'),
							time: timeItems2[1],
							need: false,
							type: 2, // 下班
							timeDiff: this.DATENOW_DIFF(`${this.DATENOW('YYYY-MM-DD')} ${timeItems2[1]}`)
						}
						// 确保时间差为正数
						if (endNode.timeDiff < 0) {
							endNode.timeDiff = -endNode.timeDiff
						}
						// 查找下班打卡记录
						let clockInLog2 = this.clockInList.find(t => t.f_RuleId == ruleItem.id && t.f_ClosingTime ==
							timeItems2[1])
						if (clockInLog2) {
							// 记录打卡ID
							endNode.clockId = clockInLog2.f_Id;
							// 标记已打卡
							endNode.clockIn = true;
							// 格式化打卡时间
							endNode.clockInTime = this.TABLEITEM_DATEFORMAT(clockInLog2.f_ClosingTimePunch,
								'YYYY-MM-DD HH:mm:ss').toString();
							// 如果上班未打卡，关联上班打卡ID
							if (!startNode.clockIn) {
								startNode.clockId = endNode.clockId
								startNode.clockIn = true;
							}
							// 判断是否早退
							endNode.clockInEarlier = this.DATE_DIFF(clockInLog2.f_ClosingTimePunch,
								`${this.DATENOW('YYYY-MM-DD')} ${timeItems2[1]}`) < -60000
						} else if (startNode.clockIn) {
							// 如果上班已打卡，关联上班打卡ID
							endNode.clockId = startNode.clockId
						}
						// 将下班节点添加到当前考勤规则的时间列表中
						ruleItem.times.push(endNode)
					}
					// 过滤出未打卡的时间项
					let clockTimes = ruleItem.times.filter(t => t.clockIn != true)
					if (clockTimes.length > 0) {
						// 找到距离当前时间最近的未打卡时间项
						let minClockTimeItem = clockTimes[0]
						for (let clockTimeItem of clockTimes) {
							if (minClockTimeItem.timeDiff > clockTimeItem.timeDiff) {
								minClockTimeItem = clockTimeItem;
							}
						}
						// 标记该时间项为需要打卡
						minClockTimeItem.need = true;
						// 处理其他未打卡时间项
						for (let clockTimeItem of clockTimes) {
							const timeDiff = this.DATE_DIFF(`${this.DATENOW('YYYY-MM-DD')} ${clockTimeItem.time}`,
								`${this.DATENOW('YYYY-MM-DD')} ${minClockTimeItem.time}`)
							if (!clockTimeItem.need && timeDiff < 0) {
								// 标记为已打卡
								clockTimeItem.clockIn = true;
							}
						}
					}
					if (this.lng == 0) {
						// 如果还未获取到用户位置，将当前规则设为当前使用的规则
						this.nowRule = ruleItem;
						break;
					} else {
						// 计算当前规则的考勤范围与用户位置的距离
						ruleItem.distance = this.getDistance(ruleItem.lat, ruleItem.lng, this.lat, this.lng)
						if (this.nowRule == null ||
							this.nowRule.distance == undefined ||
							this.nowRule.distance > ruleItem.distance ||
							this.nowRule.id == ruleItem.id) {
							// 如果当前使用的规则为空或距离更远，更新当前使用的规则
							this.nowRule = ruleItem
						}
					}
				}
			},
			async setLocation() {
				// 获取用户位置信息
				const res = await this.GET_LOCATION()
				if (res) {
					// 更新用户的纬度和经度
					this.lat = res.lat;
					this.lng = res.lng;
				}
				// 重新设置当前使用的考勤规则
				this.setNowRule();
				// 更新页面唯一标识，强制刷新页面
				this.pageKey++;
				// 设置定时器，定时更新位置信息
				this.timer = setTimeout(async () => {
					await this.setLocation();
				}, this.getLoactionTime * 1000)
				// 增加获取位置的时间间隔
				this.getLoactionTime++;
				if (this.getLoactionTime > 10) {
					// 限制时间间隔最大为10秒
					this.getLoactionTime = 10;
				}
			},
			async refresh() {
				// 显示刷新提示
				this.LOADING('刷新中...')
				// 清除定时器
				clearTimeout(this.timer);
				setTimeout(async () => {
					// 清除定时器
					clearTimeout(this.timer);
					// 重新获取打卡记录列表
					this.clockInList = await this.getClockInList();
					// 更新用户位置信息
					await this.setLocation();
					// 显示打卡成功提示
					uni.showToast({
						title: this.$t("打卡成功")
					})
				}, 100)
			},
			// 计算两点之间的距离
			getDistance(lat1, lng1, lat2, lng2) {
				lat1 = lat1 || 0;
				lng1 = lng1 || 0;
				lat2 = lat2 || 0;
				lng2 = lng2 || 0;
				// 将角度转换为弧度
				let rad1 = lat1 * Math.PI / 180.0;
				let rad2 = lat2 * Math.PI / 180.0;
				let a = rad1 - rad2;
				let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
				// 地球半径
				let r = 6378137;
				// 计算距离
				let distance = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) *
					Math.pow(Math.sin(b / 2), 2)));
				return distance;
			},
			lookMap(timeItem) {
				// 构建打卡数据对象
				let clockData = {
					type: timeItem.type,
					f_RuleId: this.nowRule.id,
					f_Id: timeItem.clockId
				}
				if (timeItem.type == 1) {
					// 上班打卡
					clockData.f_OfficeTime = timeItem.time
				} else {
					// 下班打卡
					clockData.f_ClosingTime = timeItem.time
				}
				// 监听刷新事件
				this.ONCE('learun-clockIn-refresh', () => {
					setTimeout(() => {
						// 触发刷新操作
						this.refresh();
					}, 100)
				})
				// 跳转到查看考勤范围页面
				this.NAV_TO_LAYER(`./selectScope`, {
					lng: this.nowRule.lng,
					lat: this.nowRule.lat,
					distance: this.nowRule.distance,
					myLat: this.lat,
					myLng: this.lng,
					clockData
				}, true)
			},
			async getClockInList() {
				// 发送请求获取打卡记录列表
				let list = await this.HTTP_GET({
					url: '/demoApp/apppunchs',
					params: {
						f_CreateUserId: this.loginUser.f_UserId,
						f_CreateDate: this.DATENOW(),
					}
				})
				return list
			},
			async clockIn(timeItem) {
				if (this.nowRule.distance < 300) {
					// 显示打卡提示
					this.LOADING(this.$t('打卡中...'))
					// 构建打卡数据对象
					let clockData = {
						type: timeItem.type,
						f_RuleId: this.nowRule.id
					}
					if (timeItem.type == 1) {
						// 上班打卡
						clockData.f_OfficeTime = timeItem.time
					} else {
						// 下班打卡
						clockData.f_ClosingTime = timeItem.time
					}
					let res;
					if (!timeItem.clockId) {
						// 未打卡过，发送 POST 请求创建打卡记录
						res = await this.HTTP_POST({
							url: '/demoApp/apppunch',
							data: clockData
						})
					} else {
						// 已打卡过，发送 PUT 请求更新打卡记录
						res = await this.HTTP_PUT({
							url: `/demoApp/apppunch/${timeItem.clockId}`,
							data: clockData
						})
					}
					if (res) {
						// 打卡成功，触发刷新操作
						await this.refresh();
					}
				} else {
					// 不在打卡范围，显示提示
					uni.showToast({
						title: this.$t("当前未在打卡范围"),
						icon: 'error'
					})
				}
			},
		}
	}
</script>
<style lang="scss" scoped>
	.clockContent {
		position: relative;
		width: 100%;
		text-align: center;
	}

	.clockBtn {
		width: 100px;
		height: 100px;
		background: linear-gradient(180deg, #BFE9EB 0%, #4E6B6B 100%);
		box-shadow: 0px 2.5px 4px 0.5px rgba(68, 79, 80, 0.3);
		border-radius: 50%;
		text-align: center;
		margin: auto;
		margin-top: 30px;
		color: #FFFFFF;

		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;

		&.active {
			background: linear-gradient(180deg, #01F8FF 0%, #00A3A8 100%);
		}
	}
</style>