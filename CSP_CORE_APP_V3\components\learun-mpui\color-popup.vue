<template>
	<uni-popup ref="popup" @change="popupchange" type="bottom" >
			<view class="learun-color-view__wraper">
				<view class="close" @click.stop="close">
					<learun-icon type="learun-icon-error"  ></learun-icon>
				</view>
				
				<view class="learun-color-view__panel" :style="{ background: 'rgb(' + bgcolor.r + ',' + bgcolor.g + ',' + bgcolor.b + ')'}">
					<view class="background boxs" @touchstart.stop="touchstart($event, 0)" @touchmove.stop="touchmove($event, 0)">
						<view class="mask"></view>
						<view class="pointer" :style="{ top: site[0].top - 8 + 'px', left: site[0].left - 8 + 'px' }"></view>
					</view>
				</view>
				<view class="learun-color-view__control-panel">
					<view class="color-wraper">
						<view class="color-content" :style="{ background: 'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' + rgba.a + ')' }"></view>
					</view>
					<view class="control-wraper">
						<view class="controller boxs" @touchstart.stop="touchstart($event, 1)" @touchmove.stop="touchmove($event, 1)" >
							<view class="hue">
								<view class="circle" :style="{ left: site[1].left - 12 + 'px' }"></view>
							</view>
						</view>
						<view class="controller boxs" @touchstart.stop="touchstart($event, 2)" @touchmove.stop="touchmove($event, 2)" >
							<view class="transparency">
								<view class="circle" :style="{ left: site[2].left - 12 + 'px' }"></view>
							</view>
						</view>
					</view>
				</view>
				<view class="alternative">
					<view class="alternative__item" v-for="(item,index) in colorList" :key="index">
						<view class="alternative__item-content" :style="{ background: 'rgba(' + item.r + ',' + item.g + ',' + item.b + ',' + item.a + ')' }"
						 @click="selectColor(item)">
						</view>
					</view>
				</view>
				<view class="result-wraper">
					<view class="result-wraper__input">{{'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' + rgba.a + ')'}}</view>
				</view>				
				<view class="learun-popup-button-wraper" >
					<view class="learun-popup-button-ok" @click="confirm">{{$t('确认')}}</view>
				</view>
			</view>
	</uni-popup>
</template>

<script>
	export default {
		name:'learun-color-popup',
		props: {
			color: {
				type: Object,
				default () {
					return {
						r: 0,
						g: 0,
						b: 0,
						a: 1
					}
				}
			},
			spareColor: {
				type: Array,
				default () {
					return []
				}
			}
		},
		data() {
			return {
				position:null,
				flag:false,
				// rgba 颜色
				rgba: {
					r: 0,
					g: 0,
					b: 0,
					a: 1
				},
				// hsb 颜色
				hsb: {
					h: 0,
					s: 0,
					b: 0
				},
				site: [{
					top: 0,
					left: 0
				}, {
					left: 0
				}, {
					left: 0
				}],
				index: 0,
				bgcolor: {
					r: 255,
					g: 0,
					b: 0,
					a: 1
				},
				hex: '#000000',
				mode: true,
				colorList: [{
					r: 244,
					g: 67,
					b: 54,
					a: 1
				}, {
					r: 233,
					g: 30,
					b: 99,
					a: 1
				}, {
					r: 156,
					g: 39,
					b: 176,
					a: 1
				}, {
					r: 103,
					g: 58,
					b: 183,
					a: 1
				}, {
					r: 63,
					g: 81,
					b: 181,
					a: 1
				}, {
					r: 33,
					g: 150,
					b: 243,
					a: 1
				}, {
					r: 3,
					g: 169,
					b: 244,
					a: 1
				}, {
					r: 0,
					g: 188,
					b: 212,
					a: 1
				}, {
					r: 0,
					g: 150,
					b: 136,
					a: 1
				}, {
					r: 76,
					g: 175,
					b: 80,
					a: 1
				}, {
					r: 139,
					g: 195,
					b: 74,
					a: 1
				}, {
					r: 205,
					g: 220,
					b: 57,
					a: 1
				}, {
					r: 255,
					g: 235,
					b: 59,
					a: 1
				}, {
					r: 255,
					g: 193,
					b: 7,
					a: 1
				}, {
					r: 255,
					g: 152,
					b: 0,
					a: 1
				}, {
					r: 255,
					g: 87,
					b: 34,
					a: 1
				}, {
					r: 121,
					g: 85,
					b: 72,
					a: 1
				}, {
					r: 158,
					g: 158,
					b: 158,
					a: 1
				}, {
					r: 0,
					g: 0,
					b: 0,
					a: 0.5
				}, {
					r: 0,
					g: 0,
					b: 0,
					a: 0
				}, ]
			};
		},
		created() {
			this.rgba = this.color;
			if (this.spareColor.length !== 0) {
				this.colorList = this.spareColor;
			}
		},
		methods: {
			open(color) {
				if(!this.VALIDATENULL(color)){
					if(color.indexOf('rgba') != -1){
						const colorList = color.replace('rgba(','').replace(')','').split(',')
						if(colorList.length == 4){
							this.rgba.r = colorList[0]
							this.rgba.g = colorList[1]
							this.rgba.b = colorList[2]
							this.rgba.a = colorList[3]
						}
					}
				}
				this.$refs.popup.open()
			},
			close(){
				this.$refs.popup.close()
			},
			init() {
				this.hsb = this.rgbToHsb(this.rgba)				
				this.setValue(this.rgba)
				setTimeout(()=>{
					this.getSelectorQuery()
				},100)
			},
			popupchange({show}){
				if(show){
					this.init()
				}
				else{
					this.flag = false
				}
			},
			confirm() {
				this.close();
				this.$emit('confirm', {
					rgba: this.rgba,
					hex: this.hex
				})
			},
			// 选择模式
			select() {
				this.mode = !this.mode
			},
			// 常用颜色选择
			selectColor(item) {
				this.setColorBySelect(item)
			},
			touchstart(e, index) {
				if(!this.flag){
					this.flag = true
					this.getSelectorQuery()
				}
				
				const {
					pageX,
					pageY
				} = e.touches[0];
				this.pageX = pageX;
				this.pageY = pageY;
				this.setPosition(pageX, pageY, index);
			},
			touchmove(e, index) {
				const {
					pageX,
					pageY
				} = e.touches[0]
				this.moveX = pageX
				this.moveY = pageY
				
				this.setPosition(pageX, pageY, index)
			},
			/**
			 * 设置位置
			 */
			setPosition(x, y, index) {
				this.index = index;
				const {
					top,
					left,
					width,
					height
				} = this.position[index];
				
				
				// 设置最大最小值

				this.site[index].left = Math.max(0, Math.min(parseInt(x - left), width));
				if (index === 0) {
					this.site[index].top = Math.max(0, Math.min(parseInt(y - top), height));
					// 设置颜色
					this.hsb.s = parseInt((100 * this.site[index].left) / width);
					this.hsb.b = parseInt(100 - (100 * this.site[index].top) / height);
					this.setColor();
					//this.setValue(this.rgba);
				} else {
					this.setControl(index, this.site[index].left);
				}
			},
			/**
			 * 设置 rgb 颜色
			 */
			setColor() {
				const rgb = this.HSBToRGB(this.hsb);
				this.rgba.r = rgb.r;
				this.rgba.g = rgb.g;
				this.rgba.b = rgb.b;
			},
			/**
			 * 设置二进制颜色
			 * @param {Object} rgb
			 */
			setValue(rgb) {
				this.hex = '#' + this.rgbToHex(rgb);
			},
			setControl(index, x) {
				const {
					top,
					left,
					width,
					height
				} = this.position[index];

				if (index === 1) {
					this.hsb.h = parseInt((360 * x) / width);
					this.bgcolor = this.HSBToRGB({
						h: this.hsb.h,
						s: 100,
						b: 100
					});
					this.setColor()
				} else {
					this.rgba.a = (x / width).toFixed(1);
				}
				//this.setValue(this.rgba);
			},
			/**
			 * rgb 转 二进制 hex
			 * @param {Object} rgb
			 */
			rgbToHex(rgb) {
				let hex = [rgb.r.toString(16), rgb.g.toString(16), rgb.b.toString(16)];
				hex.map(function(str, i) {
					if (str.length == 1) {
						hex[i] = '0' + str;
					}
				});
				return hex.join('');
			},
			setColorBySelect(getrgb) {
				const {
					r,
					g,
					b,
					a
				} = getrgb;
				let rgb = {}
				rgb = {
					r: r ? parseInt(r) : 0,
					g: g ? parseInt(g) : 0,
					b: b ? parseInt(b) : 0,
					a: a ? a : 0,
				};
				this.rgba = rgb;
				this.hsb = this.rgbToHsb(rgb);
				this.changeViewByHsb();
			},
			changeViewByHsb() {
				const [a, b, c] = this.position;
				this.site[0].left = parseInt(this.hsb.s * a.width / 100);
				this.site[0].top = parseInt((100 - this.hsb.b) * a.height / 100);
				this.setColor(this.hsb.h);
				//this.setValue(this.rgba);
				this.bgcolor = this.HSBToRGB({
					h: this.hsb.h,
					s: 100,
					b: 100
				});

				this.site[1].left = this.hsb.h / 360 * b.width;
				this.site[2].left = this.rgba.a * c.width;

			},
			/**
			 * hsb 转 rgb
			 * @param {Object} 颜色模式  H(hues)表示色相，S(saturation)表示饱和度，B（brightness）表示亮度
			 */
			HSBToRGB(hsb) {
				let rgb = {};
				let h = Math.round(hsb.h);
				let s = Math.round((hsb.s * 255) / 100);
				let v = Math.round((hsb.b * 255) / 100);
				if (s == 0) {
					rgb.r = rgb.g = rgb.b = v;
				} else {
					let t1 = v;
					let t2 = ((255 - s) * v) / 255;
					let t3 = ((t1 - t2) * (h % 60)) / 60;
					if (h == 360) h = 0;
					if (h < 60) {
						rgb.r = t1;
						rgb.b = t2;
						rgb.g = t2 + t3;
					} else if (h < 120) {
						rgb.g = t1;
						rgb.b = t2;
						rgb.r = t1 - t3;
					} else if (h < 180) {
						rgb.g = t1;
						rgb.r = t2;
						rgb.b = t2 + t3;
					} else if (h < 240) {
						rgb.b = t1;
						rgb.r = t2;
						rgb.g = t1 - t3;
					} else if (h < 300) {
						rgb.b = t1;
						rgb.g = t2;
						rgb.r = t2 + t3;
					} else if (h < 360) {
						rgb.r = t1;
						rgb.g = t2;
						rgb.b = t1 - t3;
					} else {
						rgb.r = 0;
						rgb.g = 0;
						rgb.b = 0;
					}
				}
				return {
					r: Math.round(rgb.r),
					g: Math.round(rgb.g),
					b: Math.round(rgb.b)
				};
			},
			rgbToHsb(rgb) {
				let hsb = {
					h: 0,
					s: 0,
					b: 0
				};
				let min = Math.min(rgb.r, rgb.g, rgb.b);
				let max = Math.max(rgb.r, rgb.g, rgb.b);
				let delta = max - min;
				hsb.b = max;
				hsb.s = max != 0 ? 255 * delta / max : 0;
				if (hsb.s != 0) {
					if (rgb.r == max) hsb.h = (rgb.g - rgb.b) / delta;
					else if (rgb.g == max) hsb.h = 2 + (rgb.b - rgb.r) / delta;
					else hsb.h = 4 + (rgb.r - rgb.g) / delta;
				} else hsb.h = -1;
				hsb.h *= 60;
				if (hsb.h < 0) hsb.h = 0;
				hsb.s *= 100 / 255;
				hsb.b *= 100 / 255;
				return hsb;
			},
			getSelectorQuery() {
				const views = uni.createSelectorQuery().in(this);
				views
				.selectAll('.boxs')
				.boundingClientRect(data => {
					if (!data || data.length === 0) {
						setTimeout(() => this.getSelectorQuery(), 20)
						return
					}
					
					this.position = data
					this.setColorBySelect(this.rgba)
				})
				.exec()
			}
		}
	}
</script>

<style lang="scss" scoped >
	.learun-color-view {

		&__wraper{
			width: 100%;
			position: relative;
			padding-top: 40px;
			background: #fff;
			border-top-left-radius: 8px;
			border-top-right-radius: 8px;
			
			.close{
				position: absolute;
				top:8px;
				right: 8px;
			}
			
			.button-wraper{
				padding: 8px;
			}
		}

		&__panel{
			position: relative;
			height: 200px;
			background: rgb(255, 0, 0);
			overflow: hidden;
			box-sizing: border-box;
			margin: 0 8px 0 8px;
			.background{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0));
			}
			.mask{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				width: 100%;
				height: 200px;
				background: linear-gradient(to top, #000, rgba(0, 0, 0, 0));
			}
			.pointer {
				position: absolute;
				bottom: -8px;
				left: -8px;
				z-index: 2;
				width: 15px;
				height: 15px;
				border: 1px #fff solid;
				border-radius: 50%;
			}
		}
	
		&__control-panel{
			margin-top: 24px;
			width: 100%;
			display: flex;
			padding-left: 8px;
			box-sizing: border-box;
			
			.color-wraper{
				flex-shrink: 0;
				width: 50px;
				height: 50px;
				border-radius: 50%;
				background-color: #fff;
				background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee),
					linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee);
				background-size: 18px 18px;
				background-position: 0 0, 9px 9px;
				border: 1px #eee solid;
				overflow: hidden;
			}
			.color-content{
				width: 100%;
				height: 100%;
			}
			.control-wraper{
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				width: 100%;
				padding: 0 16px;
			}
			
			.controller {
				position: relative;
				width: 100%;
				height: 16px;
				background-color: #fff;
				background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee),
					linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee);
				background-size: 16px 16px;
				background-position: 0 0, 8px 8px;
			}
			
			.hue {
				width: 100%;
				height: 100%;
				background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
			}
			.transparency {
				width: 100%;
				height: 100%;
				background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0));
			}
			
			.circle {
				position: absolute;
				/* right: -10px; */
				top: -2px;
				width: 20px;
				height: 20px;
				box-sizing: border-box;
				border-radius: 50%;
				background: #fff;
				box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
			}
		}
		
	}
	
	
	.alternative {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		padding-right: 8px;
		box-sizing: border-box;
		margin-top: 24px;
		
		&__item {
			margin: 8px 0 0 8px;
			width: 32px;
			height: 32px;
			border-radius: 4px;
			background-color: #fff;
			background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee),
				linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee);
			background-size: 16px 16px;
			background-position: 0 0, 9px 9px;
			border: 1px #eee solid;
			overflow: hidden;
			
			&-content {
				width: 32px;
				height: 32px;
				background: rgba(255, 0, 0, 0.5);
			}
			
			&:active {
				transition: all 0.3s;
				transform: scale(1.1);
			}
		}
	}

	

	.result-wraper {
		margin-top: 16px;
		padding: 10px;
		width: 100%;
		display: flex;
		box-sizing: border-box;
		
		
		&__input {
			padding: 8px 0;
			width: 100%;
			font-size: 14px;
			box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
			color: #999;
			text-align: center;
			background: #fff;
		}
	}
</style>
