<template>
	<!-- 页面根容器 -->
	<view>
		<!-- 页面标题，使用多语言翻译显示“签名” -->
		<view class="title">{{ $t('签名') }}</view>
		<!-- 自定义签名组件，用于PC端签名交互 -->
		<crystal-single :id="propId" :isvalidate="isvalidate" :style="{width: 70}" :required="false"
			:editMode="editMode" @setValue="setValue" :screenFul="true" titleLabel="" :is-debug="isDebug" />
		<!-- 提交按钮，仅在编辑模式下显示 -->
		<button v-if="editMode" @click="saveForm" type="primary" style="width: 90%;">{{ $t('提交') }}</button>
	</view>
</template>

<script>
	import {
		compact
	} from 'lodash';
	export default {
		data() {
			return {
				propId: "", // 组件标识，用于区分不同签名场景
				isvalidate: "", // 验证状态（暂未使用具体逻辑）
				editMode: true, // 编辑模式开关，控制是否可交互及按钮显示
				id: "", // 接收URL参数中的业务ID（如二维码携带的ID）
				value: "", // 存储签名组件返回的签名数据
				isDebug: false, // 调试模式开关
				schemeId: "", // 表单方案ID
				ridProp: "", // 业务ID对应的字段属性名
				signatureProp: "", // 签名数据对应的字段属性名
				pkey: "" // 表单主键字段
			};
		},
		async onLoad(options) {
			// 解析URL参数，获取业务ID
			this.id = options.id;
			// 验证ID是否存在，不存在则提示错误
			if (!this.id) {
				this.TOAST(this.$t("二维码错误！"));
			}
			// 加载表单配置信息
			await this.getFormApi();
		},
		methods: {
			// 签名组件值变化时的回调，更新本地value
			setValue(data) {
				this.value = data;
			},
			async getFormApi() {
				// 定义错误提示信息
				let errMessage = this.$t("【PC端签名控件API】表单不存在！");
				// 第一步：获取指定关键词的表单方案（关键词为“PC端签名控件API”）
				const res1 = await this.HTTP_GET({
					url: "/custmerform/scheme/page",
					params: {
						"rows": 1, // 每页1条数据
						"page": 1, // 第一页
						"sidx": "F_CreateDate DESC", // 按创建时间倒序
						"category": "", // 分类为空
						"keyword": this.$t("PC端签名控件API") // 搜索关键词
					}
				});
				// 检查是否获取到表单方案
				if (res1.rows.length <= 0) {
					this.TOAST(errMessage);
					return;
				}
				// 提取表单方案ID
				this.schemeId = res1.rows[0].f_SchemeId;
				// 第二步：获取表单详细结构
				const res2 = await this.HTTP_GET({
					url: "/custmerform/scheme/" + res1.rows[0].f_Id // 使用表单ID获取详情
				});
				// 检查表单结构是否存在
				if (!res2.scheme || !res2.scheme.f_Scheme) {
					this.TOAST(errMessage);
					return;
				}
				// 解析表单结构JSON
				const f_Scheme = JSON.parse(res2.scheme.f_Scheme);
				// 提取表单主键字段
				this.pkey = f_Scheme.primaryKey;
				// 遍历表单组件，获取业务ID和签名对应的字段属性
				const components = f_Scheme.formInfo.tabList[0].components; // 假设使用第一个标签页的组件
				for (let i = 0; i < components.length; i++) {
					// 匹配业务ID字段（字段名为"RID"）
					if (components[i].field === "RID") {
						this.ridProp = components[i].prop;
					}
					// 匹配签名字段（字段名为"signature"）
					if (components[i].field === "signature") {
						this.signatureProp = components[i].prop;
					}
				}
			},
			async saveForm() {
				// 验证是否有签名数据
				if (!this.value) {
					this.TOAST(this.$t("请签名！"));
					return;
				}
				// 组装提交数据：包含业务ID和签名值
				const postData = {
					data: `{"${this.ridProp}":"${this.id}","${this.signatureProp}":"${this.value}"}`,
					diffFormData: [], // 差异数据（暂未使用）
					isUpdate: false, // 是否为更新操作（此处为新建）
					pkey: this.pkey, // 表单主键
					schemeId: this.schemeId // 表单方案ID
				};
				// 发送保存请求
				const resultData = await this.HTTP_POST({
					url: "/custmerform/data",
					data: postData,
					errorTips: this.$t("保存失败！")
				});
				// 处理保存结果
				if (resultData) {
					// 保存成功后返回首页并提示
					this.RELAUNCH_TO("/pages/home");
					this.TOAST(this.$t("保存成功！"), "success");
				}
			}
		}
	};
</script>

<style>
	.title {
		/* 标题样式：居中、蓝色、较大字体 */
		margin: 15px;
		/* 上下左右外边距 */
		font-size: 30px;
		/* 字体大小 */
		color: rgb(30, 144, 255);
		/* 字体颜色（天蓝色） */
		text-align: center;
		/* 文本居中 */
	}
</style>