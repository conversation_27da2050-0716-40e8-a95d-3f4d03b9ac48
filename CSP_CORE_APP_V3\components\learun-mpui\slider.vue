<template>
	<slider :block-size="16" :show-value="showTooltip" :disabled="disabled" v-model="myValue" @change="handleChange"  />
</template>

<script>
	export default {
		name:'learun-slider',
		props:{
			value:{},
			min:{
				type:Number,
				default:0
			},
			max:{
				type:Number,
				default:100
			},
			step:{
				type:Number,
				default:1
			},
			disabled:<PERSON><PERSON>an,
			showTooltip:<PERSON><PERSON>an
		},
		computed:{
			myValue:{
				get(){
					if(this.VALIDATENULL(this.value)){
						return 0
					}
					return this.value
				},
				set(val){
					this.$emit('input',val)
				}
			}
		},
		methods:{
			handleChange(event){
				const value = event.detail.value
				this.$emit('input',value)
			}
		}
	}
</script>

<style lang="scss" scoped >
	.learun-slider{
		align-items: center;
	}
</style>
