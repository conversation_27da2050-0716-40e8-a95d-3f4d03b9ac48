<template>
	<view class="page" :style="{
		  'min-height':SCREENHEIGHT()+'px',
		  'background-color':'#fff',
		  'padding-top':isWorkflow() && needWorkflowInfo?'52px':0,
		  'padding-bottom':hasBtns()?'40px':0
		  }">

		<!-- 流程信息 -->
		<view v-if="ready && isWorkflow() && needWorkflowInfo" class="bg-white fixed" style="padding: 8px;">
			<uni-segmented-control :current="wfTab" :values="wfTabList" @clickItem="wfChangeTab">
			</uni-segmented-control>
		</view>
		<learun-workflow-timeline v-if="ready && isWorkflow() && needWorkflowInfo" v-show="wfTab == 1" :wfLogs="wfLogs">
		</learun-workflow-timeline>
		<!-- 渲染表单 -->
		<learun-customform-wraper :top="needWorkflowInfo? 52 : 0" v-show="wfTab == 0" v-if="ready" :editMode="editMode"
			:scheme="{formInfo:formScheme}" :isUpdate="isUpdate" :formId="moduleId" :moduleId="moduleId"
			@ready="handleReady" isSystem :initFormValue="formData" @myAfterChangeDataEvent="afterChangeDataEvent"
			@beforeInput="beforeInput" ref="form" />
		<!-- 操作区按钮 -->
		<view v-if="ready && !isWorkflow()" class="learun-bottom-btns">
			<button v-if="mode !== 'create' && editMode && GET_BUTTON_AUTH('Delete',moduleId)" @click="handleDelete"
				type="warn">{{$t('删除')}}</button>
			<button v-if="editMode" @click="saveForm" type="primary">{{$t('保存')}}</button>
		</view>
		<!-- 流程操作区域 -->
		<view v-if="ready && isWorkflow()" class="learun-bottom-btns">
			<button v-if="['wf_create','wf_draft','wf_again'].includes(mode)"
				@click.stop="wf_draft">{{$t('保存草稿')}}</button>
			<button v-if="['wf_create','wf_draft','wf_again'].includes(mode)" @click.stop="wf_submit"
				type="primary">{{$t('流程提交')}}</button>
			<button v-if="wfIsCancel" @click.stop="wf_revokeAudit" type="warn">{{$t('撤销')}}</button>
			<!-- <button v-else-if="mode == 'wf_audit'" @click.stop="wf_open_action" type="primary">{{$t('流程处理')}}</button> -->
			<button v-else-if="wfIsRead" @click.stop="wf_read" type="primary">{{$t('确认阅读')}}</button>
			<button v-if="mode == 'wf_audit'" @click.stop="wf_action2('disagree')" type="danger">{{$t('拒绝')}}</button>
			<button v-if="mode == 'wf_audit'" @click.stop="wf_action2('agree')" type="primary">{{$t('同意')}}</button>
		</view>
		<learun-popup-buttons v-if="ready && isWorkflow()" ref="wfpopup" :buttons="wfButtons" @click="wf_action">
		</learun-popup-buttons>
	</view>
</template>

<script>
	import workflowMixins from '@/common/workflow_sys.js'
	import moment from 'moment';
	import {
		forEach,
		indexOf
	} from 'lodash'
	export default {
		mixins: [workflowMixins],
		async onLoad({
			type
		}) {
			uni.setNavigationBarTitle({
				title: this.$t("错误考勤")
			})
			await this.init(type)
		},
		onUnload() {},
		methods: {
			hasBtns() {
				return this.editMode || this.wfIsCancel || this.wfIsRead || this.wf_audit
			},
			// 页面初始化
			async init(type) {
				this.LOADING(this.$t('加载数据中…'))
				const {
					title,
					moduleId,
					keyValue
				} = this.GET_PARAM()
				this.SET_TITLE(title)
				this.moduleId = moduleId
				this.mode = type
				this.keyValue = keyValue
				this.editMode = ['create', 'edit', 'wf_create', 'wf_draft', 'wf_again'].includes(this
					.mode) // 是否是编辑状态 , 'wf_audit'
				// 流程初始化
				await this.wf_init()
				if (this.mode == "wf_lookmy") {
					this.needWorkflowInfo = true;
				}
				// 赋值
				if (['edit'].includes(this.mode)) {
					if (keyValue) { // 加载数据
						this.isUpdate = true
						this.formData = await this.loadFormData(keyValue)
						if (!this.formData) {
							this.NAV_BACK()
						}
					} else {
						this.TOAST(this.$t('缺失主键值！'))
						this.NAV_BACK()
					}
				}
				this.ready = true
				if (this.wfFormData) {
					// 表示有流程数据传递进来
					this.$nextTick(() => {
						this.isUpdate = true
						this.$refs.form.setForm(this.wfFormData, true)
					})
				}
				this.loginUser = this.GET_GLOBAL('loginUser');
				this.emp_no = this.loginUser.f_DDOpenId;
			},
			async handleReady() {
				this.HIDE_LOADING();
			},

			async loadFormData(keyValue) {
				const _formData = await this.HTTP_GET({
					url: `/hrattF004/hrattF004/${keyValue}`,
					errorTips: this.$t('获取数据失败')
				})
				if (!_formData) {
					return null
				}
				let formData = _formData

				formData.attendanceErrorDtlList.sort((a, b) => {
					return a.id - b.id
				})
				// for (let i = 0; i < formData.attendanceErrorDtlList.length; i++) {
				// 	if (formData.attendanceErrorDtlList[i].use_flag == "修正") {
				// 		let item = formData.attendanceErrorDtlList[i]
				// 		let reason4Approve = ""

				// 		if (item.from_error_flag1) {
				// 			reason4Approve += this.$t("入厂") + "1：" + item.from_time1 + "；"
				// 		}
				// 		if (item.to_error_flag1) {
				// 			reason4Approve += this.$t("出厂") + "1：" + item.to_time1 + "；"
				// 		}
				// 		if (item.from_error_flag2) {
				// 			reason4Approve += this.$t("入厂") + "2：" + item.from_time2 + "；"
				// 		}
				// 		if (item.to_error_flag2) {
				// 			reason4Approve += this.$t("出厂") + "2：" + item.to_time2 + "；"
				// 		}
				// 		if (item.from_error_flag3) {
				// 			reason4Approve += this.$t("入厂") + "3：" + item.from_time3 + "；"
				// 		}
				// 		if (item.to_error_flag3) {
				// 			reason4Approve += this.$t("出厂") + "3：" + item.to_time3 + "；"
				// 		}

				// 		item["reason4Approve"] = reason4Approve
				// 		attendanceErrorDtlList.push(item)
				// 	}
				// }
				// formData.attendanceErrorDtlList = attendanceErrorDtlList
				return formData
			},
			async getForm() {
				const _postData = await this.$refs.form.getFormValue();
				const postData = this.GET_FORMDATA(_postData, 'AttendancEerrorHdr')
				return postData
			},
			async saveForm() {
				if (this.isSubmit) {
					this.TOAST(this.$t('提交中…'))
				} else {
					this.isSubmit = true;
					this.LOADING(this.$t('正在提交…'))

					if (await this.formValidate()) {
						const res = await this.handleSave({
							keyValue: this.keyValue,
							isEdit: this.isUpdate
						})
						this.HIDE_LOADING()
						if (res) {
							console.log(610, res);
							this.EMIT(`custom-list-change-${this.moduleId}`)
							this.NAV_BACK()
							this.TOAST(`保存成功`, 'success')
						}
					} else {
						this.HIDE_LOADING()
					}
					this.isSubmit = false;
				}
			},
			async handleSave({
				keyValue,
				isEdit /*,code,node*/
			}) {
				//isEdit 是否更新数据, keyValue 流程中相当于流程processId,code 表示流程中的操作码,node 流程节点
				const postData = await this.getForm()
				let res
				if (this.isUpdate) {
					keyValue = this.keyValue;
					if (!keyValue) {
						keyValue = postData.rid;
					}
					res = await this.HTTP_PUT({
						url: `/hrattF004/hrattF004/${keyValue}`,
						data: postData,
						errorTips: this.$t('表单提交保存失败')
					})
				} else {
					// 作为流程表单的时候用来关联流程字段
					postData.rid = keyValue
					this.keyValue = keyValue;
					res = await this.HTTP_POST({
						url: `/hrattF004/hrattF004`,
						data: postData,
						errorTips: this.$t('表单提交保存失败')
					})
				}
				if (res) {
					this.isUpdate = true
				}
				return res
			},
			async handleDelete() {
				if (!(await this.CONFIRM(this.$t('删除项目'), this.$t('确定要删除该项吗？'), true))) {
					return
				}
				this.LOADING(this.$t('提交删除中…'))
				const success = await this.HTTP_DELETE({
					url: "/hrattF004/hrattF004/" + this.keyValue,
					errorTips: this.$t('删除失败')
				})
				this.HIDE_LOADING()
				if (success) {
					this.EMIT(`custom-list-change-${this.moduleId}`)
					this.NAV_BACK()
					this.TOAST(this.$t('删除成功'), 'success')
				}
			},

			//验证
			async formValidate() {
				let verifyResult = await this.$refs.form.validate();

				const postData = await this.getForm();

				if (!(verifyResult)) {
					this.TOAST(this.$t("请输入必填项 "), 'error');
					return false;
				}

				return true;
			},

			// 流程提交
			async wf_submit() {
				if (this.isSubmit) {
					this.TOAST(this.$t('提交中…'))
				} else {
					this.isSubmit = true;
					this.LOADING('正在提交…')
					if (await this.formValidate()) {
						if (await this.handleSave({
								keyValue: this.wfProcessId,
								isEdit: this.isUpdate
							})) {
							const loginInfo = this.GET_GLOBAL('loginUser')

							let wf_title = ""

							const wfData = {
								processId: this.wfProcessId,
								schemeCode: this.wfCode,
								userId: loginInfo.f_UserId,
								title: wf_title
							}

							if (!this.wfIsDraft) {
								await this.HTTP_POST({
									url: '/workflow/process/draft',
									data: wfData
								})
								this.wfCode = ''
								wfData.schemeCode = ''
								this.wfIsDraft = true
							}

							let url = '/workflow/process/create';
							if (this.mode == "wf_again") {
								url = "/workflow/process/CreateAgain";
							}

							// 获取接下来节点审核人
							if (this.wfCurrentNode.isNextAuditor) {
								const nodeUserMap = await this.HTTP_GET({
									url: '/workflow/process/nextusers',
									params: {
										processId: this.wfProcessId,
										nodeId: this.wfCurrentNode.id,
									}
								})
								const nodeUsers = []
								for (let key in nodeUserMap) {
									const nodeUserItem = nodeUserMap[key]
									if (nodeUserItem.length > 1) {
										nodeUsers.push({
											name: this.wfData.find(t => t.id == key).name,
											id: key,
											options: nodeUserItem.map(t => {
												return {
													value: t.id,
													label: t.name
												}
											})
										})
									}
								}
								if (nodeUsers.length > 0 || this.wfCurrentNode.isCustmerTitle) {
									this.ONCE('learun-wfsubmit-info', data => {
										// wfData.title = data.title
										wfData.nextUsers = data.nextUsers
										wfData.title = wf_title;
										setTimeout(async () => {
											this.LOADING('正在提交…')
											const res = await this.HTTP_POST({
												url: url,
												data: wfData
											})

											this.TOAST(`流程提交成功`, 'success')
											setTimeout(async () => {
												this.EMIT(
													`learun-workflow-list-change`)
												this.NAV_BACK()
											}, 500)
										}, 10)
									})
									this.HIDE_LOADING()
									this.NAV_TO('/pages/workflow/common/learun-wfsubmit-info', {
										nodeUsers,
										isCustmerTitle: this.wfCurrentNode.isCustmerTitle
									}, true)
									this.isSubmit = false;
									return
								}
							} else if (this.wfCurrentNode.isCustmerTitle) {
								this.ONCE('learun-wfsubmit-info', data => {
									// wfData.title = data.title
									wfData.title = wf_title;
									setTimeout(async () => {
										this.LOADING('正在提交…')
										const res = await this.HTTP_POST({
											url: url,
											data: wfData
										})
										this.TOAST(`流程提交成功`, 'success')
										setTimeout(async () => {
											this.EMIT(`learun-workflow-list-change`)
											this.NAV_BACK()
										}, 500)
									}, 10)
								})
								this.HIDE_LOADING()
								this.NAV_TO('/pages/workflow/common/learun-wfsubmit-info', {
									isCustmerTitle: this.wfCurrentNode.isCustmerTitle
								}, true)
								this.isSubmit = false;
								return
							}

							const res = await this.HTTP_POST({
								url: url,
								data: wfData
							})

							this.HIDE_LOADING()
							if (res) {
								this.TOAST(`流程提交成功`, 'success')
								setTimeout(async () => {
									this.EMIT(`learun-workflow-list-change`)
									this.NAV_BACK()
								}, 500)
							}
						} else {
							this.HIDE_LOADING()
						}
					} else {
						this.HIDE_LOADING()
					}
					this.isSubmit = false;
				}
			},

			//流程处理
			async wf_action2(btn) {
				this.LOADING('表单保存…')
				let isformValidate = true;
				if (isformValidate) {
					const res = await this.HTTP_PUT({
						url: `/workflow/process/audit/${this.wfTaskId}`,
						data: {
							code: ("agree", "agree2").includes(btn) ? "agree" : "disagree",
							name: ("agree", "agree2").includes(btn) ? "同意" : "驳回",
							des: ("agree", "agree2").includes(btn) ? "Approve" : "Reject"
						}
					})
					this.HIDE_LOADING()
				}
				this.TOAST(this.$t("操作成功！", "success"));
				setTimeout(async () => {
					this.EMIT(`learun-workflow-list-change`)
					this.NAV_BACK()
				}, 500)
			},

			async wf_draft() {
				this.LOADING('正在保存…')
				const loginInfo = this.GET_GLOBAL('loginUser')
				if (await this.handleSave({
						keyValue: this.wfProcessId,
						isEdit: this.isUpdate
					})) {
					const wfData = {
						processId: this.wfProcessId,
						schemeCode: this.wfCode,
						userId: loginInfo.f_UserId,
						title: ''
					}

					const res = await this.HTTP_POST({
						url: '/workflow/process/draft',
						data: wfData
					})
					this.wfCode = ''
					this.wfIsDraft = true
					this.HIDE_LOADING()
					if (res) {
						this.NAV_BACK()
						this.TOAST('保存成功', 'success')
					}
				} else {
					this.HIDE_LOADING()
				}
			},
		},
		data() {
			return {
				moduleId: '',
				mode: '',
				keyValue: '',
				isUpdate: false,
				editMode: false,
				ready: false,
				formData: {},
				loginUser: null,
				emp_no: "",
				isSubmit: false,
				formScheme: {
					"labelPosition": "right",
					"labelWidth": 80,
					"tabList": [{
						"components": [{
							"type": "lable",
							"label": "异常考勤",
							"contentPosition": "center",
							"fontSize": 30,
							"color": "#0090FF",
							"labelWidth": 0,
							"display": true,
							"prop": "_",
							"table": "Entity",
							"key": 0,
							"fontWeight": 600,
							"style": "font-weight:'600';fontSize:'13px';color:''#000000';textAlign:'center'"
						}, {
							"type": "guid",
							"label": "RID",
							"display": false,
							"default": "",
							"prop": "AttendancEerrorHdr_RID",
							"table": "attendanceErrorHdrEntity",
							"field": "RID"
						}, {
							"type": "encode",
							"label": "单号",
							"display": true,
							"default": "",
							"code": "FHIS Leave Note",
							"prop": "AttendancEerrorHdr_Note_No",
							"table": "attendanceErrorHdrEntity",
							"field": "Note_NO"
						}, {
							"type": "createuser",
							"label": "填单人",
							"display": true,
							"default": "",
							"prop": "AttendancEerrorHdr_Submit_UserID",
							"table": "attendanceErrorHdrEntity",
							"field": "Submit_UserID"
						}, {
							"type": "createtime",
							"label": "提交日期",
							"display": true,
							"default": "",
							"prop": "AttendancEerrorHdr_Submit_Date",
							"table": "attendanceErrorHdrEntity",
							"field": "Submit_Date"
						}, {
							"type": "datetime",
							"label": "批核时间",
							"display": true,
							"default": "",
							"dateType": "datetime",
							"format": "yyyy-MM-dd HH:mm:ss",
							"prop": "AttendancEerrorHdr_Approve_Date",
							"table": "attendanceErrorHdrEntity",
							"field": "Approve_Date",
							"placeholder": " "
						}, {
							"type": "lable",
							"label": "温馨提示：可向右滑动查看详细信息。",
							"contentPosition": "left",
							"fontSize": 12,
							"color": "#A52A2A",
							"labelWidth": 0,
							"display": true,
							"prop": "Entity_",
							"table": "Entity",
							"fontWeight": 600
						}, {
							"type": "gridtable",
							"label": "",
							"isAddBtn": false,
							"isRemoveBtn": true,
							"isShowNum": false,
							"dataSource": [],
							"children": [{
								"type": "input",
								"label": "原始/修正",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "use_flag",
								"field": "use_flag",
								"width": 53,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "工号",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "Emp_No",
								"field": "Emp_No",
								"width": 45,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "姓名",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "emp_name",
								"field": "emp_name",
								"width": 75,
								"labelAlign": "center"
							}, {
								"type": "datetime",
								"label": "日期",
								"display": true,
								"dateType": "date",
								"format": "yyyy-MM-dd",
								"readonly": false,
								"clearable": false,
								"placeholder": this.$t("选择日期"),
								"default": "",
								"prop": "Att_date",
								"field": "Att_date",
								"labelWidth": 0,
								"required": true,
								"width": 95,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "入厂1",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "from_time1",
								"field": "from_time1",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "原因",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "from_error_desc1",
								"field": "from_error_desc1",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "出厂1",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "to_time1",
								"field": "to_time1",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "原因",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "to_error_desc1",
								"field": "to_error_desc1",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "入厂2",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "from_time2",
								"field": "from_time2",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "原因",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "from_error_desc2",
								"field": "from_error_desc2",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "出厂2",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "to_time2",
								"field": "to_time2",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "原因",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "to_error_desc2",
								"field": "to_error_desc2",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "入厂3",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "from_time3",
								"field": "from_time3",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "原因",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "from_error_desc3",
								"field": "from_error_desc3",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "出厂3",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "to_time3",
								"field": "to_time3",
								"width": 100,
								"labelAlign": "center"
							}, {
								"type": "input",
								"label": "原因",
								"placeholder": this.$t("请输入"),
								"display": true,
								"default": "",
								"prop": "to_error_desc3",
								"field": "to_error_desc3",
								"width": 100,
								"labelAlign": "center"
							}],
							"classType": "1",
							"isRowFixed": false,
							"isRowMerge": false,
							"rowNum": 1,
							"prop": "attendanceErrorDtlList",
							"table": "attendanceErrorDtlList",
							"isEdit": false,
						}, ],
						"text": "主表信息"
					}]
				}
			}
		}
	}
</script>