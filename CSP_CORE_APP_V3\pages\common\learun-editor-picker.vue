<template>
	<!-- 自定义编辑器选择器组件的容器 -->
	<view class="learun-editor-picker">
		<!-- 编辑器选择器主体部分 -->
		<view class="learun-editor-picker-body">
			<!-- 工具栏，点击触发格式化操作 -->
			<view class='toolbar' @tap="format" style="height: 156px;">
				<!-- 加粗按钮，根据当前文本是否加粗添加激活样式 -->
				<view :class="formats.bold ? 'ql-active' : ''" class="iconfont icon-zitijiacu" data-name="bold">
				</view>
				<!-- 倾斜按钮，根据当前文本是否倾斜添加激活样式 -->
				<view :class="formats.italic ? 'ql-active' : ''" class="iconfont icon-zitixieti" data-name="italic">
				</view>
				<!-- 下划线按钮，根据当前文本是否有下划线添加激活样式 -->
				<view :class="formats.underline ? 'ql-active' : ''" class="iconfont icon-zitixiahuaxian"
					data-name="underline"></view>
				<!-- 删除线按钮，根据当前文本是否有删除线添加激活样式 -->
				<view :class="formats.strike ? 'ql-active' : ''" class="iconfont icon-zitishanchuxian"
					data-name="strike"></view>
				<!-- 非百度小程序环境下显示左对齐按钮 -->
				<!-- #ifndef MP-BAIDU -->
				<view :class="formats.align === 'left' ? 'ql-active' : ''" class="iconfont icon-zuoduiqi"
					data-name="align" data-value="left"></view>
				<!-- #endif -->
				<!-- 居中对齐按钮，根据当前文本对齐方式添加激活样式 -->
				<view :class="formats.align === 'center' ? 'ql-active' : ''" class="iconfont icon-juzhongduiqi"
					data-name="align" data-value="center"></view>
				<!-- 右对齐按钮，根据当前文本对齐方式添加激活样式 -->
				<view :class="formats.align === 'right' ? 'ql-active' : ''" class="iconfont icon-youduiqi"
					data-name="align" data-value="right"></view>
				<!-- 两端对齐按钮，根据当前文本对齐方式添加激活样式 -->
				<view :class="formats.align === 'justify' ? 'ql-active' : ''" class="iconfont icon-zuoyouduiqi"
					data-name="align" data-value="justify"></view>
				<!-- 非百度小程序环境下显示行高、字母间距、段前距、段后距按钮 -->
				<!-- #ifndef MP-BAIDU -->
				<view :class="formats.lineHeight ? 'ql-active' : ''" class="iconfont icon-line-height"
					data-name="lineHeight" data-value="2"></view>
				<view :class="formats.letterSpacing ? 'ql-active' : ''" class="iconfont icon-Character-Spacing"
					data-name="letterSpacing" data-value="2em"></view>
				<view :class="formats.marginTop ? 'ql-active' : ''" class="iconfont icon-722bianjiqi_duanqianju"
					data-name="marginTop" data-value="20px"></view>
				<view :class="formats.marginBottom ? 'ql-active' : ''" class="iconfont icon-723bianjiqi_duanhouju"
					data-name="marginBottom" data-value="20px"></view>
				<!-- #endif -->
				<!-- 清除格式按钮 -->
				<view class="iconfont icon-clearedformat" @tap="removeFormat"></view>
				<!-- 非百度小程序环境下显示字体、字号按钮 -->
				<!-- #ifndef MP-BAIDU -->
				<view :class="formats.fontFamily ? 'ql-active' : ''" class="iconfont icon-font" data-name="fontFamily"
					data-value="Pacifico"></view>
				<view :class="formats.fontSize === '24px' ? 'ql-active' : ''" class="iconfont icon-fontsize"
					data-name="fontSize" data-value="24px"></view>
				<!-- #endif -->
				<!-- 文本颜色按钮，根据当前文本颜色添加激活样式 -->
				<view :class="formats.color === '#0000ff' ? 'ql-active' : ''" class="iconfont icon-text_color"
					data-name="color" data-value="#0000ff"></view>
				<!-- 文本背景颜色按钮，根据当前文本背景颜色添加激活样式 -->
				<view :class="formats.backgroundColor === '#00ff00' ? 'ql-active' : ''"
					class="iconfont icon-fontbgcolor" data-name="backgroundColor" data-value="#00ff00"></view>
				<!-- 插入日期按钮 -->
				<view class="iconfont icon-date" @tap="insertDate"></view>
				<!-- 插入复选框列表按钮 -->
				<view class="iconfont icon--checklist" data-name="list" data-value="check"></view>
				<!-- 有序列表按钮，根据当前列表类型添加激活样式 -->
				<view :class="formats.list === 'ordered' ? 'ql-active' : ''" class="iconfont icon-youxupailie"
					data-name="list" data-value="ordered"></view>
				<!-- 无序列表按钮，根据当前列表类型添加激活样式 -->
				<view :class="formats.list === 'bullet' ? 'ql-active' : ''" class="iconfont icon-wuxupailie"
					data-name="list" data-value="bullet"></view>
				<!-- 撤销按钮 -->
				<view class="iconfont icon-undo" @tap="undo"></view>
				<!-- 重做按钮 -->
				<view class="iconfont icon-redo" @tap="redo"></view>
				<!-- 减少缩进按钮 -->
				<view class="iconfont icon-outdent" data-name="indent" data-value="-1"></view>
				<!-- 增加缩进按钮 -->
				<view class="iconfont icon-indent" data-name="indent" data-value="+1"></view>
				<!-- 插入分隔线按钮 -->
				<view class="iconfont icon-fengexian" @tap="insertDivider"></view>
				<!-- 插入图片按钮 -->
				<view class="iconfont icon-charutupian" @tap="insertImage"></view>
				<!-- 一级标题按钮，根据当前文本是否为一级标题添加激活样式 -->
				<view :class="formats.header === 1 ? 'ql-active' : ''" class="iconfont icon-format-header-1"
					data-name="header" :data-value="1"></view>
				<!-- 下标按钮，根据当前文本是否为下标添加激活样式 -->
				<view :class="formats.script === 'sub' ? 'ql-active' : ''" class="iconfont icon-zitixiabiao"
					data-name="script" data-value="sub"></view>
				<!-- 上标按钮，根据当前文本是否为上标添加激活样式 -->
				<view :class="formats.script === 'super' ? 'ql-active' : ''" class="iconfont icon-zitishangbiao"
					data-name="script" data-value="super"></view>
				<!-- 清空编辑器内容按钮 -->
				<view class="iconfont icon-shanchu" @tap="clear"></view>
				<!-- 从右到左文本方向按钮，根据当前文本方向添加激活样式 -->
				<view :class="formats.direction === 'rtl' ? 'ql-active' : ''" class="iconfont icon-direction-rtl"
					data-name="direction" data-value="rtl"></view>
			</view>

			<!-- 编辑器包装器 -->
			<view class="learun-editor-picker-wrapper">
				<!-- 编辑器组件，设置占位符、显示图片调整大小功能、监听状态变化和准备就绪事件 -->
				<editor id="editor" class="ql-container" :placeholder="$t('开始输入...')" show-img-resize
					@statuschange="onStatusChange" :read-only="readOnly" @ready="onEditorReady">
				</editor>
			</view>
		</view>
		<!-- 保存按钮，点击触发保存操作 -->
		<button type="primary" size="mini" class="learun-editor-picker-btn" @click="handleOk">{{$t('保存')}}</button>
	</view>
</template>

<script>
	export default {
		// 组件名称
		name: 'learun-editor-picker',
		// 组件接收的属性
		props: {
			// 是否禁用
			disabled: Boolean,
		},
		data() {
			return {
				// 编辑器是否只读
				readOnly: false,
				// 当前文本的格式信息
				formats: {},
				// 编辑器中的内容
				value: '',
				// 旧的文件 ID 列表
				oldFildIdList: [],
			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("编辑文本")
			})
			// 获取页面传递的内容
			const {
				value
			} = this.GET_PARAM()
			this.value = value
			// 获取旧的文件 ID 列表
			this.oldFildIdList = this.getFieldId(this.value)
		},
		computed: {

		},
		onBackPress() {
			// 移除编辑事件监听
			this.OFF('edit-learun-editor')
		},
		onUnload() {
			// 移除编辑事件监听
			this.OFF('edit-learun-editor')
		},
		methods: {
			// 切换编辑器只读状态
			readOnlyChange() {
				this.readOnly = !this.readOnly
			},
			// 编辑器准备就绪时的处理函数
			onEditorReady() {
				// 百度小程序环境下创建编辑器上下文
				// #ifdef MP-BAIDU
				this.editorCtx = requireDynamicLib('editorLib').createEditorContext('editor');
				// #endif

				// 非百度小程序环境下创建选择器查询，设置编辑器内容
				// #ifdef APP-PLUS || MP-WEIXIN || H5
				uni.createSelectorQuery().select('#editor').context((res) => {
					this.editorCtx = res.context
					this.editorCtx.setContents({
						html: this.value,
					})
				}).exec()
				// #endif
			},
			// 撤销操作
			undo() {
				this.editorCtx.undo()
			},
			// 重做操作
			redo() {
				this.editorCtx.redo()
			},
			// 格式化操作
			format(e) {
				let {
					name,
					value
				} = e.target.dataset
				if (!name) return
				this.editorCtx.format(name, value)
			},
			// 编辑器状态变化时的处理函数
			onStatusChange(e) {
				const formats = e.detail
				this.formats = formats
			},
			// 插入分隔线操作
			insertDivider() {
				this.editorCtx.insertDivider({
					success: function() {
						console.log('insert divider success')
					}
				})
			},
			// 清空编辑器内容操作
			clear() {
				uni.showModal({
					title: this.$t('清空编辑器'),
					content: this.$t('确定清空编辑器全部内容？'),
					success: res => {
						if (res.confirm) {
							this.editorCtx.clear({
								success: function(res) {
									console.log("clear success")
								}
							})
						}
					}
				})
			},
			// 清除格式操作
			removeFormat() {
				this.editorCtx.removeFormat()
			},
			// 插入日期操作
			insertDate() {
				const date = new Date()
				const formatDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
				this.editorCtx.insertText({
					text: formatDate
				})
			},
			// 插入图片操作
			insertImage() {
				uni.chooseImage({
					count: 1,
					success: async (res) => {
						await this.getImgSrc(res.tempFiles[0])
					}
				})
			},
			// 获取图片地址并插入到编辑器中
			async getImgSrc(tempFile) {
				const fileID = this.GUID();
				const tempSrc = await this.HTTP_UPLOAD(
					`/system/annexesfile/${fileID}`,
					tempFile.path, {
						fileName: tempFile.name,
					}
				);
				this.editorCtx.insertImage({
					// 插入图片的地址
					src: `${this.API}/system/annexesfile/${fileID}?token=${this.GET_GLOBAL("token")}`,
					alt: '图像',
					success: function() {
						console.log('insert image success')
					}
				})
			},
			// 从内容中提取文件 ID 列表
			getFieldId(value) {
				const regex = /system\/annexesfile\/([a-f0-9-]+)(?=\?token=)/g;
				let matches;
				const fildIdList = [];
				while ((matches = regex.exec(value)) !== null) {
					if (matches.index === regex.lastIndex) {
						regex.lastIndex++;
					}
					fildIdList.push(matches[1]);
				}
				return fildIdList
			},
			// 保存操作
			async handleOk() {
				let value = '';
				let data;
				this.editorCtx.getContents({
					success: (res) => {
						value = res.html
						data = res
					}
				})

				const fildIdList = this.getFieldId(value)
				for (const fileID of this.oldFildIdList) {
					if (!fildIdList.includes(fileID)) {
						// 删除图片
						await this.HTTP_DELETE({
							url: `/system/annexesfile/${fileID}`,
						});
					}
				}
				// 触发编辑事件，传递编辑器内容和数据
				this.EMIT('edit-learun-editor', {
					value: value,
					data: data
				})
				// 返回上一页
				this.NAV_BACK()
			},
		}
	}
</script>

<style lang="scss" scoped>
	// 引入编辑器图标样式
	@import "/static/editor/editor-icon.css";

	.learun-editor-picker {
		position: relative;
		height: calc(100vh - var(--window-top) - var(--status-bar-height));
		width: 100%;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	.learun-editor-picker-body {
		position: relative;
		height: 0;
		width: 100%;
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.learun-editor-picker-wrapper {
		height: 100%;
		background: #fff;
		position: relative;
		height: 0;
		width: 100%;
		flex: 1;
	}

	.iconfont {
		display: inline-block;
		padding: 8px 8px;
		width: 24px;
		height: 24px;
		cursor: pointer;
		font-size: 20px;
	}

	.toolbar {
		box-sizing: border-box;
		font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
		position: relative;
		width: 100%;
		border-bottom: 1px solid #F0F0F0;
	}

	.ql-container {
		box-sizing: border-box;
		padding: 12px 15px;
		width: 100%;
		min-height: 30vh;
		height: 100%;
		font-size: 16px;
		line-height: 1.5;
	}

	.ql-active {
		color: #06c;
	}

	.learun-editor-picker-btn {
		position: relative;
		min-height: 48px;
		width: 100%;
	}
</style>