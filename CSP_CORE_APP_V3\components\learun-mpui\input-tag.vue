<template>
    <view class="input-tag">
        <learun-auto-complete :value="inputValue" :options="myOptions" :labelKey="labelKey" :valueKey="valueKey"
            :placeholder="$t(placeholder)" :disabled="disabled" :isTag="true" @select="handleSelect">
            <template v-slot:left>
                <view style=" padding-bottom: 2px;" v-if="tags.length > 0">
                    <view v-for="(tag, index) in tags" :key="index" style="display: inline-block;margin-right: 2px;">
                        <view class="input-tag-tag" :style="myColors[index] || {}">
                            <uni-text>{{ tag }}</uni-text>
                            <uni-icons type="closeempty" :size="14" @click="handleClose(index)"
                                style="margin-left: 2px;" v-if="!disabled"/>
                        </view>
                    </view>
                </view>


            </template>
        </learun-auto-complete>
    </view>
</template>

<script>
export default {
    name: 'learun-input-tag',
    props: {
        value: [String, Number],
        placeholder: {
            type: String,
            default: '请选择'
        },
        labelKey: {
            type: String,
            default: 'label'
        },
        valueKey: {
            type: String,
            default: 'value'
        },
        options: {
            type: Array,
            default: () => []
        },
        disabled: Boolean,

        colors: {
            type: Array,
            default: () => [],
        },

        isStyleLoop: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            inputValue: ''
        }
    },
    computed: {
        myOptions() {
            const res = this.options || [];
            return res;
        },
        tags() {
            if (this.VALIDATENULL(this.value)) {
                return [];
            }
            return this.value?.split(',') || [];
        },
        myColors() {
            if (this.isStyleLoop) {
                return (
                    this.tags?.map((item, index) => {
                        let styleItem = this.colors[index % this.colors.length] || {}
                        styleItem = { ...styleItem, borderWidth: (styleItem.borderWidth || 0) + 'px' }
                        return styleItem
                    }) || []
                )
            }
            return this.colors || []
        },

    },
    methods: {
        handleClose(index) {
            this.tags.splice(index, 1)
            this.$emit('input', !this.VALIDATENULL(this.tags) ? this.tags.join(',') : '')
            this.$emit('change', { value: this.VALIDATENULL(this.tags) ? this.tags.join(',') : '' })
        },
        addTags(val) {
            if (val) {
                this.tags.push(val)
                this.inputValue = undefined
                this.$emit('input', this.tags.join(','))
                this.$emit('change', { value: this.tags.join(',') })
            } else if (this.inputValue) {
                this.tags.push(this.inputValue)
                this.inputValue = ''
                this.$emit('input', this.tags.join(','))
                this.$emit('change', { value: this.tags.join(',') })
            }
        },
        handleSelect(val) {
            this.addTags(val)
            setTimeout(() => {
                this.inputValue = ''
            }, 50)
            this.$emit('select', val)
        }
    }
}
</script>
<style lang="scss" scoped>
.input-tag {
    &-tag {
        font-size: 12px;
        font-weight: 200;
        padding: 0px 2px;
        color: #fff;
        border-radius: 3px;
        background-color: #8f939c;
        border-width: 1px;
        border-style: solid;
        border-color: #8f939c;
        cursor: pointer;
        display: flex
    }
}
</style>