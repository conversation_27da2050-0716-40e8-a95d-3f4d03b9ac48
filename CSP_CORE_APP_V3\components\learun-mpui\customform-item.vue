<template>
  <learun-layer-picker
    v-if="['buttonLayer'].includes(component.type) && config.display !== false"
    :options="getOptions(component)"
    :disabled="isNotEdit"
    :columns="config.columns"
    :onlyButton="true"
    :content="config.content"
    :type="config.type"
    :size="config.size"
    :danger="config.danger"
    :iconName="config.iconName"
    @change="handleChange"
  />
  <uni-forms-item
    v-else-if="config.display !== false"
    :label="$t(config.notLabel ? '' : config.label)"
    :name="component.id"
    :required="config.required"
    :key="component.key"
    :labelWidth="config.notLabel ? 1 : 80"
  >
    <view v-if="component.type == 'number'" class="learun-flex-box">
      <uni-number-box
        :value="value"
        @input="setValue(component.id, $event)"
        :min="this.VALIDATENULL(config.min) ? -10000000 : config.min"
        :max="this.VALIDATENULL(config.max) ? 10000000 : config.max"
        :step="config.step || 1"
        :disabled="isNotEdit"
        @change="handleChange"
      />
    </view>

    <learun-radio
      v-else-if="component.type == 'radio'"
      :value="value"
      @input="setValue(component.id, $event)"
      :options="getOptions(component)"
      :disabled="isNotEdit"
      @change="handleChange"
    />
    <learun-checkbox
      v-else-if="component.type == 'checkbox'"
      :value="value"
      @input="setValue(component.id, $event)"
      :options="getOptions(component)"
      :disabled="isNotEdit"
      @change="handleChange"
    />
    <learun-picker
      v-else-if="['select'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :options="getOptions(component)"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      @change="handleChange"
    />

    <learun-multiple-picker
      v-else-if="['selectMultiple'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :options="getOptions(component)"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      @change="handleChange"
    />

    <learun-tree-picker
      v-else-if="['treeSelect'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :isTreeData="config.dataType == 'options'"
      :options="getOptions(component)"
      :idKey="config.dataType === 'dataSource' ? config.idKey : ''"
      :pIdKey="config.dataType === 'dataSource' ? config.pidKey : ''"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      @change="handleChange"
    />
    <learun-layer-picker
      v-else-if="['inputLayer'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :options="getOptions(component)"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      :columns="config.columns"
      @change="handleChange"
    />

    <learun-time-picker
      v-else-if="['time'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :format="config.format"
      :disabled="isNotEdit"
      :clearable="config.clearable"
      :placeholder="$t(config.placeholder)"
      :selectableRange="config.selectableRange"
      @change="handleChange"
    />
    <learun-time-picker
      v-else-if="['timeRange'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :format="config.format"
      :disabled="isNotEdit"
      :clearable="config.clearable"
      :startPlaceholder="config.startPlaceholder"
      :endPlaceholder="config.endPlaceholder"
      :isRange="true"
      @change="handleChange"
    />

    <learun-datetime-picker
      v-else-if="['date', 'dateRange'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :type="`${config.picker}${component.type === 'dateRange' ? 'range' : ''}`"
      :format="config.format"
      :clearable="config.clearable"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      @change="handleChange"
    />

    <learun-area-picker
      v-else-if="['areaSelect'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :clearable="config.clearable"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      @change="handleChange"
    />

    <learun-map-picker
      v-else-if="['inputMap'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      :clearable="config.clearable"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      @change="handleChange"
    />

    <learun-upload-file
      v-else-if="['upload', 'uploadimg'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      @change="handleChange"
      :accept="config.accept"
      :limit="config.limit"
      :maxSize="config.maxSize"
      :sizeType="config.sizeType"
      :isTip="config.isTip"
      :listType="
        config.listType || component.type == 'uploadimg'
          ? 'picture-card'
          : undefined
      "
      :disabled="isNotEdit"
    />

    <learun-company-picker
      :getDataSource="getDataSource"
      v-else-if="['companySelect'].includes(component.type)"
      :value="value"
      :disabled="isNotEdit"
      @input="setValue(component.id, $event)"
      :placeholder="$t(config.placeholder)"
      @change="handleChange"
    />

    <learun-department-picker
      :getDataSource="getDataSource"
      v-else-if="['departmentSelect'].includes(component.type)"
      :value="value"
      :disabled="isNotEdit"
      :placeholder="$t(config.placeholder)"
      :companyId="limitsCompanyId"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />

    <learun-user-picker
      v-else-if="['userSelect'].includes(component.type)"
      :value="value"
      :disabled="isNotEdit"
      @input="setValue(component.id, $event)"
      :getDataSource="getDataSource"
      :placeholder="$t(config.placeholder)"
      :isMulti="config.isMultiSelect"
      :isNotSaveData="config.isNotSaveData"
      @change="handleChange"
    />
    <learun-userid-fullnames
      v-else-if="['useridFullnames'].includes(component.type)"
      :value="value"
      :disabled="true"
      :userIds="config.userIds"
    />

    <learun-code
      v-else-if="['encode'].includes(component.type)"
      :value="value"
      :code="config.code"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />

    <learun-label
      v-else-if="
        ['company', 'department', 'createUser', 'modifyUser'].includes(
          component.type
        )
      "
      :value="value"
      :type="component.type"
    />

    <learun-icon-picker
      v-else-if="['icon'].includes(component.type)"
      :value="value"
      :disabled="isNotEdit"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />

    <view
      v-else-if="['rate'].includes(component.type)"
      style="padding-top: 4px"
    >
      <uni-rate
        :value="value"
        :disabled="isNotEdit"
        @input="setValue(component.id, $event)"
        @change="handleChange($event.value)"
      />
    </view>

    <learun-qrcode
      v-else-if="['qrcode'].includes(component.type) && config.bindingType == 1"
      :value="formData[config.valueIdKey]"
      :size="config.size"
      :background="config.background"
      :color="config.color"
      :margin="config.margin"
    >
    </learun-qrcode>
    <learun-qrcode
      v-else-if="['qrcode'].includes(component.type) && config.bindingType == 2"
      :value="value"
      :size="config.size"
      :background="config.background"
      :color="config.color"
      :margin="config.margin"
    >
    </learun-qrcode>

    <!-- 签名组件 -->
    <crystal-text-editor
      v-else-if="['texteditor'].includes(component.type)"
      :value="value"
      :editorID="component.prop"
      @input="setValue(component.prop, $event)"
      @blur="handleChange($event.detail.value)"
      :disabled="isNotEdit"
    />

    <crystal-single
      v-else-if="
        ['signature'].includes(component.type) && config.bindingType == 2
      "
      :titleLabel="config.label"
      :id="component.id"
      :isvalidate="component.isvalidate"
      :value="value"
      :style="{ width: 70 }"
      :editMode="editMode"
      :required="component.required"
      @setValue="setValue(component.id, $event)"
      @change="handleChange"
      :screenFul="true"
      :showLabel="config.showSingleLabel"
      :disabled="isNotEdit"
    />

    <learun-barcode
      v-else-if="
        ['barcode'].includes(component.type) && config.bindingType == 1
      "
      :value="formData[config.valueIdKey]"
      :background="config.background"
      :color="config.color"
      :height="config.height"
      :type="config.format"
      :textAlign="config.textAlign"
      :fontOptions="config.fontOptions"
      :textPosition="config.textPosition"
      :displayValue="config.displayValue"
    >
    </learun-barcode>
    <learun-barcode
      v-else-if="
        ['barcode'].includes(component.type) && config.bindingType == 2
      "
      :value="value"
      :background="config.background"
      :color="config.color"
      :height="config.height"
      :type="config.format"
      :textAlign="config.textAlign"
      :fontOptions="config.fontOptions"
      :textPosition="config.textPosition"
      :displayValue="config.displayValue"
    >
    </learun-barcode>
    <learun-count
      v-else-if="['count'].includes(component.type)"
      :value="value"
      :decimal="config.decimal"
      :thousandSeparator="config.thousandSeparator"
      :isChinese="config.isChinese"
      :formData="formData"
      :formatJson="config.formatJson"
      :tableIndex="tableIndex"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    >
    </learun-count>

    <learun-switch
      v-else-if="['switch'].includes(component.type)"
      :value="value"
      :valueType="config.valueType"
      :activeValue="config.checkedValue"
      :inactiveValue="config.unCheckedValue"
      :disabled="isNotEdit"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />
    <learun-slider
      v-else-if="['slider'].includes(component.type)"
      :value="value"
      :min="config.min"
      :max="config.max"
      :step="config.step"
      :showTooltip="config.showTooltip"
      :disabled="isNotEdit"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />

    <learun-color-picker
      v-else-if="['inputColor'].includes(component.type)"
      :value="value"
      :disabled="isNotEdit"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />

    <uni-easyinput
      v-else-if="['createTime', 'modifyTime', 'guid'].includes(component.type)"
      :value="value"
      disabled
      :clearable="false"
    />

    <uni-easyinput
      v-else-if="component.type == 'password'"
      type="password"
      :value="value"
      @input="setValue(component.id, $event)"
      :placeholder="$t(config.placeholder)"
      :prefixIcon="config.prefixIcon"
      :suffixIcon="config.suffixIcon"
      :maxlength="config.maxlength ? config.maxlength : -1"
      :disabled="isNotEdit"
      @blur="handleChange($event.detail.value)"
    />

    <uni-easyinput
      v-else-if="['textarea'].includes(component.type)"
      type="textarea"
      :value="value"
      @input="setValue(component.id, $event)"
      @blur="handleChange($event.detail.value)"
      :placeholder="$t(config.placeholder) || ''"
      :maxlength="config.maxlength ? config.maxlength : -1"
      :disabled="isNotEdit"
      autoHeight
    />
    <learun-editor
      v-else-if="['textEditor'].includes(component.type)"
      :value="value"
      @input="setValue(component.id, $event)"
      @change="handleChange"
      :placeholder="$t(config.placeholder) || ''"
      :disabled="isNotEdit"
      autoHeight
    />
    <learun-auto-complete
      v-else-if="['autoComplete'].includes(component.type)"
      :value="value"
      :options="getOptions(component)"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      :labelKey="config.labelKey"
      :valueKey="config.valueKey"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />
    <learun-input-tag
      v-else-if="['inputTag'].includes(component.type)"
      :value="value"
      :options="getOptions(component)"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      :labelKey="config.labelKey"
      :valueKey="config.valueKey"
      :colors="config.colors"
      :isStyleLoop="config.isStyleLoop"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />
    <learun-data-picker
      v-else-if="['cascaderSelect'].includes(component.type)"
      :value="value"
      :options="getOptions(component)"
      :placeholder="$t(config.placeholder)"
      :disabled="isNotEdit"
      :labelKey="config.labelKey"
      :valueKey="config.valueKey"
      :isTreeData="config.dataType == 'options'"
      :idKey="config.dataType === 'dataSource' ? config.idKey : ''"
      :pIdKey="config.dataType === 'dataSource' ? config.pidKey : ''"
      :showFormat="config.showFormat"
      :separator="config.separator"
      :changeOnSelect="config.changeOnSelect"
      @input="setValue(component.id, $event)"
      @change="handleChange"
    />

    <uni-easyinput
      v-else-if="!['viewTable'].includes(component.type)"
      type="text"
      :value="value"
      @input="setValue(component.id, $event)"
      @blur="handleChange($event.detail.value)"
      :placeholder="$t(config.placeholder) || ''"
      :prefixIcon="config.prefixIcon || ''"
      :suffixIcon="config.suffixIcon || ''"
      :maxlength="config.maxlength ? config.maxlength : -1"
      :disabled="isNotEdit"
    />
  </uni-forms-item>
</template>

<script>
export default {
  name: "learun-customform-item",
  emits: ["change"],
  props: {
    value: {},
    component: {},
    getDataSource: Function,
    editMode: { default: true },
    tableId: String,
    tableIndex: Number,
    formId: String,
    isEdit: Boolean,
  },
  data() {
    return {};
  },
  computed: {
    config() {
      const myConfig = this.component.config;
      if (this.component.config.getUpConfig) {
        const _config = myConfig.getUpConfig({
          formData: this.formData,
          config: this.component.config,
        });
        if (_config) {
          Object.keys(_config).forEach((key) => {
            myConfig[key] = _config[key];
          });
        }
      }
      return myConfig;
      // return this.component.config;
    },
    isNotEdit() {
      if (this.isEdit) {
        return false;
      }
      return !(
        this.editMode &&
        !this.config.readonly &&
        !this.config.disabled &&
        this.config.edit != false
      );
    },
    formData() {
      return this.GET_DATA(`learun_form_data_${this.formId}`);
    },
    limitsCompanyId() {
      const loginUser = this.GET_GLOBAL("loginUser");
      if (this.config.limits == "2") {
        return loginUser.f_CompanyId;
      } else if (this.config.limits == "4") {
        return this.config.companyId;
      } else {
        return "";
      }
    },
  },
  created() {
    if (
      this.component.type == "inputLayer" ||
      this.component.type == "buttonLayer"
    ) {
      this.ON(`valueChange_${this.component.id}`, this.handleValueChange);
    }
  },
  destroyed() {
    if (
      this.component.type == "inputLayer" ||
      this.component.type == "buttonLayer"
    ) {
      this.OFF(`valueChange_${this.component.id}`, this.handleValueChange);
    }
  },
  methods: {
    handleValueChange(value) {
      if (value == undefined) {
        this.handleChange();
      } else {
        const myOptions = this.getOptions(this.component);
        const changeData = myOptions.find((t) => t.value == value);
        this.handleChange(changeData);
      }
    },
    handleChange(obj) {
      if (this.component.type != "number") {
        this.$emit("change", { data: obj, component: this.component });
      }
    },
    // 设置表单数据的方法
    setValue(path, value) {
      this.$emit("input", { path, value });
      if (this.component.type == "number") {
        this.$emit("change", { data: value, component: this.component });
      }
    },
    getOptions(component) {
      const formData = this.GET_DATA(`learun_form_data_${this.formId}`);
      let options = [];
      if (this.tableId) {
        options = this.getDataSource(
          component,
          formData[this.tableId][this.tableIndex],
          formData
        );
      } else {
        options = this.getDataSource(component, formData);
      }
      // if (component.type == 'inputLayer') {
      // 	console.log(options, component, 'inputLayer')
      // }

      return options;
    },
  },
};
</script>
