/**
 * http 和 文件 请求方法
 */
export default {
	methods: {
		// 封装的 GET 请求，集成了验证信息
		// 返回请求结果或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   params 为请求附带的提交数据
		//   errorTips 为出现错误时出现的 TOAST 文字
		async HTTP_GET({
			url,
			params,
			errorTips,
			callback
		}) {
			const [err, res] = await this.learun_requestBase(
				url,
				params,
				null,
				"GET"
			);
			const result = this.learun_handleResult(err, res, errorTips);
			callback && callback(result);
			return result;
		},

		// 封装的 POST 请求，集成了验证信息
		// 返回请求结果或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   data 为请求附带的提交数据
		//   errorTips 为出现错误时出现的 TOAST 文字
		async HTTP_POST({
			url,
			data,
			params,
			errorTips,
			callback
		}) {
			url = this.learun_params_url(url, params);
			const [err, res] = await this.learun_requestBase(url, data, null, "POST");
			const result = this.learun_handleResult(err, res, errorTips);
			callback && callback(result);
			return result;
		},

		// 封装的 POST 请求，集成了验证信息 --> 全部输出返回体
		// 返回请求结果或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   data 为请求附带的提交数据
		//   errorTips 为出现错误时出现的 TOAST 文字
		async HTTP_POST_ALL({
			url,
			data,
			params,
			errorTips,
			callback
		}) {
			url = this.learun_params_url(url, params);
			const [err, res] = await this.learun_requestBase(url, data, null, "POST");
			// const result = this.learun_handleResult(err, res, errorTips)
			// callback && callback(result)
			return res;
		},

		// 封装的 PUT 请求，集成了验证信息
		// 返回请求结果或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   data 为请求附带的提交数据
		//   errorTips 为出现错误时出现的 TOAST 文字
		async HTTP_PUT({
			url,
			data,
			params,
			errorTips,
			callback
		}) {
			url = this.learun_params_url(url, params);
			const [err, res] = await this.learun_requestBase(url, data, null, "PUT");
			const result = this.learun_handleResult(err, res, errorTips);
			callback && callback(result);
			return result;
		},

		// 封装的 PUT 请求，集成了验证信息
		// 返回请求结果或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   data 为请求附带的提交数据
		//   errorTips 为出现错误时出现的 TOAST 文字
		async HTTP_PUT_ALL({
			url,
			data,
			params,
			errorTips,
			callback
		}) {
			url = this.learun_params_url(url, params);
			const [err, res] = await this.learun_requestBase(url, data, null, "PUT");
			// const result = this.learun_handleResult(err, res, errorTips)
			// callback && callback(result)
			return res;
		},

		// 封装的 DELETE 请求，集成了验证信息
		// 返回请求结果或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   data 为请求附带的提交数据
		//   errorTips 为出现错误时出现的 TOAST 文字
		async HTTP_DELETE({
			url,
			data,
			params,
			errorTips,
			callback
		}) {
			url = this.learun_params_url(url, params);
			const [err, res] = await this.learun_requestBase(
				url,
				data,
				null,
				"DELETE"
			);

			const result = this.learun_handleResult(err, res, errorTips);
			callback && callback(result);
			return result;
		},

		// 封装的文件上传，集成了验证信息
		// 返回接口返回值或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   filePath 为临时文件的路径
		//   formData 为请求附带的提交数据
		async HTTP_UPLOAD(url, filePath, formData) {
			const [err, res] = await this.UPLOAD(url, filePath, formData);
			return this.learun_handleResult(err, res);
		},

		// 封装的文件下载，集成了验证信息
		// 返回临时文件路径或 null
		// 对网络错误、返回错误码、登录状态失效等情况做了相应处理
		//   url 为请求地址
		//   formData 为请求附带的提交数据
		async HTTP_DOWNLOAD(id, formData) {
			const [err, res] = await this.DOWNLOAD(
				`/annexes/down?fileId=${id}`,
				formData
			);

			return this.learun_handleResult(err, res);
		},

		// 发起一个 GET 请求，封装了身份验证
		//   url 为请求地址
		//   data 为请求附带的提交数据
		// 返回结果是一个数组： [error, result]
		//   error 表示错误，一般是网络错误，请求很可能根本没有发出
		//   result 包含 { statusCode, headers, data } 分别表示状态码、响应头、数据
		async GET(url, data, header) {
			return await this.learun_requestBase(url, data, header, "GET");
		},

		// 发起一个 POST 请求，封装了身份验证
		//   url 为请求地址
		//   data 为请求附带的提交数据
		// 返回结果是一个数组： [error, result]
		//   error 表示错误，一般是网络错误，请求很可能根本没有发出
		//   result 包含 { statusCode, headers, data } 分别表示状态码、响应头、数据
		async POST(url, data, header) {
			return await this.learun_requestBase(url, data, header, "POST");
		},

		// 上传一个文件 （本地临时文件），封装了身份验证
		//   url 为提交地址
		//   filePath 为本地临时文件路径
		//   formData 为上传时附带的参数
		// 返回结果是一个数组： [error, result]
		//   error 表示错误，一般是网络错误，请求很可能根本没有发出
		//   result 包含 { statusCode, data } 分别表示状态码、接口返回的数据
		async UPLOAD(url, filePath, formData) {
			const uploadUrl = this.learun_handleUrl(url);
			const header = {
				token: this.GET_GLOBAL("token"),
			};

			// #ifdef MP-DINGTALK
			return new Promise((res, rej) => {
				dd.uploadFile({
					url: uploadUrl,
					filePath,
					header,
					formData,
					fileName: "file",
					fileType: "image",
					success: (dt) => {
						dt.data = JSON.parse(dt.data);
						res([null, dt]);
					},
					fail: (rs) => {
						rej([rs, null]);
					},
				});
			});
			// #endif

			// #ifndef MP-DINGTALK
			return uni
				.uploadFile({
					url: uploadUrl,
					header,
					filePath,
					formData,
					name: "file",
					fileType: "image",
				})
				.then(([err, result]) => {
					if (!err) {
						result.data = JSON.parse(result.data);
						return [null, result];
					} else {
						return [err, null];
					}
				});
			// #endif
		},

		// 下载一个文件（下载后为临时文件），封装了身份验证
		//   url 为请求的地址
		//   formData 为请求时附带的参数
		// 返回结果是一个数组： [error, result]
		//   error 表示错误，一般是网络错误，请求很可能根本没有发出
		//   result 包含 { statusCode, tempFilePath } 分别表示状态码、下载后的临时文件路径
		async DOWNLOAD(url, formData) {
			let downloadUrl = this.learun_handleUrl(url);
			const header = {
				token: this.GET_GLOBAL("token"),
			};
			downloadUrl = downloadUrl + "?" + this.URL_QUERY(formData);

			return uni
				.downloadFile({
					url: downloadUrl,
					header,
				})
				.then(([err, result]) => {
					if (!err) {
						result.data = {
							data: result.tempFilePath,
						};
						result.statusCode = 200;
					}

					return [err, result];
				});
		},

		// 【内部方法】处理请求 url，判断是否需要添加后台地址前缀
		learun_handleUrl(url) {
			let result = url;
			if (result.startsWith("http://") || result.startsWith("https://")) {
				return result;
			}
			if (!result.startsWith("/")) {
				result = "/" + result;
			}
			if (!result.startsWith(this.API)) {
				result = this.API + result;
			}
			return result;
		},

		// 【内部方法】处理请求params参数
		learun_params_url(url, params) {
			if (params) {
				if (url.indexOf("?") == -1) {
					url += "?";
				} else {
					url += "&";
				}
				for (const key in params) {
					url += `${key}=${params[key]}&`;
				}
				url = url.substring(0, url.length - 1);
			}

			return url;
		},

		// 【内部方法】HTTP 请求基础方法
		async learun_requestBase(url, data, header, method = "GET") {
			const requestUrl = this.learun_handleUrl(url);
			const token = this.GET_GLOBAL("token");
			const requestHeader = header || {};

			if (token && token !== "undefined" && token !== "null") {
				requestHeader.token = token;
			}

			// 多语言
			const mainLanguage = this.GET_LANG_MAIN_TYPE();
			const nowLanguage = this.GET_LANG_TYPE();
			if (mainLanguage != nowLanguage) {
				requestHeader.langType = nowLanguage;
				requestHeader.langMType = mainLanguage;
			}

			return uni.request({
				url: requestUrl,
				method,
				header: requestHeader,
				data: data,
			});
		},

		// 【内部方法】处理网络请求方法的返回结果
		learun_handleResult(err, result, tips) {
			// 出现错误，一般是网络连接错误
			if (err || !result) {
				this.HIDE_LOADING();
				this.TOAST("网络请求失败，请检查您的网络连接");
				return null;
			}

			// 状态码为 410，登录状态失效
			if (
				result.statusCode === 410 ||
				(result.data && result.data.code === 401)
			) {
				if (
					this.PATH() != "/pages/login" &&
					this.PATH() != "/pages/wxlogin" &&
					this.PATH() != "/pages/workflow/releasetask/list"
				) {
					this.HIDE_LOADING();
					this.TOAST("登录状态无效，正在跳转到登录页…");
					this.CLEAR_GLOBAL();
					this.RELAUNCH_TO("/pages/login");
				}
				return null;
			} else if (
				result.statusCode !== 200 ||
				(result.data && result.data.code !== 200)
			) {
				this.HIDE_LOADING();
				if (tips) {
					const errInfo = (result.data && result.data.info) || "(未知原因)";
					const errTips =
						typeof tips === "string" ? tips : "请求数据时发生错误";
					this.TOAST(`${errTips}： ${errInfo}`);
				}
				return null;
			}

			return result.data.data;
		},
	},
};