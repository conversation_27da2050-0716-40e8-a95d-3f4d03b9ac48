<template>
	<!-- 页面容器，设置最小高度为屏幕高度，顶部内边距为 86px -->
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-top':'96px'}">
		<!-- 固定在顶部的白色背景区域，包含搜索框、图标和分段控制器 -->
		<view class="bg-white fixed shadow-md w-full">
			<!-- 包含搜索框和搜索图标按钮的横向布局 -->
			<view class="learun-flex px-4 py-3 items-center">
				<!-- 搜索框组件，绑定搜索文本，监听确认事件 -->
				<uni-search-bar :placeholder="$t('搜索任务名/关键字')" cancelButton="none" @confirm="searchChange"
					v-model="searchText"></uni-search-bar>
				<!-- 搜索图标按钮，点击打开侧边栏 -->
				<learun-icon class="ml-2" type="learun-icon-document-search" color="#2979ff"
					@click="sideOpen = true"></learun-icon>
			</view>

			<!-- 优化后的分段控制器 -->
			<view class="good-hot">
				<scroll-view scroll-x enable-flex class="scroll-view_H" type="list">
					<view v-for="(item, index) in tabList" :key="index" class="task-tab-item">
						<view class="goods-item" :class="{'active': tab == index}" @tap="changeTab(index)">
							<text class="task-tab-text">{{item}}</text>
							<view class="task-tab-indicator"
								:style="{transform: tab === index ? 'scaleX(1)' : 'scaleX(0)'}"></view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 待办任务列表 -->
		<view v-show="tab == 0">
			<!-- 列表组件 -->
			<uni-list>
				<!-- 遍历待办任务列表，生成可点击的列表项 -->
				<uni-list-item clickable @click="taskClick(item,'audit')" :key="item.f_Id"
					v-for="item in pageInfo[0].list" direction="column">
					<!-- 列表项头部插槽 -->
					<template v-slot:header>
						<view class="learun-flex justify-between">
							<!-- 任务标题 -->
							<text class="learun-list-item__title font-medium">
								<text>{{item.f_ProcessTitle}}</text>
								<!-- 任务进度 -->
								<text v-if="item.f_Step" class="text-green-500 text-xs">【{{item.f_Step}}%】</text>
							</text>
							<!-- 沟通中标签 -->
							<uni-tag v-if="item.f_State == 9" inverted size="small" :circle="true" type="success"
								style="width: 42px" :text="$t('沟通中')"></uni-tag>
							<!-- 未读标签 -->
							<uni-tag v-else-if="item.f_ReadStatus !== 1" inverted size="small" :circle="true"
								type="warning" style="width: 28px" :text="$t('未读')"></uni-tag>
						</view>
					</template>
					<!-- 列表项主体插槽 -->
					<template v-slot:body>
						<view class="learun-list-item__content learun-flex text-gray-500 text-sm">
							<!-- 任务发起人 -->
							<text>{{item.f_ProcessUserName}}</text>
							<!-- 任务创建时间 -->
							<text class="ml-auto">{{TABLEITEM_DATEFORMAT(item.f_CreateDate).toString()}}</text>
						</view>
					</template>
					<!-- 列表项底部插槽 -->
					<template v-slot:footer>
						<view class="flex flex-wrap mt-2">
							<!-- 置顶标签 -->
							<uni-tag v-if="item.f_IsUp == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('顶')"></uni-tag>
							<!-- 催办加急标签 -->
							<uni-tag v-if="item.f_IsUrge == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="warning"
								:text="$t('催办加急')"></uni-tag>
							<!-- 超时标签 -->
							<uni-tag v-if="getIsOverTime(item)" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('超时')"></uni-tag>
							<!-- 重要级别标签 -->
							<uni-tag v-if="item.level !== null" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;"
								:type="['default', 'warning', 'error'][item.level || 0]"
								:text="$t(['普通', '重要', '紧急'][item.level || 0])"></uni-tag>
							<!-- 待审批标签 -->
							<uni-tag size="small" customStyle="margin-right:4px;margin-bottom:4px;" type="success"
								inverted :text="$t('待审批')"></uni-tag>
							<!-- 单位名称标签 -->
							<uni-tag size="small" type="primary" inverted :text="$t(item.f_UnitName)"></uni-tag>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<!-- 加载更多组件，根据加载状态显示不同内容 -->
			<uni-load-more v-if="pageInfo[0].loading || pageInfo[0].status === 'noMore' "
				:status="pageInfo[0].status" />
		</view>

		<!-- 委托任务列表 -->
		<view v-show="tab == 1">
			<uni-list>
				<uni-list-item clickable @click="taskClick(item,'audit')" :key="item.f_Id"
					v-for="item in pageInfo[1].list" direction="column">
					<template v-slot:header>
						<view class="learun-flex justify-between">
							<text class="learun-list-item__title font-medium">
								<text>{{item.f_ProcessTitle}}</text>
								<text v-if="item.f_Step" class="text-green-500 text-xs">【{{item.f_Step}}%】</text>
							</text>
							<uni-tag v-if="item.f_ReadStatus !== 1" inverted size="small" :circle="true" type="warning"
								style="width: 28px" :text="$t('未读')"></uni-tag>
						</view>
					</template>
					<template v-slot:body>
						<view class="learun-list-item__content learun-flex text-gray-500 text-sm">
							<text>{{item.f_ProcessUserName}}</text>
							<text class="ml-auto">{{TABLEITEM_DATEFORMAT(item.f_CreateDate).toString()}}</text>
						</view>
					</template>
					<template v-slot:footer>
						<view class="flex flex-wrap mt-2">
							<uni-tag v-if="item.f_IsUp == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('顶')"></uni-tag>
							<uni-tag v-if="getIsOverTime(item)" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('超时')"></uni-tag>
							<uni-tag v-if="item.level !== null" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;"
								:type="['default', 'warning', 'error'][item.level || 0]"
								:text="$t(['普通', '重要', '紧急'][item.level || 0])"></uni-tag>
							<uni-tag size="small" customStyle="margin-right:4px;margin-bottom:4px;" type="warning"
								inverted :text="$t('待审批')"></uni-tag>
							<uni-tag size="small" type="primary" inverted :text="$t(item.f_UnitName)"></uni-tag>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<uni-load-more v-if="pageInfo[1].loading || pageInfo[1].status === 'noMore' "
				:status="pageInfo[1].status" />
		</view>

		<!-- 已办任务列表 -->
		<view v-show="tab == 2">
			<uni-list>
				<uni-list-item clickable @click="taskClick(item,'look')" :key="item.f_Id"
					v-for="item in pageInfo[2].list" direction="column">
					<template v-slot:header>
						<view class="learun-flex justify-between">
							<!-- 任务标题 -->
							<text class="learun-list-item__title font-medium">
								{{item.f_ProcessTitle}}
								<!-- 作废状态 -->
								<text v-if="item.isDelete == 3" class="text-red-500 text-xs">【{{$t('作废')}}】</text>
								<!-- 结束状态 -->
								<text v-else-if="item.f_IsFinished == 1"
									class="text-orange-500 text-xs">【{{$t('结束')}}】</text>
								<!-- 审批中状态 -->
								<text v-else-if="item.f_Step == 100"
									class="text-green-500 text-xs">【{{$t('审批中')}}】</text>
								<!-- 任务进度 -->
								<text v-else class="text-green-500 text-xs">{{`【${item.f_Step}%】`}}</text>
							</text>
							<!-- 撤销审核按钮 -->
							<button @click.stop="handleRevokeAudit(item)"
								v-if="item.f_IsCancel == 1 && item.f_IsFinished != 1 && item.isDelete != 3" type="warn"
								class="learun-btn text-xs px-2 py-1">{{$t('撤销')}}</button>
						</view>
					</template>
					<template v-slot:body>
						<view class="learun-list-item__content learun-flex text-gray-500 text-sm">
							<text>{{item.f_ProcessUserName}}</text>
							<text class="ml-auto">{{TABLEITEM_DATEFORMAT(item.f_MakeTime).toString()}}</text>
						</view>
					</template>
					<template v-slot:footer>
						<view class="flex flex-wrap mt-2">
							<!-- 置顶标签 -->
							<uni-tag v-if="item.f_IsUp2 == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('顶')"></uni-tag>
							<!-- 超时标签 -->
							<uni-tag v-if="getIsOverTime(item)" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('超时')"></uni-tag>
							<!-- 重要级别标签 -->
							<uni-tag v-if="item.level !== null" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;"
								:type="['default', 'warning', 'error'][item.level || 0]"
								:text="$t(['普通', '重要', '紧急'][item.level || 0])"></uni-tag>
							<!-- 作废标签 -->
							<uni-tag v-if="item.isDelete == 3" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('作废')"></uni-tag>
							<!-- 同意操作标签 -->
							<uni-tag v-else-if="item.f_IsAgree == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="success" inverted
								:text="$t(item.f_OperationName)"></uni-tag>
							<!-- 其他操作标签 -->
							<uni-tag v-else size="small" customStyle="margin-right:4px;margin-bottom:4px;"
								type="warning" inverted :text="$t(item.f_OperationName)"></uni-tag>
							<!-- 完成单位标签 -->
							<uni-tag v-if="item.f_UnitName" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="primary" inverted
								:text="`${$t('完成:')}${$t(item.f_UnitName)}`"></uni-tag>
							<!-- 当前单位标签 -->
							<uni-tag v-if="item.unitNames" size="small" type="primary" inverted
								:text="`${$t('当前:')}${$t(item.unitNames)}`"></uni-tag>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<uni-load-more v-if="pageInfo[2].loading || pageInfo[2].status === 'noMore' "
				:status="pageInfo[2].status" />
		</view>

		<!-- 传阅任务列表 -->
		<view v-show="tab == 3">
			<uni-list>
				<uni-list-item clickable @click="taskClick(item,'read')" :key="item.f_Id"
					v-for="item in pageInfo[3].list" direction="column">
					<template v-slot:header>
						<text class="learun-list-item__title font-medium">
							{{item.f_ProcessTitle}}
						</text>
					</template>
					<template v-slot:body>
						<view class="learun-list-item__content learun-flex text-gray-500 text-sm">
							<text>{{item.f_ProcessUserName}}</text>
							<text class="ml-auto">{{TABLEITEM_DATEFORMAT(item.f_CreateDate).toString()}}</text>
						</view>
					</template>
					<template v-slot:footer>
						<view class="flex flex-wrap mt-2">
							<!-- 置顶标签 -->
							<uni-tag v-if="item.f_IsUp == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="error" inverted
								:text="$t('顶')"></uni-tag>
							<!-- 重要级别标签 -->
							<uni-tag v-if="item.level !== null" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;"
								:type="['default', 'warning', 'error'][item.level || 0]"
								:text="$t(['普通', '重要', '紧急'][item.level || 0])"></uni-tag>
							<!-- 未阅标签 -->
							<uni-tag v-if="item.f_State == 1" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;" type="warning" inverted
								:text="$t('未阅')"></uni-tag>
							<!-- 已阅标签 -->
							<uni-tag v-else size="small" customStyle="margin-right:4px;margin-bottom:4px;"
								type="success" inverted :text="$t('已阅')"></uni-tag>
							<!-- 单位名称标签 -->
							<uni-tag size="small" type="primary" inverted :text="$t(item.f_UnitName)"></uni-tag>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<uni-load-more v-if="pageInfo[3].loading || pageInfo[3].status === 'noMore' "
				:status="pageInfo[3].status" />
		</view>

		<!-- 我的流程任务列表 -->
		<view v-show="tab == 4">
			<uni-list>
				<uni-list-item clickable @click="taskClick(item,'lookmy')" :key="item.f_Id"
					v-for="item in pageInfo[4].list" direction="column">
					<template v-slot:header>
						<view class="learun-flex justify-between">
							<!-- 任务标题 -->
							<text class="learun-list-item__title font-medium">
								{{item.f_Title}}
								<!-- 作废状态 -->
								<text v-if="item.f_EnabledMark == 3" class="text-red-500 text-xs">【{{$t('作废')}}】</text>
								<!-- 结束状态 -->
								<text v-else-if="item.f_IsFinished == 1"
									class="text-orange-500 text-xs">【{{$t('结束')}}】</text>
								<!-- 重新提交状态 -->
								<text v-else-if="item.f_IsAgain == 1"
									class="text-orange-500 text-xs">【{{$t('重新提交')}}】</text>
								<!-- 审批中状态 -->
								<text v-else-if="item.step == 100" class="text-green-500 text-xs">【{{$t('审批中')}}】</text>
								<!-- 任务进度 -->
								<text v-else class="text-green-500 text-xs">{{`【${item.step}%】`}}</text>
							</text>
							<!-- 催办按钮 -->
							<button @click.stop="handleUrge(item)" v-if="item.f_IsFinished != 1 && item.f_IsAgain != 1"
								type="primary" class="learun-btn text-xs px-2 py-1">{{$t('催办')}}</button>
							<!-- 撤销按钮 -->
							<button @click.stop="handleRevoke(item)" style="margin-left:8px;"
								v-if="item.f_IsFinished != 1  && item.f_IsAgain != 1 && (item.f_IsStart == 0 || item.f_IsCancel == 1)"
								type="warn" class="learun-btn text-xs px-2 py-1">{{$t('撤销')}}</button>
						</view>
					</template>
					<template v-slot:body>
						<view class="learun-list-item__content learun-flex text-gray-500 text-sm">
							<text>{{$t(item.f_SchemeName)}}</text>
							<text class="ml-auto">{{TABLEITEM_DATEFORMAT(item.f_CreateDate).toString()}}</text>
						</view>
					</template>
					<template v-slot:footer>
						<view class="flex flex-wrap mt-2">
							<!-- 重要级别标签 -->
							<uni-tag v-if="item.f_Level !== null" size="small"
								customStyle="margin-right:4px;margin-bottom:4px;"
								:type="['default', 'warning', 'error'][item.f_Level || 0]"
								:text="$t(['普通', '重要', '紧急'][item.f_Level || 0])"></uni-tag>
							<!-- 单位名称标签 -->
							<uni-tag size="small" customStyle="margin-right:4px;margin-bottom:4px;" type="primary"
								inverted :text="$t(item.unitNames)"></uni-tag>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<uni-load-more v-if="pageInfo[4].loading || pageInfo[4].status === 'noMore' "
				:status="pageInfo[4].status" />
		</view>

		<!-- 草稿任务列表 -->
		<view v-show="tab == 5">
			<uni-list>
				<uni-list-item clickable @click="taskClick(item,'draft')" :key="item.f_Id"
					v-for="item in pageInfo[5].list" direction="column">
					<template v-slot:header>
						<view class="learun-flex justify-between">
							<!-- 任务标题 -->
							<text class="learun-list-item__title font-medium">
								{{item.f_Title}}
							</text>
							<!-- 删除草稿按钮 -->
							<button type="warn" class="learun-btn text-xs px-2 py-1"
								@click.stop="deleteDraft(item)">{{$t('删除')}}</button>
						</view>
					</template>
					<template v-slot:body>
						<view class="learun-list-item__content learun-flex text-gray-500 text-sm">
							<text>{{$t(item.f_SchemeName)}}</text>
							<text class="ml-auto">{{TABLEITEM_DATEFORMAT(item.f_CreateDate).toString()}}</text>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
			<uni-load-more v-if="pageInfo[5].loading || pageInfo[5].status === 'noMore' "
				:status="pageInfo[5].status" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 当前选中的选项卡索引
				tab: 0,
				// 选项卡列表，包含待办、委托、已办、传阅、我的、草稿
				tabList: [this.$t('待办'), this.$t('委托'), this.$t('已办'), this.$t('传阅'), this.$t('我的'), this.$t('草稿')],
				// 侧边栏是否打开
				sideOpen: false,
				// 每个选项卡的分页信息，包含加载状态、列表数据、页码、总页数等
				pageInfo: [{
						loading: true,
						status: 'loading',
						list: [],
						page: 1,
						total: 1
					},
					{
						loading: true,
						status: 'loading',
						list: [],
						page: 1,
						total: 1
					},
					{
						loading: true,
						status: 'loading',
						list: [],
						page: 1,
						total: 1
					},
					{
						loading: true,
						status: 'loading',
						list: [],
						page: 1,
						total: 1
					},
					{
						loading: true,
						status: 'loading',
						list: [],
						page: 1,
						total: 1
					},
					{
						loading: true,
						status: 'loading',
						list: [],
						page: 1,
						total: 1
					}
				],
				// 搜索数据，包含关键字、时间范围等
				searchData: {},
				// 日期范围筛选条件
				dateRange: null,
				// 搜索框中的文本
				searchText: '',
				// 标签栏高度
				tabBarHeight: 44
			}
		},

		// 页面加载时执行初始化操作
		async onLoad({
			type
		}) {
			uni.setNavigationBarTitle({
				title: this.$t("我的任务")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 执行初始化方法
				await this.init(type)
			}
		},
		// 页面卸载时，移除事件监听
		onUnload() {
			this.OFF(`learun-workflow-list-change`)
		},
		methods: {
			// 初始化页面
			async init(type) {
				// 监听列表更新事件
				this.ON(`learun-workflow-list-change`, this.refreshList)
				// 获取页面传递的参数
				const param = this.GET_PARAM()
				// 设置当前选中的选项卡索引
				this.tab = type || param?.type || 0
				// 拉取当前选项卡的列表数据
				await this.fetchList()
			},

			// 拉取当前正在查看的（选项卡的）列表
			async fetchList() {
				// 获取当前选中的选项卡索引
				const tab = this.tab
				// 获取当前选项卡的分页信息
				const pageInfo = this.pageInfo[tab]
				// 如果当前页码大于总页数，不再加载数据
				if (pageInfo.page > pageInfo.total) {
					return
				}
				// 设置加载状态为 true
				pageInfo.loading = true

				let result

				// 设置搜索数据的每页条数、页码和排序规则
				this.searchData.rows = 20
				this.searchData.page = pageInfo.page
				this.searchData.sidx = 'F_CreateDate DESC'

				// 根据不同的选项卡索引，调用不同的接口获取数据
				switch (tab) {
					case 0:
						result = await this.fetchUnCompletedTask(pageInfo)
						break
					case 1:
						result = await this.fetchDelegateTask(pageInfo)
						break
					case 2:
						this.searchData.sidx = 'F_IsUp2 DESC,t1.F_CreateDate DESC'
						result = await this.fetchCompletedTask(pageInfo)
						break
					case 3:
						this.searchData.sidx = 't.F_CreateDate DESC'
						result = await this.fetchReadTask(pageInfo)
						break
					case 4:
						result = await this.fetchMyTask(pageInfo)
						break
					case 5:
						result = await this.fetchDraftTask(pageInfo)
						break
				}
				// 设置加载状态为 false
				pageInfo.loading = false

				if (!result) {
					return
				}

				// 过滤出新的数据
				const newList = result.rows.filter(t => !pageInfo.list.some(t2 => t2.f_Id === t.f_Id))
				// 更新总页数
				pageInfo.total = result.total
				// 将新数据追加到列表中
				pageInfo.list = pageInfo.list.concat(newList)
				// 页码加 1
				pageInfo.page++

				// 如果当前页码大于总页数，设置状态为 noMore
				if (pageInfo.page > pageInfo.total) {
					pageInfo.status = "noMore"
				}
			},

			// 刷新列表（清空并重新拉取）
			async refreshList() {
				// 重置当前选项卡的分页信息
				this.$set(this.pageInfo, this.tab, {
					loading: true,
					status: 'loading',
					list: [],
					page: 1,
					total: 1
				})
				// 拉取当前选项卡的列表数据
				await this.fetchList()
			},

			// 加载待办任务数据
			async fetchUnCompletedTask(pageInfo) {
				// 设置排序规则
				this.searchData.sidx = 't.F_IsUp DESC,t.f_IsUrge DESC,t.F_CreateDate DESC'
				// 发送 POST 请求获取待办任务数据
				const result = await this.HTTP_POST({
					url: '/workflow/process/uncompleted/mypage',
					params: this.searchData,
					data: {
						keyWord: this.searchData.keyWord
					},
					errorTips: this.$t('加载任务时出错')
				})
				return result
			},

			// 加载委托任务数据
			async fetchDelegateTask(pageInfo) {
				// 发送 GET 请求获取委托任务数据
				const result = await this.HTTP_GET({
					url: '/workflow/process/delegate/mypage',
					params: this.searchData,
					errorTips: this.$t('加载任务时出错')
				})
				return result
			},

			// 加载已办任务数据
			async fetchCompletedTask(pageInfo) {
				// 发送 POST 请求获取已办任务数据
				const result = await this.HTTP_POST({
					url: '/workflow/process/completed/mypage',
					params: this.searchData,
					data: {
						keyWord: this.searchData.keyWord
					},
					errorTips: this.$t('加载任务时出错')
				})

				return result
			},

			// 加载传阅任务数据
			async fetchReadTask(pageInfo) {
				// 发送 GET 请求获取传阅任务数据
				const result = await this.HTTP_GET({
					url: '/workflow/process/read/mypage',
					params: this.searchData,
					errorTips: this.$t('加载任务时出错')
				})

				return result
			},

			// 加载我的流程任务数据
			async fetchMyTask(pageInfo) {
				// 发送 POST 请求获取我的流程任务数据
				const result = await this.HTTP_POST({
					url: '/workflow/process/mypage',
					params: this.searchData,
					data: {
						keyWord: this.searchData.keyWord
					},
					errorTips: this.$t('加载任务时出错')
				})
				return result
			},

			// 加载草稿任务数据
			async fetchDraftTask(pageInfo) {
				// 发送 GET 请求获取草稿任务数据
				const result = await this.HTTP_GET({
					url: '/workflow/process/mydraftpage',
					params: this.searchData,
					errorTips: this.$t('加载任务时出错')
				})
				return result
			},

			// 点击任务后跳转进入
			taskClick(item, type) {
				if (type == 'draft') {
					// 跳转到草稿编辑页面
					this.NAV_TO(`../releasetask/single?type=${type}`, item)
				} else {
					if (type === 'audit') {
						// 将任务标记为已读
						item.f_ReadStatus = 1
					}
					if (item.f_Type == 4) {
						type = 'again'
					}
					// 跳转到任务详情页面
					this.NAV_TO(`./single?type=${type}`, item)
				}
			},

			// 撤销审核
			async handleRevokeAudit(selectItem) {
				// 确认是否撤销审核
				if (!(await this.CONFIRM(this.$t('撤销审核'),
						`${this.$t('确定要撤销审核')}「${selectItem.f_ProcessTitle || this.$t('(未命名)')}」${this.$t('吗？')}`,
						true))) {
					return
				}
				// 显示加载提示
				this.LOADING(this.$t('正在撤销…'))
				// 发送 PUT 请求撤销审核
				const success = await this.HTTP_PUT({
					url: `/workflow/process/audit/revoke/${selectItem.f_ProcessId}?taskId=${selectItem.f_Id}`,
					errorTips: this.$t('撤销审核时发生错误')
				})
				// 隐藏加载提示
				this.HIDE_LOADING()
				if (success) {
					// 提示撤销成功
					this.TOAST(this.$t('撤销成功！'))
					// 从列表中移除该任务
					const list = this.pageInfo[2].list
					const index = list.findIndex(item => item.f_Id === selectItem.f_Id)
					list.splice(index, 1)
				}
			},

			// 催办流程
			async handleUrge(selectItem) {
				// 确认是否催办流程
				if (!(await this.CONFIRM(this.$t('催办流程'),
						`${this.$t('确定要催办流程')}「${selectItem.f_Title || this.$t('(未命名)')}」${this.$t('吗？')}`, true))) {
					return
				}

				// 显示加载提示
				this.LOADING(this.$t('正在催办…'))
				// 发送 PUT 请求催办流程
				this.HTTP_PUT({
					url: `/workflow/process/urge/${selectItem.f_Id}`,
					errorTips: this.$t('催办流程时发生错误')
				}).then(success => {
					// 隐藏加载提示
					this.HIDE_LOADING()
					if (success) {
						// 提示催办成功
						this.TOAST(this.$t('催办成功！'))
					}
				})
			},

			// 撤销流程
			async handleRevoke(selectItem) {
				// 确认是否撤销流程
				if (!(await this.CONFIRM(this.$t('撤销流程'),
						`${this.$t('确定要撤销流程')}「${selectItem.f_Title || this.$t('(未命名)')}」${this.$t('吗？')}`, true))) {
					return
				}

				// 显示加载提示
				this.LOADING(this.$t('正在撤销…'))
				let success
				if (selectItem.f_IsStart == 0) {
					// 发送 PUT 请求撤销流程
					success = await this.HTTP_PUT({
						url: `/workflow/process/revoke/${selectItem.f_Id}`,
						errorTips: this.$t('撤销流程时发生错误')
					})
				} else if (selectItem.f_IsCancel == 1) {
					// 发送 PUT 请求撤销审核
					success = await this.HTTP_PUT({
						url: `/workflow/process/audit/revoke/${selectItem.f_Id}`,
						errorTips: this.$t('撤销流程时发生错误')
					})
				}

				// 隐藏加载提示
				this.HIDE_LOADING()

				if (success) {
					// 提示撤销成功
					this.TOAST(this.$t('撤销成功！'))
					// 从列表中移除该任务
					const list = this.pageInfo[4].list
					const index = list.findIndex(item => item.f_Id === selectItem.f_Id)
					list.splice(index, 1)
				}
			},

			// 弹层菜单：点击编辑草稿
			editDraft() {
				// 跳转到草稿编辑页面
				this.NAV_TO('/pages/nworkflow/releasetask/single?type=draft', this.selectItem, true)
				// 取消选择
				this.cancelSelect()
			},

			// 删除草稿
			async deleteDraft(selectItem) {
				// 确认是否删除草稿
				if (!(await this.CONFIRM(this.$t('删除草稿'),
						`${this.$t('确定要删除草稿')}「${selectItem.f_Title || this.$t('(未命名)')}」${this.$t('吗？')}`, true))) {
					return
				}

				// 显示加载提示
				this.LOADING(this.$t('正在删除…'))
				// 发送 DELETE 请求删除草稿
				this.HTTP_DELETE({
					url: `/workflow/process/draft/${selectItem.f_Id}`,
					errorTips: this.$t('删除草稿时发生错误')
				}).then(success => {
					// 隐藏加载提示
					this.HIDE_LOADING()
					if (success) {
						// 提示删除成功
						this.TOAST(this.$t('删除成功！'))
						// 从列表中移除该草稿
						const list = this.pageInfo[5].list
						const index = list.findIndex(item => item.f_Id === selectItem.f_Id)
						list.splice(index, 1)
					}
				})
			},

			// 搜索/筛选内容发生改变时调用
			searchChange() {
				const result = {}
				// 有输入搜索内容则添加参数
				if (this.searchText) {
					result.keyWord = this.searchText
				}
				// 如果有日期范围选择则添加日期参数
				if (this.dateRange && this.dateRange.length === 2) {
					result.startTime = this.dateRange[0]
					result.endTime = this.dateRange[1]
				}
				// 更新搜索数据
				this.searchData = result
				// 刷新列表
				this.refreshList()
			},

			// 更改选项卡
			changeTab(index) {
				// 如果选项卡未改变，不执行任何操作
				if (this.tab === index) {
					return
				}
				// 设置新的选项卡索引
				this.tab = index
				// 获取对应选项卡的分页信息
				const pageInfo = this.pageInfo[index]
				// 如果列表为空，则加载数据
				if (pageInfo.list.length === 0) {
					this.fetchList()
				}
			},
			
			// 下拉刷新函数
			async onPullDownRefresh() {
				this.searchData = {}
				this.searchText = ''
				
				await this.refreshList()
				uni.stopPullDownRefresh()
			},
			
			// 沉底刷新函数
			onReachBottom() {
				this.fetchList()
			},

			// 判断任务是否超时
			getIsOverTime(item) {
				if (!item.f_LimitDate) {
					return false
				}
				// 获取当前时间戳
				const now = new Date().getTime()
				// 获取任务截止时间戳
				const limitDate = new Date(item.f_LimitDate).getTime()
				// 如果当前时间大于截止时间，则任务超时
				return now > limitDate
			}
		}
	}
</script>

<style lang="scss">
	.good-hot {
		padding: 0 15px;
		margin-bottom: 5px;
		margin-top: 5px;
	}

	/* 注意点，测试下来，设置水平滑动也要给一个固定高度 */
	.scroll-view_H {
		width: 100%;
		white-space: nowrap;
		height: 27px;
		flex-direction: row;
		touch-action: pan-x;
	}

	.scroll-view_H view {
		display: inline-block;
		/* width: 320rpx; */
		height: -1%;
		margin-right: 30px;
		border-radius: 10rpx;
	}

	.task-tab-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		padding: 0 15px;
		margin: 0 5px;
		cursor: pointer;
	}

	/* 去掉最后一个元素的 margin-right: 16rpx 设置为0 */
	.scroll-view_H view:last-child {
		margin-right: 0;
	}

	.scroll-view_H .goods-item {
		display: flex;
		height: 100%;
		/* width: 320rpx; */
		flex-direction: column;
	}

	.scroll-view_H .goods-item image {
		width: 100%;
		height: 380rpx;
		border-radius: 10rpx 10rpx 0 0;
	}

	.scroll-view_H .goods-item text {
		margin-top: 8rpx;
	}

	.task-tab-text {
		font-size: 15px;
		font-weight: 500;
		transition: color 0.3s;
	}

	.active .task-tab-text {
		color: #2979ff;
	}

	.task-tab-indicator {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 2px;
		background-color: #2979ff;
		transform-origin: center;
		transform: scaleX(0);
		transition: transform 0.3s ease;
	}

	.active .task-tab-indicator {
		transform: scaleX(1);
	}
	
	/* 新增样式：防止滑动冲突 */
	.scroll-view_H {
		overscroll-behavior-x: contain;
		-webkit-overflow-scrolling: touch;
	}
	
	/* 确保固定元素不影响滚动 */
	.fixed {
		z-index: 100;
	}
	
	/* 为列表内容添加适当的内边距 */
	.page {
		padding-top: 140px; /* 调整顶部内边距，避免内容被固定元素遮挡 */
	}
</style>    