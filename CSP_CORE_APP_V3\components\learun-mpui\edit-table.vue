<template>
	<view v-if="columns && columns.filter(t=>t.display !== false).length > 0" class="learun-edit-table">
		<view v-if="title" class="learun-edit-table__title">
			<text>{{$t(title)}}</text>
		</view>
		<view class="learun-edit-table__body" v-if="classType == 2">
			<uni-table border stripe :emptyText="this.$t('暂无更多数据')">
				<uni-tr>
					<uni-th v-show="isShowNum">{{$t("序号")}}</uni-th>
					<uni-th v-for="(col,index) in columns" :width="col.config.width" :align="col.config.labelAlign"
						:style="{display: col.config.display ? 'table-cell' : 'none'}">{{col.config.label}}</uni-th>
				</uni-tr>
				<uni-tr v-for="(row,index) in value">
					<uni-td v-show="isShowNum">{{index+1}}</uni-td>
					<uni-td v-for="(col,index) in columns" :width="col.config.width"
						:style="{display: col.config.display ? 'table-cell' : 'none'}">
						<template>
							<view :style="{width: col.config.width + 'px;'}">
								{{myDisplayText(getValue(row, col.id, col),col)}}
							</view>
						</template>
					</uni-td>
				</uni-tr>
			</uni-table>
		</view>
		<view class="learun-edit-table__body" v-else>
			<learun-edit-table-item v-for="(row,index) in  value" :key="index" :num="index + 1" :rowData="row"
				:columns="columns" :getDataSource="getDataSource" :formId="formId" :tableId="tableId"
				:editMode="editMode" @deleteRow="deleteRow(index,row)" @change="handleChange" ref="tableTtem" />
		</view>
		<view v-if="hasAdd" class="learun-edit-table__add" @click="handleAdd">
			<text>{{$t(addBtnText)}}</text>
		</view>
	</view>
</template>

<script>
	import get from "lodash/get"
	import moment from 'moment';
	export default {
		name: 'learun-edit-table',
		props: {
			title: String,
			editMode: {
				type: Boolean,
				default: true,
			},
			value: {
				type: Array,
				default: () => []
			},
			columns: {
				type: Array,
				default: () => []
			},
			addBtnText: {
				type: String,
				default: '新增一行' //新增一行
			},

			hasAddBtn: {
				type: Boolean,
				default: true,
			},
			hasRemoveBtn: {
				type: Boolean,
				default: true,
			},


			getDataSource: Function,
			formData: {},
			tableId: String,
			formId: String,
			required: Boolean,
			classType: String,
			isShowNum: Boolean
		},
		data() {
			return {
				myData: {},
				showLable: false,
			}
		},
		computed: {
			hasAdd() {
				return this.editMode && this.hasAddBtn
			},
			
			childTableData() {
				const res = this.value || []
				return res.filter((item) => item.learun_table_flag !== 'delete')
			}
		},

		methods: {
			handleAdd() {
				this.$emit('addRow')
			},
			deleteRow(index, row) {
				this.$emit('deleteRow', {
					index: index,
					row: row
				})
			},
			handleChange($event) {
				this.$emit('rowChange', $event)
			},
			async validate() {
				if (this.required) {
					if (this.value.length == 0) {
						this.TOAST(`${this.$t(this.title) || this.$t('表格') }${this.$t("需要添加数据！")}`)
						return false
					}
				}
				if (this.$refs.tableTtem) {
					for (let i = 0, len = this.$refs.tableTtem.length; i < len; i++) {
						const res = await this.$refs.tableTtem[i].validate()
						if (!res) {
							return false
						}
					}
				}
				return true
			},
			// 获取表单数据的方法
			getValue(data, path, scheme) {
				let result = get(data, path);
				if (["datetime", "date"].includes(scheme.dateType) && scheme.format) {
					result = moment(result).format(scheme.format.replace("yyyy", "YYYY").replace("dd", "DD"));
				}
				return result
			},
			myDisplayText(value, scheme) {
				if (this.displayText && scheme) {
					return this.displayText(value, scheme)
				}
				return value
			},
		}
	}
</script>
<style lang="scss" scoped>
	.learun-edit-table {
		&__title {
			font-size: 13px;
			color: #666666;
			display: flex;
			align-items: center;
			width: 100%;
			height: 32px;
		}


		&__add {
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 40px;
			color: $uni-primary;

			border-bottom: 1px solid $uni-border-1;
		}

		margin-bottom: 16px;
	}
</style>