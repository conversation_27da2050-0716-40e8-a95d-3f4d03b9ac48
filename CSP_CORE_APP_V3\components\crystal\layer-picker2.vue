<template>
	<view>
		<view @click.stop="handleClick" class="learun-input learun-input-border" :class="{'learun-input-placeholder':VALIDATENULL(value)}" >
			<view class="learun-input__content" ><text>{{label()}}</text></view>
			<view class="learun-input-icon-right">
				<uni-icons v-if="VALIDATENULL(value)"  type="bottom" size="14" color="#c0c4cc" ></uni-icons>
				<view  v-else-if="!disabled"  @tap.stop="handleClear" >
					<uni-icons type="clear" size="14" color="#c0c4cc" ></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>

export default {
  name: 'crystal-layer-picker2',
  props: {
    value: { default: null },
		placeholder:{
			type:String,
			default:'请选择'
		},
		disabled:{
			type:Boolean,
			default:false
		},
		clearable:{
			type:<PERSON><PERSON>an,
			default:true
		},
		columns:{
			type:Array,
			default:() => []
		},
		options:{
			type:Array,
			default:()=>[]
		},
		labelKey:{
				type:String,
				default:'label'
		},
		valueKey:{
				type:String,
				default:'value'
		},
    title:String
  },

  methods: {
		handleClick(){
			if (this.disabled) {
			  return
			}
			
			this.ONCE('learun-layer-picker', data => {
			  this.$emit('input', data[this.valueKey])
			  this.$emit('change', data)
			})
			
			// this.NAV_TO('/pages/common/learun-layer-picker', {columns:this.myColumns,options:this.options}, true)
			this.NAV_TO('/pagesHRATTF/hrattF001/layer-picker', {columns:this.myColumns,options:this.options}, true)
		},
		handleClear(){
			this.$emit('input', undefined)
			this.$emit('change', undefined)
		},
		label(){
			if(this.VALIDATENULL(this.value)){
				return this.$t(this.placeholder)
			}
			else{
				const res = this.options.find(t=>t[this.valueKey] == this.value)
				if(res){
					return res[this.labelKey]
				}
				else{
					return this.value
				}
			}
		},
  },

  computed: {
    displayPlaceholder() {
      if (this.disabled) {
        return ''
      }

      if (this.placeholder) {
        return this.placeholder
      }

      return this.title ? this.$t("请选择") + `${this.$t(this.title)}` : this.$t('请选择…')
    },
		myColumns(){
			return this.columns.filter(t=>!t.hidden)
		}
  }
}
</script>
