export default {
	data(){
		return {
			formBaseScheme: {
				"form": {
					"labelPosition": "right",
					"labelWidth": 80,
					"layerType": "modal",
					"codeAfterSetData": ""
				},
				"components": [{
					"id": "9160012361747099085223",
					"type": "label",
					"groupId": "",
					"config": {
						"content": "请假申请",
						"size": 30,
						"align": "center",
						"color": "#0090FF",
						"fontWeight": 600,
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_rid",
					"type": "guid",
					"groupId": "",
					"config": {
						"label": "RID",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "rid"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_Note_NO",
					"type": "encode",
					"groupId": "",
					"config": {
						"label": "单号",
						"display": true,
						"code": "",
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_Note_NO"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_submit_UserID",
					"type": "createUser",
					"groupId": "",
					"config": {
						"label": "填单人",
						"display": true,
						"mode": "create",
						"table": "fhisLeaveHeaderEntity",
						"field": "submit_UserID"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_Submit_Date",
					"type": "createTime",
					"groupId": "",
					"config": {
						"label": "提交日期",
						"display": true,
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_Submit_Date"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_Type",
					"type": "select",
					"groupId": "",
					"config": {
						"label": "请假类型",
						"display": true,
						"dataType": "dataSource",
						"defaultValue": "",
						"options": [],
						"assignment": [],
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_Type",
						"dataCode": "FHIS_Leave_Type",
						"labelKey": "leave_description",
						"valueKey": "leave_type",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_List_RID",
					"type": "select",
					"groupId": "",
					"config": {
						"label": "年份",
						"display": false,
						"dataType": "options",
						"defaultValue": "",
						"options": [{
								"value": "2023",
								"label": "2023"
							}, {
								"value": "2022",
								"label": "2022"
							}, {
								"value": "2021",
								"label": "2021"
							},
							],
						"assignment": [],
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_List_RID"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "6961176811747099244207",
					"type": "label",
					"groupId": "",
					"config": {
						"content": "可用0天",
						"size": 14,
						"align": "left",
						"fontStyle": "normal",
						"weight": "normal",
						"display": false
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_Way",
					"type": "radio",
					"groupId": "",
					"config": {
						"label": "请假方式",
						"display": true,
						"dataType": "dataItem",
						"defaultValue": "1",
						"options": [],
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_Way",
						"dataCode": "Leave_Way",
						"labelKey": "f_ItemName",
						"valueKey": "f_ItemValue",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_from_Date",
					"type": "date",
					"groupId": "",
					"config": {
						"label": "开始日期",
						"format": "YYYY-MM-DD",
						"display": true,
						"placeholder": "请选择",
						"picker": "date",
						"table": "fhisLeaveHeaderEntity",
						"field": "from_Date",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_from_Time",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "开始时间",
						"placeholder": "请输入时间(4位数字,如0830)",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "from_Time",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_to_Date",
					"type": "date",
					"groupId": "",
					"config": {
						"label": "结束日期",
						"format": "YYYY-MM-DD",
						"display": true,
						"placeholder": "请输入",
						"picker": "date",
						"table": "fhisLeaveHeaderEntity",
						"field": "to_Date",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_to_Time",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "结束时间",
						"placeholder": "请输入时间(4位数字,如1800)",
						"defaultValue": "",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "to_Time",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_remark",
					"type": "textarea",
					"groupId": "",
					"config": {
						"label": "请假原因",
						"placeholder": "请输入",
						"defaultValue": "",
						"display": true,
						"rows": 3,
						"table": "fhisLeaveHeaderEntity",
						"field": "remark"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_appraiser_UserID",
					"type": "inputLayer",
					"groupId": "",
					"config": {
						"label": "直属上司",
						"display": true,
						"dataType": "dataSource",
						"isPage": true,
						"isMultiSelect": false,
						"height": 504,
						"layerWidth": 960,
						"columns": [{
							"i": "fff4e801-7c59-40ac-9214-aef51b988065",
							"label": "本地名",
							"prop": "f_nickname"
						}, {
							"i": "77b560a3-cb61-423d-90fd-4d8617e738f7",
							"label": "英文名",
							"prop": "f_realname"
						}, {
							"i": "9e1b461e-a29f-4887-8b10-646e96f42f6c",
							"label": "UserID",
							"prop": "f_userid"
						}],
						"isKeyword": true,
						"table": "fhisLeaveHeaderEntity",
						"field": "appraiser_UserID",
						"layerSearchInputList": [],
						"dataCode": "C00_Active",
						"labelKey": "f_realname",
						"valueKey": "f_userid",
						"defaultValue": "",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_m_Approve_UserID",
					"type": "inputLayer",
					"groupId": "",
					"config": {
						"label": "部门经理",
						"display": true,
						"dataType": "dataSource",
						"isPage": true,
						"isMultiSelect": false,
						"height": 504,
						"layerWidth": 960,
						"columns": [{
							"i": "fff4e801-7c59-40ac-9214-aef51b988065",
							"label": "本地名",
							"prop": "f_nickname"
						}, {
							"i": "77b560a3-cb61-423d-90fd-4d8617e738f7",
							"label": "英文名",
							"prop": "f_realname"
						}, {
							"i": "9e1b461e-a29f-4887-8b10-646e96f42f6c",
							"label": "UserID",
							"prop": "f_userid"
						}],
						"isKeyword": true,
						"table": "fhisLeaveHeaderEntity",
						"field": "m_Approve_UserID",
						"layerSearchInputList": [],
						"dataCode": "O10_Active",
						"labelKey": "f_realname",
						"valueKey": "f_userid",
						"defaultValue": "",
						"required": true
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_e_Approve_UserID",
					"type": "inputLayer",
					"groupId": "",
					"config": {
						"label": "E级批核",
						"display": false,
						"dataType": "dataSource",
						"isPage": true,
						"isMultiSelect": false,
						"height": 504,
						"layerWidth": 960,
						"columns": [{
							"i": "fff4e801-7c59-40ac-9214-aef51b988065",
							"label": "本地名",
							"prop": "f_nickname"
						}, {
							"i": "77b560a3-cb61-423d-90fd-4d8617e738f7",
							"label": "英文名",
							"prop": "f_realname"
						}, {
							"i": "9e1b461e-a29f-4887-8b10-646e96f42f6c",
							"label": "UserID",
							"prop": "f_userid"
						}],
						"isKeyword": true,
						"table": "fhisLeaveHeaderEntity",
						"field": "e_Approve_UserID",
						"layerSearchInputList": [],
						"dataCode": "E00_Active",
						"labelKey": "f_realname",
						"valueKey": "f_userid",
						"defaultValue": ""
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "7131847591748922916154",
					"type": "crystalText",
					"groupId": "",
					"config": {
						"content": "",
						"display": true,
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "4758960351748922472350",
					"type": "divider",
					"groupId": "",
					"config": {
						"content": "",
						"orientation": "center"
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveDetailList",
					"type": "gridtable",
					"groupId": "",
					"config": {
						"label": "",
						"isShowNum": true,
						"isAddBtn": true,
						"isRemoveBtn": true,
						"classType": "1",
						"mergeHeader": [],
						"mergeRows": [],
						"vRepeatList": [],
						"isRowMerge": false,
						"table": "fhisLeaveDetailList",
						"display": false,
					},
					"containerId": "form",
					"level": 1,
					"children": [{
						"id": "rid",
						"name": "GUID",
						"type": "guid",
						"runtimeType": "guid",
						"groupId": "",
						"config": {
							"label": "RID",
							"display": false,
							"id": "9808257611748922548626",
							"span": 24,
							"componentType": "guid",
							"isSubTable": true,
							"field": "rid",
							"csType": "string"
						},
						"containerId": "7974840261748922539090",
						"level": 2
					}, {
						"id": "voucher_Type",
						"name": "下拉选择",
						"type": "select",
						"runtimeType": "select",
						"groupId": "",
						"config": {
							"label": "凭证类型",
							"display": true,
							"dataType": "dataItem",
							"dataCode": "Voucher_Type",
							"defaultValue": "1",
							"options": [],
							"assignment": [],
							"id": "3305837451748922588010",
							"span": 24,
							"componentType": "select",
							"isSubTable": true,
							"field": "voucher_Type",
							"csType": "string"
						},
						"containerId": "7974840261748922539090",
						"level": 2
					}, {
						"id": "file_Url",
						"name": "图片上传",
						"type": "uploadimg",
						"runtimeType": "uploadimg",
						"groupId": "",
						"config": {
							"label": "文件路径",
							"placeholder": "点击上传",
							"display": true,
							"limit": 9,
							"maxSize": 10,
							"sizeType": "MB",
							"id": "7509960011748922602756",
							"span": 24,
							"componentType": "uploadimg",
							"isSubTable": true,
							"field": "file_Url",
							"csType": "string"
						},
						"containerId": "7974840261748922539090",
						"level": 2
					}]
				}, {
					"id": "fhisLeaveHeaderEntity_signature",
					"type": "signature",
					"groupId": "",
					"config": {
						"label": "签名（请使用中文正楷）",
						"bindingType": "2",
						"valueIdKey": "",
						"width": "500",
						"height": "300",
						"modalWidth": 650,
						"modalHeight": 400,
						"modalTitle": "签名",
						"isButtonDisabled": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "signature",
						"required": true,
						"notLabel": true,
						"showSingleLabel": true,
					},
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_emp_No",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "工号",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "emp_No"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_m_Is_E",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "是否是E级",
						"placeholder": "请输入",
						"defaultValue": 0,
						"display": false,
						"patterns": [],
						"preIcon": {},
						"sufIcon": {},
						"table": "fhisLeaveHeaderEntity",
						"field": "m_Is_E"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_Day",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "请假天数",
						"defaultValue": "0",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_Day"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_not_Continu_Leave_Flag",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "是否是间断请请假",
						"defaultValue": 0,
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "not_Continu_Leave_Flag"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_approve_Status",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "状态",
						"defaultValue": "01",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "approve_Status"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_last_Update_Date",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "最后更新时间",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "last_Update_Date"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_leave_Shift_Item",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "Shift_Item",
						"defaultValue": 1,
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "leave_Shift_Item"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_appraiser_UserDate",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "直属上司批核时间",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "appraiser_UserDate"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_m_Approve_Date",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "M级批核时间",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "m_Approve_Date"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_e_Approve_Date",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "E级批核时间",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "e_Approve_Date"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_hRD_Approve_UserID",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "HRD批核人",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "hRD_Approve_UserID"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_hRD_Approve_Date",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "HRD批核时间",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "hRD_Approve_Date"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_need_HRD",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "是否需要HRD批核",
						"defaultValue": 0,
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "need_HRD"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_is_All_Voucher",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "是否已经上传完所有类型凭证",
						"defaultValue": 0,
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "is_All_Voucher"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_company_Id",
					"type": "department",
					"groupId": "",
					"config": {
						"label": "所属公司",
						"defaultValue": 0,
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "company_Id"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_synchronous_Status",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "同步状态",
						"defaultValue": 3,
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "synchronous_Status"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_synchronous_Message",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "同步失败信息",
						"defaultValue": "",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "synchronous_Message"
					}, 
					"containerId": "form",
					"level": 1
				}, {
					"id": "fhisLeaveHeaderEntity_organization_Level",
					"type": "input",
					"groupId": "",
					"config": {
						"label": "公司",
						"defaultValue": "",
						"display": false,
						"table": "fhisLeaveHeaderEntity",
						"field": "organization_Level"
					}, 
					"containerId": "form",
					"level": 1
				},]
			},
		}
	}
}