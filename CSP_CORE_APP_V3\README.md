# 力软框架移动端 · 二次开发手册
修订日期：2022年 4月 12日  
App 版本号：3.0.2  
HBuilderX 版本不低于：3.3.13.20220314

（本文档只是对初次使用框架做简单的介绍，对于框架 API、组件、平台差异等，您可查看 **/docs** 目录下的详细说明文档）
（本文档格式为 markdown，您可以使用浏览器等工具来打开，获取更佳的阅读体验）
  
# 文档目录
- **/README.md** ： 二次开发手册（本文档）；
- **/docs/api.md** ： 框架 API 文档；
- **/docs/platform.md**： 各个平台差异介绍；
- **/docs/component.md**： 移动端组件文档。
  
# 开发前的准备
力软框架移动端使用 uni-app 技术来开发。  
使用 uni-app，您只需编写一套 Vue 代码，就能编译输出成手机 App、各小程序平台、H5 网页端等多个平台的成品。  
  
力软框架移动端支持 H5、安卓 App/iOS App、微信/支付宝/钉钉小程序。  
  
需要下载安装以下工具：
- 【必装工具】：[HBuilderX](https://www.dcloud.io/hbuilderx.html) ，uni-app 的官方开发工具，**注意请下载App开发版**
- 开发微信小程序：[微信小程序开发工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html) 
- 开发支付宝/钉钉小程序：[支付宝小程序开发工具](https://opendocs.alipay.com/mini/ide/download)
- 开发 iOS /安卓 App / H5：HBuilderX 内置网页版调试工具，也可以连接手机真机调试，无需安装其他软件。  

**注意：**请使用最新版 HBuilderX。如果您已安装，可以点击菜单栏的「帮助」>「检查更新」来升级到最新版。请确保您的 HBuilderX 版本不低于本文档开头所标注的HBuilderX 开发工具版本。  
  
HBuilderX 下载后是个压缩包，将它解压到硬盘上，运行 HBuilderX.exe 即可启动；  
小程序开发工具则按照指引，依次点击下一步即可完成安装。 

**本文档只负责提示您开发过程中易错点，以及对力软框架提供的 API、工具类等给出说明，不是使用 uni-app 开发移动应用的教程。**  
学习 uni-app 移动端开发，或是在以上工具的安装、使用过程中遇到问题时，可以参考以下文档：
- [uni-app 介绍与文档](https://uniapp.dcloud.io/) 
- [微信小程序官方开发指南](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [支付宝小程序官方开发指南](https://opendocs.alipay.com/mini/developer)
- [钉钉小程序官方开发指南](https://ding-doc.dingtalk.com/doc#/dev/)
- [Vue.js 官网文档](https://cn.vuejs.org/index.html)
  
# 起步
启动 HBuilderX，点击左上角菜单：「文件」>「打开目录」，然后选择本项目的文件夹，即可打开本项目。 
  
安装后**首次**运行 HBuilderX 时，需要的操作：  
- 【必做】 点击左上角菜单>「工具」>「插件安装」，在打开的插件列表中安装「less 编译」；
- 【必做】 找到 /config.js 文件，修改公司名称、后台接口地址等相关配置；
- 【微信小程序开发】 点击左上角菜单>「工具」>「设置」>点击「运行配置」页，找到「微信开发者工具路径」，在这里请正确设置微信开发者工具的安装路径；
- 【支付宝/钉钉小程序开发】 点击左上角菜单>「工具」>「设置」>点击「运行配置」页，找到「支付宝开发者工具路径」，在这里请正确设置支付宝开发者工具的安装路径，注意支付宝小程序的路径要设置到 .exe 文件，不能直接选择目录，支付宝和钉钉共用同一个工具路径。 
  
## 小程序主体注册  
如果是开发小程序，您需要在对应的平台来申请注册自己的小程序主体账号，然后提交小程序名称、图标、类目，并申请小程序 AppID；  
后续提交审核、发布小程序，均需要在小程序公众平台上进行操作。  
  
请先申请小程序 AppID：  
[微信小程序后台](https://mp.weixin.qq.com/)  
[钉钉小程序后台](https://open-dev.dingtalk.com/)  
[支付宝小程序后台](https://openhome.alipay.com/)  

申请了小程序 AppID，打开项目根目录下的 manifest.json，请在其中「微信/支付宝小程序配置」页面填入 AppID。  
  
## 项目目录介绍
打开本项目后，可以在左侧的浏览项目目录和文件，点击文件即可进行编辑。  
本项目的目录文件介绍：
（目录）： 
- /docs： 【重要】 详细文档
- /pages： 【重要】 所有页面均放在此目录下
- /components： 使用的组件（力软自研组件、其他第三方组件等都在此）
- /static： 图片、音视频等静态资源目录
- /common： 多个页面通用的 CSS、JS 代码
- /res： 编译安卓/iOS 手机 App 用到的图标和启动封面图
- /node_modules： 使用到的 npm 库
- /unpackage (从未运行过的项目没有此文件夹)： 运行项目时会自动把编译生成的代码放在此目录下
  
（文件）：  
- App.vue： 【重要】 运行时的全局 Vue 对象，可以用来设置应用生命周期钩子、导入全局样式等
- config.js： 【重要】 多个页面的配置文件，可以控制页面显示的内容和方式
- main.js： 【重要】 项目启动时的入口 JS 文件
- pages.json： 【重要】 所有页面的路径、样式的配置、引入的组件和目录等配置
- manifest.json：【重要】 uni-app 相关的设置，在这里设置应用名称、微信 AppID 等
- package.json： 用到的 npm 库的名称和版本、uni-app 编译到钉钉小程序的编译配置等
- uni.scss： uni-app 内置的样式
- index.html： 编译到 H5 端时的页面模板
  
## uni-app 页面开发
  
uni-app 中，每一个页面都是一个 .vue 文件，它包含了模板、JS、CSS 代码；  
文件中可以引入其他 JS、CSS，引入安装好的 npm 库，也可以引入图片视频音乐等媒体。  
  
**需要注意的是，所有页面必须放置于 /pages 目录中， 且每个页面都必须在 /pages.json 中添加一条对应的记录，否则页面无法导航。**
**当您向力软框架中添加新的页面时，请及时修改 /pages.json 文件，确保所有页面都登记在该文件中。**  
例如：  
某个小程序页面，它的目录为：  
`/pages/mypage/index.vue`  
那么在 /pages.json 里，需要在 pages 列表中添加一条记录：  
`{ "path": "pages/mypage/index", "style": { "navigationBarTitleText": "我是页面的标题" } }`  
  
# 使用力软代码生成器
请先确保代码生成器的版本和 App 版本相同，不同版本可能会引发错误。  
以下是使用力软代码生成器创建页面的流程：  
- 【自动】 使用力软代码生成器发布功能时，会在 /pages 目录下生成相应的页面文件；  
- 【手动】 您需要在 /pages.json 页面配置文件中，手动把这些页面添加到 pages 字段中。  
  
例如：  
假设您使用代码生成器时，设置的输出目录名为 LR_Codedemo，功能类名为 wxtest，那么代码生成器会自动生成两个文件：
`/pages/LR_Codedemo/wxtest/list.vue` （移动页面主页面）；  
`/pages/LR_Codedemo/wxtest/single.vue` （移动表单页）。
  
然后，需要配置 /pages.json 文件：  
在 pages 数组里添加以下两条（navigationStyle 是页面的标题，可以自行更改）：  
```
{
  "pages": [
    { "path": "pages/LR_Codedemo/wxtest/list", "style": { "navigationStyle": "移动页面主页面标题" } },
    { "path": "pages/LR_Codedemo/wxtest/single", "style": { "navigationStyle": "移动表单页标题" } },
    
    // ...其他页面
  ]
}  
```
这样就完成了新自定义页面的生成。  
  
按照小程序和 uni-app 的规范， 每个页面均需要单独建立一个文件夹；但是这样将使得工程目录过于复杂，因此力软框架中，每个页面使用单个 .vue 文件，和传统 Web 开发相同，请知悉。  
也因为以上原因，新建页面时，请不要勾选「创建同名目录」这一项。  
  
-----
  
# 二次开发指南
**注意：** 小程序端不同于常规 Web 端，它没有浏览器上的 DOM 、BOM，也就是说没有 document、window 对象，因此也无法使用 jQuery 等工具；但是有同类的工具和 API 可以替代。  
**注意：** 本框架中的每个页面为单个 .vue 文件，无需按照小程序或 uni-app 的要求，为每个页面单独建一个文件夹，因为这样会导致目录结构过于复杂。   
  
每个 .vue 文件的格式如下：  
``` html
<template>
  <!-- 页面模板内容 -->
</template>

<script>
// 页面 JS 代码 ...
export default {
  // ...
}
</script>

<style lang="less" scoped>
  /* 页面样式 */
</style>
```
  
## 页面模板相关注意事项：  
- 模板遵循 Vue 模板写法，[uni-app 页面模板文档](https://uniapp.dcloud.io/component/README)；
- 网页中的 `<div>`、`<span>`、`<img>` 等标签，在 uni-app 中需要分别写成 `<view>`、`<text>`、`<image>` 等；
- 监听事件时，`@click` 需要改为 `@tap` 等对应的移动端事件 ；
- （推荐）您也可以维持 HTML 中的写法不做修改，依然写成 `<div>` 或是 `@click`，uni-app 框架会在编译时自动处理，包括 CSS 选择器名也会自动处理；
- 图片、视频、音频等媒体资源必须放置于 /static 目录下，引入示例：`src="/static/.../a.png"`；
- 为了跨端兼容性，请避免使用 .sync 绑定属性。
  
## 页面 JS 代码注意事项：  
- JS 代码遵循 Vue 开发写法，[uni-app 使用 Vue.js 时的注意事项](https://uniapp.dcloud.io/use)；
- 生命周期和常规 Vue 网页不同，uni-app 中存在「页面生命周期」、「应用生命周期」两个概念，请阅读文档以了解；
- 注意对空数据的处理，空数据在 uni-app 的不同平台中可能显示为 null 字符串或 undefined 字符串，而不是为空，这一点不同于网页版 Vue 开发，请注意处理；
- 在使用 `import` 引入其他 JS 文件时，路径中可用 `@` 来代表项目根目录，例如 `import xxx from '@/xxx/xxx.js'`，但是引入资源文件（图片、样式表）时，需要使用 `~@` 来代表根目录。
  
## 页面样式注意事项：
- uni-app 中存在一种特殊的 CSS 单位 rpx，也叫 upx（upx 已经废弃，不要使用）；具体来讲，750rpx 即等于设备屏幕宽度，例如 iPhone 6 手机屏幕宽度为 375px，因此在 iPhone 6 上的 750rpx = 375px，比例为 2:1；设备屏幕越大则 rpx 对 px 的比例越接近 1:1；
- 不同平台上 rem、em 尺寸可能表现不同（尤其是 rem），因此请慎用 rem、em 单位；
- pages.json 中无法设置页面背景色，需要使用 `page { }` 样式来设置（等同于 H5 中的 `body { }`）；pages.json 中的 `backgroundColor` 相关的指的是 iOS 系统上屏幕上下滚动露出的回弹区域背景色，并不是直接设置页面内容的背景色；
- 使用 LESS 样式时候写成 `<style lang="less" scoped>`，注意多了 `lang="less"` 的属性；样式标签的 `scoped` 属性表示这些样式仅对当前页面，绝不会影响到其他页面；使用 LESS 开发时，您需要在 HBuilder X 中安装对应的插件；（也可以使用 SASS 等，方法同）
- 复用的样式代码可以放置在 /common/css 中，引入时的路径： `~@/common/css/.../a.css` 需要写成这种形式；
- 在 /App.vue 中的样式，适用于项目全局；
- 部分页面样式（例如导航栏样式、标题样式等不属于页面内容的样式），需要在 /pages.json 中配置，参考[uni-app pages.json 配置文档](https://uniapp.dcloud.io/collocation/pages)，uni 也可能提供了 API 用来动态改变；
- 对于屏幕类型特殊的手机，如 iPhone X，由于底部两边圆角过大且底部有虚拟 Home 键，页面可能会被虚拟 Home 键挡住，使用 CSS 变量 `env(safe-area-inset-bottom)` 可以获取屏幕显示安全区距离屏幕底部的距离。
