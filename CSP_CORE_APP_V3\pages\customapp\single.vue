<template>
	<!-- 页面容器，设置最小高度为屏幕高度，背景颜色为白色，根据是否有按钮设置底部内边距 -->
	<view class="page"
		:style="{'min-height':SCREENHEIGHT()+'px','background-color':'#fff','padding-bottom':hasBtns?'40px':0}">
		<!-- 当数据准备好时，渲染自定义表单包装器组件 -->
		<learun-customform-wraper v-if="ready" :formId="formId" :editMode="editMode" :scheme="formScheme"
			:isUpdate="isUpdate" :loadParams="{key:pkey,keyValue:pkeyValue}" :moduleId="moduleId"
			:initFormValue="initFormValue" @ready="handleReady" ref="form" isChildTableVirtualDel />

		<!-- 操作区按钮，当数据准备好且有按钮显示时渲染 -->
		<view class="learun-bottom-btns" v-if="ready && hasBtns">
			<!-- 删除按钮，当不是创建模式、处于编辑模式且有删除按钮权限时显示 -->
			<button v-if="mode !== 'create' && editMode && settingBtns.findIndex(t=>t.prop == 'Delete') != -1"
				@click="handleDelete" type="warn">{{$t('删除')}}</button>
			<!-- 保存按钮，当处于编辑模式时显示 -->
			<button v-if="editMode" @click="handleSave" type="primary">{{$t('保存')}}</button>
			<!-- 编辑按钮，当没有处于编辑模式且有编辑按钮权限时显示 -->
			<button v-else-if="settingBtns.findIndex(t=>t.prop == 'Edit') != -1" @click="handleEdit"
				type="primary">{{$t('编辑')}}</button>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 是否以弹窗层形式显示
				isLayer: true,
				// 模块 ID
				moduleId: '',
				// 当前页面模式（create、edit、details 等）
				mode: '',
				// 表单 ID
				formId: '',
				// 表单方案配置
				formScheme: {},
				// 主键字段名
				pkey: '',
				// 主键值
				pkeyValue: '',
				// 是否为更新操作
				isUpdate: false,
				// 是否处于编辑模式
				editMode: false,
				// 数据是否准备好
				ready: false,
				// 按钮配置列表
				settingBtns: [],
				// 表单初始化值
				initFormValue: {}
			}
		},

		async onLoad({
			type
		}) {
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 调用初始化方法，传入页面模式
				await this.init(type)
			}
		},
		computed: {
			hasBtns() {
				// 如果没有模块 ID，不显示按钮
				if (!this.moduleId) {
					return false;
				}
				// 判断是否显示按钮的逻辑
				return (
					// 不是创建模式、处于编辑模式、有删除按钮权限且配置中有删除按钮
					(this.mode !== 'create' &&
						this.editMode &&
						this.GET_BUTTON_AUTH('Delete', this.moduleId) &&
						this.settingBtns.findIndex(t => t.prop == 'Delete') != -1)
					// 处于编辑模式
					||
					this.editMode
					// 有编辑按钮权限且配置中有编辑按钮
					||
					(this.GET_BUTTON_AUTH('Edit', this.moduleId) && this.settingBtns.findIndex(t => t.prop ==
						'Edit') != -1)
				)
			}
		},

		methods: {
			// 页面初始化方法
			async init(type) {
				// 显示加载提示
				this.LOADING(this.$t('加载数据中…'))
				// 获取页面传递的参数
				const {
					title,
					formId,
					moduleId,
					formScheme,
					key,
					keyValue,
					settingBtns,
					formData
				} = this.GET_PARAM()
				// 设置页面标题
				this.SET_TITLE(title)
				// 赋值模块 ID
				this.moduleId = moduleId
				// 赋值页面模式
				this.mode = type
				// 赋值表单 ID
				this.formId = formId
				// 赋值表单方案
				this.formScheme = formScheme
				// 赋值主键字段名
				this.pkey = key
				// 赋值主键值
				this.pkeyValue = keyValue
				// 赋值表单初始化值
				this.initFormValue = formData
				// 赋值按钮配置列表
				this.settingBtns = settingBtns || []
				// 判断是否处于编辑模式
				this.editMode = ['create', 'edit'].includes(this.mode)
				// 根据页面模式和表单类型判断是否为更新操作
				if (['edit'].includes(this.mode) || (['details'].includes(this.mode) && (this.formScheme.formType ==
						0 || this.formScheme.formType == 2))) {
					if (keyValue) {
						// 有主键值，标记为更新操作
						this.isUpdate = true
					} else {
						// 没有主键值，提示错误并返回上一页
						this.TOAST(this.$t('缺失主键值！'))
						this.NAV_BACK()
					}
				}
				if (['details'].includes(this.mode) && this.formScheme.formType == 1) {
					// 特定详情模式和表单类型，标记为更新操作
					this.isUpdate = true
				}
				// 标记数据准备好
				this.ready = true
			},
			// 表单准备好的处理方法
			handleReady() {
				// 隐藏加载提示
				this.HIDE_LOADING()
			},
			// 保存表单数据的方法
			async handleSave() {
				// 验证表单数据
				const verifyResult = await this.$refs.form.validate()
				console.log('verifyResult: ', verifyResult);
				if (verifyResult) {
					// 验证通过，显示加载提示
					this.LOADING('正在提交…')
					// 调用表单保存方法
					const res = await this.$refs.form.saveForm(this.formId, this.pkey, this.pkeyValue)
					// 隐藏加载提示
					this.HIDE_LOADING()
					if (res) {
						// 保存成功，触发列表更新事件并返回上一页
						this.EMIT(`custom-list-change-${this.formId}`)
						this.NAV_BACK()
					}
				}
			},
			// 删除表单数据的方法
			async handleDelete() {
				// 确认是否删除
				if (!(await this.CONFIRM(this.$t('删除项目'), this.$t('确定要删除该项吗？'), true))) {
					return
				}
				// 显示加载提示
				this.LOADING(this.$t('删除中…'))
				// 发送删除请求
				this.HTTP_DELETE({
					url: `/custmerform/data/${this.formId}`,
					params: {
						key: this.pkey,
						keyValue: this.pkeyValue
					},
					errorTips: this.$t('删除失败')
				}).then(success => {
					// 隐藏加载提示
					this.HIDE_LOADING()
					if (success) {
						// 删除成功，触发列表更新事件，返回上一页并提示删除成功
						this.EMIT(`custom-list-change-${this.formId}`)
						this.NAV_BACK()
						this.TOAST(this.$t('删除成功'), 'success')
					}
				})
			},
			// 进入编辑模式的方法
			handleEdit() {
				// 设置为编辑模式
				this.editMode = true
			}
		}
	}
</script>