<template>
	<view>
		<view 
			@click.stop="handleClick" 
			:class="['learun-input','learun-input-border',{'learun-input-placeholder':VALIDATENULL(value)}]"
			>
			<view class="learun-input__content" ><text>{{label}}</text></view>
			<view class="learun-input-icon-right">
				<uni-icons v-if="VALIDATENULL(value) || !clearable"  type="bottom" size="15" color="#c0c4cc" ></uni-icons>
				<view  v-else-if="!disabled && clearable"  @click.stop="handleClear" >
					<uni-icons type="clear" size="15" color="#c0c4cc" ></uni-icons>
				</view>
			</view>
		</view>
		
		<uni-popup ref="popup" type="bottom">
			<view class="learun-picker-popup" >
				<view class="learun-popup-button-close" @click.stop="close">
					<learun-icon type="learun-icon-error" :size="14" color="#737987" ></learun-icon>
				</view>
				
				<picker-view v-if="isOpen" :value="myValue" @change="bindChange" class="picker-view" >
					<picker-view-column>
						<view class="item" v-for="(item,index) in list1" :key="index">{{item.label}}</view>
					</picker-view-column>
					<picker-view-column>
						<view class="item" v-for="(item,index) in list2" :key="index">{{item.label}}</view>
					</picker-view-column>
					<picker-view-column>
						<view class="item" v-for="(item,index) in list3" :key="index">{{item.label}}</view>
					</picker-view-column>
				</picker-view>
				
				<view class="learun-popup-button-wraper" >
					<view class="learun-popup-button-ok" @click="confirm">{{$t('确认')}}</view>
				</view>
			</view>
		</uni-popup>
		
	</view>
</template>

<script>
	export default {
		name:'learun-area-picker',
		emits: ['change', 'input'],
		props:{
			value:[String],
			placeholder:{
				type:String,
				default:'请选择'
			},
			clearable:{
				type:Boolean,
				default:true
			},
			disabled:Boolean
		},
		data(){
			return {
				myValue:[],
				tmpValue:[],
				isOpen:false,
				
				list1:[],
				list2:[],
				list3:[],
				
				value1:'',
				value2:'',
				value3:''
			}
		},
		computed:{			
			label(){
				if(this.VALIDATENULL(this.value)){
					return this.$t(this.placeholder || '')
				}
				else{
					const valueList = this.value.split(',')
					const nameList = []
					const obj1 = this.GET_DATA(`learun_areas_0`).find(t=>t.value == valueList[0])
					if(obj1){
						nameList.push(obj1.label)
						const obj2 = this.GET_DATA(`learun_areas_${valueList[0]}`).find(t=>t.value == valueList[1])
						if(obj2){
							nameList.push(obj2.label)
							const obj3 = this.GET_DATA(`learun_areas_${valueList[1]}`).find(t=>t.value == valueList[2])
							if(obj3){
								nameList.push(obj3.label) 
							}
						}
					}
					return nameList.join(',') 
				}
			}
		},
		
		created(){
			this.list1 = this.GET_DATA('learun_areas_0')
		},
		
		methods:{
			handleClear(){
				if(this.disabled){
					return
				}
				this.$emit('input','')
				this.$emit('change',undefined)
			},
			handleClick(){
				if(this.disabled){
					return
				}
				if(this.VALIDATENULL(this.value)){
					this.myValue = [0,0,0]
					
					this.list2 = this.GET_DATA(`learun_areas_${this.list1[0].value}`)
					this.list3 = this.GET_DATA(`learun_areas_${this.list2[0].value}`)
					
					this.value1 = this.list1[0].value
					this.value2 = this.list2[0].value
					this.value3 = this.list3[0].value
				}
				else{
					this.myValue = []
					const valueList = this.value.split(',')
					
					
					
					this.value1 = valueList[0]
					this.value2 = valueList[1]
					this.value3 = valueList[2]

					this.list2 = this.GET_DATA(`learun_areas_${this.value1}`)
					this.list3 = this.GET_DATA(`learun_areas_${this.value2}`)
										
					this.myValue.push(this.list1.findIndex(t=>t.value == valueList[0]))
					this.myValue.push(this.list2.findIndex(t=>t.value == valueList[1]))
					this.myValue.push(this.list3.findIndex(t=>t.value == valueList[2]))
				}

				this.tmpValue = this.myValue
				this.$refs.popup.open()
				this.isOpen = true
			},
			close(){
				this.$refs.popup.close()
				this.isOpen = false
			},
			async bindChange(e){
				this.tmpValue =	e.detail.value
				
				const value1 = this.list1[this.tmpValue[0]].value
				const value2 = this.list2[this.tmpValue[1]].value
				const value3 = this.list3[this.tmpValue[2]].value
				
				if(value1 != this.value1){
					this.list2 = await this.loadData(value1)
					this.value1 = value1
					
					this.value2 = this.list2[0].value
					this.list3 = await this.loadData(this.value2)
					
					this.value3 = this.list3[0].value
					
					this.tmpValue[1] = 0
					this.tmpValue[2] = 0
					
					this.isOpen = false
				}
				else if(value2 != this.value2){
					this.list3 = await this.loadData(value2)
					this.value2 = value2
					
					this.value3 = this.list3[0].value
					this.tmpValue[2] = 0
					
					this.isOpen = false
				}
				else{
					this.value3 = value3
				}
				
				
				
				this.$nextTick(()=>{
					this.myValue = this.tmpValue
					this.isOpen = true
				})

			},
			confirm(){
				const value = `${this.value1},${this.value2},${this.value3}`
				
				this.$emit('input',value)
				
				const obj1 = this.list1.find(t=>t.value ==this.value1)
				const obj2 = this.list2.find(t=>t.value ==this.value2)
				const obj3 = this.list3.find(t=>t.value ==this.value3)
				
				this.$emit('change',[obj1,obj2,obj3])
				
				this.close()
			},
			
			async loadData(pid){
				let list =  this.GET_DATA(`learun_areas_${pid}`)
				if(!list){
					this.LOADING(this.$t('加载数据中…'))
					list = (await this.FETCH_AREAS(pid)).map(t=>({value:t.f_AreaCode,label:t.f_AreaName}))
					this.SET_DATA(`learun_areas_${pid}`,list)
					this.HIDE_LOADING()
				}
				return list
			}
		}
	}
</script>


