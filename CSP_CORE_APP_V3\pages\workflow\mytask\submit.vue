<template>
	<!-- 当页面准备好时显示页面内容 -->
	<view v-if="ready" class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-bottom': '40px'}">
		<!-- 白色背景区域，用于放置表单 -->
		<view class="bg-white" style="padding:8px;">
			<!-- uni-forms 组件，用于表单数据的管理和验证 -->
			<uni-forms :modelValue="formData" label-position="top" :label-width="320" :rules="rules" ref="myForm">
				<!-- 人员选择表单项，当 isShowUserSelect 方法返回 true 时显示 -->
				<uni-forms-item v-if="isShowUserSelect()" required name="user" :label="$t('人员选择')">
					<!-- 自定义的用户选择器组件 -->
					<learun-user-picker @change="handleChange" :value="getValue('user')"
						@input="setValue('user', $event)" :placeholder="$t('请选择')" />
				</uni-forms-item>

				<!-- 审批意见表单项 -->
				<uni-forms-item :label="$t('审批意见')" name="des" :required="true">
					<!-- 文本域输入框 -->
					<uni-easyinput type="textarea" :value="getValue('des')" @input="setValue('des', $event)"
						:placeholder="$t('请输入')" autoHeight />
				</uni-forms-item>
				<!-- 审批要点表单项 -->
				<uni-forms-item :label="$t('审批要点')" name="auditTags">
					<!-- 自定义的多选选择器组件，当 auditTags 数组存在且有元素时显示 -->
					<learun-multiple-picker v-if="auditTags && auditTags.length > 0" :value="getValue('tag')"
						@input="setValue('tag', $event)" :options="auditTags" :placeholder="$t('请选择')" />
				</uni-forms-item>
			</uni-forms>
		</view>

		<!-- 操作区按钮 -->
		<view class="learun-bottom-btns">
			<!-- 提交按钮，点击触发 handleClick 方法 -->
			<button @click.stop="handleClick" type="primary">{{$t('提交')}}</button>
		</view>
	</view>
</template>

<script>
	// 引入 lodash 的 set 方法，用于设置对象属性值
	import set from "lodash/set"
	// 引入 lodash 的 get 方法，用于获取对象属性值
	import get from "lodash/get"
	export default {
		data() {
			return {
				// 是否为弹窗层模式
				isLayer: true,
				// 页面是否准备好
				ready: false,
				// 表单数据对象
				formData: {
					// 审批意见
					des: '',
					// 选择的人员
					user: '',
					// 选择的标签
					tag: '',
				},
				// 表单验证规则
				rules: {
					des: {
						rules: [{
							// 审批意见为必填项
							required: true,
							// 验证失败的错误信息
							errorMessage: this.$t(`请填写`)
						}]
					},
					user: {
						rules: [{
							// 人员选择为必填项
							required: true,
							// 验证失败的错误信息
							errorMessage: this.$t(`请选择`)
						}]
					}
				},
				// 当前操作按钮的信息
				btn: {},
				// 工作流任务 ID
				wfTaskId: '',
				// 选择的人员姓名
				userName: '',
				// 工作流任务信息
				wfTask: {},
				// 工作流当前节点信息
				wfCurrentNode: {},
				// 工作流流程 ID
				wfProcessId: '',
				// 工作流数据
				wfData: [],
				// 是否通过 token 访问
				isToken: false,
				// 获取消息的方法
				getMessage: undefined,
				// 审批标签数组
				auditTags: [],
			}
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("任务提交")
			})
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面数据
				this.init()
			}
		},
		methods: {
			init() {
				// 从页面参数中获取相关数据
				const {
					btn,
					wfTaskId,
					wfTask,
					wfCurrentNode,
					wfProcessId,
					wfData,
					isToken,
					getMessage,
					auditTags
				} = this.GET_PARAM() || {};
				// 赋值工作流任务 ID
				this.wfTaskId = wfTaskId
				// 赋值工作流任务信息
				this.wfTask = wfTask
				// 赋值工作流当前节点信息
				this.wfCurrentNode = wfCurrentNode
				// 赋值工作流流程 ID
				this.wfProcessId = wfProcessId
				// 赋值工作流数据
				this.wfData = wfData
				// 设置页面标题为按钮的标签
				this.SET_TITLE(btn.label)
				// 赋值当前操作按钮的信息
				this.btn = btn
				// 赋值是否通过 token 访问
				this.isToken = isToken

				// 标记页面准备好
				this.ready = true

				// 赋值获取消息的方法
				this.getMessage = getMessage;
				// 赋值审批标签数组
				this.auditTags = auditTags
			},
			async handleClick() {
				// 显示加载提示
				this.LOADING(this.$t('正在提交审核…'))

				// 验证表单数据
				const {
					err
				} = await this.VALIDATEFORM(this.$refs.myForm)
				// 如果验证失败，隐藏加载提示并返回
				if (err) {
					this.HIDE_LOADING()
					return
				}

				let res;
				// 根据按钮的属性执行不同的操作
				switch (this.btn.prop) {
					case 'learun_sign':
						// 加签操作
						res = await this.sign()
						break;
					case 'learun_transfer':
						// 转移操作
						res = await this.transfer()
						break;
					default:
						// 普通审批操作
						res = await this.audit()
						break;
				}
				// 隐藏加载提示
				this.HIDE_LOADING()
				if (res) {
					// 如果操作成功且按钮属性为同意，并且有获取消息的方法，则打印消息
					if (this.btn.prop === 'agree' && this.getMessage) {
						console.log(await this.getMessage())
					}
					// 触发工作流列表更新事件
					this.EMIT(`learun-workflow-list-change`)
					if (this.isToken) {
						// 如果是通过 token 访问，重新启动应用到首页
						this.RELAUNCH_TO('/pages/home')
					} else {
						// 否则返回上两级页面
						this.NAV_BACK(2)
					}
				}
			},
			async audit() {
				// 如果工作流任务类型为 6
				if (this.wfTask && this.wfTask.f_Type == 6) {
					// 发送加签审核请求
					const res = await this.HTTP_PUT({
						url: `/workflow/process/signaudit/${this.wfTaskId}`,
						data: {
							// 操作代码
							code: this.btn.prop,
							// 操作名称
							name: this.btn.label,
							// 审批意见
							des: this.formData.des
						}
					})
					return res
				}
				// 如果按钮需要选择下一审核人
				else if (this.btn.isNextAuditor) {
					// 执行选择下一审核人操作
					await this.nextAuditor();
					return
				}
				// 普通审批请求
				else {
					const res = await this.HTTP_PUT({
						url: `/workflow/process/audit/${this.wfTaskId}`,
						data: {
							// 操作代码
							code: this.btn.prop,
							// 操作名称
							name: this.btn.label,
							// 审批意见
							des: this.formData.des,
							// 选择的标签
							tag: this.formData.tag,
						}
					})
					return res
				}

			},
			async sign() {
				// 发送加签请求
				const res = await this.HTTP_PUT({
					url: `/workflow/process/sign/${this.wfTaskId}`,
					data: {
						// 加签给的用户 ID
						toUserId: this.formData.user,
						// 操作名称
						name: this.btn.label,
						// 加签描述
						des: `${this.$t("加签给")}${this.userName}-${this.formData.des}`
					}
				})
				return res
			},
			async transfer() {
				// 发送转移请求
				const res = await this.HTTP_PUT({
					url: `/workflow/process/transfer/${this.wfTaskId}`,
					data: {
						// 转移给的用户 ID
						toUserId: this.formData.user,
						// 操作名称
						name: this.btn.label,
						// 转移描述
						des: `${this.$t("转移给")}${this.userName}-${this.formData.des}`
					}
				})
				return res
			},
			async nextAuditor() {
				// 获取下一审核人列表
				const nodeUserMap = await this.HTTP_GET({
					url: '/workflow/process/nextusers',
					params: {
						// 工作流流程 ID
						processId: this.wfProcessId,
						// 工作流当前节点 ID
						nodeId: this.wfCurrentNode.id,
					}
				})
				const nodeUsers = []
				// 处理下一审核人列表
				for (let key in nodeUserMap) {
					const nodeUserItem = nodeUserMap[key]
					if (nodeUserItem.length > 1) {
						nodeUsers.push({
							// 节点名称
							name: this.wfData.find(t => t.id == key).name,
							// 节点 ID
							id: key,
							// 审核人选项
							options: nodeUserItem.map(t => {
								return {
									value: t.id,
									label: t.name
								}
							})
						})
					}
				}
				let res;
				if (nodeUsers.length > 0) {
					// 监听 learun-wfsubmit-info 事件
					this.ONCE('learun-wfsubmit-info', async (data) => {
						setTimeout(async () => {
							// 显示加载提示
							this.LOADING(this.$t('正在提交审核…'))
							// 发送审批请求
							res = await this.HTTP_PUT({
								url: `/workflow/process/audit/${this.wfTaskId}`,
								data: {
									// 操作代码
									code: this.btn.prop,
									// 操作名称
									name: this.btn.label,
									// 审批意见
									des: this.formData.des,
									// 下一审核人列表
									nextUsers: data.nextUsers,
									// 选择的标签
									tag: this.formData.tag,
								}
							})
							if (res) {
								// 触发工作流列表更新事件
								this.EMIT(`learun-workflow-list-change`)
								if (this.isToken) {
									// 如果是通过 token 访问，重新启动应用到首页
									this.RELAUNCH_TO('/pages/home')
								} else {
									// 否则返回上两级页面
									this.NAV_BACK(2)
								}
							}
						}, 10)
					})
					// 隐藏加载提示
					this.HIDE_LOADING()
					// 跳转到选择下一审核人页面
					this.NAV_TO_LAYER('/pages/workflow/common/learun-wfsubmit-info', {
						nodeUsers,
						isCustmerTitle: this.wfCurrentNode.isCustmerTitle
					})
					return
				}
			},
			handleChange(data) {
				if (data) {
					// 更新选择的人员姓名
					this.userName = data.label;
				}
			},
			isShowUserSelect() {
				// 判断是否显示人员选择器
				if (['learun_sign', 'learun_transfer'].includes(this.btn.prop)) {
					return true;
				} else {
					return false;
				}
			},
			// 设置表单数据的方法
			setValue(path, value) {
				set(this.formData, path, value)
			},

			// 获取表单数据的方法
			getValue(path) {
				return get(this.formData, path)
			},
		}
	}
</script>