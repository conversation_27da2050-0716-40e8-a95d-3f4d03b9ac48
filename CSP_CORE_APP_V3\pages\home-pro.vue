<template>
	<view class="page home">
		<!-- 顶部搜索栏 -->
		<view class="header">
			<!-- 中间的搜索区域 -->
			<view @click="moreClick(1)" class="header-mid margin-lr">
				<view class="header-mid-icon">
					<!-- 放大镜图标 -->
					<learun-icon type="learun-icon-magnifying-glass-m" color="#B3B3B3" size="12" />
				</view>
				<!-- 搜索提示文本，使用了国际化翻译 -->
				{{$t('搜索更多应用')}}
			</view>
			<!-- #ifndef H5 表示在非H5环境下渲染此部分代码 -->
			<view class="header-right margin-right">
				<!-- 扫码图标，点击触发scanClick方法 -->
				<learun-icon @click="scanClick" type="learun-icon-scanning" color="#B3B3B3" />
			</view>
			<!-- #endif -->
		</view>
		<!-- 功能宫格列表 -->
		<view class="content padding-bottom" style="margin-bottom: 8px;">
			<!-- uni-grid是一个宫格组件，设置不显示边框，列数为5，点击宫格项触发funcListClick方法 -->
			<uni-grid :showBorder="false" :column="5" @change="funcListClick">
				<!-- 循环渲染功能宫格项 -->
				<uni-grid-item v-for="(listItem,listIndex) in myModuleApps" :key="listIndex" :index="listIndex">
					<view class="learun-grid-item">
						<view class="learun-grid-icon" style="background-color:transparent;">
							<!-- 显示宫格项的图标 -->
							<learun-icon :type="listItem.icon" :color="listItem.f_Color" size="24" />
						</view>
						<view><text class="learun-grid-text">{{listItem.f_Name}}</text></view>
					</view>
				</uni-grid-item>
				<!-- 更多功能的宫格项 -->
				<uni-grid-item :index="100">
					<view class="learun-grid-item">
						<view class="learun-grid-icon" style="background-color:transparent;">
							<!-- 更多图标 -->
							<learun-icon type="learun-icon-more-m" color="#8f939c" size="24" />
						</view>
						<view><text class="learun-grid-text">{{$t('更多')}}</text></view>
					</view>
				</uni-grid-item>
			</uni-grid>
		</view>
		<!-- 轮播图片 -->
		<view class="learun-panel" style="height:138px;">
			<swiper style="height:138px;" autoplay>
				<swiper-item style="height:138px;">
					<!-- 轮播图的图片 -->
					<image src="/static/banner.png" mode="aspectFill" style="height:100%; width:100%;"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 我的任务 -->
		<uni-card :title="$t('我的任务')" padding="0" margin="8px" :border="false" :is-shadow="false">
			<view class="learun-data-databoard">
				<!-- 待办任务项，点击触发taskClick(0) -->
				<view class="learun-data-databoard__item" @click="taskClick(0)">
					<view class="num"><text>{{nCompletedNum}}</text></view>
					<view class="label">{{$t('待办任务')}}</view>
				</view>
				<!-- 委托任务项，点击触发taskClick(1) -->
				<view class="learun-data-databoard__item" @click="taskClick(1)">
					<view class="num"><text>{{delegateNum}}</text></view>
					<view class="label">{{$t('委托任务')}}</view>
				</view>
				<!-- 已办任务项，点击触发taskClick(2) -->
				<view class="learun-data-databoard__item" @click="taskClick(2)">
					<view class="num"><text>{{completedNum}}</text></view>
					<view class="label">{{$t('已办任务')}}</view>
				</view>
			</view>
		</uni-card>

		<!-- 我的代办任务列表 -->
		<uni-card @click="taskClick(0)" :title="$t('代办任务')" padding="0" margin="8px" :border="false" :is-shadow="false">
			<view @click.stop="()=>{}">
				<uni-list>
					<!-- 循环渲染代办任务列表项，点击触发taskListClick(task) -->
					<uni-list-item clickable @click="taskListClick(task)" v-for="task in taskList"
						:title="task.f_ProcessTitle" :rightText="TABLEITEM_DATEFORMAT(task.f_CreateDate).toString()"
						:key="task.f_Id">
					</uni-list-item>
				</uni-list>
			</view>
		</uni-card>

		<!-- 柱状图 -->
		<uni-card :title="this.$t('柱状图')" padding="0" margin="8px" :border="false" :is-shadow="false">
			<qiun-data-charts type="column" :chartData="columnData" />
		</uni-card>

		<!-- 折线图 -->
		<uni-card :title="this.$t('折线图')" padding="0" margin="8px" :border="false" :is-shadow="false">
			<qiun-data-charts type="line" :chartData="columnData" />
		</uni-card>

		<!-- 饼图 -->
		<uni-card :title="this.$t('饼图')" padding="0" margin="8px" :border="false" :is-shadow="false">
			<qiun-data-charts type="pie" :chartData="pieData" />
		</uni-card>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 功能列表，存放用户的功能模块数据
				myModuleApps: [],

				// 流程信息，分别表示待办任务数量、委托任务数量、已办任务数量
				nCompletedNum: 0,
				delegateNum: 0,
				completedNum: 0,

				taskList: [], // 代办任务列表数据


				// 图表数据，柱状图和折线图的数据
				columnData: {
					"categories": ["2016", "2017", "2018", "2019", "2020", "2021"],
					"series": [{
						"name": this.$t("目标值"),
						"data": [35, 36, 31, 33, 13, 34]
					}]
				},
				pieData: {
					"series": [{
						"data": [{
							"name": this.$t("一班"),
							"value": 50
						}, {
							"name": this.$t("二班"),
							"value": 30
						}, {
							"name": this.$t("三班"),
							"value": 20
						}, {
							"name": this.$t("四班"),
							"value": 18
						}, {
							"name": this.$t("五班"),
							"value": 8
						}]
					}]
				},

			}
		},

		async onLoad(param) {
			// 页面启动时的逻辑，PAGE_LAUNCH是自定义方法，返回true则继续初始化页面
			if (await this.PAGE_LAUNCH()) {
				await this.init(param)
			}
		},
		onShow() {
			// 页面显示时的逻辑，目前为空
		},
		onHide() {
			// 页面隐藏时的逻辑，目前为空
		},

		// 本页面开启下拉刷新，用于刷新首页数据
		onPullDownRefresh() {
			this.refresh().then(() => {
				this.TOAST(this.$t('已更新首页数据'))
				uni.stopPullDownRefresh()
			})
		},

		methods: {
			// 页面初始化
			async init(param) {
				// 有参数表示可能是打开分享消息；将数据存入全局变量以备后续跳转
				if (param && param.learun && param.pagePath) {
					this.SET_GLOBAL('jumpParam', param)
				}

				// 登录状态无效，则跳转到登录页
				const stateValid = await this.checkLoginState()
				if (!stateValid) {
					this.RELAUNCH_TO('/pages/login')
					return
				}

				// 加载页面数据
				await this.refresh()

				// 监听「我的应用」列表修改
				this.ON('home-list-change', () => {
					this.HTTP_GET({
						url: '/mapp/module/mylist'
					}).then(newList => {
						this.initModule(this.GET_GLOBAL('learun_modules'), newList)
					})
				})

				this.SET_STORAGE('nextTime', null)



				// 处理小程序分享消息跳转
				// #ifdef MP 表示在小程序环境下执行此代码
				const jumpParam = this.GET_GLOBAL('jumpParam')
				if (jumpParam) {
					this.SET_GLOBAL('jumpParam', null)
					this.MP_SHARE_DECODE(jumpParam)
				}
				// #endif


			},

			// 验证登录状态
			async checkLoginState() {
				// 判断 token 字段，没有 token 则必定无登录态
				const token = this.GET_GLOBAL('token') || this.GET_STORAGE('token')
				if (!token || token === 'null' || token === 'undefined') {
					this.HIDE_LOADING()
					return false
				}
				this.SET_GLOBAL('token', token)

				// 判断是否有 loginUser 对象
				if (this.GET_GLOBAL('loginUser')) {
					return true
				}

				// 拉取用户信息以验证登录态
				const success = await this.FETCH_CURRENT_USERINFO()
				if (!success) {
					this.TOAST(this.$t('获取用户/部门/公司信息时出现错误，登录失败'))

					this.SET_GLOBAL('token', null)
					this.SET_STORAGE('token', null)

					return false
				}

				return true
			},

			// 刷新首页数据
			async refresh() {
				this.LOADING(this.$t('数据加载中'))
				// 拉取功能模块信息、初始化流程任务数据、初始化流程任务列表
				await this.fetchModules()
				await this.initWFTask()
				await this.initWFList()
				this.HIDE_LOADING()
			},

			// 拉取功能模块信息
			async fetchModules() {
				// 同时请求功能模块数据和我的功能模块信息
				const [modules, myModuleIds] = await Promise.all([
					this.HTTP_GET({
						url: '/mapp/modules'
					}), // 功能模块数据
					this.HTTP_GET({
						url: '/mapp/module/mylist'
					}) // 我的功能模块信息
				])
				this.initModule(modules, myModuleIds)
			},
			// 初始化功能模块
			initModule(modules, myModuleIds) {
				const loginUser = this.GET_GLOBAL('loginUser')
				// 功能区按钮需要处理 icon
				const myModules = []
				// 遍历模块数据，筛选出启用且用户有权限的模块，并处理图标
				modules.forEach(item => {
					if (item.f_EnabledMark == 1 && (loginUser.f_SecurityLevel == 1 || (loginUser.moduleAuthIds ||
						[]).indexOf(item.f_Id) > -1)) {
						const icon = item.f_Icon ? item.f_Icon : ''
						myModules.push({
							...item,
							icon: icon
						})
					}
				})
				this.SET_GLOBAL('learun_modules', myModules)

				myModuleIds = myModuleIds || []
				this.myModuleApps = []
				// 根据我的功能模块信息，筛选出对应的模块
				for (let id of myModuleIds) {
					const item = myModules.find(t => t.f_Id == id && t.f_IsSystem != 3)
					if (item) {
						this.myModuleApps.push(item)
					}
				}
			},



			// 更新流程任务数据
			async initWFTask() {
				const queryData = {
					rows: 1,
					page: 1,
					sidx: 'F_CreateDate DESC'
				}

				const queryData2 = {
					rows: 1,
					page: 1,
					sidx: 't1.F_CreateDate DESC'
				}

				// 同时请求待办任务、已办任务、委托任务的数据
				const [unCompletedData, completedData, delegateData] = await Promise.all([
					this.HTTP_POST({
						url: '/workflow/process/uncompleted/mypage',
						params: queryData,
						data: {}
					}),
					this.HTTP_POST({
						url: '/workflow/process/completed/mypage',
						params: queryData2,
						data: {}
					}),
					this.HTTP_GET({
						url: '/workflow/process/delegate/mypage',
						params: queryData
					})
				])
				// 更新对应的任务数量
				this.nCompletedNum = unCompletedData.records
				this.completedNum = completedData.records
				this.delegateNum = delegateData.records


			},

			async initWFList() {
				const queryData = {
					rows: 5,
					page: 1,
					sidx: 'F_CreateDate DESC'
				}
				// 加载代办任务
				const unCompletedData = await this.HTTP_POST({
					url: '/workflow/process/uncompleted/mypage',
					params: queryData,
					data: {}
				})
				this.taskList = unCompletedData.rows
			},

			taskClick(type) {
				const modules = this.GET_GLOBAL('learun_modules') || []
				// 找到我的任务模块
				const module = modules.find(t => t.f_Url == '/pages/workflow/mytask/list')
				this.TO_MODULE(module, {
					type
				})
			},
			// 点击功能按钮
			funcListClick(e) {
				if (e.detail.index == 100) {
					this.moreClick(0)
					return
				}
				// 获取点击的功能模块项
				const item = this.myModuleApps[e.detail.index]
				this.TO_MODULE(item)
			},

			// #ifndef H5 表示在非H5环境下有此方法
			// 点击左上角扫码图标，H5 无此功能
			scanClick() {
				uni.scanCode({
					scanType: ['qrCode', 'barCode'],
					success: ({
						result,
						charSet
					}) => {
						// 您可以在这里自行定制扫码后的功能
					}
				})
			},
			// #endif

			// 点击更多功能按钮
			moreClick(openSearch) {
				this.NAV_TO('/pages/home/<USER>', {
					search: openSearch ? '1' : null
				})
			},

			desktopList() {
				const modules = this.GET_GLOBAL('learun_modules') || []
				// 筛选出系统模块
				return modules.filter(t => t.f_IsSystem == 3)
			},
			navList() {
				// 获取系统模块的名称列表
				return this.desktopList().map(t => t.f_Name)
			}
		},
	}
</script>

<style lang="scss" scoped>
	.home {
		.header {
			height: 48px;
			background-color: #fff;
			display: flex;
			align-items: center;

			.header-left,
			.header-right {
				flex-grow: 0;
			}

			.header-mid {
				display: flex;
				align-items: center;

				flex-grow: 1;
				background-color: #f8f8f8;
				height: 32px;
				font-size: 12px;
				color: #B3B3B3;
				border-radius: 2px;
			}

			.header-mid-icon {
				margin: 0 8px;
			}
		}

		.content {
			position: relative;
			background-color: #fff;
		}
	}
</style>