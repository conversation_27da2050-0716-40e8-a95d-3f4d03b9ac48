<template>
	<view class="container" style="overflow: auto;">
		<view class="page-body">
			<view class='wrapper' :style="{height:(disabled ? '220px' : '300px')}">
				<view class='toolbar' @tap="format" style="height: 60px;overflow-y: auto;" v-show="!disabled">
				<!-- <view class='toolbar' @tap="format" style="overflow-y: auto;"> -->
					<view :class="formats.bold ? 'ql-active' : ''" class="iconfont icon-zitijiacu" data-name="bold">
					</view>
					<view :class="formats.italic ? 'ql-active' : ''" class="iconfont icon-zitixieti" data-name="italic">
					</view>
					<view :class="formats.underline ? 'ql-active' : ''" class="iconfont icon-zitixiahuaxian"
						data-name="underline"></view>
					<view :class="formats.strike ? 'ql-active' : ''" class="iconfont icon-zitishanchuxian"
						data-name="strike"></view>
					<!-- #ifndef MP-BAIDU -->
					<view :class="formats.align === 'left' ? 'ql-active' : ''" class="iconfont icon-zuoduiqi"
						data-name="align" data-value="left"></view>
					<!-- #endif -->
					<view :class="formats.align === 'center' ? 'ql-active' : ''" class="iconfont icon-juzhongduiqi"
						data-name="align" data-value="center"></view>
					<view :class="formats.align === 'right' ? 'ql-active' : ''" class="iconfont icon-youduiqi"
						data-name="align" data-value="right"></view>
					<view :class="formats.align === 'justify' ? 'ql-active' : ''" class="iconfont icon-zuoyouduiqi"
						data-name="align" data-value="justify"></view>
					<!-- #ifndef MP-BAIDU -->
					<!-- <view :class="formats.lineHeight ? 'ql-active' : ''" class="iconfont icon-line-height"
						data-name="lineHeight" data-value="2"></view> -->
					<!-- <view :class="formats.letterSpacing ? 'ql-active' : ''" class="iconfont icon-Character-Spacing"
						data-name="letterSpacing" data-value="2em"></view> -->
					<view :class="formats.marginTop ? 'ql-active' : ''" class="iconfont icon-722bianjiqi_duanqianju"
						data-name="marginTop" data-value="20px"></view>
					<view :class="formats.marginBottom ? 'ql-active' : ''" class="iconfont icon-723bianjiqi_duanhouju"
						data-name="marginBottom" data-value="20px"></view>
					<!-- #endif -->

					<!-- <view class="iconfont icon-clearedformat" @tap="removeFormat"></view> -->

					<!-- #ifndef MP-BAIDU -->
					<!-- <view :class="formats.fontFamily ? 'ql-active' : ''" class="iconfont icon-font"
						data-name="fontFamily" data-value="Pacifico"></view> -->
					<view :class="formats.fontSize === '24px' ? 'ql-active' : ''" class="iconfont icon-fontsize"
						data-name="fontSize" data-value="24px"></view>
					<!-- #endif -->
					<view :class="formats.color === '#0000ff' ? 'ql-active' : ''" class="iconfont icon-text_color"
						data-name="color" data-value="#0000ff"></view>
					<view :class="formats.backgroundColor === '#00ff00' ? 'ql-active' : ''"
						class="iconfont icon-fontbgcolor" data-name="backgroundColor" data-value="#00ff00"></view>
					<!-- <view class="iconfont icon-date" @tap="insertDate"></view> -->
					<!-- <view class="iconfont icon--checklist" data-name="list" data-value="check"></view> -->
					<view :class="formats.list === 'ordered' ? 'ql-active' : ''" class="iconfont icon-youxupailie"
						data-name="list" data-value="ordered"></view>
					<view :class="formats.list === 'bullet' ? 'ql-active' : ''" class="iconfont icon-wuxupailie"
						data-name="list" data-value="bullet"></view>

					<!-- <view class="iconfont icon-undo" @tap="undo"></view>
					<view class="iconfont icon-redo" @tap="redo"></view> -->

					<view class="iconfont icon-outdent" data-name="indent" data-value="-1"></view>
					<view class="iconfont icon-indent" data-name="indent" data-value="+1"></view>
					<!-- <view class="iconfont icon-fengexian" @tap="insertDivider"></view> -->
					<view class="iconfont icon-charutupian" @tap="insertImage"></view>
					<view :class="formats.header === 1 ? 'ql-active' : ''" class="iconfont icon-format-header-1"
						data-name="header" :data-value="1"></view>
					<view :class="formats.script === 'sub' ? 'ql-active' : ''" class="iconfont icon-zitixiabiao"
						data-name="script" data-value="sub"></view>
					<view :class="formats.script === 'super' ? 'ql-active' : ''" class="iconfont icon-zitishangbiao"
						data-name="script" data-value="super"></view>

					<!-- <view class="iconfont icon-shanchu" @tap="clear"></view> -->

					<!-- <view :class="formats.direction === 'rtl' ? 'ql-active' : ''" class="iconfont icon-direction-rtl"
						data-name="direction" data-value="rtl"></view> -->
				</view>
				<view class="editor-wrapper">
					<editor :id="id" class="ql-container" :placeholder="$t('开始输入...')"
						 @statuschange="onStatusChange" @ready="onEditorReady"
						@blur="onBlur" @input="onInput" :read-only="disabled"
						:show-img-toolbar="!disabled"
						style="width: 100%;">
					</editor>
				</view>
				<!-- <view>
					<button @tap="getCon">打印文本内容</button>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>	
	export default {
    name: 'crystal-text-editor',
    props: {
      value: String,
			editorID: String,
			disabled: {
				type: Boolean,
				default: false
			},
    },
		data() {
			return {
				readOnly: false,
				formats: {},
				id: "editor",
				htmlValue: "",
				lastHtml: "",
			}
		},
		onLoad() {
			// #ifndef MP-BAIDU
			uni.loadFontFace({
				family: 'Pacifico',
				source: 'url("/static/font/Pacifico-Regular.ttf")',
				success() {
					console.log('success load font')
				},
				fail() {
					console.log('fail load font load')
				}
			})
			// #endif
		},
		created() {
			this.id = this.id + this.editorID;
			
			let API = this.GET_GLOBAL('apiRoot') ||
				this.LEARUN_CONFIG('apiHost')[this.DEV ? this.LEARUN_CONFIG('devApiHostIndex') : this.LEARUN_CONFIG(
					'prodApiHostIndex')];
			
			this.htmlValue = this.value;
			this.htmlValue = this.htmlValue//.replaceAll("/csp_core_app_321/pages/workflow/mytask",API)
										.replaceAll("{learun_img_api}",API + "/system/annexesfile/")
										.replaceAll("{learun_img_token}","token=" + this.GET_GLOBAL('token'));
			this.lastHtml = this.htmlValue;
			
		},
		methods: {
			readOnlyChange() {
				this.readOnly = !this.readOnly
			},
			getCon() {
				this.editorCtx.getContents({
					success: (res) => {
					},
					fail: (err) => {
					}
				})
			},
			onEditorReady() {
				// #ifdef MP-BAIDU
				this.editorCtx = requireDynamicLib('editorLib').createEditorContext(this.id);
				// #endif

				// #ifdef APP-PLUS || MP-WEIXIN || H5
				uni.createSelectorQuery().select('#' + this.id).context((res) => {
					this.editorCtx = res.context
				}).exec()
				// #endif
				this.editorCtx.setContents({
					html: this.htmlValue,
					success: (res) => {
						console.log('文本详情：', res)
					},
					fail: (err) => {
						console.log(err)
					}
				})
				// this.editorCtx.getContents({
				// 	success: (res) => {
				// 	},
				// 	fail: (err) => {
				// 	}
				// })
			},
			undo() {
				this.editorCtx.undo()
			},
			redo() {
				this.editorCtx.redo()
			},
			format(e) {
				let {
					name,
					value
				} = e.target.dataset
				if (!name) return
				this.editorCtx.format(name, value)
			},
			onStatusChange(e) {
				const formats = e.detail
				this.formats = formats
			},
			insertDivider() {
				this.editorCtx.insertDivider({
					success: function() {
						console.log('insert divider success')
					}
				})
			},
			clear() {
				uni.showModal({
					title: '清空编辑器',
					content: '确定清空编辑器全部内容？',
					success: res => {
						if (res.confirm) {
							this.editorCtx.clear({
								success: function(res) {
									console.log("clear success")
								}
							})
						}
					}
				})
			},
			removeFormat() {
				this.editorCtx.removeFormat()
			},
			insertDate() {
				const date = new Date()
				const formatDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
				this.editorCtx.insertText({
					text: formatDate
				})
			},
			async insertImage() {
				if (this.disabled) {return}
				let thatVUE = this;
				const Api = this.API;
				const token = this.GET_GLOBAL('token')
				
				uni.chooseImage({
					count: 1,
					success: (res) => {
						let files = []
						let file = res.tempFiles[0];
						
						// 最终需要上传数据库的数据
						let fileFullName = file.name.substring(name.lastIndexOf('.') + 1, name.length)						
						const extname = fileFullName.toLowerCase()
						let image = {}
						
						let fileID = this.GUID();
						let _editorCtx = this.editorCtx;
						
						uni.getImageInfo({
							src: file.path,
							async success(imageinfo) {
								image.width = imageinfo.width
								image.height = imageinfo.height
								image.location = imageinfo.path
								
								files.push({
									extname: extname,
									fileType: file.fileType,
									image: image,
									name: file.name,
									path: file.path,
									size: file.size,
									fileID: fileID,
									uuid: file.uuid,
									url: file.path || file.path,
									uploadType:'add'
								})
								
								let upload_res = await thatVUE.UPLOAD_FILE(files);
								let src = Api + "/system/annexesfile/preview/" + fileID + "?token=" + token;
								
								_editorCtx.insertImage({
									src: src,
									alt: '图像',
									success: function() {
										console.log('insert image success')
									}
								})
							},
							fail(err) {
								console.log(err)
							}
						})
					}
				})
			},
			onInput(event) {
				let value = "";
				this.editorCtx.getContents({
					success: (res) => {
						value = res.html;
					}
				})
				value = value.replaceAll("token=" + this.GET_GLOBAL('token'), "{learun_img_token}");
				this.$emit('input', value);
			},
			onBlur(event) {
				let value = "";
				this.editorCtx.getContents({
					success: (res) => {
						value = res.html;
					}
				})
				value = value.replace("token=" + this.GET_GLOBAL('token'), "{learun_img_token}");
				this.$emit('blur', event);
			},
		}
	}
</script>

<style>
	@import "style/editor-icon.css";

	.page-body {
		/* height: calc(100vh - var(--window-top) - var(--status-bar-height)); */
		height: 300px;
	}

	.wrapper {
		height: 100%;
		border: 1px solid rgb(229, 229, 229); 
		border-radius: 8px; 
		width: calc(750rpx - 100px);
		/* padding: 6px 10px; */
	}

	.editor-wrapper {
		/* height: calc(100vh - var(--window-top) - var(--status-bar-height) - 80px - 46px); */
		height: 220px;
		background: #fff;
	}

	.iconfont {
		display: inline-block;
		margin: 4px 4px;
		width: 19px;
		height: 19px;
		cursor: pointer;
		font-size: 19px;
	}

	.toolbar {
		box-sizing: border-box;
		border-bottom: 1px solid rgb(229, 229, 229);
		font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
		/* width: calc(100% - 90px); */
	}

	.ql-container {
		box-sizing: border-box;
		padding: 12px 15px;
		/* width: calc(100% - 90px); */
		/* min-height: 30vh; */
		height: 100%;
		/* margin-top: 20px; */
		font-size: 16px;
		line-height: 1.5;
		
		img {
			/* width: calc(100% - 70px); */
			max-width: 100%; /* 图片最大宽度为容器宽度 */  
			height: auto; /* 保持图片宽高比 */  
		}
	}

	.ql-active {
		color: #06c;
	}
</style>