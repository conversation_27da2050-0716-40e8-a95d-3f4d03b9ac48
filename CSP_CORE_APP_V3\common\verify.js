const verify = {
  NotNull: t => t && (t.length > 0 || Boolean(t.value) || Boolean(t.id)) || '不能为空',
  Num: t => !isNaN(t) || '须输入数值',
  NumOrNull: t => t.length <= 0 || !isNaN(t) || '须留空或输入数值',
  Email: t => /^[a-zA-Z0-9-_.]+@[a-zA-Z0-9-_]+.[a-zA-Z0-9]+$/.test(t) || '须符合Email格式',
  EmailOrNull: t => t.length <= 0 || /^[a-zA-Z0-9-_.]+@[a-zA-Z0-9-_]+.[a-zA-Z0-9]+$/.test(t) ||
    '须留空或符合Email格式',
  EnglishStr: t => /^[a-zA-Z]*$/.test(t) || '须由英文字母组成',
  EnglishStrOrNull: t => t.length <= 0 || /^[a-zA-Z]*$/.test(t) || '须留空或由英文字母组成',
  Phone: t => /^[+0-9- ]*$/.test(t) || '须符合电话号码格式',
  PhoneOrNull: t => t.length <= 0 || /^[+0-9- ]*$/.test(t) || '须留空或符合电话号码格式',
  Fax: t => /^[+0-9- ]*$/.test(t) || '须符合传真号码格式',
  Mobile: t => /^1[0-9]{10}$/.test(t) || '须符合手机号码格式',
  MobileOrPhone: t => /^[+0-9- ]*$/.test(t) || /^1[0-9]{10}$/.test(t) || '须符合电话或手机号码格式',
  MobileOrNull: t => t.length <= 0 || /^1[0-9]{10}$/.test(t) || '须留空或符合手机号码格式',
  MobileOrPhoneOrNull: t => t.length <= 0 || /^1[0-9]{10}$/.test(t) || /^[+0-9- ]*$/.test(t) ||
    '须留空或符合手机/电话号码格式',
  Uri: t => /^[a-zA-z]+:\/\/[^\s]*$/.test(t) || '须符合网址Url格式',
  UriOrNull: t => t.length <= 0 || /^[a-zA-z]+:\/\/[^\s]*$/.test(t) || '须留空或符合网址Url格式'
}

export { verify }
export default verify
