<template>
	<view>
		<view @click.stop="handleClick" class="learun-input learun-input-border"
			:class="{'learun-input-placeholder':!hasValue}">
			<view class="learun-input__content"><text>{{$t(label)}}</text></view>
			<view v-if="!hasValue" class="learun-input-icon-right learun-input-icon-right-bottom">
				<uni-icons type="bottom" size="14" color="#c0c4cc"></uni-icons>
			</view>
			<view v-else-if="!disabled" class="learun-input-icon-right">
				<view @tap.stop="handleClear">
					<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
				</view>
			</view>
		</view>

		<uni-popup ref="popup" type="bottom">
			<view class="learun-picker-popup">
				<view class="learun-popup-button-close" @click.stop="close">
					<learun-icon type="learun-icon-error" :size="14" color="#737987"></learun-icon>
				</view>

				<picker-view :value="myValue" @change="bindChange" class="picker-view">

					<picker-view-column v-if="isHours">
						<view class="item" v-for="(item,index) in hours" :key="index">{{item}}</view>
					</picker-view-column>
					<picker-view-column v-if="isMinutes">
						<view class="item" v-for="(item,index) in minutes" :key="index">{{item}}</view>
					</picker-view-column>
					<picker-view-column v-if="isSecond">
						<view class="item" v-for="(item,index) in seconds" :key="index">{{item}}</view>
					</picker-view-column>

					<picker-view-column v-if="isRange">
						<view style="text-align: center;">-</view>
					</picker-view-column>

					<picker-view-column v-if="isRange && isHours">

						<view class="item" v-for="(item,index) in endHours" :key="index">{{item}}</view>
					</picker-view-column>
					<picker-view-column v-if="isRange && isMinutes">
						<view class="item" v-for="(item,index) in endMinutes" :key="index">{{item}}</view>
					</picker-view-column>
					<picker-view-column v-if="isRange && isSecond">
						<view class="item" v-for="(item,index) in endSeconds" :key="index">{{item}}</view>
					</picker-view-column>

				</picker-view>

				<view class="learun-popup-button-wraper">
					<view class="learun-popup-button-ok" @click="confirm">{{$t('确认')}}</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		name: 'learun-time-picker',
		props: {
			value: String,
			isRange: {
				type: Boolean,
				default: false
			},
			placeholder: {
				type: String,
				default: '请选择'
			},
			startPlaceholder: {
				type: String,
				default: '开始时间'
			},
			endPlaceholder: {
				type: String,
				default: '结束时间'
			},
			format: {
				type: String,
				default: 'HH:mm:ss'
			},
			selectableRange: String,
			disabled: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				myValue: [],
				tmpValue: [],
				hValue: 0,
				mValue: 0,
				sValue: 0,

				endHValue: 0,
				endMValue: 0,
				endSValue: 0

			}
		},
		computed: {
			seconds() {
				const seconds = []

				let min = 0
				let max = 59
				if (!this.VALIDATENULL(this.selectableRange)) {
					if ((!this.isHours || this.hours[this.hValue] == this.selectableRangeList[0]) &&
						(!this.isMinutes || this.minutes[this.mValue] == this.selectableRangeList[1])
					) {
						min = parseInt(this.selectableRangeList[2])
					}

					if ((!this.isHours || this.hours[this.hValue] == this.selectableRangeList[3]) &&
						(!this.isMinutes || this.minutes[this.mValue] == this.selectableRangeList[4])
					) {
						max = parseInt(this.selectableRangeList[5])
					}
				}


				for (let i = min; i <= max; i++) {
					if (i < 10) {
						i = "0" + i
					} else {
						i = "" + i
					}
					seconds.push(i)
				}
				return seconds
			},
			minutes() {
				const minutes = []
				let min = 0
				let max = 59
				if (!this.VALIDATENULL(this.selectableRange)) {
					if (!this.isHours || this.hours[this.hValue] == this.selectableRangeList[0]) {
						min = parseInt(this.selectableRangeList[1])
					}

					if (!this.isHours || this.hours[this.hValue] == this.selectableRangeList[3]) {
						max = parseInt(this.selectableRangeList[4])
					}
				}

				for (let i = min; i <= max; i++) {
					if (i < 10) {
						i = "0" + i
					} else {
						i = "" + i
					}
					minutes.push(i)
				}
				return minutes
			},
			hours() {
				const hours = []
				let min = 0
				let max = 23

				if (!this.VALIDATENULL(this.selectableRange)) {
					min = parseInt(this.selectableRangeList[0])
					max = parseInt(this.selectableRangeList[3])
				}

				for (let i = min; i <= max; i++) {
					if (i < 10) {
						i = "0" + i
					} else {
						i = "" + i
					}
					hours.push(i)
				}
				return hours
			},

			endSeconds() {
				const seconds = []

				let min = 0
				if ((!this.isHours || this.hours[this.hValue] == this.endHours[this.endHValue]) &&
					(!this.isMinutes || this.minutes[this.mValue] == this.endMinutes[this.endMValue])
				) {
					min = this.sValue
				}

				let max = 59
				if (!this.VALIDATENULL(this.selectableRange)) {
					if ((!this.isHours || this.hours[this.endHValue] == this.selectableRangeList[3]) &&
						(!this.isMinutes || this.minutes[this.endMValue] == this.selectableRangeList[4])
					) {
						max = parseInt(this.selectableRangeList[5])
					}
				}


				for (let i = min; i <= max; i++) {
					if (i < 10) {
						i = "0" + i
					} else {
						i = "" + i
					}
					seconds.push(i)
				}
				return seconds
			},
			endMinutes() {
				const minutes = []

				let min = 0
				if (!this.isHours || this.hours[this.hValue] == this.endHours[this.endHValue]) {
					min = this.mValue
				}

				let max = 59
				if (!this.VALIDATENULL(this.selectableRange)) {
					if (!this.isHours || this.hours[this.endHValue] == this.selectableRangeList[3]) {
						max = parseInt(this.selectableRangeList[4])
					}
				}

				//console.log(min,'min')

				for (let i = min; i <= max; i++) {
					if (i < 10) {
						i = "0" + i
					} else {
						i = "" + i
					}
					minutes.push(i)
				}
				return minutes
			},
			endHours() {
				const hours = []
				let min = this.hValue

				let max = 23
				if (!this.VALIDATENULL(this.selectableRange)) {
					max = parseInt(this.selectableRangeList[3])
				}


				for (let i = min; i <= max; i++) {
					if (i < 10) {
						i = "0" + i
					} else {
						i = "" + i
					}
					hours.push(i)
				}
				return hours
			},


			isHours() {
				if (this.format.indexOf('HH') != -1) {
					return true
				} else {
					return false
				}
			},
			isMinutes() {
				if (this.format.indexOf('mm') != -1) {
					return true
				} else {
					return false
				}
			},
			isSecond() {
				if (this.format.indexOf('ss') != -1) {
					return true
				} else {
					return false
				}
			},
			selectableRangeList() {
				if (this.VALIDATENULL(this.selectableRange)) {
					return []
				}

				const list = this.selectableRange.split(' - ')
				const res = []
				const vList1 = list[0].split(':')
				const vList2 = list[1].split(':')
				res.push(...vList1)
				res.push(...vList2)

				return res
			},

			minHours() {
				if (this.VALIDATENULL(this.selectableRange)) {
					return 0
				} else {
					return parseInt(this.selectableRangeList[0])
				}
			},
			maxHours() {
				if (this.VALIDATENULL(this.selectableRange)) {
					return 23
				} else {
					return parseInt(this.selectableRangeList[3])
				}
			},

			label() {
				if (this.hasValue) {
					return this.value
				}

				if (this.isRange) {
					return `${this.$t(this.startPlaceholder)} - ${this.$t(this.endPlaceholder)}`
				} else {
					return this.$t(this.placeholder)
				}
			},
			hasValue() {
				if (!this.VALIDATENULL(this.value)) {
					return true
				}
				return false
			}
		},
		methods: {
			handleClear() {
				if (this.disabled) {
					return
				}
				this.$emit('input', '')
				this.$emit('change', '')
			},
			setFormatValue(v, list, isEnd) {
				console.log(v, list, isEnd, this.format)
				const formatValue = this.DATEFORMAT(v, 'HH:mm:ss', this.format)
				console.log(formatValue, 'formatValue')
				const formatValueList = formatValue.split(':')
				if (this.isHours) {
					let hourIndex = 0
					if (isEnd) {
						hourIndex = this.endHours.findIndex(t => t == formatValueList[0])
					} else {
						hourIndex = this.hours.findIndex(t => t == formatValueList[0])
					}

					if (hourIndex == -1) {
						hourIndex = 0
					}
					list.push(hourIndex)
				}

				if (this.isMinutes) {
					let minuteIndex = 0

					if (isEnd) {
						minuteIndex = this.endMinutes.findIndex(t => t == formatValueList[1])
					} else {
						minuteIndex = this.minutes.findIndex(t => t == formatValueList[1])
					}

					if (minuteIndex == -1) {
						minuteIndex = 0
					}
					list.push(minuteIndex)
				}

				if (this.isSecond) {
					let secondIndex = 0
					if (isEnd) {
						secondIndex = this.endSeconds.findIndex(t => t == formatValueList[2])
					} else {
						secondIndex = this.seconds.findIndex(t => t == formatValueList[2])
					}

					if (secondIndex == -1) {
						secondIndex = 0
					}
					list.push(secondIndex)
				}
			},
			setValue() {
				if (this.isHours && this.isMinutes && this.isSecond) {
					this.hValue = this.tmpValue[0]
					this.mValue = this.tmpValue[1]
					this.sValue = this.tmpValue[2]

					if (this.isRange) {
						this.endHValue = this.tmpValue[4]
						this.endMValue = this.tmpValue[5]
						this.endSValue = this.tmpValue[6]
					}
				} else if (!this.isHours && this.isMinutes && this.isSecond) {
					this.mValue = this.tmpValue[0]
					this.sValue = this.tmpValue[1]

					if (this.isRange) {
						this.endMValue = this.tmpValue[3]
						this.endSValue = this.tmpValue[4]
					}
				} else if (!this.isHours && !this.isMinutes && this.isSecond) {
					this.sValue = this.tmpValue[0]

					if (this.isRange) {
						this.endSValue = this.tmpValue[2]
					}
				} else if (this.isHours && !this.isMinutes && !this.isSecond) {
					this.hValue = this.tmpValue[0]

					if (this.isRange) {
						this.endHValue = this.tmpValue[2]
					}
				} else if (this.isHours && this.isMinutes && !this.isSecond) {
					this.hValue = this.tmpValue[0]
					this.mValue = this.tmpValue[1]

					if (this.isRange) {
						this.endHValue = this.tmpValue[3]
						this.endMValue = this.tmpValue[4]
					}
				} else if (!this.isHours && this.isMinutes && !this.isSecond) {
					this.mValue = this.tmpValue[0]

					if (this.isRange) {
						this.endMValue = this.tmpValue[2]
					}
				} else if (this.isHours && !this.isMinutes && this.isSecond) {
					this.hValue = this.tmpValue[0]
					this.sValue = this.tmpValue[1]

					if (this.isRange) {
						this.endHValue = this.tmpValue[3]
						this.endSValue = this.tmpValue[4]
					}
				}
			},

			handleClick() {
				if (this.disabled) {
					return
				}

				this.myValue = []

				if (!this.VALIDATENULL(this.value)) {
					if (this.isRange) {
						const valueList = this.value.split(' - ')
						this.setFormatValue(valueList[0], this.myValue)
						this.myValue.push(0)
						this.setFormatValue(valueList[1], this.myValue, true)
					} else {
						this.setFormatValue(this.value, this.myValue)
					}
				} else {
					if (this.isHours) {
						this.myValue.push(0)
					}

					if (this.isMinutes) {
						this.myValue.push(0)
					}

					if (this.isSecond) {
						this.myValue.push(0)
					}

					if (this.isRange) {
						this.myValue.push(0)
						if (this.isHours) {
							this.myValue.push(0)
						}

						if (this.isMinutes) {
							this.myValue.push(0)
						}

						if (this.isSecond) {
							this.myValue.push(0)
						}
					}
				}

				this.tmpValue = this.myValue
				this.setValue()
				this.$refs.popup.open()
			},
			close() {
				this.$refs.popup.close()
			},
			bindChange(e) {
				this.tmpValue = e.detail.value
				this.setValue()
			},
			confirm() {
				let res = ''
				if (this.isRange) {
					let startValue = ''
					let endValue = ''
					startValue += this.hours[this.hValue] + ":"
					startValue += this.minutes[this.mValue] + ":"
					startValue += this.seconds[this.sValue]
					startValue = this.DATEFORMAT(`2022-02-21 ${startValue}`, this.format)
					endValue += this.endHours[this.endHValue] + ":"
					endValue += this.endMinutes[this.endMValue] + ":"
					endValue += this.endSeconds[this.endSValue]
					endValue = this.DATEFORMAT(`2022-02-21 ${endValue}`, this.format)

					res = `${startValue} - ${endValue}`
				} else {

					res += this.hours[this.hValue] + ":"
					res += this.minutes[this.mValue] + ":"
					res += this.seconds[this.sValue]

					res = this.DATEFORMAT(`2022-02-21 ${res}`, this.format)
				}



				this.$emit('input', res)
				this.$emit('change', res)


				this.$refs.popup.close()
			}
		}
	}
</script>