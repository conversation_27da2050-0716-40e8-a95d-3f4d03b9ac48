<template>
	<view class="page padding bg-white" style="box-sizing: border-box;">
		<view>
			<view style="text-align: center;">
				{{$t('关注CSP微信公众号')}}
				<br />
				{{$t('获取更多框架信息')}}
			</view>
			<view style="text-align: center;" class="margin-tb">
				<image mode="aspectFill" src="/static/wx-qrcode.jpg" />
			</view>
		</view>
		<view class="content">
			<text>CSP</text>
		</view>
	</view>
</template>
<script>
	export default {
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("关于CSP")
			})
			await this.PAGE_LAUNCH()
		},
	}
</script>

<style lang="scss" scoped>
	image {
		width: 120px;
		height: 120px;
	}

	.content {
		font-size: 16px;
	}
</style>