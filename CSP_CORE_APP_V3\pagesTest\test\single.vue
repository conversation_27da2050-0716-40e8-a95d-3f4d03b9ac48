<template>
	<view class="page" :style="{
		  'min-height':SCREENHEIGHT()+'px',
		  'background-color':'#fff',
		  'padding-top':isWorkflow() && needWorkflowInfo?'52px':0,
		  'padding-bottom':hasBtns()?'40px':0
		  }">
		<!-- 流程信息 -->
		<view v-if="ready && isWorkflow() && needWorkflowInfo" class="bg-white fixed" style="padding: 8px;">
			<uni-segmented-control :current="wfTab" :values="wfTabList" @clickItem="wfChangeTab">
			</uni-segmented-control>
		</view>
		<learun-workflow-timeline v-if="ready && isWorkflow() && needWorkflowInfo" v-show="wfTab == 1" :wfLogs="wfLogs">
		</learun-workflow-timeline>
		<!-- 渲染表单 -->
		<learun-customform-wraper :top="needWorkflowInfo? 52 : 0" v-show="wfTab == 0" v-if="ready" :editMode="editMode"
			:scheme="{formInfo:formScheme}" :isUpdate="isUpdate" :formId="moduleId" :moduleId="moduleId"
			@ready="handleReady" isSystem :initFormValue="formData" @myAfterChangeDataEvent="afterChangeDataEvent"
			ref="form" />
		<!-- 操作区按钮 -->
		<view v-if="ready && !isWorkflow()" class="learun-bottom-btns">
			<button v-if="mode !== 'create' && editMode && GET_BUTTON_AUTH('Delete',moduleId)" @click="handleDelete"
				type="warn">删除</button>
			<button v-if="editMode" @click="saveForm" type="primary">保存</button>
		</view>
		<!-- 流程操作区域 -->
		<view v-if="ready && isWorkflow()" class="learun-bottom-btns">
			<button v-if="['wf_create','wf_draft','wf_again'].includes(mode)" @click.stop="wf_draft">保存草稿</button>
			<button v-if="['wf_create','wf_draft','wf_again'].includes(mode)" @click.stop="wf_submit"
				type="primary">流程提交</button>
			<button v-if="wfIsCancel" @click.stop="wf_revokeAudit" type="warn">撤销</button>
			<button v-else-if="mode == 'wf_audit'" @click.stop="wf_open_action" type="primary">流程处理</button>
			<button v-else-if="wfIsRead" @click.stop="wf_read" type="primary">确认阅读</button>
		</view>
		<learun-popup-buttons v-if="ready && isWorkflow()" ref="wfpopup" :buttons="wfButtons" @click="wf_action">
		</learun-popup-buttons>
	</view>
</template>

<script>
	import workflowMixins from '@/common/workflow_sys.js'
	export default {
		mixins: [workflowMixins],
		async onLoad(params) {
			uni.setNavigationBarTitle({
				title: this.$t("表单页面")
			})
			if (await this.PAGE_LAUNCH()) {
				await this.init(params)
			}
		},
		methods: {
			hasBtns() {
				return this.editMode || this.wfIsCancel || this.wfIsRead
			},
			// 页面初始化
			async init(params) {
				this.LOADING('加载数据中…')
				const {
					type,
					title,
					moduleId,
					keyValue
				} = this.GET_PARAM() || params
				this.SET_TITLE(title)
				this.moduleId = moduleId
				//   // 获取页面权限信息
				await this.FETCH_AUTH(this.moduleId)
				this.mode = type
				this.keyValue = keyValue
				this.editMode = ['create', 'edit', 'wf_create', 'wf_draft', 'wf_again', 'wf_audit'].includes(this
					.mode) // 是否是编辑状态
				// 流程初始化
				await this.wf_init(params)
				// 赋值
				if (['edit'].includes(this.mode)) {
					if (keyValue) { // 加载数据
						this.isUpdate = true
						this.formData = await this.loadFormData(keyValue)
						if (!this.formData) {
							this.NAV_BACK()
						}
					} else {
						this.TOAST('缺失主键值！')
						this.NAV_BACK()
					}
				}
				this.ready = true
				if (this.wfFormData) {
					// 表示有流程数据传递进来
					this.$nextTick(() => {
						this.isUpdate = true
						this.$refs.form.setForm(this.wfFormData, true)
					})
				}
			},
			handleReady() {
				this.HIDE_LOADING()
			},
			afterChangeDataEvent() {
				// 表单数据改变后执行
			},
			async loadFormData(keyValue) {
				const _formData = await this.HTTP_GET({
					url: `/test/test/${keyValue}`,
					errorTips: '获取数据失败'
				})
				if (!_formData) {
					return null
				}
				const formData = {
					f_parentEntity: _formData
				}
				return formData
			},
			async getForm() {
				const _postData = await this.$refs.form.getFormValue()
				const postData = this.GET_FORMDATA(_postData, 'f_parentEntity')
				return postData
			},
			async saveForm() {
				this.LOADING('正在提交…')
				const verifyResult = await this.$refs.form.validate()
				if (verifyResult) {
					const res = await this.handleSave({
						keyValue: this.keyValue,
						isEdit: this.isUpdate
					})
					this.HIDE_LOADING()
					if (res) {
						this.EMIT(`custom-list-change-${this.moduleId}`)
						this.NAV_BACK()
					}
				} else {
					this.HIDE_LOADING()
				}
			},
			async handleSave({
				keyValue,
				isEdit /*,code,node*/
			}) {
				//isEdit 是否更新数据, keyValue 流程中相当于流程processId,code 表示流程中的操作码,node 流程节点
				const postData = await this.getForm()
				let res
				if (this.isUpdate) {
					res = await this.HTTP_PUT({
						url: `/test/test/${keyValue}`,
						data: postData,
						errorTips: '表单提交保存失败'
					})
				} else {
					// 作为流程表单的时候用来关联流程字段
					postData.f_Id = keyValue
					res = await this.HTTP_POST({
						url: `/test/test`,
						data: postData,
						errorTips: '表单提交保存失败'
					})
				}
				if (res) {
					this.isUpdate = true
				}
				return res
			},
			async handleDelete() {
				if (!(await this.CONFIRM('删除项目', '确定要删除该项吗？', true))) {
					return
				}
				this.LOADING('提交删除中…')
				const success = await this.HTTP_DELETE({
					url: "/test/test/" + this.keyValue,
					errorTips: '删除失败'
				})
				this.HIDE_LOADING()
				if (success) {
					this.EMIT(`custom-list-change-${this.moduleId}`)
					this.NAV_BACK()
					this.TOAST('删除成功', 'success')
				}
			},
		},
		data() {
			return {
				moduleId: '',
				mode: '',
				keyValue: '',
				isUpdate: false,
				editMode: false,
				ready: false,
				formData: {},
				formScheme: {
					"labelPosition": "right",
					"labelWidth": 80,
					"tabList": [{
						"components": [{
							"type": "input",
							"rule": [],
							"prop": "f_parentEntity_f_text",
							"label": "输入框",
							"placeholder": this.$t("请输入"),
							"display": true,
							"preIcon": {},
							"sufIcon": {},
							"span": 24,
							"table": "f_parentEntity",
							"field": "F_text",
							"default": ""
						}, {
							"type": "textarea",
							"rule": [],
							"prop": "f_parentEntity_f_textarea",
							"label": "多行文本",
							"placeholder": this.$t("请输入"),
							"display": true,
							"rows": 3,
							"span": 24,
							"table": "f_parentEntity",
							"field": "F_textarea",
							"default": ""
						}],
						"text": "主表信息"
					}]
				}
			}
		}
	}
</script>