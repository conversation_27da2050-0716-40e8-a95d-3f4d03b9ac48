<template>
	<view class="page" :style="{
		  'min-height':SCREENHEIGHT()+'px',
		  'background-color':'#fff',
		  'padding-top':isWorkflow() && needWorkflowInfo?'52px':0,
		  'padding-bottom':hasBtns()?'40px':0
		  }">
		<!-- 流程信息 -->
		<view v-if="ready && isWorkflow() && needWorkflowInfo" class="bg-white fixed" style="padding: 8px;">
			<uni-segmented-control :current="wfTab" :values="wfTabList" @clickItem="wfChangeTab">
			</uni-segmented-control>
		</view>
		<!-- 审批日志 -->
		<learun-table v-if="ready && isWorkflow() && needWorkflowInfo" v-show="wfTab == 1" :isPage="false"
			:columns="auditLogColumns" :dataSource="auditLogList" />
		<learun-workflow-timeline v-if="ready && isWorkflow() && needWorkflowInfo" v-show="wfTab == 2" :wfLogs="wfLogs">
		</learun-workflow-timeline>
		<!-- 审批路线 -->
		<learun-workflow-auditline v-if="ready && isWorkflow() && needWorkflowInfo" v-show="wfTab == 3"
			:list="auditLines"></learun-workflow-auditline>
		<!-- 渲染表单 -->
		<learun-customform-wraper :top="needWorkflowInfo? 52 : 0" v-show="wfTab == 0" v-if="ready" :editMode="editMode"
			:scheme="{formInfo:formScheme}" :isUpdate="isUpdate" :formId="moduleId" :moduleId="moduleId" @ready="handleReady"
			isSystem :initFormValue="formData" @myAfterChangeDataEvent="afterChangeDataEvent" ref="form" />
		<!-- 操作区按钮 -->
		<view v-if="ready && !isWorkflow()" class="learun-bottom-btns">
			<button v-if="mode !== 'create' && editMode && GET_BUTTON_AUTH('Delete',moduleId)" @click="handleDelete"
				type="warn">{{$t("删除")}}</button>
			<button v-if="editMode" @click="saveForm" type="primary">{{$t("保存")}}</button>
		</view>
		<!-- 流程操作区域 -->
		<view v-if="ready && isWorkflow()" class="learun-bottom-btns">
			<button v-if="['wf_create','wf_draft','wf_again'].includes(mode)" @click.stop="wf_draft">{{$t("保存草稿")}}</button>
			<button v-if="['wf_create','wf_draft','wf_again'].includes(mode)" @click.stop="wf_submit"
				type="primary">{{$t('流程提交')}}</button>
			<button v-if="wfIsCancel" @click.stop="wf_revokeAudit" type="warn">{{$t('撤销')}}</button>
			<!-- <button v-else-if="mode == 'wf_audit'" @click.stop="wf_open_action" type="primary">流程处理</button> -->
			<button v-else-if="wfIsRead" @click.stop="wf_read" type="primary">{{$t('确认阅读')}}</button>
			<button v-if="mode == 'wf_audit' && !isUpload" @click.stop="wf_action2('disagree')"
				type="danger">{{$t('拒绝')}}</button>
			<button v-if="mode == 'wf_audit' && !isUpload" @click.stop="wf_action2('agree')"
				type="primary">{{$t('同意')}}</button>
			<button v-if="isUpload" @click.stop="wf_action2('agree2')" type="primary">{{$t('提交')}}</button>
		</view>
		<learun-popup-buttons v-if="ready && isWorkflow()" ref="wfpopup" :buttons="wfButtons" @click="wf_action">
		</learun-popup-buttons>
	</view>
</template>

<script>
	import workflowMixins from '@/common/workflow_sys.js'
	import formScheme from '@/pagesHRATTF/hrattF001/formScheme.js'
	import moment from 'moment';
	export default {
		mixins: [workflowMixins, formScheme],
		async onLoad(params) {
			if (await this.PAGE_LAUNCH()) {
				await this.init(params)
			}
		},
		methods: {
			hasBtns() {
				return this.editMode || this.wfIsCancel || this.wfIsRead
			},
			// 页面初始化
			async init(params) {
				this.LOADING(this.$t('加载数据中…'))
				const {
					type,
					title,
					moduleId,
					keyValue,
					formReadOnly
				} = this.GET_PARAM() || params
				this.SET_TITLE(title)
				this.moduleId = moduleId

				const path = this.PATH().replace("single", "list");
				const modules = this.GET_GLOBAL("learun_modules") || [];
				const module = modules.find((t) => t.f_Url == path);
				this.listModuleId = module.f_Id;

				this.mode = type
				this.keyValue = keyValue
				this.formScheme = this.formBaseScheme
				this.componentList = this.formBaseScheme.components.map(obj => obj.id)
				// 获取页面权限信息
				await this.FETCH_AUTH(this.moduleId)
				if (formReadOnly) {
					this.editMode = !formReadOnly
				} else {
					this.editMode = ['create', 'edit', 'wf_create', 'wf_draft', 'wf_again', 'wf_audit'].includes(this
						.mode) // 是否是编辑状态, 
				}
				// 流程初始化
				await this.wf_init(params)
				// 赋值
				if (['edit', 'details'].includes(this.mode)) {
					if (keyValue) { // 加载数据
						this.isUpdate = true
						this.formData = await this.loadFormData(keyValue)
						if (!this.formData) {
							this.NAV_BACK()
						}
					} else {
						this.TOAST(this.$t('缺失主键值！'))
						this.NAV_BACK()
					}
				}
				this.ready = true
				if (this.wfFormData) {
					// 表示有流程数据传递进来
					this.$nextTick(() => {
						this.isUpdate = true
						this.$refs.form.setForm(this.wfFormData, true)
					})
				}
				this.loginUser = this.GET_GLOBAL('loginUser')
				this.emp_no = this.loginUser.f_DDOpenId
				this.leaveYearTip = this.$t(" (请先休完上一年的年假，方可休当年的年假！)")
				if ("wf_audit" == this.mode && ['23', '32'].includes(this.formData.fhisLeaveHeaderEntity.approve_Status)) {
					this.isUpload = true;
				}

				// if (this.editMode || this.isUpload) {
				// 	this.components("fhisLeaveDetailList").
				// 	this.formScheme.tabList[0].components[22].isEdit = true;
				// }
				this.components("6961176811747099244207").type = "input"
				//处理跨公司批核请假单请假类型空白问题
				if (['wf_look', 'wf_audit', 'wf_lookmy'].includes(this.mode)) {
					this.components("fhisLeaveHeaderEntity_leave_Type").config.dataCode = "FHIS_Leave_Type_Com_All"
					//查看数据和批核时，批核人中应显示全部人员，否则查回以前单时会显示空白
					this.components("fhisLeaveHeaderEntity_appraiser_UserID").config.dataCode = "C00"
					this.components("fhisLeaveHeaderEntity_m_Approve_UserID").config.dataCode = "O10"
					this.components("fhisLeaveHeaderEntity_e_Approve_UserID").config.dataCode = "E00"
				}

				if (['create', 'wf_create', 'wf_draft', 'wf_again'].includes(this.mode)) {
					this.components("fhisLeaveHeaderEntity_emp_No").config.defaultValue = this.emp_no
					this.components("fhisLeaveHeaderEntity_rid").config.display = false
					this.components("fhisLeaveHeaderEntity_leave_Note_NO").config.display = false
					this.components("fhisLeaveHeaderEntity_submit_UserID").config.display = false
					this.components("fhisLeaveHeaderEntity_leave_Submit_Date").config.display = false
					if (['create', 'wf_create'].includes(this.mode)) {
						this.components("fhisLeaveHeaderEntity_leave_Note_NO").config.defaultValue =
							`learun_code_FHIS Leave Note|${this.GUID()}`
					}
				}
				if (['wf_audit'].includes(this.mode)) {
					this.components("fhisLeaveHeaderEntity_leave_Note_NO").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_leave_Type").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_from_Date").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_from_Time").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_to_Date").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_to_Time").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_remark").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_appraiser_UserID").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_m_Approve_UserID").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_e_Approve_UserID").config.readonly = true;
					this.components("fhisLeaveHeaderEntity_signature").config.readonly = true;
				}
			},
			async handleReady() {
				//获取评估者
				if (['create', 'wf_create'].includes(this.mode)) {
					const AppraiserData = await this.HTTP_POST({
						url: '/hrattf/hrattF001/LeaveGetDataAndCheck',
						data: {
							"actionType": "getAppraiser",
							"Emp_No": this.emp_no
						}
					});
					if (AppraiserData && AppraiserData.length > 0) {
						this.setFormValue("fhisLeaveHeaderEntity_appraiser_UserID", AppraiserData[0].appraiser)
						this.setFormValue("fhisLeaveHeaderEntity_m_Approve_UserID", AppraiserData[0].m_appraiser)
						this.setFormValue("fhisLeaveHeaderEntity_e_Approve_UserID", AppraiserData[0].e_appraiser)
						if (AppraiserData[0].m_appraiser == AppraiserData[0].e_appraiser) {
							this.setFormValue("fhisLeaveHeaderEntity_m_Is_E", 1)
						} else {
							this.setFormValue("fhisLeaveHeaderEntity_m_Is_E", 0)
						}
					}
				}
				if (this.formData) {
					//是否显示凭证
					if (this.formData.fhisLeaveDetailList && this.formData.fhisLeaveDetailList.length > 0) {
						this.components("7131847591748922916154").config.display = true
						let LeaveTypeData = this.GET_DATA(this.components("fhisLeaveHeaderEntity_leave_Type").config.dataCode);
						let thatLeaveType = LeaveTypeData.find(t => t.leave_type == this.formData.fhisLeaveHeaderEntity
							.leave_Type &&
							t.company_code == this.formData.fhisLeaveHeaderEntity.company_Id.split("_")[0]
						)

						if (thatLeaveType) {
							let tipMessage = thatLeaveType.day_remark;
							tipMessage += tipMessage.length > 0 ? this.$t("，") : "";
							tipMessage += thatLeaveType.voucher_type_remark;
							this.$refs.form.setComponent("7131847591748922916154", {
								config: {
									"content": "<view>" + this.$t("凭证信息： 单张图片大小不能超过10M") + "</view><br>" +
										"<view style='color:red'>" + this.$t("提示") + "</view><view>" +
										this.$t("：") + tipMessage + "</view>",
									"display": true
								}
							});
							if (thatLeaveType.not_must_voucher_type) {
								this.not_must_Voucher_Type = thatLeaveType.not_must_voucher_type.split(",");
							}
							let Voucher_Type = thatLeaveType.voucher_type.split(",");
							let Voucher_TypeOpitions = this.GET_DATA("Voucher_Type");
							for (let j = 0; j < Voucher_Type.length; j++) {
								this.need_input_Voucher_Type[j] = Voucher_Type[j];
							}
							for (let j = 0; j < Voucher_TypeOpitions.length; j++) {
								Voucher_TypeOpitions[j].f_EnabledMark = 0;
								for (let k = 0; k < Voucher_Type.length; k++) {
									if (Voucher_TypeOpitions[j].value == Voucher_Type[k]) {
										Voucher_TypeOpitions[j].f_EnabledMark = 1;
									}
								}
							}
							this.SET_DATA("Voucher_Type", Voucher_TypeOpitions);
							//this.setCanUseDaysLable(thatLeaveType.defalut_day ? thatLeaveType.defalut_day : -1)
						}
					} else {
						this.$refs.form.setComponent("fhisLeaveDetailList", {
							type: "input",
						});
					}
					if (this.formData.fhisLeaveHeaderEntity) {
						//是否显示开始时间和结束时间
						if (this.formData.fhisLeaveHeaderEntity.leave_Way == "4") {
							this.$refs.form.setHide("fhisLeaveHeaderEntity_from_Time", false)
							this.$refs.form.setHide("fhisLeaveHeaderEntity_to_Time", false)
						}
						//受限请假显示年份和可用天数
						if (this.formData.fhisLeaveHeaderEntity.leave_List_RID) {
							let LeaveTypeData = this.GET_DATA(this.components("fhisLeaveHeaderEntity_leave_Type").config.dataCode);
							let thatLeaveType = LeaveTypeData.find(t => t.leave_type == this.formData.fhisLeaveHeaderEntity
								.leave_Type)
							if (thatLeaveType) {
								this.Leave_Allow_Code = thatLeaveType.leave_allow_code
								await this.allowLeave(this.formData.fhisLeaveHeaderEntity.leave_From_Date, true)
							}
						}
					}
				}

				this.HIDE_LOADING()
			},
			async afterChangeDataEvent(event) {
				let component = event.component
				let data = event.data
				console.log(112, "event:", event)
				// 表单数据改变后执行
				let postData = await this.getForm();
				console.log(117, "postData:", postData)
				let like_fhisLeaveHeaderEntity_leave_Way = false;
				//切换请假类型
				if (component.id == "fhisLeaveHeaderEntity_leave_Type") {
					this.setFormValue("fhisLeaveHeaderEntity_from_Date", "")
					this.setFormValue("fhisLeaveHeaderEntity_to_Date", "")
					this.setFormValue("fhisLeaveHeaderEntity_leave_List_RID", "")
					this.setReadonly("fhisLeaveHeaderEntity_to_Date",false)
					if (data == undefined) {
						this.$refs.form.setComponent("fhisLeaveHeaderEntity_leave_Way", {
							dataCode: "Leave_Way",
						});
						this.$refs.form.setHide("fhisLeaveHeaderEntity_leave_List_RID")
						if (this.formData.fhisLeaveDetailList) {
							this.formData.fhisLeaveDetailList = [];
						}
						this.$refs.form.setRequired("fhisLeaveHeaderEntity_remark", false)
					} else {
						let LeaveTypeData = this.GET_DATA(this.components("fhisLeaveHeaderEntity_leave_Type").config.dataCode);
						for (let i = 0; i < LeaveTypeData.length; i++) {
							if (LeaveTypeData[i].leave_type == data.value) {
								//受限请假
								if (LeaveTypeData[i].leave_allow) {
									this.Leave_Allow_Code = LeaveTypeData[i].leave_allow_code;
									await this.allowLeave(postData.fhisLeaveHeaderEntity.from_Date)
								} else {
									this.$refs.form.setComponent("6961176811747099244207", {
										type: "input"
									});
									this.$refs.form.setHide("6961176811747099244207")
									this.$refs.form.setHide("fhisLeaveHeaderEntity_leave_List_RID")
									this.setFormValue("fhisLeaveHeaderEntity_leave_List_RID", "")
									this.Leave_Allow_Code = ""
									this.$refs.form.setRequired("fhisLeaveHeaderEntity_leave_List_RID", false)
								}
								//修改请假方式数据源
								let LeaveWay = LeaveTypeData[i].leave_way.split(",");
								let LeaveWayOpitions = this.GET_DATA("Leave_Way");
								let leaveWayVaue = 0;
								for (let j = 0; j < LeaveWayOpitions.length; j++) {
									LeaveWayOpitions[j].f_EnabledMark = 0;
									for (let k = 0; k < LeaveWay.length; k++) {
										if (LeaveWayOpitions[j].value == LeaveWay[k]) {
											LeaveWayOpitions[j].f_EnabledMark = 1;
											leaveWayVaue = leaveWayVaue == 0 ? LeaveWay[k] : leaveWayVaue;
										}
									}
								}
								this.SET_DATA("LeaveWayOpitions", LeaveWayOpitions);
								this.$refs.form.setComponent("fhisLeaveHeaderEntity_leave_Way", {
									dataCode: "LeaveWayOpitions"
								});
								if (leaveWayVaue == 1) {
									this.$refs.form.setHide("fhisLeaveHeaderEntity_from_Time")
									this.$refs.form.setHide("fhisLeaveHeaderEntity_to_Time")
								} else {
									this.$refs.form.setHide("fhisLeaveHeaderEntity_from_Time", false)
									this.$refs.form.setHide("fhisLeaveHeaderEntity_to_Time", false)
								}
								this.setFormValue("fhisLeaveHeaderEntity_leave_Way", leaveWayVaue)
								like_fhisLeaveHeaderEntity_leave_Way = true;

								//附件
								let Voucher_Type = LeaveTypeData[i].voucher_type.split(",");
								let Voucher_TypeOpitions = this.GET_DATA("Voucher_Type");
								let Voucher_Type_Length = 0;
								for (let j = 0; j < Voucher_TypeOpitions.length; j++) {
									Voucher_TypeOpitions[j].f_EnabledMark = 0;
									for (let k = 0; k < Voucher_Type.length; k++) {
										if (Voucher_TypeOpitions[j].value == Voucher_Type[k]) {
											Voucher_TypeOpitions[j].f_EnabledMark = 1;
											Voucher_Type_Length++;
										}
									}
								}
								this.SET_DATA("Voucher_Type", Voucher_TypeOpitions);
								this.need_input_Voucher_Type = [];
								if (Voucher_Type_Length == 0) {
									if (this.formData.fhisLeaveDetailList) {
										this.formData.fhisLeaveDetailList = [];
									}
									this.$refs.form.setComponent("fhisLeaveDetailList", {
										type: "input",
									});
									this.$refs.form.setHide("7131847591748922916154")
								} else {
									this.showTip = true;
									let tipMessage = LeaveTypeData[i].day_remark;
									tipMessage += tipMessage.length > 0 ? this.$t("，") : "";
									tipMessage += LeaveTypeData[i].voucher_type_remark;
									this.$refs.form.setComponent("7131847591748922916154", {
										config: {
											"content": "<view>" + this.$t("凭证信息： 单张图片大小不能超过10M") + "</view><br>" +
												"<view style='color:red'>" + this.$t("提示") + "</view><view>" +
												this.$t("：") + tipMessage + "</view>",
											"display": true
										}
									});
									let fhisLeaveDetailList = [];
									for (let j = 0; j < Voucher_Type.length; j++) {
										let fhisLeaveDetailItem = {
											file_Url: '',
											voucher_Type: Voucher_Type[j]
										};
										fhisLeaveDetailList.push(fhisLeaveDetailItem);
										this.need_input_Voucher_Type[j] = Voucher_Type[j];
									}
									this.$refs.form.setComponent("fhisLeaveDetailList", {
										type: "gridtable",
									});
									//设置默认凭证类型
									this.setFormValue("fhisLeaveDetailList", fhisLeaveDetailList)

									//只改变数据，不会刷新子表组件，还需修改子表组件属性才行，修改属性，会刷新相对应控件
									this.$refs.form.setComponent("fhisLeaveDetailList", {
										rowNum: fhisLeaveDetailList.length
									});
								}
								this.need_E = LeaveTypeData[i].need_e;
								if (this.need_E && !this.isE) {
									this.setFormValue("fhisLeaveHeaderEntity_m_Is_E", 0)
								} else {
									this.setFormValue("fhisLeaveHeaderEntity_m_Is_E", 1)
									this.$refs.form.setHide("fhisLeaveHeaderEntity_e_Approve_UserID")
									this.$refs.form.setRequired("fhisLeaveHeaderEntity_e_Approve_UserID", false)
								}
								this.setFormValue("fhisLeaveHeaderEntity_need_HRD", LeaveTypeData[i].need_hrd)
								
								this.not_must_Voucher_Type = [];
								if (LeaveTypeData[i].not_must_voucher_type) {
									this.not_must_Voucher_Type = LeaveTypeData[i].not_must_voucher_type.split(",");
								}
								this.default_Day = LeaveTypeData[i].defalut_day ? LeaveTypeData[i].defalut_day : -1;

								let requiredItem = LeaveTypeData[i].requireditem;
								this.$refs.form.setRequired("fhisLeaveHeaderEntity_remark", false)
								if (requiredItem) {
									let requiredList = requiredItem.split(",");
									for (let j = 0; j < requiredList.length; j++) {
										this.$refs.form.setRequired(requiredList[j], true);
									}
								}
								this.mustUpVoucher = false;
								if (LeaveTypeData[i].add_must_up_voucher) {
									this.mustUpVoucher = true;
								}
							}
						}
					}
				}
				//切换受限请假年份
				if (component.id == "fhisLeaveHeaderEntity_leave_List_RID") {
					this.Limit_Leave_Day = 0;
					this.Limit_Leave_List_RID = "";
					if (component.data && component.data.value) {
						this.Limit_Leave_Day = component.data.day;
						this.Limit_Leave_List_RID = component.data.value;
					}
					this.setCanUseDaysLable()
				}
				//切换请假方式
				if (component.id == "fhisLeaveHeaderEntity_leave_Way" || like_fhisLeaveHeaderEntity_leave_Way) {
					//全天
					if (data.value == "1") {
						this.$refs.form.setHide("fhisLeaveHeaderEntity_from_Time")
						this.$refs.form.setHide("fhisLeaveHeaderEntity_to_Time")

						const shift_Item_List = await this.HTTP_POST({
							url: '/hrattf/hrattF001/LeaveGetDataAndCheck',
							data: {
								"actionType": "getShift",
								"emp_No": this.emp_no,
							}
						});
						//取班次开始时间和结束时间
						this.setFormValue("fhisLeaveHeaderEntity_from_Time", shift_Item_List[0].from_time)
						this.setFormValue("fhisLeaveHeaderEntity_to_Time", shift_Item_List[shift_Item_List.length - 1].to_time)
					}
					//非全天
					if (data.value == "4") {
						this.$refs.form.setHide("fhisLeaveHeaderEntity_from_Time", false)
						this.$refs.form.setHide("fhisLeaveHeaderEntity_to_Time", false)
						this.setFormValue("fhisLeaveHeaderEntity_from_Time", "")
						this.setFormValue("fhisLeaveHeaderEntity_to_Time", "")
					}
				}
				//请假开始日期和请假结束日期
				if (["fhisLeaveHeaderEntity_from_Date", "fhisLeaveHeaderEntity_to_Date"].includes(component.id)) {
					if (data == "") {
						this.setReadonly("fhisLeaveHeaderEntity_to_Date", false)
					} else {
						this.LOADING(this.$t("正在查询…"))
						let from_Date = postData.fhisLeaveHeaderEntity.from_Date
						let to_Date = postData.fhisLeaveHeaderEntity.to_Date
						let leave_Way = postData.fhisLeaveHeaderEntity.leave_Way
						let leave_List_RID = postData.fhisLeaveHeaderEntity.leave_List_RID
						//如果是全天请假，则重新获取当天的班次时间
						if (leave_Way == 1) {
							if (from_Date != to_Date) {
								const shift_Item_List = await this.HTTP_POST({
									url: '/hrattf/hrattF001/LeaveGetDataAndCheck',
									data: {
										"actionType": "getShift",
										"emp_No": this.emp_no,
										"From_Date": data
									}
								});

								if (component.id == "fhisLeaveHeaderEntity_from_Date") {
									this.setFormValue("fhisLeaveHeaderEntity_from_Time", shift_Item_List[0].from_time)
									this.setFormValue("fhisLeaveHeaderEntity_to_Time", shift_Item_List[shift_Item_List.length - 1]
										.to_time)
								} else {
									this.setFormValue("fhisLeaveHeaderEntity_to_Time", shift_Item_List[shift_Item_List.length - 1]
										.to_time)
								}
							}
						}

						if (component.id == "fhisLeaveHeaderEntity_from_Date") {
							if (postData.fhisLeaveHeaderEntity.limit_Leave_List_RID) {
								//重新计算受限请假天数
								await this.allowLeave(from_Date)
							}

							from_Date = data;
							if (this.default_Day != -1) {
								let date = moment(from_Date);
								let addDay = this.default_Day > 0 ? this.default_Day - 1 : this.default_Day;
								date.add(addDay, 'day');
								to_Date = date.format('YYYY-MM-DD');

								this.setFormValue("fhisLeaveHeaderEntity_to_Date", to_Date)

								const shift_Item_List = await this.HTTP_POST({
									url: '/hrattf/hrattF001/LeaveGetDataAndCheck',
									data: {
										"actionType": "getShift",
										"emp_No": this.emp_no,
										"From_Date": to_Date
									}
								});
								this.setFormValue("fhisLeaveHeaderEntity_to_Time", shift_Item_List[shift_Item_List.length - 1].to_time)

								this.setCanUseDaysLable(this.default_Day)
								this.isShowCanUseDay = true

								this.setReadonly("fhisLeaveHeaderEntity_to_Date")
							}
						} else {
							to_Date = data;
						}
						let totalDays, diffDate
						let myDate_1 = Date.parse(from_Date)
						let myDate_2 = Date.parse(to_Date)
						// 将两个日期都转换为毫秒格式，然后做差
						diffDate = Math.abs(myDate_1 - myDate_2) // 取相差毫秒数的绝对值
						totalDays = Math.floor(diffDate / (1000 * 3600 * 24)) // 向下取整
						console.log(467, totalDays)
						this.setFormValue("fhisLeaveHeaderEntity_leave_Day", totalDays)
						this.HIDE_LOADING()
					}
				}
				//请假开始结束时间
				if (["fhisLeaveHeaderEntity_from_Time", "fhisLeaveHeaderEntity_to_Time"].includes(component.id)) {
					let id = component.id;
					let value = data;
					value = value.replace(/[^0-9]/g, '')
					if (value.length == 4) {
						let hour = value.substr(0, 2);
						let min = value.substr(value.length - 2, 2);
						value = hour + ":" + min;
						if (hour > 48 || min >= 60 || (hour == 48 && min > 0)) {
							value = "";
						}
					} else {
						value = "";
					}
					this.setFormValue(id, value);
				}
				//选择部门经理
				if (component.id == "fhisLeaveHeaderEntity_m_Approve_UserID") {
					if (data) {
						this.setFormValue("fhisLeaveHeaderEntity_m_Is_E", data.ise)
						if (data.ise == 1) {
							this.$refs.form.setHide("fhisLeaveHeaderEntity_e_Approve_UserID")
							this.$refs.form.setRequired("fhisLeaveHeaderEntity_e_Approve_UserID", false)
						}
					}
					if (!this.need_E) {
						this.setFormValue("fhisLeaveHeaderEntity_m_Is_E", 0)
					}
				}

				//请假时间>15天,则需要E级批核
				if (this.$refs.form.getValue("fhisLeaveHeaderEntity_m_Is_E") == 0 &&
					this.$refs.form.getValue("fhisLeaveHeaderEntity_leave_Day") > 15 && 
					this.need_E) {
					this.$refs.form.setHide("fhisLeaveHeaderEntity_e_Approve_UserID", false)
					this.$refs.form.setRequired("fhisLeaveHeaderEntity_e_Approve_UserID")
				} else {
					//否则不需要
					this.$refs.form.setHide("fhisLeaveHeaderEntity_e_Approve_UserID")
					this.$refs.form.setRequired("fhisLeaveHeaderEntity_e_Approve_UserID", false)
				}
			},
			async loadFormData(keyValue) {
				const _formData = await this.HTTP_GET({
					url: `/hrattf/hrattf001/${keyValue}`,
					errorTips: this.$t("获取数据失败")
				})
				if (!_formData) {
					return null
				}
				const formData = _formData
				return formData
			},
			async getForm() {
				const _postData = await this.$refs.form.getFormValue()
				const postData = {}
				postData.fhisLeaveHeaderEntity = this.GET_FORMDATA(_postData, 'fhisLeaveHeaderEntity')
				postData.fhisLeaveDetailList = _postData.fhisLeaveDetailList

				//是否已经全部上传附件
				if (this.editMode || this.isUpload) {
					let Input_Voucher_Type = [];
					for (let i = 0; i < postData.fhisLeaveDetailList.length; i++) {
						if (postData.fhisLeaveDetailList[i].file_Url != "") {
							if (!Input_Voucher_Type.includes(postData.fhisLeaveDetailList[i].voucher_Type)) {
								Input_Voucher_Type.push(postData.fhisLeaveDetailList[i].voucher_Type);
							}
						}
					}
					console.log(542, Input_Voucher_Type, this.need_input_Voucher_Type, this.not_must_Voucher_Type)
					let is_All_Voucher = true;
					for (let i = 0; i < this.need_input_Voucher_Type.length; i++) {
						if (!Input_Voucher_Type.includes(this.need_input_Voucher_Type[i]) &&
							!this.not_must_Voucher_Type.includes(this.need_input_Voucher_Type[i])) {
							is_All_Voucher = false;
						}
					}

					console.log(550, is_All_Voucher)
					postData.fhisLeaveHeaderEntity.is_All_Voucher = is_All_Voucher;
				}
				console.log(551, postData)
				return postData
			},
			async saveForm() {
				this.LOADING(this.$t("正在提交…"))
				const verifyResult = await this.formValidate()
				if (verifyResult) {
					const res = await this.handleSave({
						keyValue: this.keyValue,
						isEdit: this.isUpdate
					})
					this.HIDE_LOADING()
					if (res) {
						this.EMIT(`custom-list-change-${this.listModuleId}`)
						this.NAV_BACK()
						this.TOAST(this.$t("保存成功"), 'success')
					}
				} else {
					this.HIDE_LOADING()
				}
			},
			async handleSave({
				keyValue,
				isEdit /*,code,node*/
			}) {
				//isEdit 是否更新数据, keyValue 流程中相当于流程processId,code 表示流程中的操作码,node 流程节点
				const postData = await this.getForm()
				let res
				if (this.isUpdate) {
					keyValue = this.keyValue;
					if (!keyValue) {
						keyValue = postData.fhisLeaveHeaderEntity.rid;
					}
					res = await this.HTTP_PUT({
						url: `/hrattf/hrattf001/${keyValue}`,
						data: postData,
						errorTips: this.$t("表单提交保存失败")
					})
				} else {
					// 作为流程表单的时候用来关联流程字段
					postData.fhisLeaveHeaderEntity.rid = keyValue
					res = await this.HTTP_POST({
						url: `/hrattf/hrattf001`,
						data: postData,
						errorTips: this.$t("表单提交保存失败")
					})
				}
				if (res) {
					this.isUpdate = true
				}
				return res
			},
			async handleDelete() {
				if (!(await this.CONFIRM(this.$t("删除项目"), this.$t("确定要删除该项吗？"), true))) {
					return
				}
				this.LOADING(this.$t("提交删除中…"))
				const success = await this.HTTP_DELETE({
					url: "/hrattf/hrattf001/" + this.keyValue,
					errorTips: this.$t("删除失败")
				})
				this.HIDE_LOADING()
				if (success) {
					this.EMIT(`custom-list-change-${this.listModuleId}`)
					this.NAV_BACK()
					this.TOAST(this.$t("删除成功"), 'success')
				}
			},
			setFormValue(path, value) {
				this.$refs.form.setValue({
					path: path,
					value: value
				});
			},
			setReadonly(component, readonly = true) {
				let config = this.baseComponents(component).config
				config.readonly = readonly

				this.$refs.form.setComponent(component, {
					"config": config
				});
			},
			components(id) {
				return this.formScheme.components[this.componentList.indexOf(id)]
			},
			baseComponents(id) {
				return this.formBaseScheme.components[this.componentList.indexOf(id)]
			},
			setCanUseDaysLable(Days = null) {
				let config = this.canUseDaysConfig
				config.content = this.$t("可用") +
					(Days ? Days : this.Limit_Leave_Day) +
					this.$t("天") +
					(this.Leave_Allow_Code == "AL" ? this.leaveYearTip : ""),

					this.$refs.form.setComponent("6961176811747099244207", {
						type: "label",
						config: config,
					});
			},
			async allowLeave(from_Date, isLoad = false) {
				if (!isLoad) {
					this.LOADING(this.$t("正在查询…"))
				}
				const Limit_Leave_DayList = await this.HTTP_POST({
					url: '/hrattf/hrattF001/LeaveGetDataAndCheck',
					data: {
						"actionType": "getLimit_Leave_Day",
						"Emp_No": this.emp_no,
						"From_Date": from_Date,
						"category_Code": this.Leave_Allow_Code,
						"record_Code": new Date().getFullYear()
					}
				});
				let fhisLeaveHeaderEntity_year_Data = [];
				//没有育儿假的情况
				if (Limit_Leave_DayList.length == 1 && Limit_Leave_DayList[0].err_msg && !isLoad) {
					this.Limit_Leave_Day = 0;
					this.Limit_Leave_List_RID = "";
					this.setFormValue("fhisLeaveHeaderEntity_leave_List_RID", "")
					this.setFormValue("fhisLeaveHeaderEntity_leave_Type", null)
					this.SET_DATA("fhisLeaveHeaderEntity_leave_List_RID", null);
					this.setCanUseDaysLable()
					this.TOAST(Limit_Leave_DayList[0].err_msg, "error");
				} else {
					let Limit_Leave_Day0;
					for (let i = 0; i < Limit_Leave_DayList.length; i++) {
						let label = Limit_Leave_DayList[i].record_code;
						let day = Limit_Leave_DayList[i].entitle_days - Limit_Leave_DayList[i].used_days +
							Limit_Leave_DayList[i].other_add_days;
						if (i == 0) {
							Limit_Leave_Day0 = day
						}
						if (this.Leave_Allow_Code == "91") {
							label += " " + Limit_Leave_DayList[i].description;
						}
						fhisLeaveHeaderEntity_year_Data[i] = {
							"label": label,
							"value": Limit_Leave_DayList[i].rid,
							"day": day
						};
					}
					this.SET_DATA("fhisLeaveHeaderEntity_leave_List_RID", fhisLeaveHeaderEntity_year_Data);
					if (Limit_Leave_DayList.length == 1 && !isLoad) {
						this.Limit_Leave_Day = Limit_Leave_Day0;
						this.Limit_Leave_List_RID = Limit_Leave_DayList[0].rid;
						this.setCanUseDaysLable()
						this.setFormValue("fhisLeaveHeaderEntity_leave_List_RID", this.Limit_Leave_List_RID)
						this.setReadonly("fhisLeaveHeaderEntity_leave_List_RID")
					} else {
						this.setReadonly("fhisLeaveHeaderEntity_leave_List_RID", false)
					}
					this.$refs.form.setHide("fhisLeaveHeaderEntity_leave_List_RID", false)
					this.$refs.form.setRequired("fhisLeaveHeaderEntity_leave_List_RID")
					this.$refs.form.setComponent("fhisLeaveHeaderEntity_leave_List_RID", {
						options: fhisLeaveHeaderEntity_year_Data,
					});
				}
				if (!isLoad) {
					this.HIDE_LOADING()
				}
			},
			//验证
			async formValidate() {
				let verifyResult = await this.$refs.form.validate();
				let verifyWrite = false;
				const postData = await this.getForm();
				console.log(680, postData.fhisLeaveHeaderEntity)
				if (postData.fhisLeaveHeaderEntity.signature) {
					verifyWrite = true;
					const base64Regex = /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/;
					if (!base64Regex.test(postData.fhisLeaveHeaderEntity.signature)) {
						verifyWrite = false;
					}
				}
				this.$refs.form.setComponent("fhisLeaveHeaderEntity_signature", {
					isvalidate: true,
				})

				console.log(1200, verifyResult, verifyWrite)
				if (!(verifyResult && verifyWrite)) {
					this.TOAST(this.$t("请输入必填项 "), 'error');
					return
				}

				let toastMessage = "";
				console.log(736, postData.fhisLeaveDetailList, this.mustUpVoucher)
				for (let i = 0; i < postData.fhisLeaveDetailList.length; i++) {
					if (!postData.fhisLeaveDetailList[i].voucher_Type) {
						toastMessage = this.$t("凭证类型不能为空！");
					} else if (!postData.fhisLeaveDetailList[i].file_Url && this.mustUpVoucher) {
						toastMessage = this.$t("请上传所有类型凭证！");
					}
				}
				if (toastMessage) {
					this.TOAST(toastMessage, "error");
					return
				}

				if (this.mustUpVoucher && !postData.fhisLeaveHeaderEntity.is_All_Voucher) {
					this.TOAST(this.$t("请上传所有类型凭证！"), "error");
					return
				}

				if (verifyResult && verifyWrite) {
					if (this.Limit_Leave_List_RID == null || this.Limit_Leave_List_RID == "") {
						this.Limit_Leave_List_RID = postData.fhisLeaveHeaderEntity.Leave_List_RID;
					}

					const ValidateLeaveFormatData = await this.HTTP_POST({
						url: '/hrattf/hrattF001/LeaveGetDataAndCheck',
						data: {
							"actionType": "ValidateLeaveFormat_" + (this.GET_LANG_TYPES().value == "english" ? "EN" : "CN"),
							"Emp_No": this.emp_no,
							"Leave_Note_No": postData.fhisLeaveHeaderEntity.Leave_Note_No,
							"Approve_Status": postData.fhisLeaveHeaderEntity.Approve_Status,
							"Leave_Type": postData.fhisLeaveHeaderEntity.leave_Type,
							"From_Date": postData.fhisLeaveHeaderEntity.from_Date,
							"From_Time": postData.fhisLeaveHeaderEntity.from_Time,
							"From_Date_Minute": postData.fhisLeaveHeaderEntity.From_Date_Minute,
							"To_Date": postData.fhisLeaveHeaderEntity.to_Date,
							"To_Time": postData.fhisLeaveHeaderEntity.to_Time,
							"To_Date_Minute": postData.fhisLeaveHeaderEntity.To_Date_Minute,
							"Leave_List_RID": this.Limit_Leave_List_RID,
							"Industrial_injury_Date": postData.fhisLeaveHeaderEntity.Industrial_injury_Date,
							"Cancel_Limit_Flag": 0,
							"Not_Continu_Leave_Flag": postData.fhisLeaveHeaderEntity.Not_Continu_Leave_Flag
						}
					});
					if (ValidateLeaveFormatData.length > 0) {
						if (ValidateLeaveFormatData[0].err_msg != "" && ValidateLeaveFormatData[0].err_msg != ";") {
							this.TOAST(ValidateLeaveFormatData[0].err_msg, 'error');
							return false;
						}
					}
					// if (this.DEV) {
					// 	return false;
					// } else {
					return true;
					// }
				}
				return false;
			},
			// 流程提交
			async wf_submit() {
				if (this.isSubmit) {
					this.TOAST(this.$t('提交中…'))
				} else {
					this.isSubmit = true;
					this.LOADING(this.$t("正在提交…"))
					if (await this.formValidate()) {
						if (await this.handleSave({
								keyValue: this.wfProcessId,
								isEdit: this.isUpdate
							})) {
							const postData = await this.getForm();

							let leave_type = ""
							let LeaveTypeData = this.GET_DATA(this.components("fhisLeaveHeaderEntity_leave_Type").config.dataCode);
							for (let i = 0; i < LeaveTypeData.length; i++) {
								if (LeaveTypeData[i].leave_type == postData.fhisLeaveHeaderEntity.leave_Type) {
									leave_type = LeaveTypeData[i].leave_description;
								}
							}

							let wf_title = leave_type + " " +
								postData.fhisLeaveHeaderEntity.from_Date.substr(0, 10) + " " +
								this.$t("至") +
								postData.fhisLeaveHeaderEntity.to_Date.substr(0, 10) + " "

							const wfData = {
								processId: this.wfProcessId,
								schemeCode: this.wfCode,
								userId: this.loginUser.f_UserId,
								title: wf_title
							}

							if (!this.wfIsDraft) {
								await this.HTTP_POST({
									url: '/workflow/process/draft',
									data: wfData
								})
								this.wfCode = ''
								wfData.schemeCode = ''
								this.wfIsDraft = true
							}
							console.log(this.wfCurrentNode, 'this.wfCurrentNode')

							let url = '/workflow/process/create';
							if (this.mode == "wf_again") {
								url = "/workflow/process/CreateAgain";
							}
							// 获取接下来节点审核人
							if (this.wfCurrentNode.isNextAuditor) {
								const nodeUserMap = await this.HTTP_GET({
									url: '/workflow/process/nextusers',
									params: {
										processId: this.wfProcessId,
										nodeId: this.wfCurrentNode.id,
									}
								})
								const nodeUsers = []
								for (let key in nodeUserMap) {
									const nodeUserItem = nodeUserMap[key]
									if (nodeUserItem.length > 1) {
										nodeUsers.push({
											name: this.wfData.find(t => t.id == key).name,
											id: key,
											options: nodeUserItem.map(t => {
												return {
													value: t.id,
													label: t.name
												}
											})
										})
									}
								}
								if (nodeUsers.length > 0 || this.wfCurrentNode.isCustmerTitle) {
									this.ONCE('learun-wfsubmit-info', data => {
										wfData.nextUsers = data.nextUsers
										wfData.title = wf_title;
										setTimeout(async () => {
											this.LOADING(this.$t("正在提交…"))
											const res = await this.HTTP_POST({
												url: url,
												data: wfData
											})

											this.TOAST(this.$t("流程提交成功"), 'success')
											setTimeout(async () => {
												this.EMIT(`learun-workflow-list-change`)
												this.EMIT(`custom-list-change-${this.listModuleId}`)
												this.NAV_BACK()
											}, 500)
										}, 10)
									})
									this.HIDE_LOADING()
									this.NAV_TO('/pages/workflow/common/learun-wfsubmit-info', {
										nodeUsers,
										isCustmerTitle: this.wfCurrentNode.isCustmerTitle
									}, true)
									this.isSubmit = false;
									return
								}
							} else if (this.wfCurrentNode.isCustmerTitle) {
								this.ONCE('learun-wfsubmit-info', data => {
									wfData.title = wf_title;
									setTimeout(async () => {
										this.LOADING(this.$t("正在提交…"))
										const res = await this.HTTP_POST({
											url: url,
											data: wfData
										})
										this.TOAST(this.$t("流程提交成功"), 'success')
										setTimeout(async () => {
											this.EMIT(`learun-workflow-list-change`)
											this.EMIT(`custom-list-change-${this.listModuleId}`)
											this.NAV_BACK()
										}, 500)
									}, 10)
								})
								this.HIDE_LOADING()
								this.NAV_TO('/pages/workflow/common/learun-wfsubmit-info', {
									isCustmerTitle: this.wfCurrentNode.isCustmerTitle
								}, true)
								this.isSubmit = false;
								return
							}
							const res = await this.HTTP_POST({
								url: url,
								data: wfData
							})

							this.HIDE_LOADING()
							if (res) {
								this.TOAST(this.$t("流程提交成功"), 'success')
								setTimeout(async () => {
									this.EMIT(`custom-list-change-${this.listModuleId}`)
									this.EMIT(`learun-workflow-list-change`)
									this.NAV_BACK()
								}, 500)
							}
						} else {
							this.HIDE_LOADING()
						}
					} else {
						this.HIDE_LOADING()
					}
					this.isSubmit = false;
				}
			},

			//流程处理
			async wf_action2(btn) {
				this.LOADING(this.$t("表单保存…"))
				const postData = await this.getForm();
				console.log(1026, btn, postData.fhisLeaveHeaderEntity.is_All_Voucher)
				if (btn == "agree2") {
					if (!postData.fhisLeaveHeaderEntity.is_All_Voucher) {
						this.TOAST(this.$t("请上传所有类型凭证！"), "error");
						return
					}
					let toastMessage = "";
					for (let i = 0; i < postData.fhisLeaveDetailList.length; i++) {
						if (!postData.fhisLeaveDetailList[i].voucher_Type) {
							toastMessage = this.$t("凭证类型不能为空！");
						} else if (!postData.fhisLeaveDetailList[i].file_Url) {
							toastMessage = this.$t("请上传所有类型凭证！");
						}
					}
					if (toastMessage) {
						this.TOAST(toastMessage, "error");
						return
					}
				}
				let isformValidate = true;
				let res = null
				if (isformValidate) {
					if ("agree2" == btn) {
						if (await this.handleSave({
								keyValue: this.wfProcessId,
								isEdit: this.isUpdate
							})) {
							res = await this.HTTP_PUT({
								url: `/workflow/process/audit/${this.wfTaskId}`,
								data: {
									code: ("agree", "agree2").includes(btn) ? "agree" : "disagree",
									name: ("agree", "agree2").includes(btn) ? "同意" : "驳回",
									des: ("agree", "agree2").includes(btn) ? "Approve" : "Reject"
								},
								errorTips: this.$t("批核失败")
							})
							this.HIDE_LOADING()
						}
					} else {
						res = await this.HTTP_PUT({
							url: `/workflow/process/audit/${this.wfTaskId}`,
							data: {
								code: ("agree", "agree2").includes(btn) ? "agree" : "disagree",
								name: ("agree", "agree2").includes(btn) ? "同意" : "驳回",
								des: ("agree", "agree2").includes(btn) ? "Approve" : "Reject"
							},
							errorTips: this.$t("批核失败")
						})
						this.HIDE_LOADING()
					}
				}
				console.log(998, res)
				if (res) {
					this.TOAST(this.$t("操作成功！", "success"));
					setTimeout(async () => {
						this.EMIT(`learun-workflow-list-change`)
						this.EMIT(`custom-list-change-${this.listModuleId}`)
						this.NAV_BACK()
					}, 500)
				}
			},

			async wf_draft() {
				this.LOADING(this.$t("正在保存…"))
				if (await this.handleSave({
						keyValue: this.wfProcessId,
						isEdit: this.isUpdate
					})) {
					const wfData = {
						processId: this.wfProcessId,
						schemeCode: this.wfCode,
						userId: this.loginUser.f_UserId,
						title: ''
					}

					const res = await this.HTTP_POST({
						url: '/workflow/process/draft',
						data: wfData
					})
					this.wfCode = ''
					this.wfIsDraft = true
					this.HIDE_LOADING()
					if (res) {
						this.NAV_BACK()
						this.TOAST(this.$t("保存成功"), 'success')
					}
				} else {
					this.HIDE_LOADING()
				}
			},
		},
		data() {
			return {
				moduleId: '',
				listModuleId: '',
				mode: '',
				keyValue: '',
				isUpdate: false,
				editMode: false,
				ready: false,
				formData: {},
				formScheme: {},
				componentList: [],
				canUseDaysConfig: {},
				emp_no: "",
				Leave_Allow_Code: "",
				need_E: false,
				default_Day: -1,
				mustUpVoucher: false,
				leaveYearTip: "",
				isSubmit: false,
				isUpload: false, //是否是上传附件流程
				need_input_Voucher_Type: [], //需要上传附件的类型
				not_must_Voucher_Type: [],
			}
		}
	}
</script>