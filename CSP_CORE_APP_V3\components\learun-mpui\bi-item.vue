<template>
	<view>
		<!-- 轮播图片 -->
		<view v-if="['app-imgswiper'].includes(item.type)"  class="learun-panel" :style="{'height':69 * config.h/6 + 'px'}">
			<swiper :style="{'height':69 * config.h/6 + 'px'}" autoplay>
			  <swiper-item :style="{'height':69 * config.h/6 + 'px'}" v-for="(item,index) in config.list" :key="index" >
			    <image :src="item.imgurl?`${API}/system/annexesfile/${item.imgurl}?token=${token}`:''" mode="aspectFill" style="height:100%; width:100%;"></image>
			  </swiper-item>
			</swiper>
		</view>
		<!-- 数据面板 -->
		<uni-card v-else-if="['app-databoard'].includes(item.type)"
			:title="config.name"
			padding="0"  
			margin="8px" 
			:border="false" 
			:is-shadow="false" 
			@click="gotoModule(config.moduleId)"
			>
			<view class="learun-data-databoard" >
				<view class="learun-data-databoard__item2"
					v-for="(item) in config.list" :key="item.id"
					@click.stop="gotoModule(item.moduleId || config.moduleId)"
				 >
					<view class="num"><text>{{getDataboardValue(item)}}</text></view>
					<view class="label" >{{$t(item.name)}}</view>
				</view>
			</view>
		</uni-card>
		
		<!-- 信息列表 -->
		<uni-card
			v-else-if="['app-datalist'].includes(item.type)"
			@click="gotoModule(config.moduleId)"
			:title="config.name"
			padding="0"  
			margin="8px" 
			:border="false" 
			:is-shadow="false" >
			<learun-table
				:columns="dataListColumns"
				:dataSource="dataList"
				:isPage="false"
				:showHeader="false"
				@rowClick="dataRowClick"
			/>
			<!-- <view @click.stop="()=>{}">
				<uni-list>
				    <uni-list-item
						clickable @click="dataRowClick(row,config)" 
						v-for="(row,rowIndex) in getDataList(config)"
						:title="$t(row[config.titleKey]) || ''" 
						:rightText="row[config.subTitleKey] || ''"
						:key="rowIndex"
						>
					</uni-list-item>
				</uni-list>
				
			</view> -->
		</uni-card>
		
		<!-- 我的任务 -->
		<uni-card v-else-if="['app-mytask'].includes(item.type)" 
			 :title="config.name" padding="0"  margin="8px" :border="false" :is-shadow="false" >
			<view class="learun-data-databoard"  >
				<view class="learun-data-databoard__item" @click="taskClick(0)" >
					<view class="num" ><text>{{getMyData({type:'nCompletedNum'})}}</text></view>
					<view class="label" >{{$t('待办任务')}}</view>
				</view>
				<view class="learun-data-databoard__item" @click="taskClick(1)" >
					<view class="num" ><text>{{getMyData({type:'delegateNum'})}}</text></view>
					<view class="label" >{{$t('委托任务')}}</view>
				</view>
				<view class="learun-data-databoard__item" @click="taskClick(2)">
					<view class="num" ><text>{{getMyData({type:'completedNum'})}}</text></view>
					<view class="label" >{{$t('已办任务')}}</view>
				</view>
			</view>
		</uni-card>
		
		<!-- 我的代办任务列表 -->
		<uni-card
			v-else-if="['app-mytasklist'].includes(item.type)"
			@click="taskClick(0)" 
			:title="config.name"
			:extra="$t('更多')"
			padding="0"  
			margin="8px" 
			:border="false" 
			:is-shadow="false" >
			<view @click.stop="()=>{}">
				<uni-list>
				    <uni-list-item
							clickable @click="taskListClick(task, 'audit')" 
							v-for="task in getMyData({type:'taskList'})"
							:title="task.f_ProcessTitle" 
							:rightText="TABLEITEM_DATEFORMAT(task.f_CreateDate).toString()"
							:key="task.f_Id"
							>
						</uni-list-item>
				</uni-list>
			</view>
		</uni-card>

		<!-- 柱状折线混合图 -->
		<uni-card 
			v-else-if="['app-chartlinebar'].includes(item.type)"
			:title="$t(config.name)"
			padding="0" 
			margin="8px" 
			
			:border="false" 
			:is-shadow="false" >
			<uni-data-checkbox mode="button" v-model="activeSelect" :localdata="chartOptions"></uni-data-checkbox>
			<uni-card v-if="config.isSummary" :border="false" style="margin-top:20px !important">
				<view style="display: flex;justify-content: space-between">
					<view style="color: #1677ff; fontSize: 30px">{{summaryNum.toFixed(2)}}{{config.summaryUnit}}</view>
					<view>{{config.summaryLabel}}</view>
				</view>
			</uni-card>
			<qiun-data-charts :opts="mixOptions" type="mix" :chartData="getMixData()" />
		</uni-card>
		
		<!-- 饼图 -->
		<uni-card 
			v-else-if="['app-chartpie'].includes(item.type)"
			:title="$t(config.name)"
			padding="0" 
			margin="8px"
			:border="false"
			:is-shadow="false" 
		>
			<qiun-data-charts 
				:opts="PieOpts"
			 	:type="pieType" :chartData="getPieData(config)" 
			/>
		</uni-card>
		
	</view>
</template>

<script>
	export default {
	  name: 'learun-bi-item',
		props:{
			item:{},
			getData:Function
		},
		computed:{
			config() {
				return this.item.config;
			},
			token(){
				return this.GET_GLOBAL('token')
			},
			dataListColumns() {
				let data = this.config.columns.map(t=> {
					return {...t, rowid: t.prop}
				})
				return data;
			},
			dataList() {
				if(!this.config.dataCode) {
					return []
				}
				return this.getMyData({type:'datasource',code: this.config.dataCode})
			},
			pieType() {
				let type = 'ring';
				let { series } = this.config.echarts;
				if (series.pie.roseType == 'area' || series.pie.roseType == 'roseType') {
					type = 'rose'
				}
				return type;
			},
			PieOpts() {
				let { series, color, legend } = this.config.echarts;
				let options = {
					extra: {},
					legend: {},
					fontSize: series.label.fontSize,
					fontColor: series.label.color,
					dataLabel: series.label.show,
				}
				let extraTypeConfig = {}
				if (this.pieType == 'ring') {
					if (series.pie.radiusN < series.pie.radiusW) {
						extraTypeConfig.ringWidth= series.pie.radiusW - series.pie.radiusN;
						extraTypeConfig.customRadius = series.pie.radiusW;
					}
				} else {
					extraTypeConfig.minRadius = series.pie.radiusN;
					extraTypeConfig.type = series.pie.roseType
				}
				
				options.extra[this.pieType] = extraTypeConfig;

				// 图例设置
				options.legend = this.legendConfig;

				// 颜色设置
				const myColor = color?.filter((t) => t.v).map((t) => t.v);
				if (myColor && myColor.length > 0) {
				options.color = myColor;
				}
				return options
			},
			legendConfig() {
				let { legend } = this.config.echarts;
				
				let position = 'top';
				let float = 'left';
				if (['topLeft', 'topRight', 'topCenter'].includes(legend.position)) {
					position = 'top'
				} else {
					position = 'bottom'
				}
				if (['topLeft', 'bottomLeft'].includes(legend.position)) {
					float = 'left'
				} else if (['topRight', 'bottomRight'].includes(legend.position)) {
					float = 'right'
				} else {
					float = 'center'
				}

				let legend2 = {
					show: legend.show,
					position,
					float,
					padding: legend.padding,
					fontColor: legend.textStyle.color
				};
				
				return legend2;
			},
			chartOptions() {
				if (this.config.dataList.length <= 1) {
					return [];
				}
				const res = [];
				this.config.dataList.forEach((item, index) => {
					if (item.name) {
						res.push({
							text: item.name,
							value: index,
						});
					}
				});
				this.activeSelect = res[0].value;
				return res;
			},
			summaryNum (){
				let res = 0;
				const { valueKeys, dataCode } = this.config.dataList[this.activeSelect] || {};
				const data = this.getMyData({type:'datasource',code:dataCode})
				valueKeys.forEach((valueItem) => {
					const { k } = valueItem;
					if (k) {
						data.forEach((item) => {
							res += item[k];
						});
					}
				});
				return res;
			},
			mixOptions() {
    			const { series, yAxis, xAxis, tooltip, grid } = this.config.echarts;

				let options = {
					rotate: this.config.isX,
					rotateLock: this.config.isX ? true: false,
					extra:{
						mix: {},
						tooltip: {
							showBox: !(tooltip.trigger == 'none'),
							splitLine: ['cross', 'line'].includes(tooltip.axisPointer.type),
							horizentalLine: tooltip.axisPointer.type == 'cross',
						},
					},
					xAxis: {
						title: xAxis.name || '',
						titleFontSize: xAxis.nameFontSize || 13,
						titleFontColor: xAxis.nameColor || '#666666',
						axisLine: xAxis.axisLineShow,
						axisLineColor: xAxis.axisLineColor || '#CCCCCC',
						fontColor: xAxis.axisLabelColor || '#666666'
					},
					yAxis: {
						data: []
					},
					fontSize: series.label.fontSize,
					fontColor: series.label.color,
					dataLabel: series.label.show,
				}
				const { valueKeys } = this.config.dataList[this.activeSelect] || {};
				let barConfig = {
					customColor: []
				};
				let lineConfig = {};
    			valueKeys.forEach((valueItem, index) => {
						const { config } = valueItem;
						if (config.type == 'bar') {
							barConfig.type = config.isStack ? 'stack': 'group';
							if (config.barWidth) {
							barConfig.width = config.barWidth;
							}
							if (config.colorEnd || config.color) {
								barConfig.linearType = 'custom';
								if (config.colorEnd && this.containsRgbOrRgba(config.colorEnd)) { // rgba需在转化为没有透明度的十六进制
									barConfig.customColor.push(this.rgbToHex(config.colorEnd));
								} else if (config.color && this.containsRgbOrRgba(config.color)) { // rgba需在转化为没有透明度的十六进制
									barConfig.customColor.push(this.rgbToHex(config.color));
								} 
								else{
									barConfig.customColor.push(this.color[index])
								}
							}
							
						}
					});
				options.extra.mix.column = barConfig;
				options.extra.mix.line = lineConfig;
				options.extra.mix.area = lineConfig;
				// y轴设置
				let yAxisList = this.getYConfig(yAxis)
				
				options.yAxis.data = yAxisList;
				// 指示器
				if (tooltip.axisPointer.lineStyle.color && this.containsRgbOrRgba(tooltip.axisPointer.lineStyle.color)) {
					options.extra.tooltip = this.rgbToHex(tooltip.axisPointer.lineStyle.color);
				}
				
				// 图例设置
				options.legend = this.legendConfig;
				// 边距设置
				options.padding = [grid.top, grid.right, grid.bottom, grid.left]
				return options;
			}
		},
		data(){
			return {
				isLoading:false,
				activeSelect: '0',
				color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
			}
		},

		methods:{
			taskClick(type){
				this.TO_MODULE_URL(`/pages/workflow/mytask/list`,{type})
			},
			taskListClick(item, type){
				// 点击普通的流程项
				if (item.f_Type == 4) {
					type = 'again'
				}
				this.NAV_TO(`/pages/workflow/mytask/single?type=${type}`, item)
			},
			gotoModule(id){
				if(id){
					this.TO_MODULE_ID(id)
				}
			},
			dataRowClick(){
				//this.NAV_TO(`./single?type=look`, {key:this.primaryKey,keyValue:this.editRow[`${this.primaryKey.toLowerCase()}0`],title:`${this.pageTitle}【编辑】`,formId:this.formId,formScheme:this.formScheme,dataSource:this.learun_dataSource}, true)
			},
			
			
			getMyData(params){
				if(this.getData){
					return this.getData(params)
				}
				else{
					return null
				}
			},
			getDataboardValue({dataCode,dataValueKey}){
				const data = this.getMyData({type:'datasource',code:dataCode})
				if(data && data.length > 0){
					return data[0][dataValueKey]
				}
				else{
					return 0
				}
			},
			
			getMixData() {
				const { labelKey, valueKeys, dataCode } = this.config.dataList[this.activeSelect] || {};
    			const { yAxis } = this.config.echarts;
				const columnData = {
					categories: [],
					series: [],
					rotate:this.config.isX
				}
				const data = this.getMyData({type:'datasource',code:dataCode})
				columnData.categories = data.map(t=>t[labelKey])

    			if (labelKey) {
					valueKeys.forEach((valueItem) => {
						const { n, k, config } = valueItem;
						if (k) {
							// 指标绑定字段
							const seriesItem = {
								name: n|| '',
								data: [],
								type: config.type == 'bar'? 'column': config.isArea ? 'area' : 'line',
							};
							if (config.y && yAxis.length > config.y) {
								seriesItem.index = config.y;
							}
							if (config.type == 'line' && config.lineSmooth) {
								seriesItem.style = "curve"
							}
							if (config.color) { // rgba需在转化为没有透明度的十六进制
								if (this.containsRgbOrRgba(config.color)) {
									seriesItem.color = this.rgbToHex(config.color);
								} else {
									seriesItem.color = config.color;
								}
							}
							if(k){
								const yList = []
								data.forEach(item=>{
									yList.push(item[k])
								})
								seriesItem.data = yList	
							}
							columnData.series.push(seriesItem);
						}
					});
					}
				return columnData;
			},
			getPieData({dataCode,labelKey,valueKey}){
				const pieData = {
					"series": [{
						"data": []
					}]
				}
				const data = this.getMyData({type:'datasource',code:dataCode})
				if(labelKey && valueKey){
					pieData.series[0].data = data.map(t=>{ return {name:t[labelKey],value:t[valueKey]} })
				}
				return pieData
			},

			getYConfig(settings) {
				const axisList = [];
				settings.forEach(({name, nameColor, nameFontSize, position, min, max, axisLabelColor, axisLineShow, axisLineColor})=> {
					const axis = {
						title: name || '',
						position: position?'right':'left',
						titleFontColor: nameColor || '#666666',
						titleFontSize: nameFontSize || 13,
						fontColor: axisLabelColor || '#666666',
						axisLine: axisLineShow,
						axisLineColor: axisLineColor || '#CCCCCC',
					}
					if (min != undefined) {
						axis.min = min;
					}
					if (max) {
						axis.max = max;
					}
					axisList.push(axis)
				})
				return axisList;
			},

			// 处理 rgb 和 rgba 转换为十六进制  
			rgbToHex(rgb) {
				let hex = '';  
				let colors = rgb.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);  
			
				if (colors) {  
					// RGB 或 RGBA 的每个颜色分量  
					let r = parseInt(colors[1], 10).toString(16).padStart(2, '0');  
					let g = parseInt(colors[2], 10).toString(16).padStart(2, '0');  
					let b = parseInt(colors[3], 10).toString(16).padStart(2, '0');  
			
					// // 如果包含透明度，则添加到末尾  
					// let a = colors[4] ? Math.round(parseFloat(colors[4]) * 255).toString(16).padStart(2, '0') : '';  
			
					hex = `#${r}${g}${b}`;  
				}
				return hex;  
			},

			containsRgbOrRgba(color) {
				const regex = /rgb\(|rgba\(/;
				return regex.test(color);  
			}  
		}
	}
</script>

<style>
</style>
