<template>
	<!-- 页面根容器，设置最小高度为屏幕高度，顶部内边距为86px -->
	<view class="page" :style="{'min-height':SCREENHEIGHT()+'px','padding-top':'86px'}">
		<!-- 固定在顶部的白色背景区域 -->
		<view class="bg-white fixed">
			<!-- 包含搜索框和更多图标按钮的弹性布局 -->
			<view class="learun-flex" style="padding-right: 10px;">
				<!-- 搜索框组件，支持搜索和清除操作，双向绑定搜索文本 -->
				<uni-search-bar :placeholder="$t('搜索用户名/账号')" cancelButton="none" @confirm="searchChange(false)"
					@clear="searchChange(true)" v-model="searchText"></uni-search-bar>
				<!-- 更多图标按钮，点击触发搜索条件抽屉显示 -->
				<learun-icon type="learun-icon-more-m" color="#2979ff" @click="searchClick"></learun-icon>
			</view>
			<!-- 包含部门标签和记录数标签的区域 -->
			<view style="padding: 0 0 8px 10px;">
				<!-- 显示部门名称的标签 -->
				<uni-tag customStyle="margin-right:8px;" size="small" inverted :text="departmentName"></uni-tag>
				<!-- 显示记录总数的主色调标签 -->
				<uni-tag size="small" type="primary" :text="`${records}`"></uni-tag>
			</view>
		</view>
		<!-- 列表组件，用于展示用户列表 -->
		<uni-list>
			<!-- 列表项组件，循环渲染用户列表 -->
			<uni-list-item clickable @click="itemClick(item)" v-for="(item,index) in list" :key="item.f_UserId"
				:title="item.f_RealName" :note="item.f_Account" :thumb="avatarSrc(item)"
				thumb-size="lg"></uni-list-item>
		</uni-list>
		<!-- 加载更多组件，根据加载状态显示不同信息 -->
		<uni-load-more v-if="loading || status === 'noMore' " :status="status" />
		<!-- 右侧抽屉组件，用于显示搜索条件 -->
		<uni-drawer ref="showRight" mode="right">
			<!-- 抽屉内容容器 -->
			<view class="learun-box padding-top-win">
				<!-- 抽屉标题 -->
				<view class="learun-title learun-ab">{{$t('搜索条件')}}</view>
				<!-- 可滚动区域 -->
				<view class="learun-box">
					<scroll-view style="height: 100%;" scroll-with-animation scroll-y>
						<!-- 树形视图组件，用于显示公司和部门结构 -->
						<learun-tree-view :options="companyOptions" :isTreeData="true" :loadData="loadDepartments"
							@change="handleDepartmentChange"></learun-tree-view>
					</scroll-view>
				</view>
			</view>
		</uni-drawer>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 搜索框输入的文本
				searchText: '',
				// 选中的部门名称
				departmentName: '',
				// 是否正在加载数据
				loading: true,
				// 加载更多组件的状态
				status: 'loading',
				// 当前页码
				page: 1,
				// 总页数
				total: 1,
				// 总记录数
				records: 1,
				// 用户列表数据
				list: [],
				// 公司列表数据
				companyList: [],
				// 选中的公司ID
				companyId: '',
				// 选中的部门ID
				departmentId: ''
			}
		},
		// 页面加载时触发的生命周期钩子
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("通讯录")
			})
			// 初始化页面
			await this.init()
		},
		methods: {
			// 页面初始化方法
			async init() {
				// 设置页面标题
				this.SET_TITLE('通讯录');
				// 获取用户列表数据
				await this.fetchList()
				// 获取公司列表数据
				this.companyList = await this.HTTP_GET({
					url: '/organization/companys'
				}) || []
			},
			// 列表项点击事件处理方法
			itemClick(user) {
				// 导航到聊天页面，并传递用户ID和姓名
				this.NAV_TO('/pages/msg/chat', {
					id: user.f_UserId,
					name: user.f_RealName
				})
			},
			// 获取用户列表数据的方法
			async fetchList() {
				// 如果当前页码大于总页数，停止加载
				if (this.page > this.total) {
					return
				}
				// 设置加载状态为正在加载
				this.loading = true
				// 构建请求参数
				const params = {
					rows: 20, // 每页显示的记录数
					page: this.page, // 当前页码
					sidx: 'F_CreateDate DESC', // 排序规则
					keyword: this.searchText, // 搜索关键词
					companyId: this.companyId, // 选中的公司ID
					departmentId: this.departmentId // 选中的部门ID
				}
				// 发送HTTP GET请求获取用户信息
				const result = await this.HTTP_GET({
					url: '/organization/user/page',
					params,
					errorTips: this.$t('获取用户信息失败')
				})
				// 设置加载状态为已完成
				this.loading = false
				// 如果请求结果为空，停止处理
				if (!result) {
					return
				}
				// 过滤掉已存在于列表中的用户
				const newList = result.rows.filter(t => !this.list.some(t2 => t2.f_UserId === t.f_UserId))
				// 更新总页数
				this.total = result.total
				// 更新总记录数
				this.records = result.records
				// 合并新的用户列表到现有列表
				this.list = this.list.concat(newList)
				// 页码加1
				this.page++
				// 如果当前页码大于总页数，设置加载更多状态为无更多数据
				if (this.page > this.total) {
					this.status = "noMore"
				}
			},
			// 获取用户头像地址的方法
			avatarSrc(user) {
				// 获取全局token
				const token = this.GET_GLOBAL('token')
				// 如果用户有头像，返回头像地址，否则返回默认头像地址
				return user.f_HeadIcon ? `${this.API}/system/annexesfile/${user.f_HeadIcon}?token=${token}` :
					`/static/img-avatar/head.png`
			},
			// 搜索框搜索和清除事件处理方法
			async searchChange(isClear) {
				// 如果是清除操作，清空搜索框文本
				if (isClear) {
					this.searchText = ''
				}
				// 设置加载更多状态为正在加载
				this.status = 'loading'
				// 清空用户列表
				this.list = []
				// 重置页码为1
				this.page = 1
				// 重置总页数为1
				this.total = 1
				// 重新获取用户列表数据
				await this.fetchList()
			},
			// 更多图标按钮点击事件处理方法
			searchClick() {
				// 打开右侧抽屉
				this.$refs.showRight.open()
			},
			// 树形视图选择事件处理方法
			async handleDepartmentChange({
				value,
				label,
				isCompany
			}) {
				// 更新选中的部门名称
				this.departmentName = label
				// 清空公司ID和部门ID
				this.companyId = ''
				this.departmentId = ''
				// 设置加载更多状态为正在加载
				this.status = 'loading'
				// 清空用户列表
				this.list = []
				// 重置页码为1
				this.page = 1
				// 重置总页数为1
				this.total = 1
				// 如果选择的是公司，更新公司ID
				if (isCompany) {
					this.companyId = value
				} else {
					// 否则更新部门ID
					this.departmentId = value
				}
				// 重新获取用户列表数据
				await this.fetchList()
				// 关闭右侧抽屉
				this.$refs.showRight.close()
			},
			// 树形视图加载部门数据的方法
			async loadDepartments(companyId) {
				// 根据公司ID获取部门列表
				const list = await this.FETCH_DEPARTMENTS(companyId)
				// 将部门列表转换为树形结构
				const res = this.TOTREE(list, 'f_DepartmentId', 'f_ParentId', 'f_DepartmentId', 'f_FullName')
				return res
			}
		},
		computed: {
			// 计算属性，将公司列表转换为树形结构
			companyOptions() {
				return this.TOTREE(this.companyList.map(t => ({
					...t,
					isCompany: true,
					isLoad: false
				})), 'f_CompanyId', 'f_ParentId', 'f_CompanyId', 'f_FullName')
			}
		},
		// 下拉刷新回调函数
		async onPullDownRefresh() {
			// 清空部门名称、搜索文本、公司ID和部门ID
			this.departmentName = ''
			this.searchText = ''
			this.companyId = ''
			this.departmentId = ''
			// 设置加载更多状态为正在加载
			this.status = 'loading'
			// 清空用户列表
			this.list = []
			// 重置页码为1
			this.page = 1
			// 重置总页数为1
			this.total = 1
			// 重新获取用户列表数据
			await this.fetchList()
			// 停止下拉刷新动画
			uni.stopPullDownRefresh()
		},
		// 上拉加载回调函数
		onReachBottom() {
			// 继续获取用户列表数据
			this.fetchList()
		}
	}
</script>