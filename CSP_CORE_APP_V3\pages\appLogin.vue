<template>
	<!-- 页面根容器，类名为 page 和 login-page，设置最小高度为屏幕高度 -->
	<view class="page login-page" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<!-- 页面内容容器 -->
		<view class="content">
			<!-- 头部横幅容器 -->
			<view class="head-banner">
				<!-- 页面 logo，使用背景图片展示，图片路径为 /csp_core_app/static/logo.png -->
				<view mode="aspectFit" class="logo" style="background-image: url('/csp_core_app/static/logo.png')">
				</view>
				<!-- 标题容器 -->
				<view class="title">
					<!-- 标题文本 -->
					<text>Login...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 用户名
				username: '',
				// 加密后的密码
				password: '',
				// 明文密码
				password2: '',
				// 页面是否准备好展示输入框等内容
				ready: false,
				// 是否显示 API 根地址选择器
				showApiRootSelector: false,
				// 当前选择的 API 根地址
				currentApiRoot: '',
				// API 根地址列表
				apiRootList: [],
				// 开发环境下的账户列表
				devAccountList: [],
				// 企业微信 ID
				qywxID: '',
				// 搜索数据对象
				searchData: {},
				// 开放码
				opencode: '',
			}
		},
		// 页面加载时触发的钩子函数，options 为上个页面传递的参数
		async onLoad(options) {
			// 从 options 中获取传递的用户 ID
			let userId = options.name;
			// 从 options 中获取传递的设备 ID
			let deviceId = options.deviceId;
			// 从 options 中获取传递的 token
			let token = options.token;
			// 从 options 中获取传递的应用 ID
			let appId = options.appId;
			// 用于存储请求响应结果
			let res;

			// 打印 options 信息，方便调试
			console.log(40, options)

			// 如果传递了 token 参数
			if (options.token) {
				// 发起 POST 请求进行 app 登录
				res = await this.HTTP_POST({
					// 请求的 URL
					url: `/login/applogin`,
					// 请求的数据
					data: {
						"name": userId,
						"deviceId": deviceId,
						"token": token,
						"appId": appId,
						"debug": false
					},
				});
			}
			// 打印请求响应结果，方便调试
			console.log(55, res)

			// 如果响应结果为空
			if (!res) {
				// 注释掉的代码，原本可能用于隐藏加载提示
				// console.log(58)
				// this.HIDE_LOADING()
				// 重新启动应用并跳转到登录页面
				this.RELAUNCH_TO('/pages/login')
				return
			}

			// 将响应中的 token 存储到全局变量中
			this.SET_GLOBAL('token', res.token)
			// 将响应中的 token 存储到本地存储中
			this.SET_STORAGE('token', res.token)

			// 隐藏加载提示
			this.HIDE_LOADING()
			// 注释掉的代码，原本可能用于重新启动应用并跳转到登录页面
			// this.RELAUNCH_TO('/pages/login')
			// 跳转到主页
			this.TAB_TO('/pages/home')
		},

		methods: {
			// 页面初始化方法
			async init() {
				// 标记页面准备好
				this.ready = true
			},
		}
	}
</script>

<style lang="scss" scoped>
	// 登录页面样式
	.login-page {
		// 采用 flex 布局，水平居中
		display: flex;
		justify-content: center;
		// 子元素垂直排列
		flex-direction: column;

		// 头部横幅样式
		.head-banner {
			// 底部外边距
			margin-bottom: 32px;

			// 标题样式
			.title {
				// 块级元素显示
				display: block;
				// 上下外边距
				margin: 8px 0;
				// 字体大小
				font-size: 10px;
				// 底部外边距
				margin-bottom: 16px;
				// 文字颜色
				color: $uni-main-color;
			}

			// 开发模式标题样式
			.devTitle {
				// 文字颜色
				color: $uni-error;
			}

			// 副标题样式
			.subTitle {
				// 字体大小
				font-size: 16px;
				// 文字颜色
				color: $uni-info;
			}

			// logo 样式
			.logo {
				// 背景图片自适应
				background-size: contain;
				// 高度
				height: 36px;
				// 宽度
				width: 50px;
				// 文本居中
				text-align: center;
				// 行内块级元素显示
				display: inline-block;
				// 边框圆角
				border-radius: 2px;
			}

			// 登录项样式
			.login-item {
				// 上下内边距
				padding: 12rpx 0;
				// 底部边框
				border-bottom: 1px solid #eee;
				// 底部外边距
				margin-bottom: 20rpx;

				// 深度选择器，选择 u-icon 组件并设置宽度
				/deep/ .u-icon {
					width: 80rpx;
				}
			}

			// 介绍文本样式
			.intro {
				// 字体大小
				font-size: 12px;
				// 顶部外边距
				margin-top: 8px;
				// 文字颜色
				color: $uni-base-color;
			}

			// 子介绍文本样式
			.subIntro {
				// 文字颜色
				color: $uni-extra-color;
			}
		}

		// 内容区域样式
		.content {
			// 文本居中
			text-align: center;
			// 宽度占满父元素
			width: 100%;
			// 左右内边距
			padding: 0 24px;
			// 盒模型包含内边距和边框
			box-sizing: border-box;
		}

		// 页脚样式
		.footer {
			// 绝对定位
			position: absolute;
			// 左对齐
			left: 0;
			// 右对齐
			right: 0;
			// 底部外边距
			bottom: 8px;
			// 适配安全区域
			bottom: calc(8px + env(safe-area-inset-bottom));
			// 文本居中
			text-align: center;
			// 字体大小
			font-size: 12px;
			// 文字颜色
			color: $uni-info;

			// 仅在支付宝小程序环境下的样式
			/* #ifdef MP-ALIPAY */
			bottom: 8px;
			/* #endif */
		}
	}
</style>