import get from "lodash/get"
import mapValues from "lodash/mapValues"
import moment from "moment"


import defaultConfig from '@/common/config.default.js'
import globalConfig from '@/config.js'
import cryptoJS from '@/common/aes.js'


import httpMixins from './http.js'
import platformMixins from './learunPlatform.js'


const aesKey = (key) => {
	const length = key.length;
	if (length < 32) {
		for (let i = 0, len = 32 - length; i < len; i++) {
			key += "0";
		}
		return key;
	} else {
		return key.substring(0, 32);
	}
}

/**
 * 通用工具方法、全局数据
 * 以下定义的所有方法、计算属性，均会被挂载到所有 .vue 页面的 this 上
 * 
 * 注意：小程序端，计算属性会以数据对象的形式挂载到所有页面的 Page 对象中
 * 因此，数据量较大的情况下，为优化性能请使用调用方法动态获取的形式，避免占用太多内存
 * （例如，GLOBAL 全局对象现在已经移除，改为使用 GET_GLOBAL(key) 来获取全局对象）
 */
export default {
	mixins: [httpMixins, platformMixins],

	// 【App 平台】点击返回键后移除 loading，防止某个页面出错按返回键后还有 loading
	// #ifdef APP-VUE
	onBackPress() {
		this.HIDE_LOADING()
	},
	// #endif

	methods: {
		// 设置页面标题
		SET_TITLE(title) {
			uni.setNavigationBarTitle({
				title: this.$t(title)
			})
		},

		// 暂存一个跨页面变量 （与 this.GET_PARAM 成对使用）
		SET_PARAM(val) {
			this.SET_GLOBAL('pageParam', val)
		},

		// 获取之前暂存的跨页面变量 （与 this.SET_PARAM 成对使用）
		GET_PARAM() {
			return this.GET_GLOBAL('pageParam')
		},

		// 进入某个页面
		NAV_TO(url, param, usePageParam = false) {
			uni.navigateTo({
				url: this.lr_handleNav(url, param, usePageParam)
			})
		},

		// 进入某个Layer页面,
		NAV_TO_LAYER(url, param, usePageParam = true) {
			uni.navigateTo({
				url: this.lr_handleNav(url, param, usePageParam)
			})
		},

		// 返回上个页面
		//   delta 参数为返回的次数
		NAV_BACK(delta = 1) {
			uni.navigateBack({
				delta
			})
		},

		// 跳转到某个页面，跳转后无法返回
		JUMP_TO(url, param, usePageParam = false) {
			uni.redirectTo({
				url: this.lr_handleNav(url, param, usePageParam)
			})
		},
		// 跳转到某个页面，跳转后无法返回,可携带路径参数和页面参数
		JUMP_TO_Param(url, param, usePageParam = true, routeParam) {
			uni.redirectTo({
				url: this.lr_handleNav(url, param, usePageParam, routeParam)
			})
		},

		// 转到某个 Tab 页
		TAB_TO(url, param) {
			uni.switchTab({
				url: this.lr_handleNav(url, param)
			})
		},

		// 重启应用，跳转到指定页面
		RELAUNCH_TO(url) {
			uni.reLaunch({
				url
			})
		},

		// 设置一个全局变量
		SET_GLOBAL(key, val) {
			uni.$learun.data[key] = val
		},

		// 获取一个全局变量
		GET_GLOBAL(key) {
			return uni.$learun.data[key]
		},

		// 清空全局变量
		CLEAR_GLOBAL() {
			const lang_data = this.GET_GLOBAL('lang_data');
			const lang_types = this.GET_GLOBAL('lang_types');
			const learun_icon_list = this.GET_GLOBAL('learun_icon_list')

			uni.$learun.data = {
				lang_data,
				lang_types,
				learun_datasource: {},
				learun_icon_list,
			}
		},


		// 保存数据用于组件之间的通讯
		SET_DATA(key, val) {
			uni.$learun.data.learun_datasource[key] = val
		},
		SET_ALL_DATA(data) {
			uni.$learun.data.learun_datasource = data
		},
		GET_DATA(key) {
			return uni.$learun.data.learun_datasource[key]
		},
		CLEAR_DATA() {
			uni.$learun.data.learun_datasource = {}
		},


		// 将数据写入本地缓存
		SET_STORAGE(key, data) {
			if (data === null || data === undefined) {
				uni.removeStorageSync(key)
			} else {
				if (key === 'learun_lang_type' && data === 'english') {
					uni.setLocale('en');
				}
				if (key === 'learun_lang_type' && data === 'chinese') {
					uni.setLocale('zh-Hans');
				}
				if (key === 'learun_lang_type' && data === 'traditionalchinese') {
					uni.setLocale('zh-Hant');
				}
				uni.setStorageSync(key, data)
			}
		},
		// 获取之前写入本地缓存的数据
		GET_STORAGE(key) {
			return uni.getStorageSync(key)
		},
		// 清空本地缓存
		CLEAR_STORAGE() {
			uni.clearStorageSync()
		},

		// 展示提示框
		//   title 为提示文字
		//   mask 为是否禁止点击
		//   icon 为显示图标,可以改为 'success'
		TOAST(title, icon = 'none', mask = false) {
			uni.showToast({
				title: this.$t(title),
				icon,
				mask
			})
		},
		// 停止展示 toast 提示框
		HIDE_TOAST() {
			uni.hideToast()
		},

		// 显示 loading 提示框
		//   title 为提示文字
		//   mask 为是否禁止点击
		LOADING(title, mask = true) {
			uni.showLoading({
				title: this.$t(title),
				mask
			})
		},
		// 停止展示 loading 提示框
		HIDE_LOADING() {
			uni.hideLoading()
		},

		// 展示确认提示框
		//   title 为标题
		//   content 为提示框文字内容
		//   showCancel 为是否显示取消按钮
		// 返回一个结果，true 或 false，表示用户是否确认
		async CONFIRM(title, content, showCancel = false) {
			return new Promise(res => {
				uni.showModal({
					title: this.$t(title),
					content: this.$t(content),
					showCancel,
					success: ({
						confirm
					}) => {
						res(confirm)
					},
					fail: () => {
						res(false)
					}
				})
			})
		},

		// 从 /config.js 中获取某条配置项
		// 如果在 /config.js 中获取不到，则前往 /common/config.default.js 中获取默认值
		//   path 为配置项的访问路径
		// 举例：
		//   以下语句获取是否全局开启圆形头像
		//   this.CONFIG('pageConfig.roundAvatar')
		LEARUN_CONFIG(path) {
			return get(globalConfig, path, get(defaultConfig, path))
		},


		// 监听一个全局事件
		ON(name, func) {
			uni.$on(name, func)
		},

		// 仅单次监听一个全局事件
		ONCE(name, func) {
			uni.$once(name, func)
		},

		// 触发一个全局事件
		EMIT(name, data) {
			uni.$emit(name, data)
		},

		// 移除全局事件监听器
		OFF(name, func) {
			uni.$off(name, func)
		},


		// 当前页面的路径
		// 举例：登录页为 '/pages/login'
		PATH() {
			if (!getCurrentPages) {
				return ''
			}
			const pages = getCurrentPages()

			return pages ? '/' + pages.slice(-1)[0].route : ''
		},

		// 生成一个32位 GUID 随机字符串
		//   joinChar 为分割符，默认为下划线
		GUID(joinChar = '-') {
			return `xxxxxxxx${joinChar}xxxx${joinChar}4xxx${joinChar}yxxx${joinChar}xxxxxxxxxxxx`.replace(/[xy]/g,
				c => {
					const r = Math.random() * 16 | 0;
					const v = c === 'x' ? r : (r & 0x3 | 0x8);

					return v.toString(16);
				})
		},

		// 获取指定字符串的 MD5 码
		MD5(val = '') {
			return cryptoJS.MD5(val).toString()
		},
		AesDecrypt(source, key) {
			key = cryptoJS.enc.Utf8.parse(aesKey(key)); //32位
			let iv = cryptoJS.enc.Utf8.parse("1234567890000000"); //16位
			let decrypted = cryptoJS.AES.decrypt(source, key, {
				iv: iv,
				mode: cryptoJS.mode.CBC,
				padding: cryptoJS.pad.Pkcs7,
			});
			const res = decrypted.toString(cryptoJS.enc.Utf8);

			return res;
		},

		// 格式化数据显示，将value值转化成name
		FORMAT_NAME(data, value, valueKey, labelKey) {
			data = data || []
			if (data.length == 0) {
				return ''
			}

			value = (value || '') + ''

			const valueList = value.split(',')
			const list = []

			valueList.forEach(vitem => {
				const item = data.find(item => {
					return item[valueKey] == vitem
				})
				if (item) {
					list.push(item[labelKey])
				}
			})

			return String(list)
		},

		// 树型数据转化成数组数据
		TREE_TO_ARRAY(arr, res) {
			arr.forEach(t => {
				const children = t.children
				delete t.children
				res.push(t)
				if (children) {
					this.TREE_TO_ARRAY(children, res)
				}
			})
		},
		// 获取树形数据
		TOTREE(data, idKey, pidKey, valueKey, labelKey) {
			// 数据去重根据value值
			const _data = []
			let _dataMap = {}
			data.forEach(item => {
				if (!_dataMap[item[valueKey]]) {
					_dataMap[item[valueKey]] = true
					_data.push(this.COPY(item))
				}
			})
			_dataMap = null

			if (idKey == pidKey) {
				const res = []
				_data.forEach(item => {
					item.value = item[valueKey]
					item.label = item[labelKey]
					res.push(item)
				})
				return res
			}

			const allList = this.lr_getGroupMap(_data, idKey, pidKey);
			return this.lr_getTree(null, allList, idKey, valueKey, labelKey);
		},

		// 深度复制
		COPY(data) {
			var type = this.lr_getObjType(data);
			var obj;
			if (type === 'array') {
				obj = [];
			} else if (type === 'object') {
				obj = {};
			} else {
				// 不再具有下一层次
				return data;
			}
			if (type === 'array') {
				for (var i = 0, len = data.length; i < len; i++) {
					data[i] = (() => {
						if (data[i] === 0) {
							return data[i];
						}
						return data[i];
					})();
					if (data[i]) {
						delete data[i].$parent;
					}
					obj.push(this.COPY(data[i]));
				}
			} else if (type === 'object') {
				for (var key in data) {
					if (data) {
						delete data.$parent;
					}
					obj[key] = this.COPY(data[key]);
				}
			}
			return obj;
		},

		// 判断数据是否为空
		VALIDATENULL(val) {
			// 特殊判断
			if (val && parseInt(val) === 0) return false;
			const list = ['$parent'];
			if (typeof val === 'boolean') {
				return false;
			}
			if (typeof val === 'number') {
				return false;
			}
			if (val instanceof Array) {
				if (val.length === 0) return true;
			} else if (val instanceof Object) {
				val = this.COPY(val);
				list.forEach(ele => {
					delete val[ele];
				});
				for (var o in val) {
					return false;
				}
				return true;
			} else {
				if (
					val === 'null' ||
					val == null ||
					val === 'undefined' ||
					val === undefined ||
					val === ''
				) {
					return true;
				}
				return false;
			}
			return false;
		},
		// 验证表单数据
		VALIDATEFORM(form) {
			return new Promise((resolve) => {
				if (!form || !form.validate) {
					resolve({
						err: null
					})
				}
				form.validate().then((formData) => {
					resolve({
						formData,
						err: null
					})
				}).catch(err => {
					resolve({
						formData: null,
						err
					})
				})
			})
		},

		// 时间格式化
		DATEFORMAT(value, format, myFormat) {
			if (format) {
				format = format.replace(/y/g, 'Y')
				format = format.replace(/d/g, 'D')
			}

			if (myFormat) {
				myFormat = myFormat.replace(/y/g, 'Y')
				myFormat = myFormat.replace(/d/g, 'D')
			}

			return moment(value, myFormat).format(format || 'YYYY-MM-DD HH:mm:ss')
		},
		DATE_DIFF(time1, time2) {
			return moment(time1).diff(time2)
		},

		// 获取当前时间
		DATENOW(format) {
			return moment().format(format || 'YYYY-MM-DD HH:mm:ss')
		},
		DATENOW_DIFF(time) {
			return moment().diff(time)
		},


		// 将时间日期转化为特定格式 （一般用于 <l-list-item>）
		//   datetimeString 为要格式化的日期时间字符串
		// 返回值是一个数组，它也可以当做字符串直接使用
		// 举例：
		//   如果日期是当天，则返回 ['今天 17:32']       或者 '今天 17:32'
		//   如果日期是今年，则返回 ['6月8日', '17:32']  或者 '6月8日 17:32'
		//   如果日期不是今年，返回 ['2018-06-08']      或者 '2018-06-08'
		TABLEITEM_DATEFORMAT(datetimeString, format = 'YYYY-MM-DD') {
			const dt = moment(datetimeString)
			let result = []

			if (!dt.isValid()) {
				result.toString = () => ''
				return result
			}

			const now = moment()

			if (dt.isSame(now, 'day')) {
				result = [`今天 ${dt.format('HH:mm')}`]
				result.toString = () => `今天 ${dt.format('HH:mm')}`

			} else if (dt.isSame(now, 'year')) {
				result = [dt.format('M月D日'), dt.format('HH:mm')]
				result.toString = () => dt.format('M月D日') + ' ' + dt.format('HH:mm')

			} else {
				result = [dt.format(format)]
				result.toString = () => dt.format(format)
			}

			return result
		},



		// 将一个对象编码并转化为 url 查询字符串
		//   obj 为要转换的对象，值为空则会被忽略，值为对象会被转为 JSON 字符串
		//   auth 为是否编入身份验证信息
		URL_QUERY(obj, auth = false) {
			let queryObject = obj || {}
			if (typeof obj === 'string') {
				queryObject = {
					data: obj
				}
			}

			if (auth) {
				Object.assign(queryObject, {
					token: this.GET_GLOBAL('token')
				})
			}

			return Object.entries(queryObject)
				.filter(([k, v]) => k && v)
				.map(([k, v]) => encodeURIComponent(k) + '=' + encodeURIComponent(typeof v === 'object' ? JSON
					.stringify(v) : v))
				.join('&')
		},

		// 将字符串转化为 HTML 格式 （处理表单页面的 HTML）
		CONVERT_HTML(str) {
			if (!str) {
				return ''
			}

			return str
				.replace(/{@zuojian@}|{@youjian@}|{@and@}/g, tag => ({
					'{@zuojian@}': '<',
					'{@youjian@}': '>',
					'{@and@}': '&'
				})[tag] || tag)
				.replace(/&amp;|&lt;|&gt;|&#39;|&quot;/g, tag => ({
					'&amp;': '&',
					'&lt;': '<',
					'&gt;': '>',
					'&#39;': "'",
					'&quot;': '"'
				})[tag] || tag)
		},

		// 数组分页
		PAGINATION(pageNo, pageSize, array) {
			var offset = (pageNo - 1) * pageSize;
			return (offset + pageSize >= array.length) ? array.slice(offset, array.length) : array.slice(offset,
				offset + pageSize);
		},

		// 获取js脚本
		LEARUN_EVAL(str) {
			if (eval) {
				console.log(str, 'LEARUN_EVAL')
				return eval(str)
			}
		},

		// 获取js方法
		GET_FUNCTION(funStr) {
			if (!this.VALIDATENULL(funStr)) {
				try {
					funStr = `async(learun) => {${funStr}}`;
					const fn = this.LEARUN_EVAL(funStr)
					if (typeof fn === 'function') {
						return {
							res: true,
							msg: 'ok',
							fn
						}
					} else {
						return {
							res: false,
							msg: '此方法不是一个函数'
						}
					}
				} catch (err) {
					return {
						res: false,
						msg: err
					}
				}
			} else {
				return {
					res: false,
					msg: '没设置脚本函数'
				}
			}
		},
		BUTTONS_CLICK({
			prop
		}) {
			this[`handle${prop}`] && this[`handle${prop}`]()
		},

		// 用于编码小程序分享消息的 url 查询字符串 （开发环境下还会打印出来）
		//   pageParam 为点击分享消息跳转时携带的 pageParam
		//   query 为点击分享消息跳转时携带的 query
		//   pagePath 点击分享消息跳转到小程序的页面 (默认为当前页)
		// 返回编码好的查询字符串
		MP_SHARE_ENCODE(pageParam, query, pagePath) {
			const shareObj = {
				fromUser: this.GET_GLOBAL('loginUser').userId,
				fromPlatform: this.PLATFORM,
				timestamp: new Date().valueOf(),
				pagePath: pagePath || this.pagePath,
				query: query,
				pageParam,
				learun: this.APP_VERSION
			}
			const result = this.URL_QUERY(shareObj)

			if (this.DEV) {
				console.log('【您正在分享Crystal Service Platform小程序页面】')
				console.log('====分享对象：====')
				console.log(shareObj)
				console.log('====启动路径：====')
				console.log('/pages/home')
				console.log('====启动参数：====')
				console.log(result)
				console.log('====(以上消息仅开发模式可见)====')
			}

			return result
		},

		// 解析小程序分享字符串 （会自动适配微信小程序的 url 编码）
		MP_SHARE_DECODE(info) {
			// 微信小程序中获取的分享信息是被 uri 编码过的，需要解码
			// 支付宝/钉钉小程序不需要解码
			// #ifdef MP-WEIXIN
			const shareInfo = mapValues(info, decodeURIComponent)
			// #endif

			shareInfo.pageParam = shareInfo.pageParam ? JSON.parse(shareInfo.pageParam) : undefined
			shareInfo.query = shareInfo.query ? this.urlQuery(JSON.parse(shareInfo.query)) : undefined

			if (this.DEV) {
				console.log('【您通过小程序消息分享启动了Crystal Service Platform小程序】')
				console.log('====小程序分享对象：====')
				console.log(shareInfo)
				console.log('====即将转入页面：====')
				console.log(shareInfo.pagePath)
				console.log('====设置的 url query：====')
				console.log(shareInfo.query)
				console.log('====设置的 pageParam：====')
				console.log(shareInfo.pageParam)
				console.log('====(以上消息仅开发模式可见)====')
			}

			this.SET_GLOBAL('pageParam', shareInfo.pageParam)
			uni.navigateTo({
				url: `${shareInfo.pagePath}?${this.URL_QUERY(shareInfo.query)}`
			})
		},

		SCREENHEIGHT() {
			//console.log(uni.getSystemInfoSync(),'getSystemInfoSync')
			return uni.getSystemInfoSync().windowHeight
		},
		WINDOWTOP() {
			//console.log(uni.getSystemInfoSync(),'getSystemInfoSync')
			return uni.getSystemInfoSync().windowTop
		},

		GET_VALUE(data, key) {

			if (data) {
				let value = undefined
				for (let _key in data) {
					if (_key.toLowerCase() == key.toLowerCase()) {
						value = data[_key]
						break
					}
				}
				return value
			} else {
				return undefined;
			}
		},

		// 地图方法
		GET_LOCATION() {
			return new Promise((res, rej) => {
				const sysInfo = uni.getSystemInfoSync()
				uni.getLocation({
					type: 'gcj02',
					success: (data) => {
						const lngAndlat = this.lr_toBaiDuLngAndLat(data.longitude, data.latitude);
						res({
							lat: lngAndlat.lat, //纬度
							lng: lngAndlat.lng, //经度
							accuracy: data.accuracy, //位置的精确度
						})
					},
					fail: () => {
						res(null)
					}
				});
			})
		},


		// 【内部方法】获取对象类型
		lr_getObjType(obj) {
			var toString = Object.prototype.toString;
			var map = {
				'[object Boolean]': 'boolean',
				'[object Number]': 'number',
				'[object String]': 'string',
				'[object Function]': 'function',
				'[object Array]': 'array',
				'[object Date]': 'date',
				'[object RegExp]': 'regExp',
				'[object Undefined]': 'undefined',
				'[object Null]': 'null',
				'[object Object]': 'object'
			};
			return map[toString.call(obj)];
		},

		lr_toBaiDuLngAndLat(lng, lat) {
			if (lng == null || lng == '' || lat == null || lat == '')
				return {
					lng: lng,
					lat: lat
				}
			let x_pi = 3.14159265358979324 * 3000.0 / 180.0
			let x = lng
			let y = lat
			let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi)
			let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi)
			let lngs = z * Math.cos(theta) + 0.0065
			let lats = z * Math.sin(theta) + 0.006
			return {
				lng: lngs,
				lat: lats
			}
		},

		// 【内部方法】获取数据集合
		lr_getGroupMap(data, idKey, pidKey) {
			const groupList = {}
			const map = {}
			data.forEach(item => {
				groupList[item[pidKey]] = groupList[item[pidKey]] || []
				groupList[item[pidKey]].push(item)
				map[item[idKey]] = item
			})
			return {
				group: groupList,
				map: map
			}
		},
		// 【内部方法】获取数据集合
		lr_getTree(data, allList, idKey, valueKey, labelKey) {
			const res = []
			if (data == null) {
				for (const id in allList.group) {
					if (!allList.map[id]) {
						const list = allList.group[id]
						list.forEach(item2 => {
							item2.value = item2[valueKey]
							item2.label = item2[labelKey]
							if (allList.group[item2[idKey]] && allList.group[item2[idKey]].length > 0) {
								item2.children = this.lr_getTree(allList.group[item2[idKey]], allList, idKey,
									valueKey, labelKey)
							}
							res.push(item2)
						})
					}
				}
			} else {
				data.forEach(item => {
					item.value = item[valueKey]
					item.label = item[labelKey]
					if (allList.group[item[idKey]] && allList.group[item[idKey]].length > 0) {
						item.children = this.lr_getTree(allList.group[item[idKey]], allList, idKey, valueKey,
							labelKey)
					}
					res.push(item)
				})
			}
			return res
		},

		// 【内部方法】处理页面跳转 url 和参数
		lr_handleNav(url, param, usePageParam, routeParam) {
			let query = ''
			this.SET_PARAM(undefined)
			if (param && usePageParam) {
				this.SET_PARAM(param)
			} else if (param && !usePageParam) {
				let VALIDATENULL = this.VALIDATENULL;
				let str = '?'
				if (url.includes('?')) {
					str = '&'
				}
				query += str + Object.entries(param).filter(([k, v]) => k && !VALIDATENULL(v)).map(([k, v]) => k + '=' +
					v).join('&')
			}
			if (routeParam) {
				let VALIDATENULL = this.VALIDATENULL;
				let str = '?'
				if (url.includes('?') || query.includes('?')) {
					str = '&'
				}
				query += str + Object.entries(routeParam).filter(([k, v]) => k && !VALIDATENULL(v)).map(([k, v]) => k +
					'=' + v).join('&')
			}
			return url + query
		}
	},

	computed: {
		// 请求后台接口的地址
		API() {
			return this.GET_GLOBAL('apiRoot') ||
				this.LEARUN_CONFIG('apiHost')[this.DEV ? this.LEARUN_CONFIG('devApiHostIndex') : this.LEARUN_CONFIG(
					'prodApiHostIndex')]
		},

		//  区分公司
		COMPANY(){
			// return 
		},

		// 当前是否为开发环境
		DEV() {
			return process.env.NODE_ENV === 'development'
		},

		// 获取当前移动端版本号 （定义在 config.js）
		APP_VERSION() {
			return this.LEARUN_CONFIG('appVersion')
		},

		// 当前运行平台
		// 取值 'alipay'/'weixin'/'dingtalk'/'h5'/'app'/'unknow'
		PLATFORM() {
			let result = 'unknow'

			// #ifdef MP-ALIPAY
			// #ifndef MP-DINGTALK
			result = 'alipay'
			// #endif
			// #ifdef MP-DINGTALK
			result = 'dingtalk'
			// #endif
			// #endif

			// #ifdef MP-WEIXIN
			result = 'weixin'
			// #endif

			// #ifdef H5
			result = 'h5'
			// #endif

			// #ifdef APP-VUE
			result = 'app'
			// #endif

			return result
		},

		// 获取当前运行平台的中文全称
		// 取值 '支付宝小程序'/'微信小程序'/'钉钉小程序'/'移动 H5 '/'手机 App '/'（未知）'
		PLATFORM_TEXT() {
			let result = '（未知）'

			// #ifdef MP-ALIPAY
			// #ifndef MP-DINGTALK
			result = '支付宝小程序'
			// #endif
			// #ifdef MP-DINGTALK
			result = '钉钉小程序'
			// #endif
			// #endif

			// #ifdef MP-WEIXIN
			result = '微信小程序'
			// #endif

			// #ifdef H5
			result = '移动 H5 '
			// #endif

			// #ifdef APP-VUE
			result = '手机 App '
			// #endif

			return result
		}
	}
}