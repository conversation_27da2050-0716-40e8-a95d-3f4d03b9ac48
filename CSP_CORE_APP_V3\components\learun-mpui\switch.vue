<template>
	<view class="learun-flex-box learun-switch"  >
		<switch :checked="checked" :disabled="disabled" @change="handleChange"	 style="transform:scale(0.8)" />
	</view>
</template>

<script>
	export default {
		name:'learun-switch',
		props:{
			value:{},
			valueType:{
				type:String,
				default:'boolean'
			},
			activeValue:{
				type:String,
				default:'true'
			},
			inactiveValue:{
				type:String,
				default:'false'
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		created(){
			// console.log(this.activeValue,'this.activeValue')
		},
		computed:{
			checked(){
				return this.value == this.toValueType(this.valueType,this.activeValue)
			}
		},
		methods:{
			handleChange(event){
				let value
				switch(this.valueType){
					case 'boolean':
						value = event.detail.value
						break
					case 'string':
						value =  event.detail.value?this.activeValue:this.inactiveValue
						break
					case 'number':
						value =  event.detail.value?Number(this.activeValue):Number(this.inactiveValue)
						break
				}
				this.$emit('input',value)
				this.$emit('change',value)
			},
			toValueType(type,value){
				switch(type){
					case 'boolean':
						return value == 'true'
					case 'string':
						return value
					case 'number':
						return Number(value)
				}
				return value
			}
		}
	}
</script>

<style lang="scss" scoped >
	.learun-switch{
		align-items: center;
	}
</style>
