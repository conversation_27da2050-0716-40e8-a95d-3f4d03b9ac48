<template>
  <view>
    <tki-qrcode
      :val="value"
      :size="size"
      :background="background"
      :color="color"
      :margin="margin"
      loadMake
      :onval="true"
    />
  </view>
</template>

<script>
export default {
  name: "learun-qrcode",
  onLoad() {},
  props: {
    value: {
      type: String,
      default: "",
    },
    background: String,
    color: {
      // 条码颜色
      type: String,
      default: "#000000",
    },
    margin: {
      type: Number,
      default: 0,
    },

    size: {
      type: Number,
      default: 400,
    },
  },
  data() {
    return {};
  },

  methods: {},
};
</script>

<style>
</style>