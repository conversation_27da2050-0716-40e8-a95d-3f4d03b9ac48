<template>
    <!-- 当 ready 为 true 时，渲染页面 -->
    <view v-if="ready" class="page"
        :style="{'height':SCREENHEIGHT()+'px','padding-top':'40px','padding-bottom':isAdd||showchangeListStyle ?'40px':''}">
        <!-- 当 showHeader 为 true 时，显示表格头部 -->
        <view class="crystal-table-header" style="padding:10px 10px 0px 10px; background-color: #fff;" v-if="showHeader"
            ref="tableHeader">
            <!-- 显示表头标题 -->
            <view style="width: 100%;fontWeight:bold;">{{ headerValue }}</view>
            <!-- 遍历查询参数并显示 -->
            <text v-for="(param, index) in queryParams" :key="index" style="margin-right: 10px;">
                {{ param }}
            </text>
        </view>

        <!-- 顶部固定栏，包含数据总数和筛选按钮 -->
        <view class="learun-top-bar fixed" style="padding-right: 8px;">
            <!-- 显示数据总数 -->
            <view class="learun-total-bar">
                <text>{{$t('共')}}{{total}}{{$t('条数据')}}</text>
            </view>
            <!-- 筛选按钮，点击触发 searchClick 方法 -->
            <view class="learun-search-btn" @click.stop="searchClick">
                <text>{{$t('筛选')}}</text>
                <view class="learun-search-btn__flag" />
            </view>
        </view>

        <!-- 自定义查询组件，传入表单方案、页面方案和查询参数 -->
        <crystal-custom-app-query :formScheme="formScheme" :pageScheme="pageScheme" :formData="queryParams"
            ref="queryPopup"></crystal-custom-app-query>

        <!-- 当 tableStyle 为 1 时，渲染表格组件 -->
        <learun-table v-if="tableStyle == 1" :page="page" :pageSize="rows" :total="total" :columns="columns"
            :dataSource="list" :displayText="learun_form_displayText" :style="{'height':ListHeight+'px'}"
            @rowClick="handleRowClick" @pageChange="handlePageChange" />

        <!-- 当 tableStyle 为 2 时，渲染卡片表格组件 -->
        <crystal-card-table v-else-if="tableStyle == 2" :page="page" :pageSize="rows" :total="total" :columns="columns"
            :dataSource="list" :displayText="learun_form_displayText" :header-value="headerValue"
            :query-params="queryParams" :list-height="ListHeight" :show-pagination="showPagination"
            @rowClick="handleRowClick" @pageChange="handlePageChange" />

        <!-- 底部按钮区域，包含切换列表样式和新增按钮 -->
        <view class="learun-bottom-btns">
            <!-- 切换列表样式按钮，当 showchangeListStyle 为 true 时显示 -->
            <button v-if="showchangeListStyle"
                @click.stop="changeListStyle">{{$t("切换成") + (tableStyle == 1 ? $t("卡片模式") : $t("表格模式"))}}</button>
            <!-- 新增按钮，当 isAdd 为 true 时显示 -->
            <button v-if="isAdd" @click.stop="handleAdd" type="primary">{{$t('新增')}}</button>
        </view>

        <!-- 行操作弹窗按钮组件，传入按钮列表，点击触发 BUTTONS_CLICK 方法 -->
        <learun-popup-buttons ref="popup" :buttons="rowBtns" @click="BUTTONS_CLICK" />
    </view>
</template>

<script>
    // 引入自定义表单混合对象
    import customFormMixins from '@/common/customform.js'

    export default {
        // 混入自定义表单混合对象
        mixins: [customFormMixins],
        data() {
            return {
                // 是否以层模式显示
                isLayer: true,
                // 模块 ID
                moduleId: '',
                // 表单 ID
                formId: '',
                // 表单方案
                formScheme: {},
                // 页面方案
                pageScheme: {},
                // 组件映射表
                componentMap: {},
                // 表格列配置
                columns: [],
                // 页面标题
                pageTitle: '',
                // 主键
                primaryKey: '',
                // 每页显示的行数
                rows: 20,
                // 当前页码
                page: 1,
                // 数据总数
                total: 0,
                // 数据列表
                list: [],
                // 查询参数
                queryParams: {},
                // 行操作按钮列表
                rowBtns: [
                    {
                        prop: 'Edit',
                        label: this.$t('编辑'),
                        type: 'primary'
                    },
                    {
                        prop: 'Delete',
                        label: this.$t('删除'),
                        type: 'warn'
                    },
                    {
                        prop: 'Details',
                        label: this.$t('详情')
                    }
                ],
                // 设置按钮列表
                settingBtns: [],
                // 当前编辑的行数据
                editRow: null,
                // 页面是否准备好
                ready: false,
                // 表格样式，1 为表格模式，2 为卡片模式
                tableStyle: 2,
                // 表格样式切换按钮列表
                tableStyleBtns: [
                    {
                        prop: 'TableStyle',
                        label: this.$t('表格布局')
                    },
                    {
                        prop: 'CardStyle',
                        label: this.$t('卡片布局')
                    }
                ],
                // 表头显示的值
                headerValue: "",
                // 列表区域的高度
                ListHeight: 0,
                // 是否显示切换列表样式按钮
                showchangeListStyle: true,
                // 是否显示表头
                showHeader: false,
                // 是否显示分页
                showPagination: true
            }
        },

        computed: {
            // 计算可显示的行操作按钮，过滤出有权限且在设置按钮列表中的按钮
            myBtns() {
                return this.rowBtns.filter(t => this.GET_BUTTON_AUTH(t.prop, this.moduleId) && this.settingBtns.findIndex(
                    t2 => t2.prop == t.prop) != -1)
            },
            // 计算是否显示新增按钮，判断是否有新增权限且新增按钮在设置按钮列表中
            isAdd() {
                return this.GET_BUTTON_AUTH('Add', this.moduleId) && this.settingBtns.findIndex(t => t.prop == 'Add') != -1
            }
        },

        // 页面加载时触发
        async onLoad({ formId }) {
            // 检查页面是否可以启动
            if (await this.PAGE_LAUNCH()) {
                // 初始化页面
                await this.init(formId)
            }
        },

        // 页面卸载时触发
        onUnload() {
            // 移除自定义事件监听
            this.OFF(`custom-list-change-${this.formId}`)
        },

        // 页面更新后触发，计算列表区域的高度
        updated() {
            if (this.$refs.tableHeader) {
                this.ListHeight = this.SCREENHEIGHT() - this.$refs.tableHeader.$el.offsetHeight - 80;
            } else {
                this.ListHeight = this.SCREENHEIGHT() - 80
            }
        },

        methods: {
            // 初始化页面
            async init(formId) {
                // 显示加载提示
                this.LOADING(this.$t('加载中...'))
                // 监听自定义事件，当列表数据变化时重新获取列表
                this.ON(`custom-list-change-${formId}`, this.fetchList)

                // 保存当前表单 ID
                this.formId = formId 
                // 获取页面信息
                const pageInfo = this.GET_PARAM()

                // 保存模块 ID
                this.moduleId = pageInfo.f_Id

                // 设置页面标题
                this.SET_TITLE(pageInfo.f_Name)
                // 保存页面标题
                this.pageTitle = pageInfo.f_Name

                // 获取页面权限信息
                await this.FETCH_AUTH(this.moduleId)

                // 拉取页面配置
                const pageData = await this.HTTP_GET({
                    url: `/mapp/module/${this.moduleId}`
                })
                // 解析页面方案
                const pageScheme = JSON.parse(pageData.functionEntity.scheme)
                // 保存设置按钮列表
                this.settingBtns = pageScheme.btns
				
				// 拉取默认查询参数
				const querys = pageScheme.table.querys.filter(t => t.default != null);
				if (querys) {
					const needIDQuerys = querys.filter(t => t.default == "${this.ID_NO}");
					if (needIDQuerys) {
						const id_no = await this.FETCH_DATASOURCE("ID_NO");
						if (id_no && id_no.length > 0) {
							needIDQuerys.forEach(t => {
								this.queryParams[t.prop] = id_no[0]["id_no"];
							})
						}
					}
				}

                // 拉取表单结构
                const formSchemeData = await this.HTTP_GET({
                    url: `/custmerform/scheme/history/${this.formId}`
                })

                // 解析表单方案
                const formScheme = JSON.parse(formSchemeData.f_Scheme)
                // 用于存储组件映射
                const componentMap = {}
                // 处理表单组件，分离主表和子表组件
                formScheme.formInfo.components = this.getFormSchemeComponents(formScheme.formInfo.components)
                for (let i = 0, len = formScheme.formInfo.components.length; i < len; i++) {
                    const component = formScheme.formInfo.components[i]
                    // 构建组件映射表
                    componentMap[component.id] = component
                }

                // 获取主表主键信息
                this.primaryKey = formScheme.primaryKey
                // 初始化列
                const columns = []
                for (let i = 0, len = pageScheme.table.columns.length; i < len; i++) {
                    const column = pageScheme.table.columns[i]
                    // 关联列的组件方案
                    column.scheme = componentMap[column.prop]
                    if (componentMap[column.prop]?.config?.field) {
                        if (formScheme.formType == 0 || formScheme.formType == 2) {
                            // 根据表单类型生成列的 rowid
                            column.rowid =
                                `${componentMap[column.prop].config.field.toLowerCase()}${formScheme.db.findIndex(t=>t.name == componentMap[column.prop].config.table)}`
                        } else {
                            column.rowid = `${componentMap[column.prop].config.field.toLowerCase()}`
                        }
                        column.scheme.rowid = column.rowid
                    }
                    // 检查列权限，有权限则添加到列配置中
                    if (this.GET_COLUMN_AUTH(column.rowid, this.moduleId)) {
                        columns.push(column)
                    }
                }
                // 保存列配置
                this.columns = columns

                // 保存组件映射表
                this.componentMap = componentMap
                // 保存表单方案
                this.formScheme = formScheme
                // 保存页面方案
                this.pageScheme = pageScheme

                // 特定模块的特殊配置
                if (this.moduleId == '620df7bc-d4dd-443e-a815-b7078b60d77c') {
                    this.showHeader = true;
                    this.queryParams = {
                        '1710899729365_11243': moment(new Date()).format("YYYY-MM") + '-01' + ' - ' +
                            moment(new Date()).format("YYYY-MM-DD")
                    };
                    this.rows = 31 * 3;
                    this.showPagination = false;
                }

                // 饭堂消费模块的特殊配置
                if (this.moduleId == '49525b7d-1c7d-4d2c-8840-df15176fe1ca') {
                    this.showHeader = true;
                    this.queryParams = {
                        '1734508294146_95904': moment(new Date()).format("YYYY-MM") + '-01' + ' - ' +
                            moment(new Date()).format("YYYY-MM-DD")
                    };
                    this.rows = 31 * 3;
                    this.showPagination = false;
                }

                // 加载数据源数据
                await this.fetchDataSource(this.columns)
                // 拉取列表数据
                await this.fetchList()

                // 隐藏加载提示
                this.HIDE_LOADING()

                // 标记页面准备好
                this.ready = true
            },

            // 处理表单组件，分离主表和子表组件
            getFormSchemeComponents(components) {
                // 用于存储子表组件分组
                const childrenGroup = {};
                // 用于存储主表组件
                const componentMap = [];
                components.forEach(component => {
                    if (component.config.isSubTable) {
                        // 子表组件分组存储
                        childrenGroup[component.containerId] = childrenGroup[component.containerId] || []
                        childrenGroup[component.containerId].push(component)
                    } else {
                        if (component.type === 'gridtable') {
                            // 网格表格组件关联子组件
                            childrenGroup[component.id] = childrenGroup[component.id] || []
                            component.children = childrenGroup[component.id]
                        }
                        // 添加主表组件到列表
                        componentMap.push(component)
                    }
                })
                return componentMap;
            },

            // 拉取列表数据
            async fetchList() {
                let sidx = ''
                if (this.pageScheme.table.sidx) {
                    const componentSidx = this.componentMap[this.pageScheme.table.sidx]
                    // 生成排序字段
                   sidx = `${componentSidx.config.field}${this.formScheme.formType == 1 ? '' : this.formScheme.db.findIndex(t=>t.name == componentSidx.config.table)}`
                }

                if (this.pageScheme.table.isDESC && sidx != '') {
                    // 添加降序标识
                    sidx += ' DESC'
                }

                // 发送分页请求
                const result = await this.HTTP_POST({
                    url: `/custmerform/data/page/${this.moduleId}/${this.formId}`,
                    data: {
                        paginationInputDto: {
                            rows: this.rows,
                            page: this.page,
                            sidx: sidx,
                        },
                        queryJson: JSON.stringify(this.queryParams || {})
                    },
                    errorTips: this.$t('加载数据时出错')
                })

                if (!result) {
                    return
                }
                // 拉取组织结构信息
                await this.fetchOrganizeInfo(result.rows)

                // 更新数据总数、当前页码和数据列表
                this.total = result.records
                this.page = result.page
                this.list = result.rows

                // 特定模块的表头计算
                if (this.moduleId == '620df7bc-d4dd-443e-a815-b7078b60d77c') {
                    let sumPerson_need_pay_amt = 0;
                    for (let i = 0; i < this.list.length; i++) {
                        sumPerson_need_pay_amt += this.list[i]["person_need_pay_amt"];
                    }
                    this.headerValue = this.$t("个人支付") + this.$t("：") + sumPerson_need_pay_amt.toFixed(2);
                }
                // 饭堂消费模块的表头计算
                if (this.moduleId == '49525b7d-1c7d-4d2c-8840-df15176fe1ca') {
                    let sumPerson_need_pay_amt = 0;
                    for (let i = 0; i < this.list.length; i++) {
                        sumPerson_need_pay_amt += this.list[i]["total_meal_amt"];
                    }
                    this.headerValue = this.$t("个人支付") + this.$t("：") + sumPerson_need_pay_amt.toFixed(2);
                }
                // 再次拉取组织结构信息
                await this.fetchOrganizeInfo(result.rows)
            },

            // 处理分页变化
            async handlePageChange({ current }) {
                // 显示加载提示
                this.LOADING(this.$t('加载数据中...'))
                // 更新当前页码
                this.page = current
                // 重新拉取列表数据
                await this.fetchList()
                // 隐藏加载提示
                this.HIDE_LOADING()
            },

            // 处理行点击事件
            handleRowClick({ row, index }) {
              this.editRow = row
              if (this.myBtns.length > 1) {
              	this.$refs.popup.open(`${this.$t("操作第")}${index + 1}${this.$t("行")}`, this.myBtns)
              } else if (this.myBtns.length == 1) {
              	switch (this.myBtns[0].prop) {
              		case "Edit":
              			this.handleEdit();
              			break;
              		case "Delete":
              			this.handleDelete();
              			break;
              		case "Details":
              			this.handleDetails();
              			break;
              	}
              }
            },

            // 处理新增按钮点击事件
            handleAdd() {
                let { wFlowCode } = this.settingBtns.find(t => t.prop == 'Add')
                if (wFlowCode) {
                    // 发起流程
                    this.ONCE(`learun-workflow-list-change`, this.fetchList)
                    this.NAV_TO('../workflow/releasetask/single?type=create', {
                        f_Code: wFlowCode
                    })
                } else {
                    // 跳转到新增页面
                    this.NAV_TO(`./single?type=create`, {
                        key: this.primaryKey,
                        title: `${this.pageTitle}【${this.$t("新增")}】`,
                        formId: this.formId,
                        moduleId: this.moduleId,
                        formScheme: this.formScheme,
                        settingBtns: this.settingBtns
                    }, true)
                }
            },

            // 处理编辑按钮点击事件
            handleEdit() {
                // 关闭行操作弹窗
                this.$refs.popup.close()
                // 跳转到编辑页面
                this.NAV_TO(`./single?type=edit`, {
                    key: this.primaryKey,
                    keyValue: this.editRow[`${this.primaryKey.toLowerCase()}0`],
                    title: `${this.pageTitle}【${this.$t("编辑")}】`,
                    formId: this.formId,
                    moduleId: this.moduleId,
                    formScheme: this.formScheme,
                    settingBtns: this.settingBtns
                }, true)
            },

            // 处理详情按钮点击事件
            handleDetails() {
                // 关闭行操作弹窗
                this.$refs.popup.close()
                if (this.formScheme.formType == 0 || this.formScheme.formType == 2) {
                    // 跳转到详情页面
                    this.NAV_TO(`./single?type=details`, {
                        key: this.primaryKey,
                        keyValue: this.editRow[`${this.primaryKey.toLowerCase()}0`],
                        title: `${this.pageTitle}【${this.$t("详情")}】`,
                        formId: this.formId,
                        moduleId: this.moduleId,
                        formScheme: this.formScheme,
                        settingBtns: this.settingBtns
                    }, true)
                } else {
                    // 跳转到详情页面
                    this.NAV_TO(`./single?type=details`, {
                        formData: this.editRow,
                        title: `${this.pageTitle}【${this.$t("详情")}】`,
                        formId: this.formId,
                        moduleId: this.moduleId,
                        formScheme: this.formScheme,
                        settingBtns: this.settingBtns,
                    }, true)
                }
            },

            // 处理删除按钮点击事件
            async handleDelete() {
                // 确认删除操作
                if (!(await this.CONFIRM(this.$t('删除项目', '确定要删除该项吗？'), true))) {
                    return
                }
                // 关闭行操作弹窗
                this.$refs.popup.close()

                // 显示加载提示
                this.LOADING(this.$t('提交删除中…'))
                // 发送删除请求
                this.HTTP_DELETE({
                    url: `/custmerform/data/${ this.formId}`,
                    params: {
                        key: this.primaryKey,
                        keyValue: this.editRow[`${this.primaryKey.toLowerCase()}0`]
                    },
                    errorTips: this.$t('删除失败')
                }).then(success => {
                    // 隐藏加载提示
                    this.HIDE_LOADING()
                    if (success) {
                        // 提示删除成功并重新拉取列表数据
                        this.TOAST(this.$t('删除成功'), 'success')
                        this.fetchList()
                    }
                })
            },

            // 拉取自定义应用需要数据源的字段的数据源
            async fetchDataSource(columns) {
                for (let i = 0, len = columns.length; i < len; i++) {
                    await this.learun_form_fetchDataSource(columns[i].scheme)
                }
            },

            // 拉取结果中的组织结构信息
            async fetchOrganizeInfo(list) {
                const departmentIdList = []
                const userIdList = []
                const areaList = []

                for (const row of list) {
                    for (const key in row) {
                        if (!row[key]) {
                            continue
                        }
                        const column = this.columns.find(t => t.rowid == key)
                        if (!column || !column.scheme) {
                            continue
                        }

                        switch (column.scheme.type) {
                            case 'userSelect':
                            case 'createUser':
                            case 'modifyUser':
                                if (userIdList.findIndex(t => t == row[key]) == -1) {
                                    userIdList.push(row[key])
                                }
                                break
                            case 'departmentSelect':
                            case 'department':
                                if (departmentIdList.findIndex(t => t == row[key]) == -1) {
                                    departmentIdList.push(row[key])
                                }
                                break
                            case 'areaSelect':
                                if (row[key]) {
                                    areaList.push(row[key])
                                }
                                break
                        }
                    }
                }

                // 拉取用户和部门信息
                await this.learun_form_fetchOrganizeInfo(userIdList, departmentIdList)
                // 拉取区域信息
                await this.learun_form_fetchAreaInfo(areaList)
            },

            // 处理筛选按钮点击事件
            searchClick() {
                this.ONCE('learun-customapp-query', (data) => {
                    this.queryParams = data
                    setTimeout(async () => {
                        // 显示加载提示
                        this.LOADING('加载中...')
                        // 重置页码和数据总数
                        this.page = 1
                        this.total = 2
                        this.list = []

                        // 重新拉取列表数据
                        await this.fetchList()
                        // 隐藏加载提示
                        this.HIDE_LOADING()
                    })
                })

                // 跳转到查询页面
                this.NAV_TO_LAYER(`./query`, {
                    formScheme: this.formScheme,
                    pageScheme: this.pageScheme,
                    formData: this.queryParams
                }, true)
            },

            // 显示抽屉
            showDrawer() {
                this.ONCE('learun-customapp-query', (data) => {
                    this.queryParams = data
                    setTimeout(async () => {
                        // 显示加载提示
                        this.LOADING('加载中...')
                        // 重置页码和数据总数
                        this.page = 1
                        this.total = 2
                        this.list = []

                        // 重新拉取列表数据
                        await this.fetchList()
                        // 隐藏加载提示
                        this.HIDE_LOADING()
                    })
                })
                // 打开查询弹窗
                this.$refs.queryPopup.open();
            },

            // 关闭抽屉
            closeDrawer() {
                this.ONCE('learun-customapp-query', (data) => {
                    this.queryParams = data
                    setTimeout(async () => {
                        // 显示加载提示
                        this.LOADING('加载中...')
                        // 重置页码和数据总数
                        this.page = 1
                        this.total = 2
                        this.list = []

                        // 重新拉取列表数据
                        await this.fetchList()
                        // 隐藏加载提示
                        this.HIDE_LOADING()
                    })
                })
                // 关闭查询弹窗
                this.$refs.queryPopup.close();
            },

            // 切换列表样式
            changeListStyle() {
                this.tableStyle = this.tableStyle == 1 ? 2 : 1;
                // this.$refs.popup.open(`${this.$t("操作第")}${this.$t("行")}`,this.tableStyleBtns)
            },

            // 处理表格样式按钮点击事件
            handleTableStyle() {
                // 关闭行操作弹窗
                this.$refs.popup.close()
                // 设置表格样式为表格模式
                this.tableStyle = 1
            },

            // 处理卡片样式按钮点击事件
            handleCardStyle() {
                // 关闭行操作弹窗
                this.$refs.popup.close()
                // 设置表格样式为卡片模式
                this.tableStyle = 2
            }
        }
    }
</script>