<template>
	<learun-tree-picker
		v-if="!companyId"
		:options="options"
		:loadData="loadDepartment"
		:placeholder="$t(placeholder)"
		:getShowText="getShowText"
		:disabled="disabled"
		
		@change="handleChange"
		v-model="myValue"
		
		isTreeData
		>
	</learun-tree-picker>
	<learun-tree-picker
		v-else
		:options="options"
		:loadData="loadDepartment"
		:placeholder="$t(placeholder)"
		:getShowText="getShowText"
		:disabled="disabled"
		
		@change="handleChange"
		v-model="myValue"
		
		isTreeData
		>
	</learun-tree-picker>
</template>

<script>
	export default{
		name:'learun-department-picker',
		props:{
			getDataSource:Function,
			disabled:Boolean,
			placeholder:String,
			
			value:{},
			companyId: String,
		},
		computed:{
			options(){
				if(this.getDataSource){
					let companyList = this.getDataSource({type:'companySelect'})
					if (this.companyId) {
						companyList = companyList.filter(t=>t.f_CompanyId == this.companyId)
					}
					return this.TOTREE(companyList.map(t=>({...t,disabled:true,isLoad:false})),'value','f_ParentId','value','label')
				}
				return []
			},
			myValue:{
				set(val){
					this.$emit('input',val)
				},
				get(){
					return this.value
				}
			}
		},
		methods:{
			async loadDepartment(companyId){
				const list = await this.FETCH_DEPARTMENTS(companyId)
				const res = this.TOTREE(list,'f_DepartmentId','f_ParentId','f_DepartmentId','f_FullName')
				return res
			},
			handleChange(val){
				this.$emit('change',val)
			},
			getShowText(value){
				const departmentMap = this.GET_DATA('learun_departments_map') || {}
				let department = departmentMap[value]
				
				if(department){
					return department.label
				}
				else{
					return ''
				}
			}
		}
	}
</script>

<style>
</style>

