<template>
	<!-- 页面容器，设置顶部内边距 -->
	<view class="page" style="padding-top: 56px;">
		<!-- 顶部搜索栏，固定在页面顶部，背景为白色 -->
		<view class="bg-white fixed">
			<!-- uni-search-bar 组件用于搜索应用名，绑定搜索文本和焦点状态 -->
			<uni-search-bar :placeholder="$t('搜索应用名')" :focus="focus" cancelButton="none"
				v-model="searchText"></uni-search-bar>
		</view>
		<!-- 内容区域，设置顶部内边距 -->
		<view style="padding-top: 8px;">
			<!-- 我的应用卡片，当搜索文本为空时显示 -->
			<uni-card :title="$t('我的应用')" padding="8px" margin="8px" :border="false" :is-shadow="false"
				v-if="searchText.length <= 0">
				<!-- uni-grid 组件用于展示我的应用列表，设置列数为 4，绑定点击事件 -->
				<uni-grid :showBorder="false" :column="4" @change="funcListClick($event,myListDisplay)">
					<!-- 循环渲染我的应用列表项 -->
					<uni-grid-item v-for="(listItem,listIndex) in myListDisplay" :key="listIndex" :index="listIndex">
						<!-- 应用项容器 -->
						<view class="learun-grid-item">
							<!-- 应用图标容器 -->
							<view class="learun-grid-icon">
								<!-- learun-icon 组件显示应用图标，绑定图标类型、颜色和大小 -->
								<learun-icon :type="listItem.icon" :color="listItem.f_Color" size="24"></learun-icon>
							</view>
							<!-- 应用名称容器 -->
							<view><text class="learun-grid-text">{{$t(listItem.f_Name)}}</text></view>
						</view>
						<!-- 编辑模式下显示移除图标 -->
						<view v-if="edit" class="ab-tr">
							<learun-icon type="learun-icon-circle-minus-base" size="16" color="#e43d33"></learun-icon>
						</view>
					</uni-grid-item>
				</uni-grid>

				<!-- 操作区按钮 -->
				<view class="padding-top" style="text-align: center;">
					<!-- 编辑按钮，根据编辑状态显示不同文本，绑定点击事件 -->
					<button @click="editClick" :plain="true"
						type="primary">{{edit ? $t('完成编辑') : $t('编辑“我的应用”列表')}}</button>
				</view>
				<!-- 编辑模式下显示放弃编辑按钮 -->
				<view v-if="edit" class="padding-top" style="text-align: center;">
					<button @click="cancelClick" :plain="true" type="warn">{{$t('放弃编辑')}}</button>
				</view>
			</uni-card>

			<!-- 分块显示功能区，循环渲染分组列表 -->
			<template v-for="(group, title) in groupList">
				<!-- 分组卡片，绑定标题 -->
				<uni-card :title="$t(title)" padding="8px" margin="8px" :border="false" :is-shadow="false" :key="title">
					<!-- uni-grid 组件用于展示分组内的应用列表，设置列数为 4，绑定点击事件 -->
					<uni-grid class="learun-home-more-grid"  :showBorder="false" :column="4" @change="funcListClick($event,group)">
						<!-- 循环渲染分组内的应用列表项 -->
						<uni-grid-item v-for="(listItem,listIndex) in group" :key="listIndex" :index="listIndex">
							<!-- 应用项容器 -->
							<view class="learun-grid-item">
								<!-- 应用图标容器 -->
								<view class="learun-grid-icon">
									<!-- learun-icon 组件显示应用图标，绑定图标类型、颜色和大小 -->
									<learun-icon :type="listItem.icon" :color="listItem.f_Color"
										size="24"></learun-icon>
								</view>
								<!-- 应用名称容器 -->
								<view><text class="learun-grid-text">{{$t(listItem.f_Name)}}</text></view>
							</view>
							<!-- 编辑模式下显示添加或移除图标，根据是否在编辑列表中显示不同图标 -->
							<view v-if="edit" class="ab-tr">
								<learun-icon :color="editList.includes(listItem.f_Id)?'#e43d33':'#3c9cff'"
									:type="editList.includes(listItem.f_Id) ?'learun-icon-circle-minus-base':'learun-icon-circle-add-base'"
									size="16"></learun-icon>
							</view>
						</uni-grid-item>
					</uni-grid>
				</uni-card>
			</template>
		</view>
	</view>
</template>

<script>
	// 引入 lodash 工具函数
	import without from 'lodash/without'
	import concat from 'lodash/concat'
	import keyBy from 'lodash/keyBy'
	import mapKeys from 'lodash/mapKeys'
	import mapValues from 'lodash/mapValues'
	import groupBy from 'lodash/groupBy'
	import values from 'lodash/values'

	export default {
		data() {
			return {
				// 功能分组名称映射
				groupNames: {},
				// 所有可用的功能模块
				myModules: [],
				// 我的应用列表的 ID 数组
				myList: [],
				// 编辑模式下的应用列表 ID 数组
				editList: [],
				// 搜索框中的文本
				searchText: '',
				// 搜索框是否获取焦点
				focus: false,
				// 是否处于编辑模式
				edit: false
			}
		},

		async onLoad({
			search
		}) {
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 初始化页面数据
				await this.init(search)
			}
		},

		onShow() {
			// 清空页面数据
			this.SET_ALL_DATA({})
		},

		methods: {
			// 页面初始化方法
			async init(search) {
				// 根据是否有搜索参数设置搜索框焦点
				this.focus = !!search
				// 显示加载提示
				this.LOADING(this.$t('加载功能列表中…'))
				// 同时发出三个请求，分别获取所有功能按钮、我的列表、数据字典中的功能名称表
				const [modules, myModuleIds, groupNames] = await Promise.all([
					this.HTTP_GET({
						url: '/mapp/modules'
					}),
					this.HTTP_GET({
						url: '/mapp/module/mylist'
					}),
					this.FETCH_DATAITEM('function').then(result => mapValues(keyBy(values(result),
						'f_ItemValue'), 'f_ItemName'))
				])
				// 初始化功能模块数据
				this.initModule(modules, myModuleIds, groupNames)
				// 隐藏加载提示
				this.HIDE_LOADING()
			},

			initModule(modules, myModuleIds, groupNames) {
				// 获取当前登录用户信息
				const loginUser = this.GET_GLOBAL('loginUser')
				// 过滤出可用的功能模块
				const myModules = []
				modules.forEach(item => {
					if (item.f_EnabledMark == 1 && item.f_IsSystem != 3 && (loginUser.f_SecurityLevel == 1 || (
							loginUser.moduleAuthIds || []).indexOf(item.f_Id) > -1)) {
						// 获取图标信息
						const icon = item.f_Icon ? item.f_Icon : ''
						myModules.push({
							...item,
							icon: icon
						})
					}
				})
				// 将可用的功能模块存储到全局变量中
				this.SET_GLOBAL('learun_modules', myModules)
				// 更新本地的功能模块数组
				this.myModules = myModules
				// 过滤出我的应用列表中存在的模块 ID
				this.myList = myModuleIds.filter(id => myModules.findIndex(t => t.f_Id == id) > -1)
				// 更新功能分组名称映射
				this.groupNames = groupNames
			},

			// 点击编辑按钮，开启或保存编辑模式
			async editClick() {
				if (!this.edit) {
					// 开启编辑模式，复制我的应用列表到编辑列表
					this.editList = [...this.myList]
					this.edit = true
					return
				}
				// 保存编辑结果，发送 PUT 请求更新我的应用列表
				const success = await this.HTTP_PUT({
					url: `/mapp/module/mylist`,
					data: {
						ids: this.editList.join(',')
					},
					errorTips: this.$t('「我的应用」列表更新失败')
				})

				if (!success) {
					// 更新失败，恢复编辑列表为原始的我的应用列表，关闭编辑模式
					this.editList = [...this.myList]
					this.edit = false
					return
				}
				// 更新成功，更新我的应用列表，关闭编辑模式，触发列表更新事件
				this.myList = [...this.editList]
				this.edit = false
				this.EMIT('home-list-change')
			},

			// 获取按钮图标背景色（已在列表中的为橙色，不在的为蓝色）
			funcListIconColor(item) {
				// 根据编辑状态选择使用编辑列表还是我的应用列表
				const list = this.edit ? this.editList : this.myList
				// 根据是否在列表中返回不同的颜色
				return list.includes(item.f_Id) ? '#fe955c' : '#62bbff'
			},

			// 点击按钮
			funcListClick(e, list) {
				// 获取点击的应用项
				const item = list[e.detail.index]
				if (this.edit) {
					// 编辑模式下，处理添加或移除应用
					this.itemClick(item.f_Id)
				} else {
					// 非编辑模式下，跳转到应用模块
					this.TO_MODULE(item, {
						f_Name: item.f_Name
					})
				}
			},

			// 取消编辑按钮
			cancelClick() {
				// 关闭编辑模式
				this.edit = false
			},

			// 编辑模式，功能区点击添加/移除按钮
			itemClick(id) {
				if (this.editList.includes(id)) {
					// 如果应用已在编辑列表中，移除该应用
					this.editList = without(this.editList, id)
					return
				}
				// 如果应用不在编辑列表中，添加该应用
				this.editList = concat(this.editList, id)
			}
		},

		computed: {
			// 我的应用列表
			myListDisplay() {
				// 根据编辑状态选择使用编辑列表还是我的应用列表
				const list = this.edit ? this.editList : this.myList
				// 将 ID 数组转换为应用对象数组
				return list.reduce((list, id) => [...list, this.myModules.find(t => t.f_Id === id)], [])
			},

			// 获取列表分组
			groupList() {
				// 根据搜索文本过滤功能模块，并按类型分组，然后将分组的键转换为分组名称
				return mapKeys(
					groupBy(this.myModules.filter(item => item.f_Name.includes(this.searchText)), 'f_Type'),
					(v, k) => this.groupNames[k]
				)
			}
		}
	}
</script>