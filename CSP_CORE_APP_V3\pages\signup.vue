<template>
	<view class="page">
		<!-- 返回按钮，点击触发goBack方法，使用uni-icons组件显示向左箭头 -->
		<view class="back-button" @click="goBack">
			<uni-icons type="left" size="24" color="#333"></uni-icons>
		</view>
		<view class="content">
			<!-- 多语言设置按钮，仅当语言类型数量大于1时显示，点击触发pickerLangType方法 -->
			<view @click="pickerLangType" class="learun-lang" v-if="langTypes().length > 1">
				<learun-icon type="menu-translation-m"></learun-icon>
			</view>
			<!-- 语言选择弹出层，仅当语言类型数量大于1时渲染，通过ref引用组件，绑定选项和变化事件 -->
			<learun-picker-popup v-if="langTypes().length > 1" ref="langTypePicker" :options="langTypes()"
				@change="changeLangType">
			</learun-picker-popup>
			<!-- 首页Logo显示区域，背景图为静态资源，使用mode="aspectFit"保持图片比例 -->
			<view mode="aspectFit" class="logo"
				:style="{ backgroundImage: `url('/csp_core_app_321/static/logo.png')` }">
			</view>
			<!-- 标题区域，包含开发模式标识和注册标题，使用多语言翻译$t -->
			<view class="title">
				<text v-if="DEV" class="devTitle">(开发模式)</text>
				<text class="subTitle">{{ $t('新用户注册') }}</text>
			</view>
			<!-- 公司选择Picker组件，绑定选择事件和选项数据，显示公司选择框 -->
			<picker @change="bindPickerChange" :value="companyCode" :range="companyArray" range-key="name"
				class="margin-top">
				<view class="company-view">
					<uni-icons type="home" size="16" color="rgb(192, 196, 204)" class="company-icons"></uni-icons>
					<view :class="'uni-input ' + (companyIndex >= 0 ? '' : 'placeholder-view')">
						{{ companyIndex >= 0 ? companyArray[companyIndex] : $t("请选择公司") }}
					</view>
				</view>
			</picker>
			<!-- 工号输入框，使用uni-easyinput组件，绑定工号数据和占位符，显示人物前缀图标 -->
			<uni-easyinput class="margin-top input-div" trim v-model="empNo" :placeholder="$t('请输入工号')"
				prefixIcon="person" :inputBorder="false" :height="inputHeight"></uni-easyinput>
			<!-- 首次密码输入框，密码类型，显示锁前缀图标 -->
			<uni-easyinput class="margin-top input-div" trim v-model="password" :placeholder="$t('请输入密码')"
				prefixIcon="locked" type="password" :inputBorder="false" :height="inputHeight"></uni-easyinput>
			<!-- 再次输入密码框，与首次密码进行一致性验证 -->
			<uni-easyinput class="margin-top input-div" trim v-model="verifyPassword" :placeholder="$t('请再次输入密码')"
				prefixIcon="locked" type="password" :inputBorder="false" :height="inputHeight"></uni-easyinput>
			<!-- 手机号输入区域，包含区号选择和手机号输入框 -->
			<view class="phone-view margin-top input-div">
				<!-- 区号选择器，显示电话图标，通过Picker选择区号 -->
				<uni-icons type="phone" size="16" color="rgb(192, 196, 204)" class="phone-icon"></uni-icons>
				<picker @change="bindPickerChangeCode" :value="areaCode" :range="companyCodeArray">
					<view>{{areaCode}}</view>
				</picker>
				<!-- 手机号输入框，无边框，绑定手机号数据 -->
				<uni-easyinput class="phone-input" trim v-model="phone" :placeholder="$t('请输入手机号码')"
					:inputBorder="false" :height="inputHeight"></uni-easyinput>
			</view>
			<!-- 验证码输入区域，包含输入框和获取验证码按钮 -->
			<view class="code-view">
				<uni-easyinput class="margin-top input-div code-input" trim v-model="code" :placeholder="$t('请输入验证码')"
					prefixIcon="mail-open-filled" :inputBorder="false" :height="inputHeight"></uni-easyinput>
				<!-- 获取验证码按钮，根据sendOk状态显示可点击性和文本，绑定点击事件 -->
				<button @click="getCode" :disabled="!sendOk" size="lg" line="blue" class="margin-top block" block>
					{{ sendOk ? $t('获取验证码') : $t(`请`) + ` ` + $t(`${waitSec}`) + `S` + ` ` + $t(`后`) + ` ` + $t(`重发`) }}
				</button>
			</view>
			<!-- 注册按钮，主要类型，点击触发signUp方法 -->
			<button class="margin-top" @click="signUp" type="primary">{{ $t('注册') }}</button>
		</view>
	</view>
</template>

<script>
	import moment from "moment";
	import cryptoJS from '../common/crypto.js'

	export default {
		data() {
			return {
				// 公司代码，用于存储选中公司的代码
				companyCode: "",
				// 公司索引，记录Picker中选中公司的索引
				companyIndex: -1,
				// 工号，用户输入的工号信息
				empNo: "",
				// 手机号，用户输入的手机号码
				phone: "",
				// 验证码，用户输入的验证码
				code: "",
				// 手机号区号，默认中国大陆区号
				areaCode: "+86",
				// 首次输入的密码，用于注册的密码
				password: "",
				// 再次输入的密码，用于验证密码一致性
				verifyPassword: "",
				// 是否发送了验证码，标记验证码是否已发送
				sendCode: false,
				// 是否可以发送验证码，控制按钮的可点击状态
				sendOk: true,
				// 定时器，用于处理验证码发送间隔
				timer: null,
				// 验证码重发等待秒数，默认90秒
				waitSec: 90,
				// 公司列表，存储公司名称用于Picker显示
				companyArray: [],
				// 公司详细列表，存储公司代码和名称等详细信息
				companyList: [],
				// 区号列表，存储可选择的手机号区号
				companyCodeArray: [],
				// 屏幕高度和宽度，用于动态调整输入框高度
				screenHeight: 0,
				screenWidth: 0,
				// 输入框高度，根据屏幕宽度动态计算
				inputHeight: 50,
				// 系统国家信息，用于获取对应国家的区号
				systemState: [],
			};
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("注册账号")
			})
			// 获取屏幕信息，用于动态调整输入框高度
			this.getScreenInfo();
			// 获取公司列表数据，填充companyArray和companyList
			const res = await this.HTTP_GET({
				url: '/register/companyList'
			}).then(companiesList => {
				companiesList.rows.forEach(res => {
					this.companyArray.push(res.sys_value2)
					this.companyList.push(JSON.parse(JSON.stringify(res)))
				})
			})
			// 获取系统国家信息和手机号区号列表
			this.systemState = await this.HTTP_GET({
				url: '/register/systemNation'
			})
			const resPhone = await this.HTTP_GET({
				url: '/register/phoneAreaCode'
			})
			JSON.parse(JSON.stringify(resPhone)).rows.forEach(resCode => {
				this.companyCodeArray.push(resCode.sys_value2)
			})
			this.companyCodeArray = JSON.parse(JSON.stringify(this.companyCodeArray))
		},
		onResize() {
			// 屏幕尺寸变化时重新获取屏幕信息
			this.getScreenInfo();
		},
		async onShow() {
			// 页面显示时检查本地存储的验证码发送时间，恢复定时器状态
			const savedNextTime = this.GET_STORAGE('nextTime');
			if (savedNextTime && moment(savedNextTime).isSameOrAfter(moment())) {
				this.sendCode = true;
				this.updateTimer(moment(savedNextTime));
			}
		},
		onHide() {
			// 页面隐藏时清除定时器，避免内存泄漏
			clearInterval(this.timer);
		},
		methods: {
			// 获取屏幕信息，更新屏幕宽度、高度和输入框高度
			getScreenInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.screenHeight = res.windowHeight;
						this.screenWidth = res.windowWidth;
						// 输入框高度根据屏幕宽度动态调整，范围50-200px
						this.inputHeight = Math.min(200, Math.max(50, this.screenWidth * 0.1));
					},
					fail: (err) => {
						console.error(this.$t('获取屏幕信息失败:'), err);
					}
				});
			},
			// 返回上一页，显示加载提示后导航到登录页
			goBack() {
				this.LOADING(this.$t('返回中...'));
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login'
					});
				}, 1000);
			},
			// 打开语言选择弹出层，传入当前语言类型
			pickerLangType() {
				this.$refs.langTypePicker.open(this.GET_STORAGE('learun_lang_type'))
			},
			// 获取支持的语言类型列表，用于多语言切换
			langTypes() {
				return this.GET_LANG_TYPES();
			},
			// 处理语言类型变化，更新本地存储并重新获取语言数据
			async changeLangType({
				index,
				value
			}) {
				this.SET_STORAGE('learun_lang_type', value)
				await this.FETCH_LANG_DATA()
			},
			// 获取验证码，验证输入并发送请求，启动定时器处理冷却时间
			async getCode() {
				if (!this.validateBeforeSendCode() || !this.sendOk) {
					return;
				}
				this.sendOk = false;
				const next = moment().add(90, 'seconds');
				this.SET_STORAGE('nextTime', next.format('YYYY-MM-DD HH:mm:ss'));
				this.updateTimer(next);
				this.showToast(this.$t('验证码发送成功', 'success'));
				this.sendCode = true;
			},
			// 处理注册逻辑，验证输入后调用注册API，根据结果跳转或提示
			async signUp() {
				if (!this.validateInput()) {
					return;
				}
				// 显示加载提示
				this.showLoading(this.$t('注册中...'));
				// 发送注册请求，包含公司、工号、手机号、验证码、密码等信息
				this.HTTP_POST_ALL({
					url: '/register',
					data: {
						company_Code: this.companyCode,
						emp_No: this.empNo,
						phone_Area_Code: this.areaCode,
						phone_Number: this.phone,
						code: this.code,
						password: this.MD5(this.verifyPassword),
					},
				}).then((res) => {
					this.hideLoading();
					if (res && res.data.info === '注册成功!') {
						// 注册成功后提示并跳转登录页
						this.LOADING(this.$t('注册成功，前往登录…'));
						setTimeout(() => {
							this.NAV_TO('/pages/login');
						}, 1500);
					} else {
						// 注册失败提示错误信息
						this.showToast(this.$t(res.data.info, 'error'));
					}
				});
			},
			// 验证所有输入字段是否符合要求
			validateInput() {
				// 依次验证公司、工号、密码、手机号、验证码
				if (!this.validateCompany()) return false;
				if (!this.validateEmpNo()) return false;
				if (!this.validatePassword()) return false;
				if (!this.validatePhone()) return false;
				if (!this.validateCode()) return false;
				return true;
			},
			// 验证公司是否选择，未选择时提示
			validateCompany() {
				if (this.companyCode === '') {
					this.showToast(this.$t('请选择公司'), 'error');
					return false;
				}
				return true;
			},
			// 验证工号是否为空及格式是否正确
			validateEmpNo() {
				if (this.empNo === '') {
					this.showToast(this.$t('工号不能为空'), 'error');
					return false;
				}
				if (this.empNo.length < 6) {
					this.showToast(this.$t('工号格式不正确，最少为6位工号'), 'error');
					return false;
				}
				return true;
			},
			// 区号选择变化处理，更新areaCode
			bindPickerChangeCode(e) {
				this.areaCode = this.companyCodeArray[e.detail.value]
			},
			// 验证密码是否符合复杂度要求及两次输入是否一致
			validatePassword() {
				if (this.password === '') {
					this.showToast(this.$t('请输入密码'), 'error');
					return false;
				}
				// 密码正则：至少8位，包含字母、数字和特殊字符
				const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&+-_])[A-Za-z\d@$!%*#?&+-_]{8,}$/;
				if (!passwordRegex.test(this.password)) {
					this.showToast(this.$t('密码长度至少为 8，至少含有一个字母、一个数字和一个特殊字符'), 'error', 2500);
					return false;
				}
				if (this.verifyPassword === '') {
					this.showToast(this.$t('请再次输入密码'), 'error');
					return false;
				}
				if (this.password !== this.verifyPassword) {
					this.showToast(this.$t('两次密码不一致'), 'error');
					return false;
				}
				return true;
			},
			// 验证手机号是否合法，拼接区号后检查格式
			validatePhone() {
				if (this.phone === '') {
					this.showToast(this.$t('手机号不能为空'), 'error');
					return false;
				}
				const fullPhoneNumber = this.areaCode + this.phone;
				if (!this.validatePhoneNumber(fullPhoneNumber)) {
					this.showToast(this.$t('手机号不正确'), 'error');
					return false;
				}
				return true;
			},
			// 验证验证码是否输入
			validateCode() {
				if (this.code === '') {
					this.showToast(this.$t('请获取验证码，并填入'), 'error', 2000);
					return false;
				}
				return true;
			},
			// 发送验证码前验证手机号是否合法
			validateBeforeSendCode() {
				return this.validatePhone();
			},
			// 更新验证码重发定时器，计算剩余时间
			updateTimer(nextTime) {
				const setTime = () => {
					// 判断是否可以重新发送验证码
					this.sendOk = moment(nextTime).isSameOrBefore();
					if (!this.sendOk) {
						// 计算剩余秒数
						this.waitSec = moment.duration(moment(nextTime).diff()).asSeconds().toFixed(0);
					} else {
						// 清除定时器
						clearInterval(this.timer);
					}
				};
				this.timer = setInterval(setTime, 1000);
				setTime();
			},
			// 显示Toast提示，支持自定义图标和持续时间
			showToast(title, icon = 'none', duration = 1500) {
				uni.showToast({
					title,
					icon,
					duration
				});
			},
			// 显示加载提示
			showLoading(title) {
				uni.showLoading({
					title
				});
			},
			// 隐藏加载提示
			hideLoading() {
				uni.hideLoading();
			},
			// 公司选择变化处理，更新公司代码和对应区号
			async bindPickerChange(e) {
				this.companyIndex = e.detail.value;
				const resPhone = await this.HTTP_GET({
					url: '/register/phoneAreaCode'
				})
				// 根据选中公司查找对应的区号
				JSON.parse(JSON.stringify(this.companyList)).forEach((res) => {
					if (res.sys_value2 === this.companyArray[this.companyIndex]) {
						this.companyCode = res.sys_value1
					}
					if (res.sys_value1 === this.companyCode) {
						resPhone.rows.forEach(phone => {
							if (phone.sys_value1 == res.sys_type) {
								this.areaCode = phone.sys_value2;
							}
						})
					}
				})
			},
			// 验证手机号是否符合多个国家的格式规则
			validatePhoneNumber(phoneNumber) {
				const regexPatterns = [
					// 多个国家的手机号正则表达式
					/^\+?86?1[3-9]\d{9}$/, // 中国大陆
					/^(\+?852)?[569]\d{7}$/, // 中国香港
					// 其他国家规则...
				];
				// 遍历正则表达式进行匹配
				for (const pattern of regexPatterns) {
					const isMatch = pattern.test(phoneNumber);
					console.log('isMatch: ', isMatch);
					if (isMatch) {
						return true;
					}
				}
				return false;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.page {
		height: 100%;
		width: 100%;
		background-color: #ffffff;

		// 返回按钮定位在左上角，固定位置
		.back-button {
			position: absolute;
			top: 20px;
			left: 20px;
			z-index: 10;
		}

		.content {
			text-align: center;
			width: 90%;
			max-width: 600px;
			margin: 0 auto;
			padding: 0 38rpx;
			border-radius: 20px 20px 0 0;
			position: relative;
			top: 10%;
			background-color: #ffffff;

			// 多语言按钮定位在内容区右上角
			.learun-lang {
				position: absolute;
				top: -20px;
				right: 20px;
			}

			.title {
				display: block;
				margin: 8px 0;
				font-size: 20px;
				margin-bottom: 16px;
				color: $uni-main-color;

				.devTitle {
					color: $uni-error;
				}

				// 开发模式标题颜色为错误色
				.subTitle {
					font-size: 16px;
					color: $uni-info;
				}

				// 副标题颜色为信息色
			}

			.logo {
				background-size: contain;
				height: 110px;
				width: 150px;
				display: inline-block;
				border-radius: 2px;
			}

			// 公司选择框样式，带边框和圆角
			.company-view {
				.company-icons {
					padding: 0 5px;
				}

				border: 1px solid #E5E5E5;
				border-radius: 4px;
				min-height: 36px;
				display: flex;
				align-items: center;
			}

			.input-div {
				margin-top: 16px;
			}

			// 输入框外边距
			// 手机号输入区域弹性布局，图标、区号、输入框水平排列
			.phone-view {
				display: flex;
				align-items: center;

				.phone-icon {
					padding: 0 5px;
				}
			}

			// 验证码区域布局，输入框和按钮并排显示
			.code-view {
				display: flex;

				.phone-input {
					border-radius: 4px 0 0 4px;
				}

				// 输入框左圆角
				uni-button {
					border-radius: 0 4px 4px 0;
					border: 1px solid #E5E5E5;
					height: 38px;
				}

				// 按钮右圆角
			}

			.placeholder-view {
				color: #999;
				font-size: 12px;
				font-weight: 200;
			}

			// 占位符样式
		}

		// 小屏幕设备样式调整
		@media (max-width: 480px) {
			.page .content {
				top: 5%;
				padding: 0 20px;
			}

			.page .content .logo {
				height: 100px;
				width: 140px;
			}
		}
	}
</style>