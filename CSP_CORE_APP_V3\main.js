import Vue from 'vue'
import moment from 'moment'
import App from '@/App.vue'
//import store from '@/common/store.js'
import mixins from '@/common/mixins/index.js'

moment.locale('zh-cn')
uni.$learun = {
	data:{
		learun_datasource:{}
	}
}
Vue.mixin(mixins)
Vue.config.productionTip = process.env.NODE_ENV === 'development'
//Vue.prototype.$store = store



//new Vue({ ...App, mpType: 'app', store }).$mount()
new Vue({ ...App, mpType: 'app' }).$mount()