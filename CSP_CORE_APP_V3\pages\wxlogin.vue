<template>
  <!-- 页面容器，类名为page和login-page，最小高度根据SCREENHEIGHT函数返回值设置 -->
  <view
    class="page login-page"
    :style="{ 'min-height': SCREENHEIGHT() + 'px' }"
  >
    <view class="content">
      <!-- 头部横幅区域 -->
      <view class="head-banner">
        <!-- 显示logo，通过背景图展示，图片路径为/csp_core_app/static/logo.png -->
        <view
          mode="aspectFit"
          class="logo"
          style="background-image: url('../static/logo.png')"
        >
        </view>
        <!-- 标题区域，当前显示Loading... -->
        <view class="title">
          <text>Loading...</text>
          <!-- <text>{{testError}}</text> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 用户名
      username: "",
      // 密码
      password: "",
      // 明文密码
      password2: "",
      // 页面是否准备好
      ready: false,
      // 是否显示API根路径选择器
      showApiRootSelector: false,
      // 当前选择的API根路径
      currentApiRoot: "",
      // API根路径列表
      apiRootList: [],
      // 开发账号列表
      devAccountList: [],
      // 企业微信ID
      qywxID: "",
      // 搜索数据
      searchData: {},
      // 开放码
      opencode: "",
      // testError:'',
    };
  },
  async onLoad(options) {
    //option为object类型，会序列化上个页面传递的参数
    let res = null;
    this.searchData.openid = options.code;
    this.opencode = options.code;

    //--------快捷表单跳转参数--------------
    if (options && options.state) {
      const instate = options.state;
      // 若state中包含create，处理创建表单跳转参数
      if (instate.indexOf("create") != -1) {
        let inparam = {};
        inparam.optype = "create";
        inparam.toformid = instate.replace("create", "");
        // 将创建表单参数保存到全局变量中
        this.SET_GLOBAL("createformparam", inparam);
        //console.log(inparam,"inparam")
      }
      // 若state中包含workflowDetail，处理跳转至明细页面
      if (instate.indexOf("workflowDetail") != -1) {
        let NavToPagesUrl = "/pages/navToPages?page='" + instate + "'";
        // 将跳转URL保存到全局变量中
        this.SET_GLOBAL("NavToPagesUrl", NavToPagesUrl);
      }
      // 若state中包含workflowList，处理跳转至工作流列表页面
      if (instate.indexOf("workflowList") != -1) {
        let NavToPagesUrl =
          "/pages/navToPages?page='/pages/workflow/mytask/list'";
        // 将跳转URL保存到全局变量中
        this.SET_GLOBAL("NavToPagesUrl", NavToPagesUrl);
      }
      // 若state以ToPayroll开头，处理跳转至工资单页面
      if (instate.indexOf("ToPayroll") == 0) {
        let NavToPagesUrl = "/pages/navToPages?page='ToPayroll'";
        // 将跳转URL保存到全局变量中
        this.SET_GLOBAL("NavToPagesUrl", NavToPagesUrl);
      }
      // 若state以ToPayrollEnd开头，处理跳转至工资单结束页面
      if (instate.indexOf("ToPayrollEnd") == 0) {
        let NavToPagesUrl = "/pages/navToPages?page='ToPayrollEnd'";
        // 将跳转URL保存到全局变量中
        this.SET_GLOBAL("NavToPagesUrl", NavToPagesUrl);
      }
      //options.optype && options.toformid
    }
    //---------------------------------------
    if (options && options.code) {
      if (options.code != "undefined") {
        let url = `/login/openidqy/${options.code}`;
        // 根据type参数调整登录URL
        if (options.type == "EnCode") {
          // 中山
          url = `/login/openidgzh/${options.code}`;
        }
        if (options.code == "PublicLogin") {
          // 公共账号匿名登陆
          url = `/login/openidgzh/2E617FBB96F159AD71A3C441D327AC49`;
        }
        console.log(84, url);
        // this.testError = url
        res = await this.HTTP_POST({
          url: url,
          params: "",
          data: {},
          errorTips: "登录时发生错误",
        });
        // this.testError + res
        // res = {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************.jGX9cOJvTamyhUaw_WAgZkcQ4GI-F9Ky3QxM86-pRSA"};
        if (!res) {
          // 登录失败，重新跳转到登录页
          this.RELAUNCH_TO("/pages/login");
          return;
        }
        // 将token保存到全局变量和本地存储中
        this.SET_GLOBAL("token", res.token);
        this.SET_STORAGE("token", res.token);
      }
    }
    // 从缓存中把token放到全局变量中
    if (
      (this.GET_GLOBAL("token") == undefined ||
        this.GET_GLOBAL("token") == null) &&
      this.GET_STORAGE("token") != undefined &&
      this.GET_STORAGE("token") != null
    ) {
      this.SET_GLOBAL("token", this.GET_STORAGE("token"));
    }
    // 获取当前用户信息
    const success = await this.FETCH_CURRENT_USERINFO();
    if (!success) {
      // 获取用户信息失败，提示错误
      // this.TOAST('获取用户/部门/公司信息时出现错误，登录失败');
      return;
    }

    // 加载图标数据
    await this.FETCH_ICONS();

    // 隐藏加载提示
    this.HIDE_LOADING();
    // 跳转到主页
    this.TAB_TO("/pages/home");
  },

  methods: {
    // 页面初始化
    async init() {
      this.ready = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.login-page {
  // 使用flex布局，垂直居中内容
  display: flex;
  justify-content: center;
  flex-direction: column;

  .head-banner {
    // 底部外边距
    margin-bottom: 32px;

    .title {
      // 块级元素显示
      display: block;
      // 上下外边距
      margin: 8px 0;
      // 字体大小
      font-size: 10px;
      // 底部外边距
      margin-bottom: 16px;
      // 字体颜色
      color: $uni-main-color;
    }

    .devTitle {
      // 开发模式标题颜色
      color: $uni-error;
    }

    .subTitle {
      // 副标题字体大小和颜色
      font-size: 16px;
      color: $uni-info;
    }

    .logo {
      // 背景图自适应
      background-size: contain;
      // 高度
      height: 36px;
      // 宽度
      width: 50px;
      // 文本居中
      text-align: center;
      // 行内块级元素显示
      display: inline-block;
      // 圆角
      border-radius: 2px;
    }

    .login-item {
      // 上下内边距
      padding: 12rpx 0;
      // 底部边框
      border-bottom: 1px solid #eee;
      // 底部外边距
      margin-bottom: 20rpx;

      // 深度选择器，选择u-icon组件
      /deep/ .u-icon {
        width: 80rpx;
      }
    }

    .intro {
      // 字体大小
      font-size: 12px;
      // 顶部外边距
      margin-top: 8px;
      // 字体颜色
      color: $uni-base-color;
    }

    .subIntro {
      // 副标题颜色
      color: $uni-extra-color;
    }
  }

  .content {
    // 文本居中
    text-align: center;
    // 宽度
    width: 100%;
    // 左右内边距
    padding: 0 24px;
    // 盒模型计算方式
    box-sizing: border-box;
  }

  .footer {
    // 绝对定位
    position: absolute;
    left: 0;
    right: 0;
    bottom: 8px;
    bottom: calc(8px + env(safe-area-inset-bottom));
    // 文本居中
    text-align: center;
    // 字体大小
    font-size: 12px;
    // 字体颜色
    color: $uni-info;
    /* #ifdef MP-ALIPAY */
    bottom: 8px;
    /* #endif */
  }
}
</style>
