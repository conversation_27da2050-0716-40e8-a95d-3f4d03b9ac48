<template>
  <!-- Markdown编辑器容器 -->
  <div id="markdown-container" style="width: 100%; height: 1000px;"></div>
</template>

<script>
  // 引入Markdown编辑器样式
  import 'cherry-markdown/dist/cherry-markdown.css';
  // 引入Markdown编辑器组件
  import Cherry from 'cherry-markdown';

  export default {
    mounted() {
      /**
       * 文件上传处理函数（模拟实现）
       * @param {File} file - 上传的文件对象
       * @param {Function} callback - 上传成功回调函数（url, options）
       */
      function myFileUpload(file, callback) {
        // 模拟异步上传（实际项目中替换为真实上传逻辑）
        setTimeout(() => {
          const url = `https://example.com/uploads/${file.name}`; // 模拟文件URL

          // 根据文件类型设置不同的显示选项
          if (/video/i.test(file.type)) { // 视频文件
            callback(url, {
              name: '视频', // 显示名称
              poster: `${url}?poster=true`, // 视频封面
              isBorder: true, // 显示边框
              isShadow: true, // 显示阴影
              isRadius: true // 圆角显示
            });
          } else if (/image/i.test(file.type)) { // 图片文件
            callback(url, {
              name: '图片', // 显示名称
              isBorder: true, // 显示边框
              isShadow: true, // 显示阴影
              isRadius: true, // 圆角显示
              width: '60%', // 宽度百分比
              height: 'auto' // 高度自适应
            });
          } else { // 其他文件类型
            callback(url); // 仅返回URL
          }
        }, 1000);
      }

      /**
       * 初始化Markdown编辑器
       * 配置说明：https://cherry-markdown.js.org/guide/configuration.html
       */
      new Cherry({
        id: 'markdown-container', // 绑定容器ID
        value: '## hello', // 初始Markdown内容
        fileUpload: myFileUpload, // 自定义文件上传函数

        // 工具栏配置（核心功能区域）
        toolbars: {
          // 顶部工具栏（主操作区）
          toolbar: [
            'undo', 'redo', '|', // 撤销/重做/分隔符
            
            // 格式工具栏（组合按钮）
            {
              bold: ['bold', 'italic', 'underline', 'strikethrough', 'sub', 'sup', 'ruby']
            },
            
            'color', 'size', '|', // 颜色/字号/分隔符
            'header', 'list', 'panel', '|', // 标题/列表/面板/分隔符
            
            // 插入工具栏（组合按钮）
            {
              insert: [
                'image', 'audio', 'video', // 多媒体
                'link', 'hr', 'br', // 链接/水平线/换行
                'code', 'formula', 'toc', // 代码/公式/目录
                'table', 'drawIo' // 表格/流程图
              ]
            },
            
            'graph' // 图表工具
          ],

          // 侧边栏（辅助功能区）
          sidebar: ['theme', 'mobilePreview', 'copy'], // 主题/手机预览/复制

          // 顶部右侧工具栏（快捷操作区）
          toolbarRight: ['fullScreen', 'export'], // 全屏/导出

          // 悬浮工具栏（选中文本时显示）
          bubble: [
            'bold', 'italic', 'underline', 'strikethrough', // 基础格式
            'sub', 'sup', 'ruby', '|', // 上下标/ruby/分隔符
            'color', 'size' // 颜色/字号
          ],

          // 行首工具栏（光标在行首时显示）
          float: ['table', 'code', 'graph'] // 表格/代码/图表
        }
      });
    }
  };
</script>