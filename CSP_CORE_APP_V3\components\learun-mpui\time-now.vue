<template>
	<text>{{time}}</text>
</template>

<script>
	export default {
		name: 'learun-time-now',
		props: {
			value: String,
			format: {
				type: String,
				default: 'HH:mm:ss'
			}
		},
		data() {
			return {
				time:''
			}
		},
		created(){
			this.refreshTime();
		},
		methods: {
			refreshTime() {
				this.time = this.DATENOW(this.format)
				this.$emit('input', this.time)
				setTimeout(() => {
					this.refreshTime();
				}, 1000)
			}
		}
	}
</script>