<template>
	<view class="page home" :style="{'min-height':SCREENHEIGHT()+'px'}">
		<!-- 顶部渐变背景条，用于过渡顶部颜色到白色背景 -->
		<view class="gradient-bar"></view>
		<!-- 顶部搜索栏容器，包含搜索输入和功能按钮 -->
		<view class="header">
			<!-- 搜索输入区域，点击可展开搜索功能 -->
			<view @click="moreClick(1)" class="header-mid margin-lr">
				<view class="header-mid-icon">
					<!-- 放大镜图标，提示搜索功能 -->
					<learun-icon type="learun-icon-magnifying-glass-m" color="#B3B3B3" size="12" />
				</view>
				<!-- 搜索提示文本，使用多语言翻译 -->
				{{$t('搜索更多应用')}}
			</view>
			<!-- 扫码功能按钮，点击跳转到扫码页面 -->
			<view class="scan-button" @click="scanningBtn">
				<uni-icons type="camera" size="30"></uni-icons>
			</view>
			<!-- 非H5环境下的扫码图标（如小程序/APP） -->
			<!-- #ifndef H5 -->
			<view class="header-right margin-right">
				<learun-icon @click="scanClick" type="learun-icon-scanning" color="#B3B3B3" />
			</view>
			<!-- #endif -->
		</view>
		<!-- 功能宫格列表容器，展示我的应用和更多选项 -->
		<view class="content padding-bottom" style="margin-bottom: 8px;">
			<uni-grid :showBorder="false" :column="5" @change="funcListClick" :key=gridKey>
				<!-- 循环渲染我的应用列表，每个宫格项包含图标和名称 -->
				<uni-grid-item v-for="(listItem,listIndex) in myModuleApps" :key="listIndex" :index="listIndex">
					<view class="learun-grid-item">
						<view class="learun-grid-icon" style="background-color:transparent;">
							<!-- 应用图标，动态设置图标类型和颜色 -->
							<learun-icon :type="listItem.icon" :color="listItem.f_Color" size="24" />
						</view>
						<!-- 应用名称，使用多语言翻译 -->
						<view><text class="learun-grid-text">{{$t(listItem.f_Name)}}</text></view>
					</view>
				</uni-grid-item>
				<!-- 更多功能宫格项，点击加载更多应用 -->
				<uni-grid-item :index="100">
					<view class="learun-grid-item">
						<view class="learun-grid-icon" style="background-color:transparent;">
							<learun-icon style="transform: rotate(90deg) scale(1.5) translateY(3px);"
								type="more-outlined" color="#8f939c" size="24" />
						</view>
						<view><text class="learun-grid-text">{{$t('更多')}}</text></view>
					</view>
				</uni-grid-item>
			</uni-grid>
		</view>
		<!-- 桌面配置展示区域，根据配置加载导航和数据可视化组件 -->
		<view v-if="homeSettingReady">
			<!-- 导航栏，显示不同桌面模块 -->
			<view v-if="navList().length > 1" style="margin-bottom: 8px;">
				<learun-nav v-model="tab" :items="navList()" />
			</view>
			<!-- 动态加载数据可视化组件，每个组件对应一个桌面模块 -->
			<view v-if="tab == index && isLoadData" v-for="(item, index) of tabList" :key="index">
				<learun-bi :config="item" :getData="getDesktopData" />
			</view>
		</view>
	</view>
</template>


<script>
	export default {
		data() {
			return {
				// 我的应用列表，存储应用图标、名称等信息
				myModuleApps: [],
				// 桌面配置加载完成标志
				homeSettingReady: false,
				// 当前选中的导航标签索引
				tab: 0,
				// 桌面模块配置列表，包含数据可视化配置
				tabList: [],
				// 是否加载任务统计数据标志
				isLoadTaskNum: false,
				// 是否加载任务列表数据标志
				isLoadTaskList: false,
				// 数据源数据，存储接口返回的业务数据
				datasource: {},
				// 页面加载状态标志
				isLoad: true,
				// 页面隐藏状态标志
				isHide: false,
				// 数据加载允许标志
				isLoadData: true,
				// 用于触发uni-grid重新渲染的key值
				gridKey: 1,
				// 页面刷新标记，用于处理刷新后的数据加载
				isRefreshed: false
			};
		},

		async onLoad(param) {
			this.isHide = false;
			// 初始化页面数据，包括用户信息、应用列表等
			await this.init(param);
			this.isLoad = false;
			// 设置页面标题为“首页”
			this.SET_TITLE('首页');
		},

		async onShow() {
			// 页面显示时处理语言切换和标签栏文本
			if (!this.isLoad) {
				this.SET_ALL_DATA(this.datasource);
				this.isHide = false;
				this.homeSettingReady = true;
			}

			// 延迟执行标签栏文本更新，确保 $t 方法已加载
			setTimeout(() => {
				this.updateTabBarTexts();
			}, 500);

			// 刷新宫格列表
			this.gridKey++;

			// 刷新首页数据
			await this.refresh(false);
		},

		onHide() {
			// 页面隐藏时重置状态，释放资源
			this.isHide = true;
			this.homeSettingReady = false;
		},

		// 下拉刷新处理函数，用于更新首页数据
		onPullDownRefresh() {
			this.refresh(true).then(() => {
				this.TOAST('已更新首页数据');
				uni.stopPullDownRefresh(); // 停止下拉刷新动画
			});
		},

		beforeMount() {
			// 监听页面卸载事件，用于记录刷新标记
			window.addEventListener('beforeunload', this.handleBeforeUnload);
		},

		mounted() {
			// 确保组件完全挂载后再更新标签栏文本
			this.updateTabBarTexts();
			// 检查本地存储中的刷新标记，处理刷新后逻辑
			const refreshFlag = sessionStorage.getItem('isRefreshed');
			if (refreshFlag === 'true') {
				this.isRefreshed = true;
				sessionStorage.removeItem('isRefreshed'); // 清除标记
				this.handleRefreshCompleted(); // 执行刷新后数据加载
			}
		},

		beforeDestroy() {
			// 移除事件监听器，防止内存泄漏
			window.removeEventListener('beforeunload', this.handleBeforeUnload);
		},

		methods: {
			updateTabBarTexts() {
				// 检查 $t 方法是否存在
				if (typeof this.$t === 'function') {
					uni.setTabBarItem({
						index: 0,
						text: this.$t("首页")
					});
					uni.setTabBarItem({
						index: 1,
						text: this.$t("工作台")
					});
					uni.setTabBarItem({
						index: 2,
						text: this.$t("新闻")
					});
					uni.setTabBarItem({
						index: 3,
						text: this.$t("我的")
					});
				}
			},
			// 页面即将卸载时记录刷新标记
			handleBeforeUnload() {
				sessionStorage.setItem('isRefreshed', 'true');
			},

			// 处理页面刷新完成后的数据重新加载
			async handleRefreshCompleted() {
				this.isHide = false;
				await this.init({}); // 重新初始化页面数据
				this.isLoad = false;
				this.gridKey++; // 触发宫格列表刷新
				await this.refresh(false); // 刷新首页数据
				this.SET_TITLE('首页'); // 重置页面标题
			},

			// 页面初始化核心方法，处理登录验证和数据加载
			async init(param) {
				// 处理页面跳转参数，存储到全局状态
				if (param && param.learun && param.pagePath) this.SET_GLOBAL('f', param);
				if (param && param.optype && param.toformid) this.SET_GLOBAL('createformparam', param);

				// 验证用户登录状态，无有效token则跳转登录页
				const stateValid = await this.checkLoginState();
				if (!stateValid) {
					this.RELAUNCH_TO('/pages/login');
					return;
				}

				// 处理快捷创建表单参数，跳转对应流程页面
				const createformparam = this.GET_GLOBAL('createformparam');
				if (createformparam && createformparam.optype === 'create') {
					const wfList = await this.getWorkflowList(createformparam.toformid);
					if (wfList.length > 0) {
						this.NAV_TO('/pages/workflow/releasetask/single?type=create', {
							f_Code: createformparam.toformid,
							f_Name: createformparam.toformid
						}, true);
						return;
					}
				}

				// 处理公共跳转参数，跳转指定页面
				const NavToPagesUrl = this.GET_GLOBAL("NavToPagesUrl");
				if (NavToPagesUrl) {
					this.SET_GLOBAL('NavToPagesUrl', null);
					this.NAV_TO(NavToPagesUrl);
					return;
				}

				// 监听“我的应用”列表变化，重新加载应用数据
				this.ON('home-list-change', () => {
					this.HTTP_GET({
						url: '/mapp/module/mylist'
					}).then(newList => {
						this.initModule(this.GET_GLOBAL('learun_modules'), newList);
					});
				});

				// 清空下次加载时间缓存，确保数据及时更新
				this.SET_STORAGE('nextTime', null);
			},

			// 登录状态验证方法，检查token有效性并获取用户信息
			async checkLoginState() {
				let token = this.GET_GLOBAL('token') || this.GET_STORAGE('token');
				if (!token) return false; // 无token则登录失效

				this.SET_GLOBAL('token', token);
				// 已存在用户信息则直接返回有效
				if (this.GET_GLOBAL('loginUser')) return true;

				// 获取用户信息，验证登录状态
				const success = await this.FETCH_CURRENT_USERINFO();
				if (!success) {
					this.TOAST('获取用户信息失败，登录状态失效');
					this.SET_GLOBAL('token', null);
					this.SET_STORAGE('token', null);
					return false;
				}
				return true;
			},

			// 刷新首页数据核心方法，并行加载应用和配置信息
			async refresh(isRefreshTask) {
				this.LOADING(this.$t('数据加载中')); // 显示加载提示
				this.homeSettingReady = false;
				this.datasource = {}; // 重置数据源

				// 并行获取模块列表和我的应用列表
				const [modules, myModuleIds] = await Promise.all([
					this.HTTP_GET({
						url: '/mapp/modules'
					}),
					this.HTTP_GET({
						url: '/mapp/module/mylist'
					})
				]);
				this.initModule(modules, myModuleIds); // 初始化应用模块数据

				await this.initSetting(isRefreshTask); // 初始化桌面配置

				// 页面未隐藏时更新数据并标记配置就绪
				if (!this.isHide) {
					this.SET_ALL_DATA(this.datasource);
					this.homeSettingReady = true;
				}
				this.HIDE_LOADING(); // 隐藏加载提示
			},

			// 初始化功能模块
			initModule(modules, myModuleIds) {
				const loginUser = this.GET_GLOBAL('loginUser');
				const myModules = [];
				if (loginUser) {
					modules.forEach(item => {
						if (item.f_EnabledMark === 1 && (loginUser.f_SecurityLevel === 1 || (loginUser
								.moduleAuthIds || []).indexOf(item.f_Id) > -1)) {
							const icon = item.f_Icon ? item.f_Icon : '';
							myModules.push({
								...item,
								icon
							});
						}
					});
					this.SET_GLOBAL('learun_modules', myModules);
				}
				myModuleIds = myModuleIds || [];
				this.myModuleApps = [];
				myModuleIds.forEach(id => {
					const item = myModules.find(t => t.f_Id === id && t.f_IsSystem !== 3);
					if (item) {
						this.myModuleApps.push(item);
					}
				});
			},

			// 初始化桌面配置方法，加载各模块的数据源配置
			async initSetting(isRefreshTask) {
				const desktopList = this.desktopList(); // 获取桌面模块列表
				this.tabList = [];

				for (const item of desktopList) {
					// 获取模块配置方案
					const deskSetting = await this.HTTP_DELETE({
						url: `/desktop/setting/module/${item.f_Id}`
					});
					const deskScheme = JSON.parse(deskSetting.lr_desktop_schemeEntity.f_Content);

					// 标记是否需要加载任务相关数据
					deskScheme.forEach(schemeItem => {
						if (['app-mytasklist'].includes(schemeItem.type)) this.isLoadTaskList = true;
						if (['app-mytask'].includes(schemeItem.type)) this.isLoadTaskNum = true;
					});

					this.tabList.push(deskScheme.sort((a, b) => a.y - b.y)); // 按y坐标排序
				}

				await this.loadDesktopData([], isRefreshTask); // 加载桌面数据源数据
			},

			// 加载桌面数据源和任务数据方法
			async loadDesktopData(datasourceList, isRefreshTask) {
				let isRefresh = isRefreshTask || !this.GET_STORAGE('nextRefreshTime');
				if (isRefresh) {
					// 5秒内不重复获取数据，提升性能
					const currentTime = new Date();
					currentTime.setSeconds(currentTime.getSeconds() + 5);
					this.SET_STORAGE('nextRefreshTime', currentTime);

					// 加载任务统计和列表数据
					if (this.isLoadTaskNum || this.isLoadTaskList) {
						await this.initWFTaskAndList();
					}

					// 加载其他数据源数据（图表/表格等）
					const dataPromises = datasourceList.map(dataCode =>
						this.FETCH_DATASOURCE(dataCode).then(res => {
							this.datasource[dataCode] = res || [];
						})
					);
					await Promise.all(dataPromises);
				} else {
					// 从缓存获取数据
					const homeTaskData = this.GET_STORAGE('homeTaskData');
					if (homeTaskData) {
						this.datasource = {
							...this.datasource,
							...homeTaskData
						};
					}
				}

				this.isLoadData = true; // 标记数据加载完成
			},

			// 获取工作流任务统计和列表数据
			async initWFTaskAndList() {
				const res = await this.HTTP_GET({
					url: '/workflow/process/getapphomedata'
				});
				if (res) {
					// 存储任务统计数据（待办/已完成/委托）
					this.datasource = {
						...this.datasource,
						nCompletedNum: res.unCompletedCount,
						completedNum: res.completedCount,
						delegateNum: res.delegateCount,
						taskList: res.taskList || []
					};
					this.SET_STORAGE('homeTaskData', this.datasource); // 缓存数据
				}
			},

			// 数据可视化组件数据源获取方法
			getDesktopData({
				type,
				code
			}) {
				switch (type) {
					case 'nCompletedNum': // 待办任务数
					case 'delegateNum': // 委托任务数
					case 'completedNum': // 已完成任务数
						return this.GET_DATA(type) || 0;
					case 'taskList': // 任务列表
						return this.GET_DATA(type) || [];
					case 'datasource': // 自定义数据源
						return this.GET_DATA(code) || [];
					default:
						return null;
				}
			},

			// 功能宫格点击事件处理，跳转对应应用
			funcListClick(e) {
				if (e.detail.index === 100) { // 更多按钮
					this.moreClick(0); // 跳转更多应用页面
					return;
				}
				const item = this.myModuleApps[e.detail.index];
				this.TO_MODULE(item); // 跳转到应用页面
			},

			// 非H5环境扫码功能（调用原生扫码API）
			// #ifndef H5
			scanClick() {
				uni.scanCode({
					scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
					success: ({
						result
					}) => {
						// 扫码结果处理逻辑（预留扩展）
					}
				});
			},
			// #endif

			// 更多功能/搜索功能跳转
			moreClick(openSearch) {
				this.TAB_TO('/pages/home/<USER>', {
					search: openSearch ? '1' : null
				});
			},

			// 获取桌面模块列表（系统级模块）
			desktopList() {
				return (this.GET_GLOBAL('learun_modules') || []).filter(t => t.f_IsSystem === 3);
			},

			// 获取导航栏标签列表（模块名称）
			navList() {
				return this.desktopList().map(t => t.f_Name);
			},

			// 扫码功能入口，跳转到扫码页面
			scanningBtn() {
				this.NAV_TO('/pages/scan');
			},

			// 获取指定流程方案的工作流列表
			async getWorkflowList(stfcode) {
				return (await this.HTTP_GET({
						url: '/workflow/scheme/mylist',
						errorTips: this.$t('加载数据时出错')
					}))
					?.filter(t => t.f_IsInApp === 1 && t.f_Code === stfcode) || [];
			}
		}
	};
</script>


<style lang="scss" scoped>
	.home {

		// 顶部渐变条样式，从灰色渐变到白色
		.gradient-bar {
			height: 25px;
			background: linear-gradient(to bottom, #ccc, #fff);
		}

		.header {
			height: 48px;
			background-color: #fff;
			display: flex;
			align-items: center;

			// 左右两侧固定宽度，中间弹性扩展
			.header-left,
			.header-right {
				flex-grow: 0;
			}

			.header-mid {
				flex-grow: 1;
				height: 32px;
				background-color: #f8f8f8;
				border-radius: 2px;
				display: flex;
				align-items: center;
				font-size: 12px;
				color: #B3B3B3;

				.header-mid-icon {
					margin: 0 8px;
				}
			}

			// 扫码按钮样式，圆形背景，点击态变色
			.scan-button {
				margin-right: 15px;
				padding: 8px;
				background-color: #f5f5f5;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				&:hover {
					background-color: #e0e0e0;
				}
			}
		}

		.content {
			position: relative;
			background-color: #fff;
			// 内容区域样式，包含内边距和底部间距
		}
	}
</style>