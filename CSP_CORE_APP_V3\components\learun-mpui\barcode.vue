<template>
  <view>
    <view class="barcode">
      <view class="barnum" :style="styleName" v-if="textPosition == 'top'">{{
        value
      }}</view>
      <canvas
        canvas-id="barcode"
        id="barcode"
        :style="{ height: height + 'px' }"
      />
      <view class="barnum" :style="styleName" v-if="textPosition == 'bottom'">{{
        value
      }}</view>
    </view>
  </view>
</template>

<script>
import { BarCode } from "@uni-plugs/uni-code";
export default {
  props: {
    value: [String, Number],
    type: {
      type: String,
      default: "CODE128", //条码类型 默认CODE128 可选值 CODE39 UPCE UPC EAN13 ITF ITF14 MSI Codabar Pharmacode
    },
    height: {
      // 高度
      type: Number,
      default: 100,
    },
    displayValue: {
      type: Boolean, // 是否显示文本
      default: true,
    },
    fontOptions: {
      // 文字样式
      type: String,
      default: "",
    },
    textAlign: {
      // 文字对齐方式
      type: String,
      default: "left",
    },
    textPosition: {
      // 文本位置
      type: String,
      default: "bottom",
    },
    background: String,
    color: {
      // 条码颜色
      type: String,
      default: "#000000",
    },
    margin: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  computed: {
    styleName() {
      return {
        width: "200px",
        color: this.color,
        backgroundColor: this.background,
        textAlign: this.textAlign,
        fontWeight: this.fontOptions.indexOf("bold") != -1 ? "bold" : "",
        fontStyle:
          this.fontOptions.indexOf("italic") != -1 ? "italic" : "normal",
        display: this.displayValue ? "block" : "none",
      };
    },
  },
  mounted() {
    this.findCan();
  },
  methods: {
    findCan() {
      //条形码
      BarCode({
        id: "barcode",
        code: String(this.value),
        color: this.color,
        bgColor: this.background,
        type: this.type,
        width: 200,
        height: this.height,
      });
    },
  },
};
</script>