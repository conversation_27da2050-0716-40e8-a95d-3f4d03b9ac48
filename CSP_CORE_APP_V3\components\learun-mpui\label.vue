<template>
	<uni-easyinput :value="name" disabled :clearable="false" />
</template>

<script>
	export default {
		name:'learun-label',
		props:{
			value:{},
			type:String
		},
		computed:{
			name(){
				if(this.VALIDATENULL(this.value)){
					return ''
				}
				else{
					let obj = null
					switch(this.type){
						case 'company':
							return this.FORMAT_NAME(this.GET_DATA('learun_company_list'),this.value,'f_CompanyId','f_FullName')
						case 'department':
							const departmentMap = this.GET_DATA('learun_departments_map') || {}
							obj = departmentMap[this.value]
							break
						case 'createUser':
						case 'modifyUser':
							const userMap = this.GET_DATA('learun_users_map') || {}
							obj = userMap[this.value]
							// console.log(userMap,'userMap')
							break
					}
					// console.log(obj,this.value,'learun-label',this.type)
					return obj ? obj.label : ''
				}
			}
		}
		
	}
</script>