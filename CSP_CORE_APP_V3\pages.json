{
  "pages": [
    {
      "path": "pages/home",
      "style": {
       "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "桌面设计"
      }
    },
    {
      "path": "pages/login",
      "style": {
        "navigationStyle": "custom",
        "disableScroll": true
      }
	
    },
	{
		"path": "pages/signup",
		"style": {
			"navigationBarTitleText": "注册账号",
			"disableScroll": true,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/changePassword",
		"style": {
			"navigationBarTitleText": "修改密码",
			"disableScroll": true,
			"navigationStyle": "custom"
		}
	},
    {
      "path": "pages/msg",
      "style": {
        "navigationBarTitleText": "消息",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/msg/chat",
      "style": {
        "navigationBarTitleText": "对话列表",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/contact",
      "style": {
        "navigationBarTitleText": "通讯录",
        "enablePullDownRefresh": true
      }
    },
	{
		"path": "pages/connection",
		"style": {
			"navigationBarTitleText": "通讯录",
			"enablePullDownRefresh": true,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/sign/signForPC",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/password/dialog",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/wxlogin",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/wxloginTest",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/navToPages",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/appLogin",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/news",
		"style": {
			"navigationBarTitleText": "新闻",
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/scan",
		"style": {
			"navigationBarTitleText": "扫描",
			"enablePullDownRefresh": true,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/newsDetails",
		"style": {
			"navigationBarTitleText": "新闻详情",
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/password/reset",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
	{
		"path": "pages/password/set",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
    {
     "path": "pages/my",
     "style": {
     	"navigationBarBackgroundColor": "#0c86d8",
     	"navigationBarTextStyle": "white",
     	"navigationBarTitleText": "我的",
     	"backgroundColorTop": "#0c86d8",
     	"backgroundColorBottom": "#f3f3f3",
     	"navigationStyle": "custom"
     }
    },
   {
   	"path": "pages/home/<USER>",
   	"style": {
   		"navigationStyle": "custom"
   	}
   },
    {
      "path": "pages/common/learun-user-picker",
      "style": {
        "navigationBarTitleText": "选择用户",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/common/learun-icon-picker",
      "style": {
        "navigationBarTitleText": "图标选择"
      }
    },
    {
      "path": "pages/common/learun-layer-picker",
      "style": {
        "navigationBarTitleText": "选择一个选项",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/common/learun-editor-picker",
      "style": {
        "navigationBarTitleText": "编辑文本"
      }
    },
    {
      "path": "pages/customapp/list",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/customapp/single",
      "style": {
        "navigationBarTitleText": ""
      }
    },
	{
		"path": "pages/loadWeb",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	},
    {
      "path": "pages/customapp/query",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/workflow/mytask/list",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/workflow/mytask/single",
      "style": {
        "navigationBarTitleText": "任务详情",
        "backgroundColorTop": "#FFFFFF"
      }
    },
    {
      "path": "pages/workflow/mytask/submit",
      "style": {
        "navigationBarTitleText": "任务提交",
        "backgroundColorTop": "#FFFFFF"
      }
    },
    {
      "path": "pages/workflow/mytask/sign",
      "style": {
        "navigationBarTitleText": "流程加签与审核",
        "backgroundColor": "#FFFFFF"
      }
    },
    {
      "path": "pages/workflow/releasetask/list",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/workflow/releasetask/single",
      "style": {
        "navigationBarTitleText": "创建流程"
      }
    },
    {
      "path": "pages/workflow/common/learun-wfsubmit-info",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
	{
		"path": "pages/cherry_markdown",
		"style": {
			"navigationBarTitleText": "",
			"enablePullDownRefresh": false,
			"navigationStyle": "custom"
		}
	}
  ],
  "subPackages": [
    {
      "root": "pagesMy",
      "pages": [
        {
          "path": "info",
          "style": {
            "navigationBarTitleText": "我的信息"
          }
        },
        {
          "path": "contact",
          "style": {
            "navigationBarTitleText": "我的联系方式"
          }
        },
        {
          "path": "qrcode",
          "style": {
            "navigationBarTitleText": "我的二维码"
          }
        },
        {
          "path": "password",
          "style": {
            "navigationBarTitleText": "更改密码"
          }
        },
        {
          "path": "learun",
          "style": {
            "navigationBarTitleText": "关于CSP",
            "navigationBarBackgroundColor": "#0c86d8",
            "navigationBarTextStyle": "white",
            "backgroundColor": "#FFFFFF"
          }
        },
        {
          "path": "framework",
          "style": {
            "navigationBarTitleText": "Cystal system platform",
            "navigationBarBackgroundColor": "#0c86d8",
            "pageOrientation": "portrait",
            "navigationBarTextStyle": "white",
            "backgroundColor": "#FFFFFF"
          }
        }
      ]
    },
    {
      "root": "pagesDemo",
      "pages": [
        {
          "path": "clock/index",
          "style": {
            "navigationBarTitleText": "考勤打卡",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "clock/setting",
          "style": {
            "navigationBarTitleText": "考勤设置",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "clock/addSetting",
          "style": {
            "navigationBarTitleText": "添加考勤组",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "clock/selectTime",
          "style": {
            "navigationBarTitleText": "班次时间",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "clock/selectScope",
          "style": {
            "navigationBarTitleText": "考勤范围",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pagesTest",
      "pages": [
        {
          "path": "test/list",
          "style": {
            "navigationBarTitleText": "测试流程"
          }
        },
        {
          "path": "test/query",
          "style": {
            "navigationBarTitleText": "查询条件",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "test/single",
          "style": {
            "navigationBarTitleText": "表单页面"
          }
        }
      ]
    },
    {
      "root": "pagesTest1",
      "pages": [
        {
          "path": "test0724/list",
          "style": {
            "navigationBarTitleText": "测试代码生成"
          }
        },
        {
          "path": "test0724/query",
          "style": {
            "navigationBarTitleText": "查询条件",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "test0724/single",
          "style": {
            "navigationBarTitleText": "表单页面"
          }
        }
      ]
    },
	{
		"root": "pagesHRATTF",
		"pages": [{
				"path": "hrattF001/list",
				"style": {
					"navigationBarTitleText": "请假申请",
					"navigationStyle": "custom"
					// "enablePullDownRefresh": true
				}
			},
			{
				"path": "hrattF001/query",
				"style": {
					"navigationBarTitleText": "请假申请",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "hrattF001/single",
				"style": {
					"navigationBarTitleText": "请假申请",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "hrattF001/layer-picker",
				"style": {
					"navigationBarTitleText": "选择人员",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "hrattf004/single",
				"style": {
					"navigationBarTitleText": "错误考勤",
					"navigationStyle": "custom"
				}
			}
		]
	}
  ],
  "preloadRule": {
    "pages/my": {
      "network": "all",
      "packages": [
        "pagesMy"
      ]
    }
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "enablePullDownRefresh": false,
    "navigationBarBackgroundColor": "#FFF",
    "backgroundColor": "#f3f3f3",
    "navigationBarTitleText": "Crystal Service Platform",
    "mp-alipay": {
      "allowsBounceVertical": "NO"
    }
  },
  "tabBar": {
    "color": "#8c8c8c",
    "selectedColor": "#3398DC",
    "borderStyle": "black",
    "backgroundColor": "#f5f5f5",
    "list": [ 
      {
        "pagePath": "pages/home",
        "iconPath": "static/img-bar/tab-home.png",
        "selectedIconPath": "static/img-bar/tab-home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/home/<USER>",
        "iconPath": "static/img-bar/tab-application.png",
        "selectedIconPath": "static/img-bar/tab-application-active.png",
        "text": "工作台"
      },
	  {
	    "pagePath": "pages/news",
	    "iconPath": "static/img-bar/news.png",
	    "selectedIconPath": "static/img-bar/newsColour.png",
	    "text": "新闻"
	  },
      {
        "pagePath": "pages/my",
        "iconPath": "static/img-bar/tab-my.png",
        "selectedIconPath": "static/img-bar/tab-my-active.png",
        "text": "我的"
      }
    ]
  },
  "easycom": {
    "custom": {
      "^learun-(.*)": "@/components/learun-mpui/$1.vue",
      "tki-qrcode": "@/components/tki-qrcode/tki-qrcode.vue",
	  "crystal-(.*)": "@/components/crystal/$1.vue"
    }
  },
  "workers": "static/workers"
}