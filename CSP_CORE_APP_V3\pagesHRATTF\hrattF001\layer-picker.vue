<template>
  <view class="page" style="padding-top: 56px;">
		<view  class="bg-white fixed">
			<uni-search-bar :placeholder="$t('搜索关键字')" cancelButton="none" @confirm="searchChange(false)" @clear="searchChange(true)" @input="searchChange(false)" v-model="searchText"></uni-search-bar>
		</view>
		<uni-list v-if="ready" >
			<!-- <uni-list-item clickable @click="itemClick(item)" :key="index" v-for="(item,index) in showOptions" direction="column">
				<template v-slot:body >
					<view>
						<view v-for="(col,index2) in columns" :key="index2" class="learun-list-item__content learun-flex" >
							<text>{{col.label}}</text>
							<text>{{item[col.prop]}}</text>
						</view>
					</view>
				</template>
			</uni-list-item> -->
			<uni-list-item clickable @click="itemClick(item)" v-for="(item,index) in showOptions" :key="index"
				:title="item.f_realname" :note="item.f_userid" :thumb="avatarSrc(item)" thumb-size="lg">
				<!-- <template v-slot:footer>
					<view v-if="isMulti" style="display: flex;align-items: center;">
						<learun-icon v-if="values.indexOf(item.f_UserId) > -1" type="learun-icon-circle-correct" size="20"
							color="#2979ff" />
					</view>
				</template> -->
			</uni-list-item>
		</uni-list>
  </view>
</template>

<script>
export default {
  data() {
    return {
			options:[],
			showOptions:[],
			columns:[],
			
			searchText:'',
			
			page:1,
			total:1,
			
			ready:false
    }
  },

  onBackPress() {
    this.OFF('learun-layer-picker')
  },
  onUnload() {
    this.OFF('learun-layer-picker')
  },

  onLoad() {
    this.init()
  },

  methods: {
    // 页面初始化
    init() {
      const { columns,options } = this.GET_PARAM()
			this.options = options
			this.showOptions = []
			this.columns = columns
			this.fetchList()
			this.ready = true
    },

    // 某一项被点击
    itemClick(item) {
      this.EMIT('learun-layer-picker', item)
      this.NAV_BACK()
    },
		
		searchChange(isClear){
			if(isClear){
				this.searchText = ''
				this.showOptions = this.options
			}
			this.page = 1
			this.total = 1
			this.showOptions = []
			this.fetchList()
		},
  
		fetchList(){
			if(this.page > this.total){
				return
			}
			console.log(90,this.searchText)
			
			let options = []
			if(this.searchText){
				options = this.options.filter(t=>{
						let res = false
						for(const col of this.columns){
							const item = (t[col.prop] || '') + ''
							if(item.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) != -1){
									res = true
									break
							}
						}
						return res
				})
			}
			else{
				options = this.options
			}
			this.total = options.length
			const list = this.PAGINATION(this.page,20,options) 
			
			this.showOptions = this.showOptions.concat(list)
			this.page++
		},
		avatarSrc(user) {
			const token = this.GET_GLOBAL('token')
			return user.f_HeadIcon ? `${this.API}/system/annexesfile/${user.f_HeadIcon}?token=${token}` :
				`/static/img-avatar/head.png`
		},
	},
  computed: {
  },
	/**
	 * 上拉加载回调函数
	 */
	onReachBottom() {
		this.fetchList()
	}
}
</script>


