<template>
	<!-- 页面容器，设置最小高度为屏幕高度，背景颜色为白色，底部有内边距 -->
	<view class="page" :style="{
            'min-height': SCREENHEIGHT() + 'px',
            'background-color': '#fff',
            'padding-bottom': '40px',
        }">
		<!-- 自定义表单包装组件，当数据准备好时显示 -->
		<learun-customform-wraper v-if="ready" :editMode="true" :scheme="{ formInfo: formScheme }" :isUpdate="false"
			:formId="moduleId" :moduleId="moduleId" @ready="handleReady" :isAuth="false" isSystem :initFormValue="{}"
			@myAfterChangeDataEvent="afterChangeDataEvent" ref="form" />
		<!-- 操作区按钮，当数据准备好时显示 -->
		<view v-if="ready" class="learun-bottom-btns">
			<!-- 提交按钮，点击触发 wfsubmit 方法 -->
			<button @click="wfsubmit" type="primary">{{ $t("提交") }}</button>
		</view>
	</view>
</template>

<script>
	export default {
		// 页面加载时执行初始化操作
		async onLoad() {
			// 检查页面是否可以启动
			if (await this.PAGE_LAUNCH()) {
				// 执行初始化方法
				await this.init();
			}
		},
		// 页面返回时，移除事件监听
		onBackPress() {
			this.OFF("learun-wfsubmit-info");
		},
		// 页面卸载时，移除事件监听
		onUnload() {
			this.OFF("learun-wfsubmit-info");
		},
		data() {
			return {
				// 是否以弹窗层形式显示
				isLayer: true,
				// 模块 ID
				moduleId: "learun-wfsubmit-info",
				// 数据是否准备好
				ready: false,
				// 是否自定义标题
				isCustmerTitle: false,
				// 加签人姓名
				signUserName: "",
				// 转办人姓名
				transferUserName: "",
				// 沟通人姓名
				connectUserName: "",
				// 节点用户列表
				nodeUsers: [],
				// 表单方案
				formScheme: {
					form: {
						// 标签位置
						labelPosition: "right",
						// 标签宽度
						labelWidth: 80,
					},
					// 表单组件列表
					components: [],
				},
				// 是否需要描述信息
				isDes: false,
				// 是否需要上传文件
				isUploadFile: false,
				// 是否需要设置重要级别
				isLevel: false,
				// 流程 ID
				processId: "",
				// 节点 ID
				nodeId: "",
				// 操作代码
				operationCode: "",
				// 工作流数据
				wfData: [],
				// 是否显示下一审核人
				isNextAuditor: false,
				// 委托用户列表
				delegateUserList: [],
				// 驳回节点列表
				rejectNodeList: [],
				// 审批要点列表
				auditTags: [],
				// 驳回策略
				isRejectBackOld: undefined,
				// 任务类型
				taskType: undefined,
			};
		},
		methods: {
			// 页面初始化方法
			async init() {
				// 显示加载提示
				this.LOADING(this.$t("加载数据中…"));
				// 设置页面标题
				this.SET_TITLE(this.$t("填写流程信息！"));
				// 获取页面传递的参数
				const params = this.GET_PARAM();
				// 节点用户列表
				this.nodeUsers = params.nodeUsers || [];
				// 是否自定义标题
				this.isCustmerTitle = params.isCustmerTitle;
				// 是否需要描述信息
				this.isDes = params.isDes;
				// 是否需要上传文件
				this.isUploadFile = params.isUploadFile;
				// 是否需要设置重要级别
				this.isLevel = params.isLevel;
				// 委托用户列表
				this.delegateUserList = params.delegateUserList;
				// 驳回节点列表
				this.rejectNodeList = params.rejectNodeList || [];

				// 获取表单组件列表
				const components = this.formScheme.components;
				// 获取当前登录用户信息
				const loginInfo = this.GET_GLOBAL("loginUser");

				// 提交人选择组件
				if (this.delegateUserList?.length > 0) {
					// 构建选择项
					const options = this.delegateUserList.map((t) => ({
						value: t.f_UserId,
						label: t.f_RealName,
					}));
					// 添加“自己”选项
					options.push({
						value: loginInfo.f_UserId || "",
						label: "自己"
					});
					// 添加选择组件到表单组件列表
					components.push({
						type: "select",
						id: "userId",
						config: {
							label: "发起人",
							display: true,
							dataType: "options",
							options: options,
							defaultValue: loginInfo.f_UserId || "",
							table: "wf",
							field: "userId",
						},
					});
				}

				// 驳回到选择组件
				if (params.rejectNodeList?.length > 0) {
					// 添加选择组件到表单组件列表
					components.push({
						type: "select",
						id: "nodeId",
						config: {
							label: "驳回到",
							display: true,
							dataType: "options",
							options: params.rejectNodeList,
							labelKey: "name",
							valueKey: "id",
							table: "wf",
							field: "nodeId",
							required: true,
							placeholder: this.$t("请选择驳回节点"),
						},
					});
				}

				// 下一审核人组件
				if (params.processId) {
					// 获取下一节点审核人信息
					const auditorList = await this.getNextNodeAuditor(
						params.processId,
						params.nodeId,
						params.operationCode,
						params.wfData,
						params.isNextAuditor,
						loginInfo.f_UserId || "",
						params.rejectNodeList || [],
						params.isRejectBackOld
					);
					// 将审核人组件添加到表单组件列表
					components.push(...auditorList);
				}

				// 加签组件
				if (params.operationCode == "learun_sign") {
					// 添加用户选择组件到表单组件列表
					components.push({
						type: "userSelect",
						id: "signUser",
						config: {
							label: "加签给",
							display: true,
							placeholder: this.$t("请选择人员"),
							table: "wf",
							field: "signUser",
							required: true,
						},
					});
				}

				// 转办组件
				if (params.operationCode == "learun_transfer") {
					// 添加用户选择组件到表单组件列表
					components.push({
						type: "userSelect",
						id: "transferUser",
						config: {
							label: "转办给",
							display: true,
							placeholder: this.$t("请选择人员"),
							table: "wf",
							field: "transferUser",
							required: true,
						},
					});
				}

				// 沟通组件
				if (params.operationCode == "learun_connect") {
					// 添加用户选择组件到表单组件列表
					components.push({
						type: "userSelect",
						id: "connectUser",
						config: {
							label: "沟通给",
							display: true,
							placeholder: this.$t("请选择人员"),
							table: "wf",
							field: "connectUser",
							required: true,
							isMultiSelect: true,
						},
					}, {
						// 添加单选组件到表单组件列表
						type: "radio",
						id: "connectType",
						config: {
							label: "允许继续沟通",
							display: true,
							table: "wf",
							field: "connectType",
							required: true,
							dataType: 'options',
							options: [{
									value: '1',
									label: this.$t('是')
								},
								{
									value: '2',
									label: this.$t('否')
								},
							],
							defaultValue: '1'
						},
					});
				}

				// 流程标题组件
				if (this.isCustmerTitle) {
					// 添加输入组件到表单组件列表
					components.push({
						type: "input",
						id: "title",
						config: {
							label: "流程标题",
							placeholder: "请输入",
							patterns: [],
							display: true,
							default: "",
							table: "wf",
							field: "title",
						},
					});
				}

				// 重要级别组件
				if (this.isLevel) {
					// 添加选择组件到表单组件列表
					components.push({
						type: "select",
						id: "level",
						config: {
							label: "重要级别",
							display: true,
							placeholder: this.$t("请选择重要级别"),
							dataType: "options",
							options: [{
									label: this.$t("普通"),
									value: 0
								},
								{
									label: this.$t("重要"),
									value: 1
								},
								{
									label: this.$t("紧急"),
									value: 2
								},
							],
							defaultValue: 0,
							table: "wf",
							field: "level",
						},
					});
				}

				// 驳回策略设置组件
				if (params.operationCode == "disagree" && params.taskType != 5) {
					// 添加选择组件到表单组件列表
					components.push({
						type: "select",
						id: "isRejectBackOld",
						config: {
							label: "驳回策略",
							display: true,
							dataType: "options",
							options: [{
									value: 1,
									label: this.$t("驳回节点通过后,返回我")
								},
								{
									value: 2,
									label: this.$t("驳回节点通过后,按顺序流转")
								},
							],
							defaultValue: 1,
							table: "wf",
							field: "isRejectBackOld",
						},
					});
				}

				// 审批要点组件
				if (params.auditTags && params.auditTags.length > 0) {
					// 添加多选组件到表单组件列表
					components.push({
						type: "selectMultiple",
						id: "auditTags",
						config: {
							multiple: true,
							label: "审批要点",
							display: true,
							dataType: "options",
							options: params.auditTags,
							table: "wf",
							field: "auditTags",
						},
					});
				}

				// 处理意见组件
				if (this.isDes) {
					// 添加文本域组件到表单组件列表
					components.push({
						type: "textarea",
						id: "des",
						config: {
							label: "处理意见",
							placeholder: this.$t("请输入"),
							patterns: [],
							display: true,
							default: "0112",
							table: "wf",
							field: "des",
							required: params.isDesReq ||
								params.operationCode == "disagree" ||
								params.operationCode == "createAgain" ?
								true :
								false,
						},
					});
				}

				// 附件上传组件
				if (this.isUploadFile) {
					// 添加上传组件到表单组件列表
					components.push({
						type: "upload",
						id: "fileId",
						config: {
							label: "附件",
							display: true,
							table: "wf",
							field: "fileId",
						},
					});
				}

				// 更新表单组件列表
				this.formScheme.components = components;
				// 标记数据准备好
				this.ready = true;
			},
			// 表单准备好时的回调方法
			handleReady() {
				// 隐藏加载提示
				this.HIDE_LOADING();
			},
			// 表单数据变化后的回调方法
			async afterChangeDataEvent({
				component,
				data
			}) {
				if (component.id == "nodeId") {
					if (this.rejectNodeList?.length > 0) {
						// 获取表单数据
						const formData = await this.$refs.form.getFormValue();
						for (let key in formData) {
							if (key.startsWith("nextNodes-")) {
								const targetKey = `nextNodes-${data?.id}`;
								if (key === targetKey && data) {
									// 显示目标组件
									this.$refs.form.setHide(targetKey, false);
								} else {
									// 隐藏其他组件
									this.$refs.form.setHide(key, true);
								}
							}
						}
					}
				}

				if (component.id == "signUser") {
					// 更新加签人姓名
					this.signUserName = data?.label || "";
				}
				if (component.id == "transferUser") {
					// 更新转办人姓名
					this.transferUserName = data?.label || "";
				}
				if (component.id == "connectUser") {
					// 更新沟通人姓名
					this.connectUserName = String((data || []).map((t) => t.f_RealName));
				}
			},
			// 点击提交按钮的处理方法
			async wfsubmit() {
				// 验证表单数据
				const verifyResult = await this.$refs.form.validate();
				if (verifyResult) {
					const formDataRes = {};
					// 获取表单数据
					const formData = await this.$refs.form.getFormValue();
					Object.keys(formData).forEach((key) => {
						if (key.indexOf("nextNodes-") != -1) {
							formDataRes.nextUsers = formDataRes.nextUsers || {};
							formDataRes.nextUsers[key.replace("nextNodes-", "")] =
								formData[key];
						} else {
							formDataRes[key] = formData[key];
						}
					});

					if (this.signUserName) {
						// 添加加签人姓名到结果数据
						formDataRes.signUserName = this.signUserName;
					}

					if (this.transferUserName) {
						// 添加转办人姓名到结果数据
						formDataRes.transferUserName = this.transferUserName;
					}
					if (this.connectUserName) {
						// 添加沟通人姓名到结果数据
						formDataRes.connectUserName = this.connectUserName;
					}

					// 触发事件，传递表单数据
					this.EMIT("learun-wfsubmit-info", formDataRes);
					// 重置相关数据
					this.reset();
					// 返回上一页
					this.NAV_BACK();
				}
			},
			// 重置方法
			reset() {
				this.signUserName = "";
				this.transferUserName = "";
				this.connectUserName = "";
			},
			// 获取下一节点审核人信息的方法
			async getNextNodeAuditor(
				processId,
				nodeId,
				operationCode,
				wfData,
				isNextAuditor,
				userId,
				rejectNodeList,
				isRejectBackOld
			) {
				// 发送请求获取下一节点审核人
				const nextNodes = await this.HTTP_GET({
					url: "/workflow/process/nextusers",
					params: {
						processId,
						nodeId,
						operationCode,
						userId,
						nextNodeId: rejectNodeList.length > 0 ?
							rejectNodeList.map((t) => t.id).join(",") :
							"",
					},
				});
				const auditorList = [];
				Object.keys(nextNodes).forEach((key) => {
					const userList = nextNodes[key] || [];
					const node = wfData.find((t) => t.id == key);

					const formKey = `nextNodes-${key}`;
					if (node) {
						if (node.type === "startEvent" && !node.name) {
							node.name = this.$t("开始节点");
						}
						if (
							(node.anotherUser && isRejectBackOld != 1) ||
							userList.length == 0 ||
							(userList.length == 1 && userList[0].isAdmin)
						) {
							// 添加用户选择组件到审核人列表
							auditorList.push({
								type: "userSelect",
								id: formKey,
								config: {
									label: rejectNodeList.length > 0 ? "处理人" : node.name,
									display: rejectNodeList.length > 0 ? false : true,
									placeholder: this.$t("请选择处理人"),
									table: "wf",
									field: formKey,
									required: true,
									isMultiSelect: true,
									defaultValue: userList.map((t) => t.id).join(",") || "",
									isNotSaveData: true,
								},
							});
						} else if (
							!isNextAuditor ||
							isRejectBackOld == 1 ||
							userList.length == 1
						) {
							// 添加用户 ID 全名显示组件到审核人列表
							auditorList.push({
								type: "useridFullnames",
								id: formKey,
								config: {
									label: rejectNodeList.length > 0 ? "处理人" : node.name,
									display: rejectNodeList.length > 0 ? false : true,
									table: "wf",
									field: formKey,
									required: true,
									defaultValue: userList.map((t) => t.id).join(",") || "",
									userIds: userList.map((t) => t.id) || [],
								},
							});
						} else {
							// 添加多选组件到审核人列表
							auditorList.push({
								type: "selectMultiple",
								id: formKey,
								config: {
									multiple: true,
									label: rejectNodeList.length > 0 ? "处理人" : node.name,
									display: rejectNodeList.length > 0 ? false : true,
									placeholder: this.$t("请选择审核人"),
									dataType: "options",
									defaultValue: userList.map((t) => t.id).join(",") || "",
									options: userList.map((t) => ({
										label: t.name,
										value: t.id
									})),
									table: "wf",
									field: formKey,
									required: true,
								},
							});
						}
					}
				});

				return auditorList;
			},
		},
	};
</script>

<style>
</style>