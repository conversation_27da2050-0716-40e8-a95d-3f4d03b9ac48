<template>
  <view class="learun-view-table" >
		<view  v-if="title" class="learun-view-table__title" >
			<text>{{$t(title)}}</text>
		</view>
		<view class="learun-view-table__body" >
			<learun-view-table-item 
				v-for="(row,index) in  dataSource"
				:key="index"
				:num="index + 1"
				:rowData="row"
				:columns="columns"
				/>
		</view>
  </view>
</template>

<script>
export default {
  name: 'learun-view-table',
  props: {
		title:String,
		columns:{
			type:Array,
			default:()=>[]
		},
		code:String,
		paramFiled:[String,Number]
  },
	data(){
		return {
			dataSource:[]
		}
	},
	watch:{
		paramFiled:{
			handler(){
				this.viewTableData()
			},
			immediate: true
		}
	},
  methods: {
		async viewTableData(){
			if(this.paramFiled && this.code){
				const data = await this.FETCH_DATASOURCE(this.code,this.paramFiled)
				this.dataSource =  data || []
			}
			else{
				this.dataSource = []
			}
		}
  }
}
</script>
<style lang="scss" scoped >
	.learun-view-table{
		&__title{
			font-size: 13px;
			color: #666666;
			display: flex;
			align-items: center;
			width: 100%;
			height: 32px;
		}		
		margin-bottom: 16px;
	}
</style>
