<template>
	<!-- 新闻页面的根容器 -->
	<view class="news-page">
		<!-- 导航栏部分 -->
		<view class="navbar">
			<!-- 导航栏标题，使用多语言翻译显示 '新闻资讯与公告' -->
			<text class="navbar-title">{{$t('新闻资讯与公告')}}</text>
		</view>
		<!-- 资讯与公告切换标签栏 -->
		<view class="tab-bar">
			<!-- 使用 v-for 指令循环渲染每个标签项 -->
			<view v-for="(title, index) in messageList" :key="index"
				:class="{ 'tab-item': true, 'active': currentTab === index }" @click="!isSwitching && switchTab(index)">
				<!-- 显示标签的名称，使用多语言翻译 -->
				{{$t(title.f_ItemName)}}
			</view>
		</view>
		<!-- 内容区域 -->
		<view class="content">
			<!-- 根据当前选中的标签显示对应的资讯列表 -->
			<view v-if="currentTab === toggleTitle" class="news-list" :class="{ 'fade-in': isAnimating }">
				<!-- 循环渲染每条新闻资讯 -->
				<view v-for="(news, index) in displayedNewsList" :key="index" class="news-item"
					@click="!isSwitching && goNewMessage(news)">
					<!-- 新闻缩略图区域 -->
					<view class="news-thumbnail">
						<!-- 显示新闻的缩略图，如果没有则显示默认图片 -->
						<image :src="news.icon || '../static/404/Announcement.png'" mode="aspectFit"
							style="height: 100%;"></image>
					</view>
					<!-- 新闻信息区域 -->
					<view class="news-info">
						<!-- 新闻标题 -->
						<text class="news-title">{{ news.title }}</text>
						<!-- 新闻发布日期，如果没有则显示 '暂无发布日期' -->
						<text class="news-date">{{ news.published_date || '暂无发布日期' }}</text>
					</view>
				</view>
				<!-- 加载动画，当正在加载数据时显示 -->
				<view v-if="isLoading" class="loading-animation">
					<!-- 旋转的加载图标 -->
					<div class="spinner"></div>
					<!-- 加载提示文字，使用多语言翻译 -->
					<text>{{$t('加载中...')}}</text>
				</view>
				<!-- 加载更多提示，当还有更多数据且不在加载状态时显示 -->
				<view v-if="hasMoreData &&!isLoading" class="loading-more">{{$t('加载更多...')}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 当前选中的标签索引，默认为 0
				currentTab: 0,
				// 用于切换标题时的过渡状态
				toggleTitle: 0,
				// 存储标签标题列表的数据
				messageList: [],
				// 后端返回的所有新闻数据列表
				listALL: [],
				// 当前选中标签对应的新闻列表
				newsList: [],
				// 当前显示在页面上的新闻列表
				displayedNewsList: [],
				// 每次加载的新闻数量
				loadCount: 10,
				// 当前已经加载的新闻的索引
				currentLoadIndex: 0,
				// 是否还有更多数据可供加载
				hasMoreData: false,
				// 是否正在加载数据的标志
				isLoading: false,
				// 是否正在进行动画效果的标志
				isAnimating: false,
				// 是否正在切换标签的标志
				isSwitching: false
			};
		},
		async onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t("新闻")
			})
			try {
				// 并行获取标签标题列表和新闻列表数据
				const [messageListRes, newsListRes] = await Promise.all([
					// 获取标签标题列表数据
					this.FETCH_DATAITEM('NewsType'),
					// 获取新闻列表数据
					this.HTTP_POST({
						url: `/data/dbsource/newsList/list`,
						data: {
							paramsJson: "{}",
							sidx: ""
						}
					})
				]);
				// 将获取到的标签标题列表数据赋值给 messageList
				this.messageList = messageListRes;
				// 将获取到的所有新闻数据列表赋值给 listALL
				this.listALL = newsListRes;
				// 初始化切换到第一个标签
				this.switchTab(0);
				// 监听窗口滚动事件，用于触发加载更多数据
				window.addEventListener('scroll', this.handleScroll);
			} catch (error) {
				// 数据加载失败时，在控制台输出错误信息
				console.error('数据加载失败:', error);
			}
		},
		beforeDestroy() {
			// 组件销毁前，移除滚动事件监听，防止内存泄漏
			window.removeEventListener('scroll', this.handleScroll);
		},
		methods: {
			// 标签切换功能的方法
			async switchTab(tabIndex) {
				// 设置正在切换标签的标志为 true
				this.isSwitching = true;
				// 设置正在进行动画的标志为 true
				this.isAnimating = true;
				// 更新切换标题的过渡状态
				this.toggleTitle = tabIndex;
				// 更新当前选中的标签索引
				this.currentTab = tabIndex;
				// 延迟 0.5 秒，实现动画过渡效果
				await new Promise(resolve => setTimeout(resolve, 500));
				// 使用 filter 方法过滤出当前选中标签对应的新闻列表
				this.newsList = this.listALL.filter(res => res.type === tabIndex + 1);
				// 重置当前加载的索引为 0
				this.currentLoadIndex = 0;
				// 加载数据
				this.loadData();
				// 设置正在进行动画的标志为 false
				this.isAnimating = false;
				// 设置正在切换标签的标志为 false
				this.isSwitching = false;
			},
			// 加载数据的方法
			loadData() {
				// 计算本次加载的起始索引
				const start = this.currentLoadIndex;
				// 计算本次加载的结束索引
				const end = start + this.loadCount;
				// 截取当前选中标签对应的新闻列表中从起始索引到结束索引的数据，赋值给 displayedNewsList
				this.displayedNewsList = this.newsList.slice(0, end);
				// 更新当前加载的索引
				this.currentLoadIndex = end;
				// 判断是否还有更多数据可供加载
				this.hasMoreData = this.currentLoadIndex < this.newsList.length;
			},
			// 加载更多数据的方法
			async loadMoreData() {
				// 当还有更多数据且不在加载状态和切换标签状态时，进行加载更多操作
				if (this.hasMoreData && !this.isLoading && !this.isSwitching) {
					// 设置正在加载数据的标志为 true
					this.isLoading = true;
					try {
						// 计算本次加载更多的起始索引
						const start = this.currentLoadIndex;
						// 计算本次加载更多的结束索引
						const end = start + this.loadCount;
						// 截取当前选中标签对应的新闻列表中从起始索引到结束索引的数据
						const newData = this.newsList.slice(start, end);
						// 将新数据追加到当前显示的新闻列表中
						this.displayedNewsList = [...this.displayedNewsList, ...newData];
						// 更新当前加载的索引
						this.currentLoadIndex = end;
						// 判断是否还有更多数据可供加载
						this.hasMoreData = this.currentLoadIndex < this.newsList.length;
					} catch (error) {
						// 加载更多数据失败时，在控制台输出错误信息
						console.error('加载更多数据失败:', error);
					} finally {
						// 无论加载成功还是失败，都将正在加载数据的标志设置为 false
						this.isLoading = false;
					}
				}
			},
			// 点击新闻项跳转详情页的方法
			goNewMessage(message) {
				// 当不在切换标签状态时，进行跳转操作
				if (!this.isSwitching) {
					// 显示加载提示
					this.LOADING('加载中…');
					// 延迟 1.5 秒后跳转到新闻详情页
					setTimeout(() => {
						uni.navigateTo({
							url: `/pages/newsDetails?id=${message.id}`
						});
					}, 1500);
				}
			},
			// 处理滚动事件的方法
			handleScroll() {
				// 获取文档元素的滚动距离、总高度和可视高度
				const {
					scrollTop,
					scrollHeight,
					clientHeight
				} = document.documentElement;
				// 当滚动到距离底部 50px 时，触发加载更多数据的操作
				if (scrollTop + clientHeight >= scrollHeight - 50) {
					this.loadMoreData();
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	/* 新闻页面的整体样式 */
	.news-page {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f9f9f9;
	}

	/* 导航栏样式 */
	.navbar {
		height: 60px;
		background-color: #007aff;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		position: sticky;
		top: 0;
		z-index: 10;
	}

	/* 导航栏标题样式 */
	.navbar-title {
		color: #fff;
		font-size: 16px;
		font-weight: bold;
	}

	/* 标签栏样式 */
	.tab-bar {
		display: flex;
		background-color: #fff;
		border-bottom: 1px solid #eee;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
		position: sticky;
		top: 60px;
		z-index: 10;
	}

	/* 标签项样式 */
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 15px 0;
		font-size: 15px;
		color: #333;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	/* 激活状态的标签项样式 */
	.tab-item.active {
		color: #007aff;
		border-bottom: 3px solid #007aff;
		transform: scale(1.05);
	}

	/* 内容区域样式 */
	.content {
		flex: 1;
		padding: 20px;
	}

	/* 新闻列表样式 */
	.news-list {
		display: flex;
		flex-direction: column;
		gap: 20px;
		opacity: 1;
		transition: opacity 1.5s ease;
	}

	/* 带有淡入动画效果的新闻列表样式 */
	.news-list.fade-in {
		opacity: 0;
	}

	/* 新闻项样式 */
	.news-item {
		display: flex;
		background-color: #fff;
		border-radius: 15px;
		box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;
		transform-style: preserve-3d;
		perspective: 1000px;
	}

	/* 鼠标悬停时新闻项的样式 */
	.news-item:hover {
		transform: translateY(-5px) rotateX(3deg) rotateY(-3deg);
		box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.1);
	}

	/* 新闻缩略图区域样式 */
	.news-thumbnail {
		width: 110px;
		height: 80px;
		margin-right: 20px;
		overflow: hidden;
		border-radius: 15px 0 0 15px;
	}

	/* 新闻缩略图图片样式 */
	.news-thumbnail image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	/* 新闻信息区域样式 */
	.news-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 6px;
	}

	/* 新闻标题样式 */
	.news-title {
		font-size: 12px;
		font-weight: 450;
		color: #6a6a6a;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	/* 新闻发布日期样式 */
	.news-date {
		font-size: 12px;
		color: #999;
	}

	/* 加载更多提示样式 */
	.loading-more {
		text-align: center;
		padding: 20px;
		color: #999;
		font-size: 18px;
	}

	/* 加载动画样式 */
	.loading-animation {
		text-align: center;
		padding: 30px;
		color: #999;
		font-size: 18px;
	}

	/* 加载图标样式 */
	.spinner {
		border: 5px solid rgba(0, 0, 0, 0.1);
		border-left-color: #007aff;
		border-radius: 50%;
		width: 40px;
		height: 40px;
		animation: spin 1s linear infinite;
		margin: 0 auto 15px;
	}

	/* 加载图标旋转动画 */
	@keyframes spin {
		to {
			transform: rotate(360deg);
		}
	}
</style>